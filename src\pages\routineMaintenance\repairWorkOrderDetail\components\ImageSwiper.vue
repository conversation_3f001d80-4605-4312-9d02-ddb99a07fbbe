<template>
  <view class="swiper_box">
    <uv-swiper
      :list="formatList"
      keyName="url"
      height="200"
      imgMode="aspectFit"
      :autoplay="false"
      :showTitle="true"
      @change="(e) => (currentShow = e.current)"
      @click="onSwiperClick"
    >
      <template v-slot:indicator>
        <view class="indicator">
          <view
            class="indicator__dot"
            v-for="(item, index) in formatList"
            :key="index"
            :class="[index === currentShow && 'indicator__dot--active']"
          />
        </view>
      </template>
    </uv-swiper>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { showImg } from '@/utils/index'

const props = defineProps({
  // 轮播图片列表
  swiperList: {
    type: Array,
    default: () => [],
  },
})

// 当前显示索引
const currentShow = ref(0)

// 格式化图片列表
const formatList = computed(() => {
  if (!props.swiperList) return []
  return props.swiperList.map((item) => ({ title: item.title, url: showImg(item.url) }))
})

// 点击预览图片
const onSwiperClick = (index) => {
  uni.previewImage({
    urls: formatList.value.map(item => item.url),
    current: index
  })
}
</script>

<style lang="scss" scoped>
.swiper_box {
  height: 400rpx;
  .indicator {
    display: flex;
    justify-content: center;
    &__dot {
      height: 16rpx;
      width: 16rpx;
      border-radius: 100px;
      background-color: #d9d9d9;
      margin: 0 5px;
      transition: background-color 0.3s;
      &--active {
        background-color: #4378ff;
      }
    }
  }
}
:deep(.uv-swiper__wrapper__item__wrapper__title) {
  display: flex !important;
  align-items: center;
  justify-content: center;
  bottom: unset !important;
  right: unset !important;
  width: 148rpx;
  height: 44rpx;
  border-radius: 8rpx 0rpx 16rpx 0rpx;
  background-color: rgba(37, 37, 37, 0.8);
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 32rpx;
  margin: 0 2rpx;
}
</style>
