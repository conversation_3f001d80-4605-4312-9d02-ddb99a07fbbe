<template>
  <view class="charts-box">
    <template v-if="monthStatistics.length > 0">
      <scroll-view :scroll-top="0" scroll-y="true" class="charts-parent">
        <view class="charts-item"><l-echart ref="chartRef"></l-echart></view>
      </scroll-view>
      <view class="level-box flex-between-center">
        <view
          class="level-item flex-between-center"
          :class="activeLevel === '1' ? 'active1' : ''"
          @click="changeLevel('1')"
        >
          <view class="level-icon"></view>
          <view class="level-name">超限一级</view>
        </view>
        <view
          class="level-item flex-between-center"
          :class="activeLevel === '2' ? 'active2' : ''"
          @click="changeLevel('2')"
        >
          <view class="level-icon"></view>
          <view class="level-name">超限二级</view>
        </view>
        <view
          class="level-item flex-between-center"
          :class="activeLevel === '3' ? 'active3' : ''"
          @click="changeLevel('3')"
        >
          <view class="level-icon"></view>
          <view class="level-name">超限三级</view>
        </view>
      </view>
    </template>
    <view class="no-data" v-else>
      <image class="img" :src="noDataImg" mode="aspectFit" />
      <view class="text">暂无数据</view>
    </view>
  </view>
</template>

<script setup>
import * as echarts from "echarts";
import { ref } from "vue";
import { getSysteminfo } from "@/utils";
import { AssetService } from "@/service";
import { onLoad } from "@dcloudio/uni-app";
const echartsHeight = ref("400rpx");
const activeLevel = ref("3");
let monthStatistics = ref([]);
let allMonthStatistics = ref({});
const screenWidth = getSysteminfo().phoneWindowWidth; // 屏幕宽度（px）
const rpxToPx = (rpx) => (screenWidth / 750) * rpx; // 将 rpx 转换为 px
const chartRef = ref(null);
import noDataImg from "@/static/shmStatic/image/asset-nodata-img.png";
const seriesData = ref([]);
const option = ref({
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
    confine: true,
  },
  legend: {
    show: false,
  },
  grid: {
    left: 0,
    right: "8%",
    bottom: 0,
    top: "1%",
    containLabel: true,
  },
  xAxis: [
    {
      type: "value",
      axisLine: {
        show: false,
        lineStyle: {
          color: "#999999",
        },
      },
      splitLine: {
        show: false, // 隐藏 X 轴网格线
      },
      axisLabel: {
        show: false,
        color: "#666666",
      },
    },
  ],
  yAxis: {
    type: "category",
    axisTick: { show: false },
    data: [],
    axisLine: {
      lineStyle: {
        color: "#D9D9D9",
      },
    },
    splitLine: {
      show: false, // 隐藏 Y 轴网格线
    },
    inverse: true, // 设置反转顺序，横向柱状图（yAxis为类目轴）默认从下到上排列数据，导致数组最后一个元素显示在顶部
    axisLabel: {
      color: "#A2A2A2",
      lineHeight: rpxToPx(26), // 设置行高为 20px
      formatter: function (value) {
        // 每行显示10个字符
        const maxCharsPerLine = 4;
        const regex = new RegExp(`.{1,${maxCharsPerLine}}`, "g");
        const lines = value.match(regex);
        if (lines.length > 2) {
          return lines.slice(0, 2).join("\n") + "..."; // 只显示两行，超出部分省略
        }
        return lines ? lines.join("\n") : value;
      },
    },
  },
  series: [
    {
      name: "报警次数",
      type: "bar",
      label: {
        show: true, // 直接配置 label
        position: "right",
        valueAnimation: true,
        fontSize: rpxToPx(24),
        color: "#606060",
      },
      barWidth: rpxToPx(24), // 将 40rpx 转换为 px
      barCategoryGap: 10, // , // 柱子之间的间距为类目宽度的 30%
      itemStyle: {
        borderRadius: [rpxToPx(16), rpxToPx(16), rpxToPx(16), rpxToPx(16)], // 右下、左下为圆角
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: "#F98989" }, // 渐变色起点
            { offset: 1, color: "#F55152" }, // 渐变色终点
          ],
          global: false,
        },
      },
      data: seriesData.value,
    },
  ],
});

let myChart = null;
// 获取接口数据
const getMonthStatistics = () => {
  AssetService.monthStatistics().then((res) => {
    // 横向柱状图，需反转顺序，确保数据顺序正常
    // for (const key in res.data) {
    //   if (Object.prototype.hasOwnProperty.call(res.data, key)) {
    //     res.data[key] = res.data[key].slice().reverse();
    //   }
    // }
    allMonthStatistics.value = res.data;
    // 初始默认展示超限三级
    monthStatistics.value = res.data.levelThree
      ? res.data.levelThree.map((item) => item.count * 1)
      : [];
    option.value.yAxis.data =
      res.data.levelThree?.map((item) => item.assetName) || [];
    option.value.series[0].data = monthStatistics.value;
    if (monthStatistics.value.length > 4) {
      echartsHeight.value =
        450 + 50 * (monthStatistics.value.length - 4) + "rpx";
    }
    // 组件能被调用必须是组件的节点已经被渲染到页面上
    setTimeout(async () => {
      if (!chartRef.value) return;
      myChart = await chartRef.value.init(echarts);
      myChart.setOption(option.value);
    }, 200);
  });
};

// 切换超限等级
const changeLevel = (level) => {
  activeLevel.value = level;
  if (level === "1") {
    option.value.series[0].data = allMonthStatistics.value["levelOne"]?.map(
      (item) => item.count * 1
    );
    option.value.yAxis.data =
      allMonthStatistics.value["levelOne"]?.map((item) => item.assetName) || [];
    option.value.series[0].itemStyle.color.colorStops = [
      { offset: 0, color: "#89BAFF" }, // 渐变色起点
      { offset: 1, color: "#2C84FF" }, // 渐变色终点
    ];
  } else if (level === "2") {
    option.value.series[0].data = allMonthStatistics.value["levelTwo"]?.map(
      (item) => item.count * 1
    );
    option.value.yAxis.data =
      allMonthStatistics.value["levelTwo"]?.map((item) => item.assetName) || [];
    option.value.series[0].itemStyle.color.colorStops = [
      { offset: 0, color: "#FBB87E" }, // 渐变色起点
      { offset: 1, color: "#ED862D" }, // 渐变色终点
    ];
  } else if (level === "3") {
    option.value.series[0].data = allMonthStatistics.value["levelThree"]?.map(
      (item) => item.count * 1
    );
    option.value.yAxis.data =
      allMonthStatistics.value["levelThree"]?.map((item) => item.assetName) ||
      [];
    option.value.series[0].itemStyle.color.colorStops = [
      { offset: 0, color: "#F98989" }, // 渐变色起点
      { offset: 1, color: "#F55152" }, // 渐变色终点
    ];
  }
  myChart.setOption(option.value);
};

onLoad(() => {
  setTimeout(() => {
    getMonthStatistics();
  }, 300);
});
</script>

<style lang="scss" scoped>
.no-data {
  width: 100%;
  padding: 24rpx 0;
  .img {
    margin: 0 auto;
    display: block;
    width: 160rpx;
    height: 160rpx;
  }
  .text {
    text-align: center;
    font-size: 28rpx;
    color: #a09f9f;
  }
}
.charts-box {
  width: 100%;
  .charts-parent {
    margin-top: 20rpx;
    width: 100%;
    height: 414rpx;
    box-sizing: border-box;
    padding-bottom: 28rpx;
  }
  .charts-item {
    width: 100%;
    height: v-bind(echartsHeight);
  }
  .level-box {
    padding: 0 26rpx;
    .level-item {
      box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(0, 0, 0, 0.08);
      box-sizing: border-box;
      border: 2rpx solid #fff;
      border-radius: 8rpx;
      color: #373737;
      font-size: 24rpx;
      padding: 0 18rpx;
      height: 52rpx;
      line-height: 52rpx;
      .level-icon {
        width: 24rpx;
        height: 24rpx;
        border-radius: 12rpx;
        margin-right: 8rpx;
      }
      &:nth-child(1) {
        .level-icon {
          background: #2f86ff;
        }
      }
      &:nth-child(2) {
        .level-icon {
          background: #ed862d;
        }
      }
      &:nth-child(3) {
        .level-icon {
          background: #f55152;
        }
      }
      &.active1 {
        border: 2rpx solid #2f86ff;
      }
      &.active2 {
        border: 2rpx solid #f55152;
      }
      &.active3 {
        border: 2rpx solid #f55152;
      }
    }
  }
}
</style>
