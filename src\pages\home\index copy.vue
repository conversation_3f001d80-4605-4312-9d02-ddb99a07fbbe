<!--
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-10 16:56:09
 * @Description: 
-->
<template>
  <view class="container">
    <!-- <view class="page_title">智慧养护</view> -->
    <!-- 导航栏 -->
    <uv-navbar
      title="智慧养护"
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      leftIconColor="#fff"
      :bgColor="bgColor"
      @leftClick="goBack"
    >
    </uv-navbar>
    <!-- 选择项目 -->
    <view class="project_picker" @click="toChooseProject">
      <template v-if="curProjectName">
        <image class="left_icon" :src="project_picker_icon"></image>
        <view class="cur_project">{{ curProjectName }}</view>
        <image
          class="right_icon"
          src="../../static/icon/right_icon_white.png"
        />
      </template>
    </view>
    <!-- 统计数据 -->
    <view class="data_statistics">
      <view
        class="data_item"
        v-for="(item, index) in dataStatistics"
        :key="index"
      >
        <view :class="['data_val', item.redColor ? 'red_val' : '']">{{
          item.val
        }}</view>
        <view class="data_title">{{ item.title }}</view>
      </view>
    </view>
    <!-- 菜单列表 -->
    <view class="menu_list">
      <template v-for="item in menuList" :key="item.id">
        <view class="menu_item" @click="toMenuPage(item)">
          <image class="menu_icon" :src="item.iconPath" mode="widthFix" />
          <view class="menu_title">{{ item.title }}</view>
        </view>
      </template>
    </view>

    <view class="todo_title">
      <view class="todo_title_bar"></view>
      <view class="todo_title_text">我的待办</view>
    </view>
    <SearchTabGroup @queryList="queryList" />

    <view class="today_todos" v-if="hasAuth('roadConditionInspection')">
      <view class="task_title_box">
        <view class="task_title"
          >今日路况检查待办
          <text class="num"
            >（{{ roadConditionFinishedNum }}/{{
              roadConditionTotalNum
            }}）</text
          >
        </view>
        <view class="more" @click="onMore('roadCondition')">
          <text>更多</text>
          <image
            class="more_icon"
            src="../../static/icon/more_primary_icon.png"
            mode="widthFix"
          />
        </view>
      </view>
      <view class="nodata_card" v-if="!roadConditionList.length"
        >暂无待办任务~</view
      >
      <view class="card_box" v-else>
        <RoadInspectionItemCard
          v-for="item in roadConditionList"
          :key="item.id"
          :cardItem="item"
          @onCardCallBack="roadInspectionCallback"
        ></RoadInspectionItemCard>
      </view>
    </view>
    <view class="today_todos" v-if="hasAuth('routinePreserve')">
      <view class="task_title_box">
        <view class="task_title"
          >今日日常保养待办
          <text class="num"
            >（{{ preserveFinishedNum }}/{{ preserveTotalNum }}）</text
          ></view
        >
        <view class="more" @click="onMore('preserve')">
          <text>更多</text>
          <image
            class="more_icon"
            src="../../static/icon/more_primary_icon.png"
            mode="widthFix"
          />
        </view>
      </view>
      <view class="nodata_card" v-if="!routinePreserveList.length"
        >暂无待办任务~</view
      >
      <view class="card_box" v-else>
        <RoutinePreserveItemCard
          v-for="item in routinePreserveList"
          :key="item.id"
          :cardItem="item"
          @onCardCallBack="routinePreserveCallback"
        ></RoutinePreserveItemCard>
      </view>
    </view>
    <view class="today_todos" v-if="hasAuth('routineMaintenance')">
      <view class="task_title_box">
        <view class="task_title"
          >今日日常维修待办
          <text class="num"
            >（{{ maintenanceFinishedNum }}/{{
              routineMaintenanceList.length
            }}）</text
          ></view
        >
        <view class="more" @click="onMore('maintenance')">
          <text>更多</text>
          <image
            class="more_icon"
            src="../../static/icon/more_primary_icon.png"
            mode="widthFix"
          />
        </view>
      </view>
      <view class="nodata_card" v-if="!routineMaintenanceList.length"
        >暂无待办任务~</view
      >
      <view class="card_box">
        <RoutineMaintenanceItemCard
          v-for="item in routineMaintenanceList"
          :key="item.id"
          :cardItem="item"
          @onCardCallBack="routineMaintenanceCallback"
        ></RoutineMaintenanceItemCard>
      </view>
    </view>
    <view
      class="list_nodata_img"
      v-if="
        (!hasAuth('roadConditionInspection') &&
          !hasAuth('routinePreserve') &&
          !hasAuth('routineMaintenance')) ||
        1
      "
    >
      <image
        class="img"
        src="../../static/image/homepage_nodata_20241029.png"
        mode="widthFix"
      />
      <view class="tips">道路养护，有您更美好！</view>
    </view>
    <view class="black"></view>
    <!-- 底部切换按钮 -->
    <ylg-my-btns
      :switchActive="switchActive"
      @myBtnCallback="myBtnCallback"
    ></ylg-my-btns>
    <!-- <CheckVersionModal /> -->
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import {
  onLoad,
  onShow,
  onPageScroll,
  onPullDownRefresh,
  onBackPress,
} from "@dcloudio/uni-app";
import { computed, ref } from "vue";
import RoadInspectionItemCard from "../roadConditionInspection/components/listItemCard.vue";
import RoutinePreserveItemCard from "../routinePreserve/components/listItemCard.vue";
import RoutineMaintenanceItemCard from "../routineMaintenance/components/ListItemCard.vue";
import {
  DataAcquisitionService,
  ProjectService,
  RoadInspectionService,
  RoutinePreserveService,
  RoutineMaintenanceService,
} from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { hasAuth } from "@/config/permissions.js";
// import CheckVersionModal from "@/pages/userCenter/components/CheckVersionModal.vue"; //更新版本
import project_picker_icon from "@/static/icon/project_picker_icon.png";
import SearchTabGroup from "./components/SearchTabGroup.vue";

// todo
const queryList = () => {
  console.log("queryList");
};
// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;

// 自定义导航栏背景色
// 监听页面滚动，修改导航栏背景色
let bgColor = ref("transparent");
onPageScroll((e) => {
  if (e.scrollTop > 35) {
    bgColor.value = "#4383F8";
  } else {
    bgColor.value = "transparent";
  }
});

const goBack = () => {
  console.log("日常养护返回", getCurrentPages());
  try {
    uni.navigateBack({
      delta: 1,
    });
  } catch (error) {
    console.log("日常养护返回 error", error);
  }
};

onBackPress((backOptions) => {
  console.log("触发home页返回1");
  if (backOptions.from === "backbutton") {
    console.log("触发home页返回2");
    return false;
  } else if (backOptions.from === "navigateBack") {
    console.log("触发home页返回3");
    return false;
  }
});

onLoad(async () => {
  console.log("homei请求数据");
  await getProjects();
});
onShow(() => {
  switchActive.value = false;
  curProjectName.value = projectInfo.projectName || "";
  console.log("onshow curProjectName.value", projectInfo.projectName);
  if (projectInfo.projectId) {
    getStatisticData();
    getRoadConditionTodos();
    getRoutinePreserveTodos();
    getRoutineMaintenanceTodos();
  }
});

onPullDownRefresh(async () => {
  console.log("onPullDownRefresh");
  await getProjects();
  uni.stopPullDownRefresh();
});

// 今日路况检查待办
let loading = ref(false);
let roadConditionList = ref([]);
let roadConditionFinishedNum = ref(0);
let roadConditionTotalNum = ref(0);
const getRoadConditionTodos = async () => {
  try {
    console.log("请求请求", projectInfo.projectId);

    loading.value = true;
    let { code, data } = await RoadInspectionService.getHomeTodayList(
      projectInfo.projectId
    );
    console.log("今日路况检查待办", data);
    if (code == 200) {
      roadConditionList.value = data.list || [];
      roadConditionFinishedNum.value = data.finishNum;
      roadConditionTotalNum.value = data.totalNum;
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log("请求今日列表失败", error);
  }
};

// 今日日常保养待办
let routinePreserveList = ref([]);
let preserveFinishedNum = ref(0);
let preserveTotalNum = ref(0);
const getRoutinePreserveTodos = async () => {
  try {
    let { code, data } = await RoutinePreserveService.getHomeTodayList(
      projectInfo.projectId
    );
    console.log("今日日常保养待办", code, data);

    if (code == 200) {
      routinePreserveList.value = data.list || [];
      preserveFinishedNum.value = data.finishNum;
      preserveTotalNum.value = data.totalNum;
    }
  } catch (error) {
    console.log("请求今日日常保养待办失败", error);
  }
};

// 今日日常维修待办
let routineMaintenanceList = ref([]);
let maintenanceFinishedNum = ref(0);
const getRoutineMaintenanceTodos = async () => {
  try {
    let { code, data } = await RoutineMaintenanceService.getTodayList(
      projectInfo.projectId
    );
    console.log("今日日常维修待办", code, data);
    if (code == 200) {
      data.toDo?.forEach((item) => {
        item.childWorkNum = Number(item.childWorkNum);
        if (item.finishNum == 0) {
          item.percentage = 0;
        } else {
          item.percentage =
            (Number(item.finishNum) / Number(item.childWorkNum)).toFixed(2) *
            100;
        }
      });
      routineMaintenanceList.value = data.toDo || [];
      maintenanceFinishedNum.value = data.toDo.filter(
        (item) => item.workStatus == "4" || item.workStatus == "5"
      ).length;
    }
  } catch (error) {
    console.log("请求今日列表失败", error);
  }
};

// 路况检查卡片点击回调
const roadInspectionCallback = (envRes) => {
  console.log("卡片回调", envRes);
  uni.navigateTo({
    url: `/pages/roadConditionInspection/inspection?pageFrom=home&taskStatus=${envRes.taskStatus}&taskId=${envRes.id}`,
  });
};
// 日常保养卡片点击回调
const routinePreserveCallback = (envRes) => {
  uni.navigateTo({
    url: `/pages/routinePreserve/preserve?pageFrom=home&taskStatus=${envRes.taskStatus}&taskId=${envRes.id}`,
  });
};
// 日常维修卡片点击回调
const routineMaintenanceCallback = (envRes) => {
  console.log("卡片回调", envRes);
  // 驳回的工单
  if (envRes.workResult == "0") {
    uni.navigateTo({
      url: `/pages/routineMaintenance/repairWorkOrderDetail/index?workId=${envRes.id}`,
    });
  } else {
    switch (envRes.workStatus) {
      // "待施工",
      case "2":
        uni.navigateTo({
          url: `/pages/routineMaintenance/repairWorkOrderDetail/_2StartConstruction?id=${envRes.id}&workCode=${envRes.workCode}&workStatus=${envRes.workStatus}`,
        });
        break;
      // "施工中"
      case "3":
        uni.navigateTo({
          url: `/pages/routineMaintenance/repairWorkOrderDetail/_3UnderConstruction?id=${envRes.id}&workCode=${envRes.workCode}&workStatus=${envRes.workStatus}&workResult=${envRes.workResult}`,
        });
        break;
      // "待核验"
      case "4":
        uni.navigateTo({
          url: `/pages/routineMaintenance/repairWorkOrderDetail/_4Verification?workId=${envRes.id}`,
        });
        break;
      // "已完成"
      case "5":
        uni.navigateTo({
          url: `/pages/routineMaintenance/repairWorkOrderDetail/_5Completed?workId=${envRes.id}`,
        });
        break;

      default:
        break;
    }
  }
};

// 查看更多
const onMore = (type) => {
  switch (type) {
    case "roadCondition":
      uni.navigateTo({
        url: `/pages/roadConditionInspection/index`,
      });
      break;
    case "preserve":
      uni.navigateTo({
        url: `/pages/routinePreserve/index`,
      });
      break;
    case "maintenance":
      uni.navigateTo({
        url: `/pages/routineMaintenance/index`,
      });
      break;

    default:
      break;
  }
};

let curProjectName = ref("");
let noData = ref(true);
const getProjects = async () => {
  try {
    const { code, data } = await ProjectService.getProjectList();
    console.log("请求项目", data);
    if (code == 200 && data.length > 0) {
      if (!projectInfo.projectId && !projectInfo.projectName) {
        await projectInfo.updateProject({
          projectId: data[0].projectId,
          projectName: data[0].projectName,
          projectPermission: data[0].projectPermission,
        });
      }
      curProjectName.value = projectInfo.projectName || "";
      console.log("请求项目 curProjectName.value", projectInfo.projectId);
      getStatisticData();
      getRoadConditionTodos();
      getRoutinePreserveTodos();
      getRoutineMaintenanceTodos();
    }
    noData.value = false;
  } catch (error) {
    console.log("catch", error);
    noData.value = false;
  }
};

const toChooseProject = () => {
  uni.navigateTo({
    url: `/pages/home/<USER>
  });
};

let switchActive = ref(false);
const myBtnCallback = (envRes) => {
  switch (envRes.type) {
    case "toggoleSwitch":
      switchActive.value = !switchActive.value;
      break;
    case "toReportEvent":
      uni.navigateTo({
        url: `/pages/roadConditionInspection/eventReporting?pageFrom=index&inspectObject=1`,
      });
      break;
    default:
      break;
  }
};

// 顶部统计数据
let dataStatistics = ref([
  {
    val: 0,
    title: "今日待办",
  },
  {
    val: 0,
    title: "超时待办",
    redColor: true,
  },
  {
    val: 0,
    title: "今日完成",
  },
  {
    val: 0,
    title: "今日上报",
  },
]);
const getStatisticData = async () => {
  let { code, data } = await DataAcquisitionService.getHomeStatisticData({
    projectId: projectInfo.projectId,
  });
  console.log("统计数据", data);
  dataStatistics.value = [
    {
      val: data.todoNum || 0,
      title: "今日待办",
    },
    {
      val: data.timeoutNum || 0,
      title: "超时待办",
      redColor: true,
    },
    {
      val: data.finishNum || 0,
      title: "今日完成",
    },
    {
      val: data.reportNum || 0,
      title: "今日上报",
    },
  ];
};

let menuList = computed(() => {
  return [
    {
      id: "dataAcquisition",
      url: "/pages/dataAcquisition/index",
      iconPath: "../../static/icon/data_acquisition_icon.png",
      title: "数据采集",
      // 当projectPermission>2时，才展示“数据采集”的入口，1-查看；2-编辑；3-管理
      isPermission:
        Number(projectInfo.projectPermission) >= 2 &&
        hasAuth("dataAcquisition"),
    },
    {
      id: "roadConditionInspection",
      url: "/pages/roadConditionInspection/index",
      iconPath: "../../static/icon/road_survey_icon.png",
      title: "路况检查",
      isPermission: hasAuth("roadConditionInspection"),
    },
    {
      id: "routinePreserve",
      url: "/pages/routinePreserve/index",
      iconPath: "../../static/icon/inspection_task_icon.png",
      title: "日常保养",
      isPermission: hasAuth("routinePreserve"),
    },
    {
      id: "routineMaintenance",
      url: "/pages/routineMaintenance/index",
      iconPath: "../../static/icon/maintenance_tasks_icon.png",
      title: "日常维修",
      isPermission: hasAuth("routinePreserve"),
    },
    {
      id: "eventHandling",
      url: "/pages/eventHandling/index",
      iconPath: "../../static/icon/eventHandling_home_icon.png",
      title: "事件处置",
      isPermission: hasAuth("eventHandling"),
    },
  ];
});
// 菜单页面跳转
const toMenuPage = (item) => {
  console.log("toMenuPage", item);
  if (!item.isPermission) {
    // let msg =
    //   item.id === "dataAcquisition" && Number(projectInfo.projectPermission) < 2
    //     ? "当前项目暂无编辑权限,请切换项目"
    //     : "当前账号无该功能权限,请联系管理员~";
    let msg = "暂无当前功能权限，如有需使用请联系系统管理员";
    uni.showToast({
      title: msg,
      duration: 3000,
      icon: "none",
    });
    return;
  }
  if (item.url) {
    uni.navigateTo({
      url: item.url,
    });
  }
};
</script>
<style lang="scss" scoped>
.no_data_container {
  min-height: 100vh;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .nodata_img {
    width: 516rpx;
    height: 1240rpx;
  }
}
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  padding: 0 40rpx;
  padding-top: v-bind(systemBarHeight);
  background-image: url("../../static/image/home_bg_new.png");
  background-color: #f0f5ff;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.page_title {
  text-align: center;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
  line-height: 42rpx;
}
.project_picker {
  margin-top: 84rpx;
  display: flex;
  align-items: center;
  .left_icon {
    display: block;
    margin-right: 12rpx;
    width: 36rpx;
    height: 36rpx;
  }
  .cur_project {
    font-family:
      PingFang SC,
      PingFang SC;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 40rpx;
  }
  .right_icon {
    display: block;
    margin-left: 16rpx;
    width: 20rpx;
    height: 20rpx;
  }
}
.data_statistics {
  margin-top: 32rpx;
  padding: 32rpx 38rpx;
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-radius: 24rpx;
  .data_item {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #373737;
    line-height: 40rpx;
    text-align: center;
    .data_val {
      font-family:
        Bakbak One,
        Bakbak One;
      font-weight: bold;
      font-size: 48rpx;
      color: #373737;
      line-height: 68rpx;
    }
    .red_val {
      color: #ff2d2d;
    }
    .data_title {
      margin-top: 8rpx;
      color: #909090;
    }
  }
}
.menu_list {
  margin: 40rpx 0 48rpx 0;
  // display: flex;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 36rpx 0;
  height: 336rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 28rpx;
  box-shadow: 0 0 20rpx 0 rgba(146, 153, 180, 0.2);
  .menu_item {
    // margin-right: 36rpx;
    width: 140rpx;
    height: 140rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 16rpx 22rpx 18rpx 22rpx;
    box-sizing: border-box;
    text-align: center;
    .menu_icon {
      width: 80rpx;
      height: 80rpx;
      margin-bottom: 12rpx;
    }
    .menu_title {
      font-family: PingFang SC-Regular;
      font-size: 24rpx;
      font-weight: 500;
      color: #5f6374;
      line-height: 32rpx;
    }
  }
  .menu_item:last-child {
    margin-right: 0;
  }
}
.todo_title {
  display: flex;
  align-items: center;
  gap: 0 12rpx;
  height: 52rpx;
  margin-bottom: 28rpx;
  .todo_title_bar {
    width: 8rpx;
    height: 32rpx;
    background: #3c62fa;
    border-radius: 4rpx 4rpx 4rpx 4rpx;
  }
  .todo_title_text {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #373737;
  }
}
.today_todos {
  margin-bottom: 48rpx;
}
.list_nodata_img {
  width: 670rpx;
  height: 816rpx;
  border-radius: 8rpx;
  background: #ffffff;
  box-sizing: border-box;
  padding-top: 92rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  .img {
    display: block;
    width: 486rpx;
    height: 444rpx;
  }
  .tips {
    margin-top: 48rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 36rpx;
    color: #d0d0d0;
    line-height: 50rpx;
  }
}
.black {
  height: 200rpx;
}
.btn_box {
  display: flex;
  padding-bottom: 32rpx;
  .down_btn {
    display: inline-block;
    width: 300rpx;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 32rpx;
  }
}

.task_title_box {
  display: flex;
  justify-content: space-between;
  .task_title {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 600;
    font-size: 32rpx;
    color: #373737;
    line-height: 44rpx;
    .num {
      color: #ff3132;
    }
  }
  .more {
    text {
      font-family: PingFang SC-Medium;
      font-size: 28rpx;
      color: #4378ff;
      line-height: 40rpx;
    }
    .more_icon {
      margin-left: 8rpx;
      width: 20rpx;
      height: 20rpx;
    }
  }
}
.nodata_card {
  height: 280rpx;
  text-align: center;
  box-sizing: border-box;
  padding: 120rpx 0;
  margin-top: 28rpx;
  border-radius: 28rpx;
  background: #fff;
  box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #707070;
  line-height: 40rpx;
}
.bottom_btn {
  position: fixed;
  right: 40rpx;
  bottom: 28rpx;
  z-index: 999;
  .mine_switch_btn {
    width: 68rpx;
    height: 68rpx;
    transition: transform 2 ease;
  }
  .rotated {
    transform: rotate(45deg);
  }
  .option_btn_box {
    position: absolute;
    top: -230rpx; /* 根据需要调整 */
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    transition:
      opacity 0.8s ease,
      transform 0.8s ease;
    opacity: 0;
    transform: translateY(20px); /* 初始位置在下方 */
  }
  .option_btn_box.show {
    opacity: 1;
    transform: translateY(0); /* 动画效果: 从下方移入 */
  }
  .event_report_btn,
  .mine_btn,
  .ai_btn {
    margin-top: 28rpx;
    width: 68rpx;
    height: 68rpx;
  }
}
</style>
