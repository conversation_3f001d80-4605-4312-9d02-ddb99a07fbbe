<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-11-28 17:34:55
 * @Description: 
-->
<!DOCTYPE html>
<html style="">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <title></title>
    <style>
      body,
      html {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
        /* transform: translateX(-1.5px); */
      }
      #mapDiv {
        position: relative;
      }
      img {
        object-fit: contain !important;
      }
    </style>
    <!-- 引入uniapp -->
    <!-- <script
      src="./hybrid_html_uni.webview.1.5.5.js"
      type="text/javascript"
    ></script> -->
    <script
      type="text/javascript"
      src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js"
    ></script>
    <!-- 引入天地图 -->
    <script
      src="https://api.tianditu.gov.cn/api?v=4.0&tk=307b00f916df23b531cf212d1e73216b"
      type="text/javascript"
    ></script>

    <body style="width: 100%; height: 100%" onLoad="onLoad()">
      <div id="mapDiv" style="width: 100%; height: 100%"></div>
    </body>
    <script>
      // 获取应用发来的 URL 参数
      const url = location.href;
      const urlParams = new URLSearchParams(url.split("?")[1]);
      let isDetail = JSON.parse(urlParams.get("isDetail"));
      let detailLng = "";
      let detailLat = "";
      let ControlTop = urlParams.get("ControlTop") || "530px"; // 右下角控件按钮的位置
      let pageFrom = urlParams.get("pageFrom") || ""; // 页面来源
      console.log('页面来源！！！', pageFrom);
      

      if (isDetail) {
        detailLng = urlParams.get("longitude");
        detailLat = urlParams.get("latitude");
      }

      // 接收应用发送过来的数据
      function receiveData(data) {
        console.log("下面是我收到的信息内容：", data.type);
        switch (data.type) {
          // 初始化时获取当前所处位置
          case "initLocation":
            toCurPosition(data.data.type, data.data.lng, data.data.lat);
            break;
          // 选择某个地址
          case "changeLocation":
            map.panTo(new T.LngLat(data.data.lng, data.data.lat));
            upnewCenterMark(new T.LngLat(data.data.lng, data.data.lat));
            break;
          // 通过经纬度逆解析中文地址
          case "getReverseAddress":
            reverseGeocode(new T.LngLat(data.data.lng, data.data.lat));
            break;
          // 桩号采集页面，点击编辑按钮，切换页面状态
          case "changeDetailState":
            isDetail = false;
            break;
          // 更新桩号列表
          case "upnewPilelist":
            upnewPileMarkers(data.data.list);
            break;
            // 选择某个桩号
          case "choosePile":
            addPileLabel(data.data.choosedId, data.data.dataList);
            break;

          default:
            break;
        }
      }

      let map; //地图实例
      let zoom = 15; //缩放等级
      let resetMarker; // 右下角重置标记
      let centerMarker; // 中心点标记
      let pileMarkers = []; // 桩号点标记
      let markLabel; // 桩号点label
      let isHandlingViewport = false; // 控制正在调整视野时的行为
      let curPosition = ''; // 当前位置经纬度

      //等待dom加载
      function onLoad() {
        let lnglat = {};
        const devicePixelRatio = window.devicePixelRatio || 1;
        console.log("devicePixelRatio", devicePixelRatio);
        // map = new T.Map('mapDiv',{  renderer: 'webgl' // 使用WebGL进行 });//实例化地图
        map = new T.Map("mapDiv", { dpi: devicePixelRatio }); //实例化地图
        map.centerAndZoom(new T.LngLat(114.09974, 22.549), zoom); //默认数值
        map.isDrag(true);
        map.disableDoubleClickZoom(); // 禁止双击放大
        
        // 添加控件，右下角重置按钮
        addResetControl();

        // 设置事件监听--移动结束
        map.addEventListener("moveend", function () {
          if (isHandlingViewport) {
            return; // 如果正在调整视野，跳过moveend的处理
          }
          if (isDetail) {
            console.log("详情页，拖动地图");
          } else {
            console.log("1触发移动", curPosition);
            upnewCenterMark(curPosition||'');
          }
        });

        // 设置事件监听--缩放结束
        map.addEventListener("zoomend", function(e) {
          if(pageFrom == "pileNumberCollection" || pageFrom == "coordinatePicking"){
            // 获取当前地图的缩放级别
            let zoomLevel = map.getZoom();
            // console.log('缩放地图了！！', zoomLevel);
            zoom = zoomLevel;
          }
        });

        // 等待uniapp加载完成后发送信息
        document.addEventListener("UniAppJSBridgeReady", () => {
          uni.postMessage({
            data: {
              title: "天地图",
              msg: "我滴任务完成啦",
            },
          });
        });
      }

      //根据经纬度重新设置地图
      function toCurPosition(sceneType, targetLng, targetLat) {
        let geolocation = new T.Geolocation();
        getCurrentPositionCallback = function (e) {
          console.log("根据经纬度重新设置地图", sceneType, targetLng, targetLat);
          // reverseGeocode(e.lnglat)
          if (targetLng && targetLat) {
            if (sceneType === "viewport") {
              // 场景1：桩号选择——当前选择桩号为本地新增桩号，且在1km视野范围以外；
              // -----：则需要调整地图视野，将该桩号囊括进来；
              isHandlingViewport = true; // 开始调整视野
              console.log("！！！调整缩放比例，涵盖新增桩号");

              map.centerAndZoom(e.lnglat, zoom);
              map.setViewport([e.lnglat, new T.LngLat(targetLng, targetLat)]);
              if (centerMarker) { // 如果已经有中心标记，先移除
                map.removeOverLay(centerMarker);
              }
              centerMarker = new T.Marker(e.lnglat);
              map.addOverLay(centerMarker);
              isHandlingViewport = false; // 完成视野调整
            } else if(sceneType === "curPile") {
              // 场景2：桩号采集——当前选择桩号为接口返回桩号；
              // -----：则需要将该桩号设置为地图中心点；
              map.centerAndZoom(new T.LngLat(targetLng, targetLat), zoom);
              if (centerMarker) {
                map.removeOverLay(centerMarker);
              }
            } else if (sceneType === "getPosition") {
              // 场景3：桩号采集——桩号采集页面，调用uni.getLocation获取的当前定位，需要传递进来设置为地图中心点，表示用户所在位置；
              curPosition = new T.LngLat(targetLng, targetLat);
              console.log("获取当前定位", targetLng, targetLat, curPosition);
              map.centerAndZoom(new T.LngLat(targetLng, targetLat), zoom);
            }
          } else {
            console.log("！！！原本初始逻辑，中心点为当前定位");
            if (this.getStatus() == 0) {
              map.centerAndZoom(e.lnglat, zoom);
              lnglat = e.lnglat;
              centerMarker = new T.Marker(e.lnglat);
              map.addOverLay(centerMarker);
            }
            if (this.getStatus() == 1) {
              map.centerAndZoom(e.lnglat, e.level);
              lnglat = e.lnglat;
              centerMarker = new T.Marker(e.lnglat);
              map.addOverLay(centerMarker);
            }
            map.setZoom(zoom);
          }
        };
        //获取当前用户经纬度
        geolocation.getCurrentPosition(getCurrentPositionCallback);
      }

      // 添加右下角控件，重置中心点的位置
      let LocationControl = T.Control.extend({
        initialize: function () {
          // 创建控件元素
          this.button = document.createElement("div");
          this.button.innerHTML =
            '<img src="../icon/current_position.png" style="width:40px;height:40px;">'; // 自定义图标
          this.button.style.cursor = "pointer";
          this.button.style.poaition = "absolute";
          this.button.style.top = ControlTop;
          this.button.style.right = "0px";
          this.button.style.display = "flex";
          // this.button.style.alignItems = "center";
          // this.button.style.justifyContent = "center";
          this.button.style.boxSizing = "border-box";
          this.button.style.padding = "20px";
          this.button.style.width = "80px";
          this.button.style.height = "80px";
          // 通过 JavaScript 禁用点击高亮效果
          this.button.style.setProperty('-webkit-tap-highlight-color', 'transparent');
          // this.button.style.backgroundColor = "pink";
          this.button.title = "定位当前位置";
          let targetElement = document.getElementById("mapDiv");
          targetElement.appendChild(this.button);
          // 绑定点击事件
          this.button.onclick = () => {
            if (isDetail) {
              this.resetToDetailLocate();
            } else {
              this.locateUser();
            }
          };
        },
        onAdd: function (map) {
          return this.button;
        },
        onRemove: function (map) {
          // 清理工作
        },
        // 设置地图中心为用户当前位置
        locateUser: function () {
          console.log("点击右下角重置按钮");

          // if (navigator.geolocation) {
          //   navigator.geolocation.getCurrentPosition(
          //     (position) => {
          //       const lng = position.coords.longitude;
          //       const lat = position.coords.latitude;
          //       map.panTo(new T.LngLat(lng, lat));
          //       upnewCenterMark(new T.LngLat(lng, lat));
          //       console.log("当前位置:", lng, lat);
          //     },
          //     (error) => {
          //       console.error("获取位置失败:", error);
          //       alert("无法获取当前位置");
          //     }
          //   );
          // } else {
          //   alert("浏览器不支持地理定位");
          // }
          // 告诉应用，点击右下角重置按钮了
          uni.postMessage({
            data: { msg: "点击右下角重置按钮", action: "resetClick" },
          });



        },
        // 设置地图中心为详情位置
        resetToDetailLocate: function () {
          map.panTo(new T.LngLat(detailLng, detailLat));
          upnewCenterMark(new T.LngLat(detailLng, detailLat));
        },
      });
      function addResetControl() {
        resetMarker = new LocationControl();
        map.addControl(resetMarker);
      }

      // 更新中心点标记
      function upnewCenterMark(centerP = "") {
        let center = centerP || map.getCenter();
        // 清空当前定位（避免下一次触发地图移动后，地图不更新）
        curPosition = '';
        console.log("2更新中心点标记", curPosition);

        // 1清除所有标记
        map.clearOverLays();
        // 2更新中心标记
        centerMarker = new T.Marker(center);
        map.addOverLay(centerMarker);
        if (!isDetail) {
          // 3告诉应用，移动了地图，中心点发生变化
          uni.postMessage({
            data: { msg: "地图中心点变化", action: "moveMap", center },
          });
          reverseGeocode(center);
        }
      }

      // 更新附近桩号标注点
      function upnewPileMarkers(piles) {
        pileMarkers.forEach((pm) => {
          map.removeOverLay(pm);
        });
        pileMarkers = [];

        piles.forEach((pile) => {
          addPileMarker(pile);
        });
        console.log("渲染桩号完毕", pileMarkers);
      }
      function addPileMarker(pile) {
        let markerObj = {
          icon: new T.Icon({
            iconUrl: "../icon/pile_number_mark.png",
            iconSize: new T.Point(30, 40),
            iconAnchor: new T.Point(15, 30),
          }),
        };
        let pointMarker = new T.Marker(
          new T.LngLat(pile.longitude, pile.latitude),
          markerObj
        );
        map.addOverLay(pointMarker);
        pileMarkers.push(pointMarker);
      }

      // 点击某个桩号，添加label
      function addPileLabel(pileId, list) {
        console.log("addPileLabel", pileId, list);
        if (markLabel) {
          map.removeOverLay(markLabel); // 仅在 markLabel 有效时移除，移除所有桩号点的label
        }
        list.forEach((item) => {
          console.log("addPileLabeladdPileLabel", item);
          if (item.id == pileId) {
            markLabel = new T.Label({
              text: item.stakeName,
              position: new T.LngLat(item.longitude, item.latitude),
              offset: new T.Point(0, -20),
            });
            map.addOverLay(markLabel); // 给当前选中的桩号设置label
          }
        });
      }

      // 创建地理编码，逆地理编码对象
      let geocode = new T.Geocoder();
      // 通过经纬度，获取地址
      function reverseGeocode(LngLat) {
        console.log("3逆解析中文地址");
        geocode.getLocation(LngLat, (res) => {
          let result = res.result;
          if (result && result.msg === "ok") {
            uni.postMessage({
              data: {
                msg: "通过经纬度逆解析中文地址",
                action: "reverseGeocode",
                address: result.result.formatted_address,
              },
            });
          }
        });
      }
    </script>
  </head>
</html>
