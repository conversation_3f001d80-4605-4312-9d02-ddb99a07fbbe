<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-10 15:55:01
 * @Description: 
-->
<template>
  <view class="page_container">
    <view class="top_bg"></view>
    <view class="page_content">
      <view class="main_info_card">
        <view>
          <view class="title-box">
            <view class="left">
              <view class="bar"> </view>
              <view class="title">计划详情 </view>
              <view class="required">必填 </view>
            </view>
          </view>
          <view class="info_box">
            <view class="left_title">结束时间</view>
            <view class="right_content">
              <uv-input
                v-model="state.fDate"
                disabled
                disabledColor="#ffffff"
                placeholderStyle="color: rgb(192, 196, 204);"
                placeholder="请选择"
                suffixIcon="arrow-right"
                suffixIconStyle="color: #8E8E8E;width: 7px;height: 12px;"
                border="none"
                @click="showPicker"
              ></uv-input>
              <uv-datetime-picker
                ref="datetimePicker"
                v-model="state.date"
                mode="date"
                :formatter="dateFormatter"
                :minDate="minDate"
                @confirm="confirmDate"
              >
              </uv-datetime-picker>
            </view>
          </view>
          <view class="info_box">
            <view class="left_title">保养路段</view>
            <view class="right_content">
              <uv-input
                v-model="state.sectionName"
                disabled
                disabledColor="#ffffff"
                placeholderStyle="color: rgb(192, 196, 204);"
                placeholder=""
                suffixIcon="arrow-right"
                suffixIconStyle="color: #8E8E8E;width: 7px;height: 12px;"
                border="none"
              ></uv-input>
            </view>
          </view>
          <view class="info_box">
            <view class="left_title">起点桩号</view>
            <view class="right_content">
              <uv-input
                v-model="state.stakeName"
                disabled
                disabledColor="#ffffff"
                placeholder="请选择"
                suffixIcon="arrow-right"
                suffixIconStyle="color: #8E8E8E;width: 7px;height: 12px;"
                border="none"
                @click="chooseStake"
              ></uv-input>
            </view>
          </view>
          <view class="info_box">
            <view class="left_title">终点桩号</view>
            <view class="right_content">
              <uv-input
                v-model="state.endStakeName"
                disabled
                disabledColor="#ffffff"
                placeholder="请选择"
                suffixIcon="arrow-right"
                suffixIconStyle="color: #8E8E8E;width: 7px;height: 12px;"
                border="none"
                @click="chooseEndStake"
              ></uv-input>
            </view>
          </view>
          <view class="trajectory-box">
            <view class="trajectory-title">是否生成保养轨迹</view>
            <view class="trajectory-tabs">
              <view
                v-for="(item, index) in dictData.inspectTrajectory"
                :key="index"
                class="trajectory-tab"
                :class="{
                  'trajectory-active': item.dictKey === activeInspectTrajectory,
                }"
                @click="trajectoryClick(item.dictKey)"
              >
                {{ item.dictValue }}
              </view>
            </view>
          </view>
        </view>
        <view class="content-box">
          <view class="title-box">
            <view class="left">
              <view class="bar"> </view>
              <view class="title">保养内容 </view>
              <view class="required">必填 </view>
            </view>
            <uv-icon
              style="position: absolute; right: 28rpx; top: 28rpx"
              name="plus-circle"
              color="#A09F9F"
              size="14"
              @click="chooseContent"
            ></uv-icon>
          </view>
          <view v-if="contentList.length" class="content-list">
            <view
              class="content-item"
              v-for="(item, index) in contentList"
              :key="index"
            >
              <view class="left">
                <view class="name">
                  {{ "【" + item.code + "】" + item.name }}
                </view>
                <view class="unit">
                  {{
                    "¥" +
                    (item.unitPrice === null || item.unitPrice === ""
                      ? "-"
                      : item.unitPrice) +
                    "/" +
                    item?.unitName
                  }}
                </view>
              </view>
              <view
                v-if="item?.planNumber === null || item?.planNumber === ''"
                class="center"
                @click="openContentPopup(item)"
              >
                <image
                  class="icon"
                  :src="eventHandling_num_null"
                  mode="aspectFill"
                />
                <view class="num" style="color: #f06060">-</view>
              </view>
              <view v-else class="center" @click="openContentPopup(item)">
                <image
                  class="icon"
                  :src="eventHandling_num_true"
                  mode="aspectFill"
                />
                <view class="num">{{ item.planNumber }}</view>
              </view>

              <view class="right" @click="openContentPopup(item)">
                <uv-icon name="arrow-right" color="#8E8E8E" size="18"></uv-icon>
              </view>
            </view>
          </view>
          <BlankBox v-else />
        </view>
        <view class="userList-box">
          <view class="title-box">
            <view class="left">
              <view class="bar"> </view>
              <view class="title">保养人员 </view>
              <view class="required">必填 </view>
            </view>
            <uv-icon
              style="position: absolute; right: 28rpx; top: 28rpx"
              name="plus-circle"
              color="#A09F9F"
              size="14"
              @click="chooseUsers"
            ></uv-icon>
          </view>
          <view v-if="userList.length" class="userList-list">
            <view v-if="userList.length > 4" class="userList-items">
              <view
                class="userList-item"
                v-for="(item, index) in userList.slice(0, 4)"
                :key="index"
              >
                <image
                  v-if="item.userFace"
                  style="width: 76rpx; height: 76rpx"
                  :src="showImg(item.userFace)"
                  mode="aspectFill"
                />
                <view v-else>
                  {{ item.userName.slice(-2) }}
                </view>
              </view>
              <view class="userList-item"> +{{ userList.length - 4 }} </view>
            </view>
            <view v-else class="userList-items">
              <view
                class="userList-item"
                v-for="(item, index) in userList"
                :key="index"
              >
                <image
                  v-if="item.userFace"
                  style="width: 76rpx; height: 76rpx"
                  :src="showImg(item.userFace)"
                  mode="aspectFill"
                />
                <view v-else>
                  {{ item.userName.slice(-2) }}
                </view>
              </view>
            </view>
          </view>
          <BlankBox v-else />
        </view>
        <view>
          <view class="title-box">
            <view class="left">
              <view class="bar"> </view>
              <view class="title">路线规划 </view>
              <view class="required">必填 </view>
            </view>
          </view>
          <view class="info_box" style="border-bottom: 2rpx solid #f0f0f0">
            <view class="left_title">路线规划</view>
            <view class="right_content">
              <uv-input
                disabled
                disabledColor="#ffffff"
                :placeholder="routeInputInfo.tip"
                :placeholderStyle="routeInputInfo.placeholderStyle"
                suffixIcon="arrow-right"
                suffixIconStyle="color: #8E8E8E;width: 7px;height: 12px;"
                border="none"
                @click="routePlanning"
              ></uv-input>
            </view>
          </view>
        </view>
        <view>
          <view class="title-box">
            <view class="left">
              <view class="bar"> </view>
              <view class="title">保养说明 </view>
            </view>
          </view>
          <view class="remark-box">
            <uv-textarea
              v-model="state.remark"
              count
              height="120px"
              maxlength="150"
              placeholder="请输入保养说明（最多150字）"
            ></uv-textarea>
          </view>
        </view>
        <uv-skeleton
          class="skeleton"
          rows="14"
          title
          :loading="pageLoading"
        ></uv-skeleton>
      </view>
      <view style="height: 300rpx"> </view>
    </view>
    <view class="submit-btn">
      <view class="submit-left">
        <view class="cost">预计花费 </view>
        <view class="total">
          <view class="num">{{ planCost }}</view>
          <view class="unit">元</view>
        </view>
      </view>
      <view
        v-if="canLanch"
        class="submit-right can-submit"
        @click="creatPlanAndOpen"
      >
        启用
      </view>
      <view v-else class="submit-right">启用</view>
    </view>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      bgColor="rgba(255,255,255,0.5)"
      font-size="24rpx"
    ></uv-loading-page>
    <ContentPopup
      style="z-index: 9999"
      ref="contentPopupRef"
      @contentChange="contentChange"
    />
  </view>
</template>
<script setup>
import { ref, reactive, computed } from "vue";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { DataAcquisitionService, eventHandlingApi } from "@/service";
import dayjs from "dayjs";
import BlankBox from "./components/BlankBox.vue";
import eventHandling_num_null from "@/static/icon/eventHandling_num_null.png";
import eventHandling_num_true from "@/static/icon/eventHandling_num_true.png";
import { showImg } from "@/utils";

const activeInspectTrajectory = ref("2");
const trajectoryClick = (key) => {
  activeInspectTrajectory.value = key;
  state.inspectTrajectory = key;
};
const state = reactive({
  date: "",
  fDate: "",
  sectionId: "",
  sectionName: "",
  stakeId: "",
  stakeName: "",
  stakeLongitude: "",
  stakeLatitude: "",
  stakeAddress: "",

  endStakeId: "",
  endStakeName: "",
  endStakeLongitude: "",
  endStakeLatitude: "",
  endStakeAddress: "",

  inspectTrajectory: activeInspectTrajectory.value,
  remark: "",
});
const routeInfo = reactive({
  url: "",
  tab: "",
  type: "",
});
const routeInputInfo = computed(() => {
  if (routeInfo.url) {
    return {
      tip: "已规划",
      placeholderStyle: "color:#4378FF;",
    };
  } else if (
    state.stakeLongitude &&
    state.stakeLatitude &&
    state.stakeAddress &&
    state.endStakeLongitude &&
    state.endStakeLatitude &&
    state.endStakeAddress
  ) {
    return {
      tip: "规划路线",
      placeholderStyle: "color:#A09F9F;",
    };
  } else {
    return {
      tip: "请先补充完整桩号信息",
      placeholderStyle: "color:#F06F6F;",
    };
  }
});
const datetimePicker = ref();
const minDate = ref(dayjs().valueOf());
const showPicker = () => {
  datetimePicker.value.open();
};
const confirmDate = (e) => {
  state.fDate = dayjs(e.value).format("YYYY-MM-DD");
};
const dateFormatter = (type, value) => {
  if (type === "year") {
    return `${value}年`;
  }
  if (type === "month") {
    return `${value}月`;
  }
  if (type === "day") {
    return `${value}日`;
  }
  return value;
};

const chooseStake = () => {
  uni.navigateTo({
    url: `/pages/eventHandling/addMaintainPlan/components/StakeList?&stakeType=start&sectionId=${
      state.sectionId
    }&stakeId=${state.stakeId}`,
  });
};
const chooseEndStake = () => {
  uni.navigateTo({
    url: `/pages/eventHandling/addMaintainPlan/components/StakeList?&stakeType=end&sectionId=${
      state.sectionId
    }&stakeId=${state.endStakeId}`,
  });
};
// popup
import ContentPopup from "./components/ContentPopup.vue";

const contentPopupRef = ref();
const openContentPopup = (record) => {
  contentPopupRef.value.open(record);
};
const contentList = ref([]);
const planCost = computed(() => {
  const blo = contentList.value.every(
    (item) => item?.planNumber === null || item?.planNumber === ""
  );
  if (blo) {
    return "-";
  } else {
    const isValidArr = contentList.value.filter(
      (item) => !(item?.planNumber === null || item?.planNumber === "")
    );
    return isValidArr.reduce((pre, cur) => {
      return pre + Number(cur.planNumber) * Number(cur.unitPrice);
    }, 0);
  }
});
const chooseContent = () => {
  uni.navigateTo({
    url: `/pages/eventHandling/addMaintainPlan/components/ContentList?contentList=${JSON.stringify(contentList.value)}`,
  });
};

const contentChange = (record, type) => {
  switch (type) {
    case "comfirm":
      contentList.value = contentList.value.map((item) => {
        return item.id === record.id ? record : item;
      });
      break;
    case "delete":
      contentList.value = contentList.value.filter(
        (item) => item.id !== record.id
      );
      break;
    default:
      break;
  }
};
const userList = ref([]);
const chooseUsers = () => {
  uni.navigateTo({
    url: `/pages/eventHandling/addMaintainPlan/components/UserList?userList=${JSON.stringify(userList.value)}`,
  });
};
const routePlanning = () => {
  if (
    state.stakeLongitude &&
    state.stakeLatitude &&
    state.stakeAddress &&
    state.endStakeLongitude &&
    state.endStakeLatitude &&
    state.endStakeAddress
  ) {
    const startParams = `stakeLongitude=${String(state.stakeLongitude)}&stakeLatitude=${String(state.stakeLatitude)}&stakeAddress=${String(state.stakeAddress)}`;
    const endParams = `endStakeLongitude=${String(state.endStakeLongitude)}&endStakeLatitude=${String(state.endStakeLatitude)}&endStakeAddress=${String(state.endStakeAddress)}`;
    uni.navigateTo({
      url: `/pages/eventHandling/addMaintainPlan/components/RoutePlanning?isDetail=${true}&${startParams}&${endParams}&activeTab=${routeInfo.tab}&activePlan=${routeInfo.type}`,
    });
  } else {
    uni.showToast({
      icon: "none",
      title: "请先选择起点桩号和终点桩号",
    });
  }
};
const dictData = reactive({
  inspectTrajectory: [],
});
const getDicts = async () => {
  const { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes: "inspectTrajectory",
  });
  if (code == 200) {
    for (const key in dictData) {
      dictData[key] = data[key];
    }
  }
};
const taskId = ref("");
const eventHandleRemark = ref("");
const pageLoading = ref(false);
onLoad((options) => {
  taskId.value = options?.taskId;
  eventHandleRemark.value = options?.eventHandleRemark;
  getDicts();
  getDetail();
  uni.$on("updateContentData", (data) => {
    console.log("接收到保养内容数据:", data);
    contentList.value = data;
  });
  uni.$on("updateUserData", (data) => {
    console.log("接收到保养人员数据:", data);
    userList.value = data;
  });
  uni.$on("updateStakeData", (data, type) => {
    console.log("接收到桩号数据:", data, type);
    const { id, stakeName, longitude, latitude, locationAddress } = data;
    routeInfo.url = "";
    routeInfo.tab = "";
    routeInfo.type = "";
    switch (type) {
      case "start":
        state.stakeId = id;
        state.stakeName = stakeName;
        state.stakeLongitude = longitude;
        state.stakeLatitude = latitude;
        state.stakeAddress = locationAddress;
        break;
      case "end":
        state.endStakeId = id;
        state.endStakeName = stakeName;
        state.endStakeLongitude = longitude;
        state.endStakeLatitude = latitude;
        state.endStakeAddress = locationAddress;
        break;
      default:
        break;
    }
  });

  uni.$on("updateRouteInfo", (data) => {
    console.log("接收到规划:", data);
    const { url, tab, type } = data || {};
    routeInfo.url = url;
    routeInfo.tab = tab;
    routeInfo.type = type;
  });
});
onUnload(() => {
  uni.$off("updateContentData");
  uni.$off("updateUserData");
  uni.$off("updateStakeData");
  uni.$off("updateRouteInfo");
});
const detailData = ref({});
const getDetail = async () => {
  try {
    pageLoading.value = true;
    const { code, data } = await eventHandlingApi.detailWithTask(taskId.value);
    console.log("任务详情", code, data);
    if (code == 200) {
      detailData.value = data;
      const { sectionId, sectionName, stakeId, endStakeId } = data;
      state.sectionId = sectionId;
      state.sectionName = sectionName;
      if (stakeId) {
        const { code: startCode, data: startData } =
          await eventHandlingApi.stakeDetail(stakeId);
        if (startCode === 200) {
          const { id, stakeName, longitude, latitude, locationAddress } =
            startData;
          state.stakeId = id;
          state.stakeName = stakeName;
          state.stakeLongitude = longitude;
          state.stakeLatitude = latitude;
          state.stakeAddress = locationAddress;
        }
      }
      if (endStakeId) {
        const { code: endCode, data: endData } =
          await eventHandlingApi.stakeDetail(endStakeId);
        if (endCode === 200) {
          const { id, stakeName, longitude, latitude, locationAddress } =
            endData;
          state.endStakeId = id;
          state.endStakeName = stakeName;
          state.endStakeLongitude = longitude;
          state.endStakeLatitude = latitude;
          state.endStakeAddress = locationAddress;
        }
      }
    }
    pageLoading.value = false;
  } catch (error) {
    pageLoading.value = false;
  }
};

const canLanch = computed(() => {
  const disposalValid =
    state.fDate &&
    state.sectionId &&
    state.stakeId &&
    state.endStakeId &&
    state.inspectTrajectory;
  const maintainValid =
    contentList.value.length > 0 &&
    contentList.value.every(
      (item) => !(item?.planNumber === null || item?.planNumber === "")
    );
  const userValid = userList.value.length > 0;
  const routeValid = routeInfo.url;
  if (disposalValid && maintainValid && userValid && routeValid) {
    return true;
  }
  return false;
});
const creatPlanAndOpen = () => {
  console.log("canLanch", canLanch.value);

  const { projectId, sectionId, id } = detailData.value;
  const params = {
    planName: null,
    planCode: null,
    startDate: dayjs().format("YYYY-MM-DD"),
    endDate: state.fDate,
    projectId,
    sectionId,
    startStakeId: state.stakeId,
    startStakeName: state.stakeName,

    endStakeId: state.endStakeId,
    endStakeName: state.endStakeName,
    inspectTrajectory: state.inspectTrajectory,
    remark: state.remark,
    planSectionList: [
      {
        id: null,
        startStakeId: state.stakeId,
        startStakeName: state.stakeName,
        startLongitude: Number(state.stakeLongitude),
        startLatitude: Number(state.stakeLatitude),
        startAddress: state.stakeAddress,

        endStakeId: state.endStakeId,
        endStakeName: state.endStakeName,
        endLongitude: Number(state.endStakeLongitude),
        endLatitude: Number(state.endStakeLatitude),
        endAddress: state.endStakeAddress,
        planUserList: userList.value.map((item) => {
          return {
            userId: item.userId,
          };
        }),
        trajectoryUrl: routeInfo.url,
        taskFinishRule: "2", // todo??? 需求没说 默认 一人完成
      },
    ],
    maintainConfigs: contentList.value.map((item) => {
      const { id, name, unitName, unitPrice, planNumber } = item;
      return {
        configId: id, //配置id
        content: name, // 作业内容
        units: unitName, // 单位
        unitPrice, // 单价（元）
        planNumber, // 计划作业数量
        planTotal: Number(unitPrice) * Number(planNumber), // 预计花费（元）
      };
    }),
    eventId: id,
    handleType: "8", // 写死
    eventHandleRemark: eventHandleRemark.value, // 外层件处置备注
  };
  console.log(params, "params");
  eventHandlingApi
    .dailyMaintainPlanSave(params)
    .then((res) => {
      if (res.code === 200) {
        console.log("requesRes", res);
        uni.showToast({
          icon: "none",
          title: "生成计划并启用成功！",
        });
        uni.navigateBack({
          delta: 2,
        });
      }
    })
    .finally(() => {});
};
</script>
<style lang="scss" scoped>
$borderStyle: 2rpx solid #f0f0f0;
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page_container {
  background-color: #f2f2f2f2;
  min-height: 100vh;
  overflow: hidden;
  position: relative;
  .top_bg {
    position: fixed;
    top: 0;
    width: 100vw;
    height: 100rpx;
    background-color: #065bff;
  }
  .page_content {
    position: relative;
    top: 20rpx;
    padding: 0 40rpx;
    box-sizing: border-box;
  }
  .main_info_card {
    position: relative;
    box-sizing: border-box;
    width: 670rpx;
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
    padding-top: 2rpx;
    .title-box {
      position: relative;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      padding: 0 28rpx;
      margin: 40rpx 0;
      .left {
        display: flex;
        align-items: center;
        .bar {
          margin-right: 8rpx;
          width: 6rpx;
          height: 28rpx;
          background: #4378ff;
          border-radius: 4rpx;
        }
        .title {
          margin-right: 12rpx;
          font-size: 32rpx;
          font-weight: bold;
          color: #373737;
        }
        .required {
          display: inherit;
          align-items: center;
          justify-content: center;
          width: 84rpx;
          height: 40rpx;
          font-size: 24rpx;
          color: #f06f6f;
          background: #fff0f0;
          border-radius: 8rpx;
        }
      }
    }

    .info_box {
      display: flex;
      align-items: center;
      //   margin-top: 24rpx;
      padding: 0 28rpx;
      height: 88rpx;
      border-top: $borderStyle;
      .left_title {
        width: 140rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #a09f9f;
        line-height: 40rpx;
      }
      .right_content {
        flex: 1;
        font-weight: 400;
        font-size: 28rpx;
        color: #373737;
        line-height: 40rpx;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // overflow: hidden;
      }
      .right_excel {
        margin-top: 20rpx;
        width: 100%;
        border-radius: 4rpx;
        border: 2rpx solid #f2f2f2;
        .table_header {
          height: 50%;
          line-height: 48rpx;
          background-color: rgba(242, 242, 242, 1);
          font-weight: 400;
          font-size: 28rpx;
          color: #636363;
        }
        .table_body {
          height: 50%;
          line-height: 48rpx;
          font-weight: 400;
          font-size: 28rpx;
          color: #404040;
          border-bottom: 2rpx solid #f2f2f2;
        }
        .table_body:last-child {
          border-bottom: none;
        }
        .table_item_text {
          width: 50%;
          text-align: center;
        }
      }
    }
    .trajectory-box {
      // height: 176rpx;
      padding: 24rpx 28rpx 0;
      // margin: 24rpx 0 28rpx 0;
      border-top: $borderStyle;
      border-bottom: $borderStyle;
      .trajectory-title {
        font-weight: 400;
        font-size: 28rpx;
        color: #a09f9f;
        line-height: 40rpx;
      }
      .trajectory-tabs {
        display: flex;
        gap: 0 96rpx;
        margin-top: 20rpx;
        margin-bottom: 28rpx;
        .trajectory-tab {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 200rpx;
          height: 64rpx;
          background: #f2f2f2;
          border-radius: 16rpx;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #606060;
          &.trajectory-active {
            background: #4378ff;
            color: #fff;
          }
        }
      }
    }
    .content-box {
      .content-list {
        .content-item {
          display: flex;
          justify-content: space-between;
          height: 124rpx;
          border-top: 2rpx solid #f2f2f2;
          padding: 24rpx 28rpx;
          box-sizing: border-box;
          .left {
            .name {
              width: 308rpx;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 400;
              font-size: 28rpx;
              color: #404040;
              margin-bottom: 16rpx;
            }
            .unit {
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 400;
              font-size: 24rpx;
              color: #a09f9f;
              line-height: 32rpx;
            }
          }
          .center {
            display: flex;
            align-items: center;
            width: 180rpx;
            .icon {
              display: block;
              width: 26rpx;
              height: 20rpx;
            }
            .num {
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 400;
              font-size: 28rpx;
              color: #a09f9f;
              line-height: 32rpx;
            }
          }
          .right {
            display: flex;
            align-items: center;
          }
        }
      }
      .content-list:last-child {
        border-bottom: 2rpx solid #f2f2f2;
      }
    }
    .userList-box {
      .userList-list {
        .userList-items {
          display: flex;
          gap: 0 28rpx;
          height: 124rpx;
          padding: 24rpx 28rpx;
          box-sizing: border-box;
          border-top: 2rpx solid #f2f2f2;
          border-bottom: 2rpx solid #f2f2f2;
          .userList-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 76rpx;
            height: 76rpx;
            background: #4378ff;
            border-radius: 8rpx;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #ffffff;
          }
        }
      }
    }
    .remark-box {
      margin: 0 28rpx 52rpx;
    }
    .touch_bar {
      .down_img {
        position: absolute;
        bottom: 8rpx;
        left: 50%;
        transform: translateX(-40rpx);
        display: block;
        width: 80rpx;
        height: 8rpx;
      }
    }
  }
  .submit-btn {
    display: flex;
    align-items: center;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 160rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0 -8rpx 20rpx 0 rgba($color: #444c6c, $alpha: 0.15);
    padding: 0 40rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    .submit-left {
      flex: 1;
      overflow: hidden;
      .cost {
        font-size: 28rpx;
        color: #404040;
        margin-bottom: 2rpx;
      }
      .total {
        display: flex;
        gap: 0 12rpx;
        .num {
          font-size: 40rpx;
          line-height: 40rpx;
          color: #3c62fa;
          align-self: flex-end;
        }
        .unit {
          align-self: flex-end;
          font-size: 24rpx;
          color: #a09f9f;
        }
      }
    }

    .submit-right {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 232rpx;
      height: 96rpx;
      border-radius: 8rpx;
      background: #b0b0b0;
      font-weight: 500;
      font-size: 40rpx;
      color: #ffffff;
      &.can-submit {
        background: #4378ff;
      }
    }
  }
}
</style>
