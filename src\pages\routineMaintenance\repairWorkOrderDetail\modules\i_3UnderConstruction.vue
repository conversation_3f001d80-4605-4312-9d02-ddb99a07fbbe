<template>
  <view class="construction-container">
    <view class="construction-content">
      <view class="content-wrapper">
        <ConstructionCard
          :base-info="repairWorkOrderBaseInfo"
          :detail-info="repairWorkOrderDetail"
          :child-order-list="repairChildWorkOrderList"
          :config-detail="workOrderConfigDetail"
          :is-show-bar="true"
          :is-show-mark="hasReject"
        />
      </view>

      <!-- 正常流程 -->
      <view v-if="!hasReject">
        <view class="child-order-wrap">
          <ChildWorkOrderDetail
            :child-work-order-list="repairChildWorkOrderList"
            :child-order-detail="childOrderDetail"
            :current-page="currentPage"
            :show-report-btn="true"
            :repair-work-order-base-info="repairWorkOrderBaseInfo"
            :work-order-config-detail="workOrderConfigDetail"
            mode="carousel"
            @page-change="handlePageChange"
            @report="handleReport"
          />

          <view v-if="hasMultipleOrders" class="switcher-wrap">
            <NumberSwitcher
              :current-page="currentPage"
              :total-pages="totalPages"
              @change="handlePageChange"
            />
          </view>
        </view>
      </view>

      <!-- 被驳回 -->
      <view v-else class="reject-wrap">
        <ReviewRecordCard :check-log-list="repairWorkOrderDetail.vericaContent || []" />
        <TaskCompletionCard
          :complete-info="childWorkOrderCompleteInfo"
          :show-tab-list="true"
          :repair-work-order-base-info="repairWorkOrderBaseInfo"
          :work-order-config-detail="workOrderConfigDetail"
          @go-detail="handleGoDetail"
          @to-re-report="handleReReport"
        />
      </view>
    </view>

    <view class="progress-footer">
      <view class="progress-section">
        <view class="progress-row">
          <text class="progress-label">维修任务进度</text>
          <text class="progress-value"
            >{{ finishedCount }}/{{ totalPages }}</text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import ConstructionCard from "../components/card/ConstructionCard.vue";
import ChildWorkOrderDetail from "../components/ChildWorkOrderDetail.vue";
import NumberSwitcher from "../components/NumberSwitcher.vue";
import ReviewRecordCard from "../components/card/ReviewRecordCard.vue";
import TaskCompletionCard from "../components/card/TaskCompletionCard.vue";
import { RoutineMaintenanceService } from "@/service";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 工单详情信息
  repairWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  },
  // 工单配置详情
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单完成信息
  childWorkOrderCompleteInfo: {
    type: Object,
    default: () => ({}),
  },
});

// 响应式数据
const currentPage = ref(1); // 当前页码
const childOrderDetail = ref({}); // 子工单详情数据

// 计算属性
const totalPages = computed(() => props.repairChildWorkOrderList.length); // 总页数
const hasMultipleOrders = computed(() => totalPages.value > 1); // 是否有多个工单
const hasReject = computed(() => props.childWorkOrderCompleteInfo.reject?.length > 0); // 是否有驳回记录

// 已完成的工单数量
const finishedCount = computed(() =>
  props.repairChildWorkOrderList?.filter(item => item.workStatus === "4").length || 0
);

/**
 * 获取子工单详情
 * @param {string} eventId - 事件ID
 */
const getChildDetail = async (eventId) => {
  if (!eventId) return;
  try {
    const { data } =
      await RoutineMaintenanceService.getInspectEventDetail(eventId);
    childOrderDetail.value = data;
  } catch (error) {
    console.error("获取子工单详情失败:", error);
  }
};

/**
 * 处理页码切换
 * @param {number} page - 页码
 */
const handlePageChange = (page) => {
  currentPage.value = page;
  getChildDetail(props.repairChildWorkOrderList[page - 1]?.eventId);
};

/**
 * 处理上报操作
 * @param {Object} item - 工单项
 */
const handleReport = (item) => {
  const finishedNum =
    props.repairChildWorkOrderList?.filter((order) => order.workStatus === "4").length ||
    0;

  const flag = props.repairChildWorkOrderList.length - finishedNum === 1;

  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/report?pageFrom=startBuild&workId=${item.parentId}&childWorkId=${item.id}&saveStatus=${item.saveStatus}&childWorkCode=${item.workCode}&isLastChild=${flag}`,
  });
};

/**
 * 跳转到子工单详情页
 * @param {Object} info - 工单信息
 */
const handleGoDetail = (info) => {
  const id = props.repairWorkOrderBaseInfo?.id;
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/index?workId=${id}&childWorkId=${info.childWorkId}`,
  });
};

/**
 * 处理重新上报操作
 * @param {Object} info - 工单信息
 */
const handleReReport = (info) => {
  const id = props.repairWorkOrderBaseInfo?.id;
  const { childWorkId, saveStatus, childWorkCode, flag } = info;
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/report?workId=${id}&childWorkId=${childWorkId}&saveStatus=${saveStatus}&childWorkCode=${childWorkCode}&isLastChild=${flag}`,
  });
};

// 监听子工单列表变化，自动获取第一个子工单详情
watch(
  () => props.repairChildWorkOrderList,
  (newList) => {
    if (newList?.length > 0) {
      currentPage.value = 1;
      getChildDetail(newList[0]?.eventId);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.construction-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f4f8ff;

  .construction-content {
    flex: 1;
    overflow-y: auto;

    .content-wrapper {
      padding: 0 40rpx;
    }

    .child-order-wrap {
      margin-top: 40rpx;
    }

    .reject-wrap {
      margin-top: 40rpx;
      padding: 0 40rpx;
      display: flex;
      flex-direction: column;
      gap: 40rpx;
    }

    .switcher-wrap {
      margin: 32rpx 0;
    }
  }

  .progress-footer {
    flex-shrink: 0;
    display: flex;
    justify-content: center;

    .progress-section {
      width: 100%;
      background: #fff;
      padding: 32rpx 40rpx;
      box-shadow: 0 -4rpx 20rpx rgba(68, 76, 108, 0.15);

      .progress-row {
        display: flex;
        align-items: center;

        .progress-label {
          color: #373737;
          font-size: 32rpx;
          font-weight: 400;
        }

        .progress-value {
          margin-left: 8rpx;
          color: #ff3838;
          font-size: 32rpx;
          font-weight: 400;
        }
      }
    }
  }
}
</style>
