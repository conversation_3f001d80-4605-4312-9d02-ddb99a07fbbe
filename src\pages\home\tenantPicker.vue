<template>
  <view class="container">
    <view class="tips">{{ pageOptions.tips }}</view>
    <template v-if="!pageLoading">
      <view class="list_box" v-if="filterList.length">
        <view
          :class="[
            'card',
            curItem.tenantId === item.tenantId ? 'active_card' : '',
          ]"
          v-for="item in filterList"
          :key="item.id"
          @click="chooseItem(item)"
        >
          <image
            class="item_icon"
            :src="
              curItem.tenantId === item.tenantId
                ? tenant_active_icon
                : tenant_inactive_icon
            "
          />
          <view class="item_title">{{ item.tenantName }}</view>
        </view>
      </view>
      <no-data class="nodata" v-else></no-data>
    </template>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      font-size="24rpx"
    ></uv-loading-page>
    <view class="bottom_box">
      <view class="choosed_data_show" v-if="curItem.tenantName"
        >已选 {{ curItem.tenantName }}</view
      >
      <view
        :class="['btn', curItem.tenantId ? '' : 'btn-forbid']"
        @click="confirm"
        >{{ pageOptions.btnName }}
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { UserService, ProjectService } from "@/service";
import tenant_active_icon from "/static/icon/tenant_active_icon.png";
import tenant_inactive_icon from "/static/icon/tenant_inactive_icon.png";
import { useUserStore } from "@/store/user";
const userInfo = useUserStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { useSystemStore } from "@/store/system";
const systemInfo = useSystemStore();
const defaultOptions = {
  login: {
    tips: "请选择需要登录的组织",
    btnName: "确认登录",
  },
  change: {
    tips: "请选择需要切换的组织",
    btnName: "切换",
  },
};
const pageOptions = ref({});
onLoad(async (options) => {
  // 获取项目列表
  await getList();
  const { tenantId, pageType } = options || {};
  // 当前已选项目回显
  handelShowCurItem(tenantId);
  // 获取显示的tips和btnName
  pageOptions.value = defaultOptions[pageType];
});

// 查询租户列表
const filterList = ref([]);
const pageLoading = ref(false);
const getList = async () => {
  pageLoading.value = true;
  const res = await UserService.getUserTenantList();
  filterList.value = res.data;
  pageLoading.value = false;
};

// 回显当前已选择的项目，并将其置顶
const handelShowCurItem = (curTenantId) => {
  if (!curTenantId) return;
  const curItemIndex = ref(-1);
  filterList.value.forEach((item, index) => {
    if (item.tenantId == curTenantId) {
      curItem.value = item;
      curItemIndex.value = index;
    }
  });
  if (curItemIndex.value !== -1) {
    const [it] = filterList.value.splice(curItemIndex.value, 1);
    filterList.value.unshift(it);
  }
};

// 选择租户
const curItem = ref({});
const chooseItem = (item) => {
  if (curItem.value.tenantId === item.tenantId) return; // 已选择的跳过
  curItem.value = item;
};

// 提交
const confirm = async () => {
  if (!curItem.value.tenantId) return;
  // 调刷新token接口
  const { code, data } = await UserService.authChange(curItem.value.tenantId);
  console.log('刷新token接口结果:',data);
  if (code === 200) {
    userInfo.updateToken(data.access_token);
    // 职位,待后端处理
    const postName = curItem.value.postName || null;
    const { userFace,userFullName,tenantId, tenantName } = data.user;
    userInfo.updateUser({
      userFace,
      userFullName,
      postName,
      tenantId,
      tenantName,
    });
    // 更新保存的systemCodes
    let loginSysCode = data.user.userAuthority.systemCodes
    systemInfo.updateSystemCode(loginSysCode);
    // 获取资源列表
    // await getResourceList(data.user.userAuthority.systemCodes[0]);
    await getResourceList(data.user.userAuthority.systemCodes.join(","));
    await getProjects();
    uni.redirectTo({
      url: "/pages/platform/index",
    });
  }
};
// 重新设置权限资源
const getResourceList = async (systemCode) => {
  try {
    let { code, data } = await UserService.authResource(systemCode);
    if (code == 200) {
      uni.setStorageSync("permissions", data);
    }
  } catch (error) {
    console.log(error);
  }
};

// 请求项目列表
const getProjects = async () => {
  try {
    const { code, data } = await ProjectService.getProjectList();
    console.log("请求项目", data);
    if (code == 200 && data.length > 0) {
      await projectInfo.updateProject({
        projectId: data[0].projectId,
        projectName: data[0].projectName,
        projectPermission: data[0].projectPermission,
      });
    }else{
      await projectInfo.updateProject({
        projectId: '',
        projectName: '',
        projectPermission: '',
      });
    }
  } catch (error) {
    console.log("catch", error);
  }
};

</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 40rpx;
  background: #f4f8ff;
  box-sizing: border-box;
  .tips {
    color: #404040;
    font-family: PingFang SC-Regular;
    font-size: 32rpx;
    margin-bottom: 48rpx;
  }
  .list_box {
    margin: 12rpx 0 200rpx 0; //
    .card {
      display: flex;
      align-items: center;
      height: 120rpx;
      background: #ffffff;
      padding: 22rpx 28rpx;
      box-sizing: border-box;
      font-family: PingFang SC-Medium;
      border: 2rpx solid #f0f0f0;
      border-radius: 16rpx;
      box-shadow: 4rpx 4rpx 20rpx 0rpx rgba(2552, 255, 255, 0.04);
      margin-bottom: 32rpx;
      .item_icon {
        display: block;
        width: 52rpx;
        height: 52rpx;
        margin-right: 24rpx;
      }
      .item_title {
        font-weight: 400;
        color: #373737;
        font-size: 32rpx;
        line-height: 44rpx;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
    .active_card {
      border-color: #4378ff;
      background: #ecf1ff;
      .item_title {
        color: #4378ff;
      }
    }
  }
}

.nodata {
  margin-top: 136rpx;
}
.bottom_box {
  width: calc(100% - 80rpx);
  position: fixed;
  bottom: 0rpx;
  padding-bottom: 20rpx;
  background: #f4f8ff;
  .choosed_data_show {
    padding-top: 20rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .btn {
    width: 670rpx;
    height: 84rpx;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
  .btn-forbid {
    background: #a09f9f;
  }
}
</style>
