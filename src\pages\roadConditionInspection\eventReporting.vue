<!--
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-03-28 09:28:50
 * @Description: 
-->
<template>
  <view class="container">
    <scroll-view scroll-y="true" class="content">
      <ylg-auto-form
        ref="autoFormRef"
        :isAllDisabled="false"
        :isFromDetail="false"
        :formConfig="formConfig"
        :formData="formData"
        :rules="rules"
        :btnLoading="btnLoading"
        :labelStyle="labelStyle"
        :formItemStyle="formItemStyle"
        :placeholderStyle="placeholderStyle"
        cancelBtnText="取 消"
        confirmBtnText="上 报"
        @onChange="onFormInpChange"
        @onDictChange="onFormDictChange"
        @onRightSlot="onGetLocation"
        @onSubmit="onFormSubmit"
      ></ylg-auto-form>
      <view class="black" style="height: 100rpx"></view>
      <uv-toast ref="toast"></uv-toast>
    </scroll-view>

  </view>
</template>
<script setup>
import { computed, ref, reactive } from "vue";
import { onLoad, onShow, onBackPress } from "@dcloudio/uni-app";
import { getSysteminfo, uploadFilePromise } from "@/utils";
import {
  getCurLocation,
  reverseGeocode,
  getRegionCodeByLatLng,
} from "@/utils/location";
// import ylgAutoForm from "@/components/ylg-auto-form.vue";
import { DataAcquisitionService, RoadInspectionService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

// 获取手机底部安全距离
const systemBottomSafeArea = `${
  Number(getSysteminfo().bottomSafeArea) * 2 || 40
}rpx`;
console.log("手机底部安全距离", systemBottomSafeArea);

let pageFrom = ref("");
let inspectPlanId = ref("");
let inspectTaskId = ref("");
let sectionId = ref("");
let sectionName = ref("");

// 病害计量单位
let diseasesCountUnit = ref("")

onLoad((options) => {
  // 1清空pinia中缓存的之前选择的数据项：如所属路线、所属路段等；
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: "",
      value: "",
      label: "",
    },
  });
  // 2处理从上个页面带过来的值
  console.log("optionsssss", options);
  // 用于在事件对象名称选择页面，关联请求哪个接口
  uni.setStorage({
    key: "inspectObject",
    data: options?.inspectObject,
    success: function () {
      console.log("存储成功");
    },
    fail: function (err) {
      console.error("存储失败", err);
    },
  });
  // 页面来源：列表首页、任务执行页
  pageFrom.value = options.pageFrom;
  if(options.pageFrom == 'inspection'){
    inspectPlanId.value = options?.inspectPlanId;
    inspectTaskId.value = options?.inspectTaskId;
    sectionId.value = options?.sectionId;
    sectionName.value = options?.sectionName;
    setNestedValue(
      formData.value,
      "inspectTaskId".split("."),
      options?.inspectTaskId
    );
    setNestedValue(formData.value, "sectionId".split("."), options?.sectionId);
    // setNestedValue(formData.value, 'sectionIdLabel'.split("."), sectionName.value);
    uni.setStorage({
      key: "inspectPlanId",
      data: options?.inspectPlanId,
      success: function () {
        console.log("存储成功");
      },
      fail: function (err) {
        console.error("存储失败", err);
      },
    });
    uni.setStorage({
      key: "sectionId",
      data: options?.sectionId,
      success: function () {
        console.log("存储成功");
      },
      fail: function (err) {
        console.error("存储失败", err);
      },
    });
    // 查询接口枚举库：事件对象类型、
    getInterfaceMenu();
  }

  // 2查询字典枚举库
  getDicts();
  // 3获取当前定位
  getAddress();
});

onShow(() => {
  // 处理从别的页面中选择之后的数据显示，如所属路线选择
  onFormSelChange();
});

// form表单样式配置
const labelStyle = reactive({
  width: "208rpx",
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  lineHeight: "33rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  overflow: "hidden",
});
const placeholderStyle = ref("color: #C1C1C1");



let dicts = ref({});
const getDicts = async () => {
  let { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes:
      "eventType,yhBusType,updownMark,embankmentDiseasesType,pavementDiseasesType,bridgeDiseasesType,tunnelDiseasesType,culvertDiseasesType,rfDiseasesType",
  });
  if (code == 200) {
    data.yhBusType = data.yhBusType.filter(item=>Number(item.dictKey)>2);
    dicts.value = data;
  }
};
let eventObjectTypeMenu = ref([]);
const getInterfaceMenu = async () => {
  try {
    // 事件对象类型
    let { code, data } = await RoadInspectionService.getEventObjType(
      inspectPlanId.value
    );
    console.log("事件对象类型", code, data);
    if (code == 200) {
      eventObjectTypeMenu.value = data;
    }
  } catch (error) {
    console.log("事件对象类型失败", error);
  }
};

const getAddress = async () => {
  try {
    let locationRes = await getCurLocation();
    if (locationRes.errMsg == "getLocation:ok") {
      formData.value.longitude = locationRes.longitude;
      formData.value.latitude = locationRes.latitude;
      formData.value.address = await reverseGeocode(
        locationRes.longitude,
        locationRes.latitude
      );
      getRegionCodeByLatLng(formData.value.longitude,formData.value.latitude).then(res=>{
        console.log('获取行政区域编码',res );
        formData.value.politicCode = res;
      }).catch(err=>{
        console.error('获取行政区域编码失败:', err);
      })
    }
  } catch (error) {
    console.log("获取定位失败", error);
    uni.showToast({
      icon: "none",
      title: "获取当前定位失败" + error,
    });
  }
};

// 表单数据配置
let formData = ref({
  // 项目id
  projectId: projectInfo.projectId,
  // 任务
  inspectTaskId: inspectTaskId.value,
  // 事件类型
  eventType: "",
  eventTypeLabel: "",

  // 所属路段
  sectionId: sectionId.value,
  sectionIdLabel: sectionName.value,

  // 事件对象类型
  eventObjectType: "",
  eventObjectTypeLabel: "",

  // 病害类型
  diseasesType: "",
  diseasesTypeLabel: "",

  // 病害计量
  diseasesCount: "",

  // 事件对象名称
  eventObject: "",
  eventObjectLabel: "",

  // 位置方向
  updownMark: "",
  updownMarkLabel: "",

  // 起点桩号
  stake: "",
  stakeId: "", // 1820375081585016833
  stakeIdLabel: "", // K130+900
  // 终点桩号
  endStake: "",
  endStakeId: "", // 1820375081585016833
  endStakeIdLabel: "", // K130+900

  // 地址信息
  address: "",
  longitude: "", // 104.897869
  latitude: "", // 38.457587
  politicCode: "", // 行政区编码

  // 描述
  remark: "",

  // 照片
  fileAttributes: [],
});
const rules = computed(() => {
  return {
    eventTypeLabel: {
      type: "string",
      required: true,
      message: "请选择事件类型",
      trigger: ["blur", "change"],
    },
    sectionIdLabel: {
      type: "string",
      required: pageFrom.value == 'inspection' ? false : true,
      message: "请选择所属路段",
      trigger: ["blur", "change"],
    },
    eventObjectTypeLabel: {
      type: "string",
      required: true,
      message: "请选择事件对象类型",
      trigger: ["blur", "change"],
    },
    diseasesTypeLabel: {
      type: "string",
      required: formData.value.eventType == 1 ? true : false,
      message: "请选择病害类型",
      trigger: ["blur", "change"],
    },
    diseasesCount: {
      type: "string",
      required: formData.value.eventType == 1 ? true : false,
      message: "请输入病害计量",
      trigger: ["blur", "change"],
    },
    eventObjectLabel: {
      type: "string",
      required: true,
      message: "请选择事件对象名称",
      trigger: ["blur", "change"],
    },
    updownMarkLabel: {
      type: "string",
      required: true,
      message: "请选择位置方向",
      trigger: ["blur", "change"],
    },
    address: {
      type: "string",
      required: true,
      message: "请选择地址信息",
      trigger: ["blur", "change"],
    },
    remark: {
      type: "string",
      required: true,
      message: "请输入事件描述",
      trigger: ["blur"],
    },
    fileAttributes: [
      {
        validator: (rule, value, callback) => {
          console.log("照片校验", value);
          // value.length==2;
          return (value.length>0) && (value.length<3);
        },
        message: "请上传照片",
        trigger: ["blur", "change"],
      },
    ],
  };
});

const formConfig = computed(() => {
  return [
    {
      items: [
        {
          type: "select",
          label: "事件类型",
          placeholder: "请选择",
          unionKey: "eventTypeLabel",
          options: dicts.value.eventType || [],
        },
        {
          type: "select",
          label: "所属路段",
          placeholder: pageFrom.value == "inspection"?sectionName.value: "请选择",
          unionKey: "sectionIdLabel",
          // optionsPath: "",
          optionsPath: pageFrom.value == "inspection"? "":
            "/pages/dataAcquisition/relationInfoChoose/roadPartChoose",
        },
        {
          type: "select",
          label: "事件对象类型",
          placeholder: "请选择",
          unionKey: "eventObjectTypeLabel",
          options: pageFrom.value == 'inspection'?eventObjectTypeMenu.value || [] : dicts.value.yhBusType|| [],
        },
        formData.value.eventType == 1 && {
          type: "select",
          label: "病害类型",
          placeholder: "请选择",
          unionKey: "diseasesTypeLabel",
          options: diseasesTypeDicts.value || [],
          pathKey: "eventObjectType",
          isNeedPathKeyValue: true,
          toastMsg: "请先选择事件对象类型~",
        },
        formData.value.diseasesTypeLabel && {
          type: "input",
          inputType: 'number',
          label: "病害计量",
          placeholder: "请输入",
          maxlen: 8,
          unionKey: "diseasesCount",
          sideInfo: diseasesCountUnit.value
        },
        {
          type: "select",
          label: "事件对象名称",
          placeholder: "请选择",
          unionKey: "eventObjectLabel",
          optionsPath:
            "/pages/roadConditionInspection/relationInfoChoose/eventTargetNameChoose",
          pathKey: "eventObjectType",
          isNeedPathKeyValue: true,
          toastMsg: "请先选择事件对象类型~",
        },
        {
          type: "select",
          label: "位置方向",
          placeholder: "请选择",
          unionKey: "updownMarkLabel",
          options: dicts.value.updownMark || [],
        },
        {
          type: "select",
          label: "起点桩号",
          placeholder: "请选择（非必填）",
          unionKey: "stakeIdLabel",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose?noNew=true`,
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: "请先选择所属路段~",
        },
        {
          type: "select",
          label: "终点桩号",
          placeholder: "请选择（非必填）",
          unionKey: "endStakeIdLabel",
          optionsPath: `/pages/roadConditionInspection/relationInfoChoose/allStakeChoose`,
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: "请先选择所属路段~",
        },
        {
          type: "custom",
          label: "地址信息",
          placeholder: "请获取定位",
          slotText: "坐标拾取",
          unionKey: "address",
        },
        {
          type: "textarea",
          label: "事件描述",
          maxlen: 150,
          placeholder: "请输入",
          unionKey: "remark",
        },
        {
          type: "upload",
          label: "照片上传",
          fileList:ref(formData.value.fileAttributes),
          maxCount: 2,
          unionKey: "fileAttributes",
        },
      ],
    },
  ];
});

let diseasesTypeDicts = computed(() => {
  let menu = {
    3: "embankmentDiseasesType",
    4: "pavementDiseasesType",
    5: "bridgeDiseasesType",
    6: "tunnelDiseasesType",
    7: "culvertDiseasesType",
    8: "rfDiseasesType",
  };
  let type = menu[formData.value.eventObjectType];

  return dicts.value[type];
});

const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
  console.log("setNestedValue", formData.value);
};
// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  console.log("formItem数据修改回调", unionKey, val);
  setNestedValue(formData.value, unionKey.split("."), val);
};
// formItem dict 字典数据修改
const onFormDictChange = ({ val, unionKey }) => {
  // 如果选择的是 “事件对象类型”，则需要清空表单中选择的病害类型、事件对象名称
  if (
    unionKey === "eventObjectTypeLabel" &&
    formData.value.eventObjectType !== val
  ) {
    formData.value.diseasesType = "";
    formData.value.diseasesTypeLabel = "";
    formData.value.eventObject = "";
    formData.value.eventObjectLabel = "";
    // 并清空pinia中缓存的之前选择的数据项
    dataAcquisitionInfo.updateDataAcquisition({
      stateData: {
        key: "",
        value: "",
        label: "",
      },
    });
  }

  if(unionKey === "eventTypeLabel"){
    console.log('切换事件类型eventTypeLabel');
    
  }

  // 选择病害类型后，自动带出病害计量单位
  if(unionKey === "diseasesTypeLabel"){
    console.log('病害类型列表', diseasesTypeDicts.value);
    let curChoose = diseasesTypeDicts.value.find(item=>item.dictKey === val);
    diseasesCountUnit.value = curChoose.remark || '';
  }
};
// formItem 从选择页面回调的 select 数据修改
let autoFormRef = ref(null);
const onFormSelChange = () => {
  console.log('dataAcquisitionInfo.stateData.key',dataAcquisitionInfo.stateData.key);
  
  // 1如果选择的是 “所属路段”，则需要清空表单中选择的桩号信息
  if (
    dataAcquisitionInfo.stateData.key === "sectionIdLabel" &&
    formData.value.sectionId !== dataAcquisitionInfo.stateData.value
  ) {
    formData.value.stakeId = "";
    formData.value.stakeIdLabel = "";
    formData.value.endStakeId = "";
    formData.value.endStakeIdLabel = "";
    uni.setStorage({
      key: "sectionId",
      data: dataAcquisitionInfo.stateData.value,
      success: function () {
        console.log("存储成功");
      },
      fail: function (err) {
        console.error("存储失败", err);
      },
    });
  }
  // 2处理select选择的label
  setNestedValue(
    formData.value,
    dataAcquisitionInfo.stateData.key.split("."),
    dataAcquisitionInfo.stateData.label
  );
  // 3处理select选择的value
  let str = dataAcquisitionInfo.stateData.key.replace(/Label$/, "");
  setNestedValue(
    formData.value,
    str.split("."),
    dataAcquisitionInfo.stateData.value
  );
  
  // 2如果选择的是 “地址信息”
  // if (formData.value.addressLabel) {
  if (dataAcquisitionInfo.stateData.key === "addressLabel") {
    console.log("选择更改1", formData.value);
    // let trans = transformLngLat(
    //   formData.value.addressLabel.split(",")[0],
    //   formData.value.addressLabel.split(",")[1]
    // );
    // formData.value.longitude = String(trans[0]);
    // formData.value.latitude = String(trans[1]);
    formData.value.longitude = formData.value.addressLabel.split(",")[0];
    formData.value.latitude = formData.value.addressLabel.split(",")[1];
    getRegionCodeByLatLng(formData.value.longitude,formData.value.latitude).then(res=>{
      console.log('获取行政区域编码',res );
      formData.value.politicCode = res;
    }).catch(err=>{
      console.error('获取行政区域编码失败:', err);
    })
    delete formData.value.addressLabel;
    autoFormRef.value.formRef.validateField('address');
  }
  console.log("选择更改", formData.value);
  if (autoFormRef.value?.formRef) {
    autoFormRef.value.formRef.validateField(dataAcquisitionInfo.stateData.key);
  }
};

// 获取定位、地址
const onGetLocation = async () => {
  // let str = "";
  // if(formData.value.longitude && formData.value.latitude){
  //   str = `&curLongitude=${formData.value.longitude}&curLatitude=${formData.value.latitude}&curAddress=${formData.value.address}`
  // }
  uni.navigateTo({
    url: `/pages/roadConditionInspection/coordinatePicking?unionKey=addressLabel`,
  });
};

// form表单提交回调
let toast = ref(null);
let res = ref({});
let btnLoading = ref(""); // '0'-暂存按钮；'1'-提交按钮
const onFormSubmit = async ({ data, status: action }) => {
  console.log("事件上报", data, action);
  
  if (action == 0) {
    // 取消
    uni.navigateBack({
      delta: 1,
    });
  } else {
    // 上报
    try {
      btnLoading.value = String(action);
      // 事件上报来源
      data.eventSource = pageFrom.value == "inspection"?1:2;


      // 照片路径处理
      data.fileAttributes.forEach(item=>{
        let startIndex = item.url.indexOf("/file");
        if ( startIndex!== -1) {
          item.url = item.url.substring(startIndex);
          item.path = item.url;
        } else {
          console.log("未找到 '/file' 子字符串");
        }
      })
      console.log('//////1',data);
      res.value = await RoadInspectionService.submitEvent(data);
      btnLoading.value = "";
      console.log("res", res.value.data);
      if (res.value.code == 200) {
        toast.value.show({
          type: "success",
          message: `事件上报成功`,
          complete() {
            uni.navigateBack({
              delta: 1,
            });
          },
        });
      }
    } catch (error) {
      btnLoading.value = "";
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f4f8ff;
}
.content {
  background-color: #fff;
}
.uv-form {
  .right_slot_btn {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 24rpx;
    color: #4378ff;
    line-height: 34rpx;
  }
  :deep(.uv-textarea) {
    padding: 0;
  }
  :deep(.uni-input-input) {
    color: #404040;
  }
  :deep(.uni-textarea-textarea) {
    color: #404040;
  }
}
.btn_box {
  background-color: #f4f8ff;
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx 40rpx v-bind(systemBottomSafeArea) 40rpx;
  position: fixed;
  bottom: 0;
  // bottom: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
