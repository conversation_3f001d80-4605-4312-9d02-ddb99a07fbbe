<template>
  <view class="verification-container">
    <view class="verification-content">
      <view class="content-wrapper">
        <!-- 施工信息卡片 -->
        <ConstructionCard
          :base-info="repairWorkOrderBaseInfo"
          :detail-info="repairWorkOrderDetail"
          :child-order-list="repairChildWorkOrderList"
          :config-detail="workOrderConfigDetail"
          :is-show-bar="true"
        />

        <!-- 任务完成情况卡片 -->
        <view class="completion-section">
          <TaskCompletionCard
            :complete-info="childWorkOrderCompleteInfo"
            @go-detail="handleChildOrderDetail"
          />
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-footer">
      <button
        class="btn btn--reject"
        @click="handleReject"
      >
        驳回
      </button>
      <button
        class="btn btn--approve"
        @click="handleApprove"
      >
        验收通过
      </button>
    </view>
  </view>
</template>

<script setup>
import ConstructionCard from "../components/card/ConstructionCard.vue";
import TaskCompletionCard from "../components/card/TaskCompletionCard.vue";
import { buildUrlWithParams } from "@/utils";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 工单详情信息
  repairWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  },
  // 工单配置详情
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单完成信息
  childWorkOrderCompleteInfo: {
    type: Object,
    default: () => ({}),
  },
});

/**
 * 跳转到子工单详情页
 * @param {Object} info - 工单信息
 */
const handleChildOrderDetail = (info) => {
  const params = {
    workId: props.repairWorkOrderBaseInfo?.id,
    childWorkId: info.childWorkId,
  };

  uni.navigateTo({
    url: buildUrlWithParams(
      "/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/index",
      params
    ),
  });
};

/**
 * 驳回工单
 */
const handleReject = () => {
  uni.navigateTo({
    url: "/pages/common/rejectedPage?type=repairWorkOrder",
  });
};

/**
 * 验收通过
 */
const handleApprove = () => {
  uni.navigateTo({
    url: "/pages/routineMaintenance/repairWorkOrderDetail/acceptanceMeasurement/acceptanceDetail",
  });
};
</script>

<style lang="scss" scoped>
@import "../common.scss";

.verification-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f4f8ff;

  .verification-content {
    flex: 1;
    overflow-y: auto;

    .content-wrapper {
      padding: 0 40rpx;
    }

    .completion-section {
      padding-top: 40rpx;
    }
  }

  .action-footer {
    height: 160rpx;
    display: flex;
    align-items: center;
    gap: 40rpx;
    padding: 0 40rpx;
    flex-shrink: 0;

    .btn {
      height: 96rpx;
      border-radius: 8rpx;
      font-size: 40rpx;
      font-weight: 600;
      border: none;
      outline: none;
      transition: all 0.2s ease;
      font-family: PingFang SC, sans-serif;

      &--reject {
        flex: 1;
        background: #ddd;
        color: #62697b;

        &:active {
          background: #ccc;
          transform: scale(0.99);
        }
      }

      &--approve {
        flex: 2;
        background: #4378ff;
        color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(67, 120, 255, 0.2);

        &:active {
          background: #3366ee;
          transform: scale(0.99);
        }
      }
    }
  }
}
</style>
