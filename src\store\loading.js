/*
 * @Author: chenhl
 * @Date: 2024-07-17 10:47:45
 * @LastEditors: chenhl
 * @LastEditTime: 2024-07-17 10:48:37
 * @FilePath: /01ylg-yh-app/src/store/loading.js
 * @Description: 
 */
import { defineStore } from "pinia";

let timer = null;
export default defineStore({
  id: "loading",
  state: () => ({
    loading: false,
    options: {
      title: "",
      mask: true,
    },
  }),
  getters: {},
  actions: {
    showLoading(options = {}) {
      Object.assign(this.options, options);
      this.loading = true;
    },
    hideLoading() {
      Object.assign(this.options, { title: "", mask: true });
      this.loading = false;
    },
    timeout() {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        this.hideLoading();
        timer = null;
      }, 32 * 1000);
    },
  },
});
