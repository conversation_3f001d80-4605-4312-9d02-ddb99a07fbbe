<template>
  <view class="confirm-container">
    <view class="confirm-content">
      <WorkOrderWrapCard :base-info="repairWorkOrderBaseInfo">
        <template #header>
          <view class="title-section">
            <view class="title-bar">
              <view class="title-indicator" />
              <view class="title-text">维修工单接受</view>
            </view>
          </view>
        </template>

        <template #body>
          <!-- 基础信息 -->
          <view
            v-for="item in infoList"
            :key="item.key"
            class="info-row"
          >
            <view class="info-label">{{ item.label }}</view>
            <view class="info-value">{{ item.value || "-" }}</view>
          </view>

          <!-- 子工单详情 -->
          <view class="child-order-section">
            <ChildWorkOrderDetail
              :child-work-order-list="repairChildWorkOrderList"
              :child-order-detail="childOrderDetail"
              :current-page="currentPage"
              @page-change="handlePageChange"
            />

            <!-- 页码切换器 -->
            <view
              v-if="hasMultipleOrders"
              class="switcher-container"
            >
              <NumberSwitcher
                :current-page="currentPage"
                :total-pages="totalPages"
                @change="handlePageChange"
              />
            </view>
          </view>
        </template>
      </WorkOrderWrapCard>
    </view>

    <view v-if="hasPermission" class="action-footer">
      <button class="confirm-btn" @click="handleConfirm">
        确认工单
      </button>
    </view>
  </view>
</template>
<script setup>
import { ref, computed, watch } from "vue";
import ChildWorkOrderDetail from "../components/ChildWorkOrderDetail.vue";
import NumberSwitcher from "../components/NumberSwitcher.vue";
import WorkOrderWrapCard from "../components/card/WorkOrderWrapCard.vue";
import { RoutineMaintenanceService } from "@/service";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  }
});

// 响应式数据
const currentPage = ref(1); // 当前页码
const childOrderDetail = ref({}); // 子工单详情数据

// 计算属性
const totalPages = computed(() => props.repairChildWorkOrderList.length); // 总页数
const hasMultipleOrders = computed(() => totalPages.value > 1); // 是否有多个工单

// 权限检查：是否有确认权限
const hasPermission = computed(() => {
  return props.repairWorkOrderBaseInfo?.resourceList?.includes("dailyRepairConfirm") ?? false;
});

// 工具函数：格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return "";
  return dateTime.replace(/T/, " ").substring(0, 16);
};

// 工单基础信息列表
const infoList = computed(() => [
  {
    key: "createTime",
    label: "接收时间：",
    value: formatDateTime(props.repairWorkOrderBaseInfo?.createTime),
  },
  {
    key: "createUserName",
    label: "工单来源：",
    value: props.repairWorkOrderBaseInfo?.createUserName,
  },
]);

/**
 * 获取子工单详情
 * @param {string} eventId - 事件ID
 */
const getChildDetail = async (eventId) => {
  if (!eventId) return;
  try {
    const { data } = await RoutineMaintenanceService.getInspectEventDetail(eventId);
    childOrderDetail.value = data;
  } catch (error) {
    console.error("获取子工单详情失败:", error);
  }
};

/**
 * 处理页码切换
 * @param {number} page - 页码
 */
const handlePageChange = (page) => {
  currentPage.value = page;
  getChildDetail(props.repairChildWorkOrderList[page - 1]?.eventId);
};

/**
 * 处理确认操作，跳转到确认页面
 */
const handleConfirm = () => {
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/acceptanceMeasurement/confirmOrder?workId=${props.repairWorkOrderBaseInfo.id}`,
  });
};

// 监听子工单列表变化
watch(
  () => props.repairChildWorkOrderList,
  (newList) => {
    if (newList?.length > 0) {
      currentPage.value = 1;
      getChildDetail(newList[0]?.eventId);
    }
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped>
@import "../common.scss";

.confirm-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f4f8ff;

  .confirm-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 40rpx;
  }

  .title-section {
    margin-top: 28rpx;
    display: flex;
    align-items: center;

    .title-bar {
      display: flex;
      align-items: center;

      .title-indicator {
        margin-right: 8rpx;
        width: 6rpx;
        height: 28rpx;
        background: $primary-color;
        border-radius: 4rpx;
      }

      .title-text {
        font-family: $font-family;
        font-weight: 500;
        font-size: 32rpx;
        color: $text-secondary;
      }
    }
  }

  .info-row {
    display: flex;
    margin-top: 24rpx;

    .info-label {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-muted;
      line-height: 40rpx;
      flex-shrink: 0;
    }

    .info-value {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-primary;
      line-height: 40rpx;
    }
  }

  .child-order-section {
    padding: 40rpx 0;
  }

  .switcher-container {
    margin-top: 32rpx;
    padding: 0 28rpx;
  }

  .action-footer {
    height: 160rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 40rpx;
    flex-shrink: 0;

    .confirm-btn {
      width: 100%;
      height: 84rpx;
      border-radius: 8rpx;
      font-weight: 600;
      background: #4378ff;
      color: #fff;
      font-size: 40rpx;
      border: none;
      transition: all 0.2s ease;
      box-shadow: 0 2rpx 8rpx rgba(67, 120, 255, 0.2);
      font-family: PingFang SC, sans-serif;

      &:active {
        background: #3366ee;
        transform: scale(0.99);
      }
    }
  }
}
</style>
