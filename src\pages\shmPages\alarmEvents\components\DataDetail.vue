<template>
  <view>
    <view class="p-28">
      <view class="data-detail">
        <view
          class="flex-center time-box"
          v-if="statisticsData && statisticsData.pointCode"
        >
          <view class="time startTime">{{ statisticsData.dataStartTime }}</view>
          <view class="line"></view>
          <view class="time endTime" @click="showPicker">{{
            statisticsData.dataEndTime
          }}</view>
        </view>
        <view class="charts-item"><l-echart ref="chartRef"></l-echart></view>

        <view class="flex-start-top point-box">
          <view
            class="point ellipsis-one"
            v-for="(item, index) of points"
            :key="index"
            :class="{
              active:
                statisticsData.pointCode + statisticsData.monitorItem ===
                item.pointCode + item.monitorItem,
            }"
            @click="handlePointClick(item.pointCode, index, item.monitorItem)"
            >{{ item.pointCode }}</view
          >
        </view>
      </view>
    </view>
    <view
      class="point-content"
      v-if="statisticsData && statisticsData.pointCode"
    >
      <view class="shm-same-add-title">报警规则</view>
      <view class="p-28" v-show="statisticsData.overLimitValue">
        <view
          class="flex-between-center monitoring-items"
          :class="'monitoring-items' + statisticsData.alarmLevel"
        >
          <view class="flex-between-center">
            <image
              v-if="statisticsData.alarmLevel === '1'"
              src="../../../../static/shmStatic/icon/level1-icon-waring.png"
              mode="scaleToFill"
              class="level-icon"
            />
            <image
              v-else-if="statisticsData.alarmLevel === '2'"
              src="../../../../static/shmStatic/icon/level2-icon-waring.png"
              mode="scaleToFill"
              class="level-icon"
            />
            <image
              v-else
              src="../../../../static/shmStatic/icon/level3-icon-waring.png"
              mode="scaleToFill"
              class="level-icon"
            />
            <view class="point-msg">
              <view class="point-name">{{ statisticsData.pointCode }}</view>
              <view class="point-time">{{ statisticsData.alarmTime }}</view>
            </view>
          </view>
          <view class="unit">
            <text class="unit-num">{{ statisticsData.overLimitValue }}</text>
            <text class="unit-text">{{ statisticsData.unit }}</text>
          </view>
        </view>
      </view>
      <view class="flex-start-top same-msg">
        <view class="label">监测项</view>
        <view class="content">{{ statisticsData.monitorItem || "--" }}</view>
      </view>
      <view class="flex-start-top same-msg">
        <view class="label">关联设备</view>
        <view class="content">{{ statisticsData.deviceName || "--" }}</view>
      </view>
      <view class="flex-start-top same-msg">
        <view class="label">关联监测位置</view>
        <image
          class="location-img"
          mode="aspectFit"
          :src="showImg(statisticsData.locationPicture)"
          @click="previewImg(statisticsData.locationPicture)"
        ></image>
      </view>
    </view>
    <uv-picker
      ref="pickerTimeRef"
      :defaultIndex="selectedTimeIndex"
      :columns="columns"
      @change="changeTime"
      @confirm="confirmTime"
    ></uv-picker>
  </view>
</template>
<script setup>
import { computed, nextTick, reactive, ref } from "vue";
import { showImg } from "@/utils";
import dayjs from "dayjs";
import { AssetService } from "@/service";
import * as echarts from "echarts";
import { getSysteminfo } from "@/utils";
const screenWidth = getSysteminfo().phoneWindowWidth; // 屏幕宽度（px）
const rpxToPx = (rpx) => (screenWidth / 750) * rpx; // 将 rpx 转换为 px
const seriesData = ref([]);
const paramsData = ref({});
const columns = ref([]);
const generateTimeArraysBeforeCurrent = (currentTime, isScrollChange) => {
  // 解析当前时间
  let [currentHour, currentMinute, currentSecond] = currentTime
    .split(":")
    .map(Number);

  if (!isScrollChange && Number(selectedTime[0]) > currentHour) {
    currentMinute = 1;
    currentSecond = 1;
  }
  if (!isScrollChange && Number(selectedTime[1]) > currentMinute) {
    currentSecond = 1;
  }

  // console.log(
  //   "计算时分秒列项",
  //   selectedTimeIndex,
  //   currentHour,
  //   currentMinute,
  //   currentSecond
  // );

  // 生成小时数组（当前小时到23）
  const hoursArray = Array.from({ length: 24 - currentHour }, (_, i) => {
    const hour = currentHour + i;
    return hour < 10 ? `0${hour}` : `${hour}`;
  });

  // 生成分钟数组（当前分钟到59）
  const minutesArray = Array.from({ length: 60 - currentMinute }, (_, i) => {
    const minute = currentMinute + i;
    return minute < 10 ? `0${minute}` : `${minute}`;
  });

  // 生成秒数数组（当前秒数到59）
  const secondsArray = Array.from({ length: 60 - currentSecond }, (_, i) => {
    const second = currentSecond + i;
    return second < 10 ? `0${second}` : `${second}`;
  });
  console.log("解析时间", secondsArray);

  return [hoursArray, minutesArray, secondsArray];
};

let myChart = null;
const chartRef = ref(null);
let hasData = ref(false);
// 图表基础配置
const baseObj = computed(() => {
  return {
    legend: {
      show: false,
    },
    tooltip: {
      trigger: "axis",
      textStyle: {
        fontSize: 16,
      },
      axisPointer: {
        type: "none",
        animation: false,
        label: {
          backgroundColor: "#505765",
        },
      },
    },
    grid: {
      left: hasData.value ? "4%" : "11%",
      right: "8%",
      bottom: "0%",
      top: "18%",
      containLabel: true,
    },
    dataZoom: [
      {
        type: "inside", // 内置缩放，不显示控制条但可交互
        start: 0,
        end: 100,
      },
    ],
  };
});
const colors = ["#64C26D", "#E95A5A"]; // 在线绿色，离线灰色
const states = ["在线", "离线"];
let chartData = ref([]);
// 状态图表单独配置信息
const chartOptionsStatus = () => {
  return {
    color: colors,
    tooltip: {
      trigger: "item",
      confine: true, // 强制不超出画布
      padding: [12, 16], // 增加内边距提升可读性
      extraCssText: "max-width: 80% !important;", // 限制最大宽度
      textStyle: {
        fontSize: 12, // 缩小字号适应移动端
      },
      formatter: (params) => {
        const data = params.data;
        // return `${data.state}`
        return `${data.state}\n${data.start}\n${data.end}`;
      },
    },
    dataZoom: [
      {
        type: "inside", // 内置缩放，不显示控制条但可交互
        start: 0,
        end: 100,
      },
    ],
    grid: {
      left: hasData.value ? "3%" : "11%",
      right: "5%",
      top: "8%",
      bottom: 0,
      containLabel: true,
    },
    xAxis: {
      type: "time",
      axisTick: {
        show: false, // 显示刻度线
        lineStyle: {
          color: "#666", // 刻度线颜色
        },
      },
      axisLabel: {
        rotate: 45, // 标签旋转角度
        showMaxLabel: false,
        showMinLabel: false,
        color: "#86909C", // 设置字体颜色
      },
      axisLine: {
        show: true,
        color: "#eee", // 分割线颜色
      },
      splitLine: {
        show: true, // 显示分割线
        lineStyle: {
          color: "#ddd", // 分割线颜色
          type: "dashed", // 设置为虚线
        },
      },
    },
    yAxis: {
      type: "category",
      axisTick: {
        show: false,
      },
      data: states,
      axisLine: { show: false },
      splitLine: {
        show: false, // 显示分割线
        lineStyle: {
          color: "#eee", // 分割线颜色
        },
      },
    },
    series: [
      {
        type: "custom",
        renderItem: (params, api) => {
          console.log("series  api", api);

          const yIndex = api.value(0);
          const start = api.coord([api.value(1), yIndex]);
          const end = api.coord([api.value(2), yIndex]);
          const height = 20;
          // 获取颜色
          const color = colors[states.indexOf(api.value(3))];
          return {
            type: "rect",
            shape: echarts.graphic.clipRectByRect(
              {
                x: start[0],
                y: start[1] - height / 2,
                width: end[0] - start[0],
                height: height,
              },
              params.coordSys
            ),
            style: {
              fill: color, // 直接设置填充颜色
              lineWidth: 0, // 边框宽度（可选）
            },
          };
        },
        encode: {
          x: [1, 2], // 开始时间和结束时间
          y: 0, // 状态索引
        },
        data: chartData.value.flatMap((category, yIndex) =>
          category.periods.map((period) => ({
            state: category.state,
            start: period.start,
            end: period.end,
            value: [
              yIndex, // yAxis 的索引
              new Date(period.start).getTime(), // 开始时间
              new Date(period.end).getTime(), // 结束时间
              category.state, // 状态名称（第 4 个值）
            ],
            itemStyle: {
              color: colors[states.indexOf(category.state)],
            },
          }))
        ),
      },
    ],
  };
};

// 获取单个预警阈值
const getLineColor = (min, max) => {
  // 判断条件并直接返回结果
  return min || min === 0 || max || max === 0
    ? [
        {
          gt: min !== null ? min * 1 : undefined,
          lte: max !== null ? max * 1 : undefined,
          color: "#F14C4C",
        },
      ]
    : [];
};

let chartOptions = null;
// 点击测点更新图表

const getSingleOption = (data) => {
  // debugger
  console.log("data", data);
  // alarmDataType 1，2-折线 3-状态
  if (paramsData.value.alarmDataType !== "3") {
    // 当上下限为无穷时，以99999和-99999代替，避免echarts报错
    let lowerLimit =
      data.upperLimit && !data.lowerLimit ? -99999 : data.lowerLimit;
    let upperLimit =
      data.lowerLimit && !data.upperLimit ? 99999 : data.upperLimit;
    let arr = getLineColor(lowerLimit, upperLimit);
    let markLineList = [];
    if (arr.length > 0) {
      if (arr[0].lte) {
        markLineList.push({
          yAxis: arr[0].lte,
        });
      }
      if (arr[0].gt) {
        markLineList.push({
          yAxis: arr[0].gt,
        });
      }
    }

    let markLine = {
      silent: true,
      lineStyle: {
        color: "#F14C4C",
      },
      symbol: ["none", "none"], // 去掉箭头
      data: markLineList,
    };
    let visualMap = {
      top: 50,
      right: 10,
      show: false,
      pieces: arr,
      outOfRange: {
        color: "#9a60b4",
      },
    };
    console.log(visualMap);
    chartOptions = {
      ...baseObj.value,
      visualMap: visualMap,
      yAxis: {
        type: "value",
        splitNumber: 5, // 将Y轴分成5段，产生6个刻度线
        name: `单位/${data.unit}`,
        axisLabel: {
          hideOverlap: true, // 自动隐藏重叠的标签
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#D1D1D1",
          },
        },
      },
      xAxis: {
        // 根据x轴数据决定type类型
        type: "time",
        boundaryGap: false,
        axisLabel: {
          rotate: 45, // 标签旋转角度
          showMaxLabel: false,
          showMinLabel: false,
          color: "#86909C", // 设置字体颜色
        },
        // min: dayjs(paramsData.value.alarmTime).subtract(10, 'minute').format('YYYY-MM-DD HH:mm:ss'),
        min:
          data.data && data.data.length > 0
            ? dayjs(data.data[0][0])
                .subtract(1, "second")
                .format("YYYY-MM-DD HH:mm:ss")
            : "",
        // 把max范围加1秒，避免最后一个点画不出来
        max: dayjs(paramsData.value.alarmTime)
          .add(1, "second")
          .format("YYYY-MM-DD HH:mm:ss"),
        // max: '2025-03-12 10:38:59'
        // 注： x轴不指定data,自动会从series取
      },
      series: [
        {
          data: data.data ? data.data : [],
          type: "line",
          showSymbol: false,
          markLine: markLine,
        },
      ],
    };
  } else {
    chartData.value = [];
    if (data.data) {
      let online = data.data.filter((item) => item.deviceStatus === "1");
      let offline = data.data.filter((item) => item.deviceStatus === "0");
      chartData.value = [
        {
          state: "在线",
          periods: online.map((item) => ({
            start: item.startTime,
            end: item.endTime,
          })),
        },
        {
          state: "离线",
          periods: offline.map((item) => ({
            start: item.startTime,
            end: item.endTime,
          })),
        },
      ];
      console.log(chartData.value);
    }
    chartOptions = chartOptionsStatus();
  }

  // 组件能被调用必须是组件的节点已经被渲染到页面上
  setTimeout(async () => {
    if (!chartRef.value) return;

    if (myChart) {
      console.log(myChart);
      console.log(chartOptions);
      nextTick(() => {
        // myChart.resize({ width: 175});
        myChart.resize({ width: rpxToPx(350) });
      });
      myChart.setOption(chartOptions);
    } else {
      myChart = await chartRef.value.init(echarts);
      nextTick(() => {
        // myChart.resize({ width: 175});
        myChart.resize({ width: rpxToPx(350) });
      });
      myChart.setOption(chartOptions);
    }
  }, 200);
};

// 预览图片
const previewImg = (url) => {
  uni.previewImage({
    urls: [showImg(url)], // 需要预览的图片HTTP链接列表
    current: showImg(url), // 当前显示图片的链接索引
  });
};

// 定义一个响应式变量存储用户上次选择的索引 [hourIndex, minuteIndex, secondIndex]
let selectedTime = reactive([]);
let selectedTimeIndex = reactive([]);
// 时间选择事件
const confirmTime = (e) => {
  selectedTime = e.value;
  let time = statisticsData.value.alarmTime.split(" ")[0];
  endTimeValue.value = `${time} ${e.value[0]}:${e.value[1]}:${e.value[2]}`;
  console.log(endTimeValue.value);
  updateChartData(endTimeValue.value);
};

// 更新echarts的数据
const updateChartData = (endTime) => {
  AssetService.appMonitorPointDataDetail({
    alarmEventId: paramsData.value.id,
    endTime,
  }).then((res) => {
    let resData = res.data;
    points.value = resData;
    statisticsData.value = resData.filter(
      (item) => item.pointCode === statisticsData.value.pointCode
    )[0];
    console.log("查看statisticsData.value", statisticsData.value);
    if (
      res.data &&
      res.data.length > 0 &&
      res.data[0].data &&
      res.data[0].data
    ) {
      seriesData.value = res.data[0].data;
      getSingleOption(statisticsData.value);
    } else {
      seriesData.value = [];
    }
  });
};

// 点击测点
let curChoosePointInd = ref(0);
const handlePointClick = (pointCode, index, monitorItem) => {
  statisticsData.value = points.value.filter(
    (item) => item.pointCode + item.monitorItem === pointCode + monitorItem
  )[0];
  hasData.value = statisticsData.value.data.length ? true : false;
  curChoosePointInd.value = index;
  getSingleOption(statisticsData.value);
};

const pickerTimeRef = ref(null);
// 时间显示
let defaultEndTime = ref({});
const showPicker = () => {
  console.log(
    "showTime!!!",
    defaultEndTime.value[statisticsData.value.pointCode],
    dayjs(defaultEndTime.value[statisticsData.value.pointCode])
      .add(1, "second")
      .format("HH:mm:ss")
  );

  columns.value = generateTimeArraysBeforeCurrent(
    dayjs(defaultEndTime.value[statisticsData.value.pointCode])
      .add(1, "second")
      .format("HH:mm:ss"),
    false
  );
  // // statisticsData.value.dataEndTime

  let hInd = 0;
  columns.value[0].find((val, index) => {
    if (val === selectedTime[0]) {
      hInd = index;
    }
  });
  let mInd = 0;
  columns.value[1].find((val, index) => {
    if (val === selectedTime[1]) {
      mInd = index;
    }
  });
  let sInd = 0;
  columns.value[2].find((val, index) => {
    if (val === selectedTime[2]) {
      sInd = index;
    }
  });
  selectedTimeIndex = [hInd, mInd, sInd];
  console.log("打开时间弹窗", selectedTimeIndex, columns.value);

  pickerTimeRef.value.open();
};

// 生成分钟数组的逻辑
const getMinutes = (currentMinute) => {
  return Array.from({ length: 60 - currentMinute }, (_, i) =>
    (currentMinute + i).toString().padStart(2, "0")
  );
};

// 时间改变
const changeTime = (e) => {
  console.log("滑动时间", e);
  // let time = statisticsData.value.dataStartTime;
  let time = dayjs(defaultEndTime.value[statisticsData.value.pointCode])
    .add(1, "second")
    .format("HH:mm:ss");
  const [currentHour, currentMinute, currentSecond] = time
    .split(":")
    .map(Number);
  console.log("获取分秒", getMinutes(1));
  if (e.columnIndex === 0) {
    if (e.value[0] > currentHour) {
      console.log("查看时间列项", columns.value);
      columns.value[1] = getMinutes(1);
      columns.value[2] = getMinutes(1);
    } else {
      columns.value[1] = generateTimeArraysBeforeCurrent(time, true)[1];
      columns.value[2] = generateTimeArraysBeforeCurrent(time, true)[2];
    }
  } else if (e.columnIndex === 1) {
    if (e.value[0] > currentHour || e.value[1] > currentMinute) {
      // debugger;
      columns.value[2] = getMinutes(1);
    } else {
      // debugger;
      columns.value[2] = generateTimeArraysBeforeCurrent(time, true)[2];
    }
  }
};

const statisticsData = ref({});
const points = ref([]);
const endTimeValue = ref(null);
// 获取接口数据
let count = 0;
const getMonthStatistics = (alarmEventId) => {
  AssetService.appMonitorPointDataDetail({
    alarmEventId,
    endTime: endTimeValue.value || "",
  }).then((res) => {
    count++;
    points.value = res.data;
    res.data.forEach((item) => {
      item.dataEndTimeDesc =
        dayjs().format("YYYY-MM-DD") + " " + item.dataEndTime;
      item.dataEndTime = dayjs(item.dataEndTimeDesc)
        .add(endTimeValue.value ? 0 : 1, "second")
        .format("HH:mm:ss");
      if (count == 1) {
        defaultEndTime.value[item.pointCode] = item.dataEndTimeDesc;
      }
    });
    console.log("查看组装的时间对象", defaultEndTime.value);

    statisticsData.value =
      res.data && res.data.length > 0
        ? res.data[curChoosePointInd.value || 0]
        : {};
    hasData.value = statisticsData.value.data.length ? true : false;
    // endTimeValue.value =
    //   statisticsData.value && statisticsData.value.dataEndTime
    //     ? statisticsData.value.dataEndTime
    //     : "";
    if (
      res.data &&
      res.data.length > 0 &&
      res.data[0].data &&
      res.data[0].data
    ) {
      //   option.value.series[0].data = res.data[0].data
      seriesData.value = res.data[0].data;
      // getSingleOption(res.data[0]);
      getSingleOption(res.data[curChoosePointInd.value]);
    } else {
      seriesData.value = [];
    }
  });
};

const initData = (obj) => {
  paramsData.value = obj;
  getMonthStatistics(obj.id);
};

defineExpose({
  initData,
});
</script>
<style lang="scss" scoped>
.p-28 {
  padding: 0 28rpx;
}
.data-detail {
  width: 100%;
  border-radius: 16rpx;
  border: 2rpx solid #d9d9d9;
  position: relative;
  .time-box {
    padding: 28rpx 0;
    .line {
      width: 40rpx;
      height: 2rpx;
      background: #c0c0c0;
      margin: 0 28rpx;
    }
    .time {
      width: 124rpx;
      height: 40rpx;
      line-height: 40rpx;
      text-align: center;
      background: #e5e5e5;
      border-radius: 8rpx;
      box-sizing: border-box;
      border: 2rpx solid #e5e5e5;
      font-size: 24rpx;
      &.endTime {
        background: #ffffff;
        border-radius: 8rpx;
        border: 2rpx solid #4378ff;
      }
    }
  }
  .charts-item {
    height: 350rpx;
  }
  .point-box {
    width: 100%;
    flex-wrap: wrap;
    box-sizing: border-box;
    padding: 28rpx 14rpx 0 14rpx;
    .point {
      width: 49%;
      height: 52rpx;
      color: #a09f9f;
      font-size: 24rpx;
      line-height: 52rpx;
      border-radius: 8rpx;
      text-align: center;
      box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
      border: 2rpx solid #fff;
      box-sizing: border-box;
      margin-bottom: 20rpx;
      &:nth-child(2n - 1) {
        margin-right: 2%;
      }
      &.active {
        border: 2rpx solid #165dff;
        box-shadow: 0rpx 0rpx 8rpx 0 rgba($color: #000000, $alpha: 0.1);
      }
    }
  }
}
.point-content {
  padding-top: 48rpx;
  .shm-same-add-title {
    margin-left: 28rpx;
    margin-bottom: 28rpx;
  }
  .monitoring-items {
    border-radius: 8rpx;
    padding: 14rpx;
    .level-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 48rpx;
    }
    .point-name,
    .point-time {
      font-size: 24rpx;
      color: #404040;
      line-height: 28rpx;
      height: 28rpx;
      margin-bottom: 10rpx;
    }
    .point-name {
      height: auto;
    }
    .point-time {
      color: #a09f9f;
      margin-bottom: 0;
    }
    .unit-num {
      font-size: 40rpx;
      margin-right: 6rpx;
    }
    .unit-text {
      font-size: 24rpx;
    }
    &.monitoring-items1 {
      background: #f4f8ff;
      border: 2rpx solid #4995ff;
      .unit-num,
      .unit-text {
        color: #4995ff;
      }
    }
    &.monitoring-items2 {
      background: #fff8f1;
      border: 2rpx solid #e3893b;
      .unit-num,
      .unit-text {
        color: #e3893b;
      }
    }
    &.monitoring-items3 {
      background: #fff4f4;
      border: 2rpx solid #ed5354;
      .unit-num,
      .unit-text {
        color: #ed5354;
      }
    }
  }
  .same-msg {
    min-height: 88rpx;
    border-bottom: 2rpx solid #f0f0f0;
    padding: 24rpx 40rpx;
    box-sizing: border-box;
    .label,
    .content {
      color: #404040;
      font-size: 28rpx;
      text-align: left;
      width: 168rpx;
      margin-right: 28rpx;
      line-height: 40rpx;
    }
    .content {
      flex: 1;
      line-height: 40rpx;
      word-break: break-all;
    }
    &:last-child .label {
      width: 180rpx;
    }
    .location-img {
      width: 200rpx;
      height: 200rpx;
    }
  }
}
</style>
