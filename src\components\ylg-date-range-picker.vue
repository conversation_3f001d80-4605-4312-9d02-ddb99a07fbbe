<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-14 20:35:09
 * @Description: 
-->
<template>
  <view class="">
    <uv-popup ref="popup" mode="bottom" round="24" @change="change">
      <scroll-view class="content" :scroll-y="true" :show-scrollbar="false">
        <!-- tab选择 -->
        <view class="tabs_box">
          <view
            :class="['item', curTab === 0 ? 'active' : '']"
            @click="changeT(0)"
            >选择月份</view
          >
          <view
            :class="['item', curTab === 1 ? 'active' : '']"
            @click="changeT(1)"
            >自定义时间</view
          >
          <view :class="['bar_line', curTab == 1 ? 'change_line' : '']"></view>
        </view>
        <view class="date_picker_box">
          <!-- 选择月份 -->
          <picker-view
            v-if="curTab === 0"
            :indicator-style="indicatorStyle"
            :value="dateVal"
            @change="dateChange"
            class="picker_view"
            :style="{ height: '580rpx' }"
          >
            <picker-view-column>
              <view
                :class="['date_item', year == item ? 'active_date_item' : '']"
                v-for="(item, index) in years"
                :key="index"
                >{{ item }}年</view
              >
            </picker-view-column>
            <picker-view-column>
              <view
                :class="['date_item', month == item ? 'active_date_item' : '']"
                v-for="(item, index) in months"
                :key="index"
                >{{ item }}月</view
              >
            </picker-view-column>
          </picker-view>
          <!-- 选择自定义时间 -->
          <template v-if="curTab === 1">
            <view class="quick_btns_box">
              <view
                :class="[
                  'quick_btn',
                  recentActiceVal === 3 ? 'recent_active_btn' : '',
                ]"
                @click="quickChooseDate(3)"
                >近3个月</view
              >
              <view
                :class="[
                  'quick_btn',
                  recentActiceVal === 6 ? 'recent_active_btn' : '',
                ]"
                @click="quickChooseDate(6)"
                >近半年</view
              >
              <view
                :class="[
                  'quick_btn',
                  recentActiceVal === 12 ? 'recent_active_btn' : '',
                ]"
                @click="quickChooseDate(12)"
                >近一年</view
              >
            </view>
            <view class="dateval_range">
              <view
                :class="['start_val', isRangeStartActive ? 'active_val' : '']"
                @click="focusRangeStart(true)"
              >
                <text
                  v-if="startDateDesc"
                  :style="{ color: isRangeStartActive ? '#4378ff' : '#a09f9f' }"
                  >{{ startDateDesc }}</text
                >
                <text v-else>开始时间</text>
              </view>
              <view class="to">至</view>
              <view
                :class="['end_val', isRangeStartActive ? '' : 'active_val']"
                @click="focusRangeStart(false)"
              >
                <text
                  v-if="endDateDesc"
                  :style="{ color: isRangeStartActive ? '#a09f9f' : '#4378ff' }"
                  >{{ endDateDesc }}</text
                >
                <text v-else>结束时间</text>
              </view>
            </view>
            <picker-view
              :indicator-style="indicatorStyle"
              :value="dateRangeVal"
              @change="dateChange"
              class="picker_view"
              :style="{ height: '380rpx' }"
            >
              <picker-view-column>
                <view
                  :class="{
                    date_item: true,
                    active_date_item: isRangeStartActive
                      ? startYear === item
                      : endYear === item,
                  }"
                  v-for="(item, index) in years"
                  :key="index"
                  >{{ item }}年</view
                >
              </picker-view-column>
              <picker-view-column>
                <view
                  :class="{
                    date_item: true,
                    active_date_item: isRangeStartActive
                      ? startMonth === item
                      : endMonth === item,
                  }"
                  v-for="(item, index) in months"
                  :key="index"
                  >{{ item }}月</view
                >
              </picker-view-column>
              <picker-view-column>
                <view
                  :class="{
                    date_item: true,
                    active_date_item: isRangeStartActive
                      ? startDay === item
                      : endDay === item,
                  }"
                  v-for="(item, index) in days"
                  :key="index"
                  >{{ item }}日</view
                >
              </picker-view-column>
            </picker-view>
          </template>
        </view>
        <!-- 确定按钮 -->
        <view class="btn_box">
          <view class="comfirm_btn btn" @click="comfirm">确定</view>
        </view>
      </scroll-view>
      <uv-toast ref="toast" position="top"></uv-toast>
    </uv-popup>
  </view>
</template>
<script setup>
import dayjs from "dayjs";
import { reactive, ref, computed, watch, nextTick } from "vue";
import { getDaysInMonth } from "@/utils";
// const list = reactive([{ name: "月份选择" }, { name: "自定义时间" }]);
const props = defineProps({});

let curTab = ref(0);
const changeT = (val) => {
  curTab.value = val;
};

// 选择年月
let date = ref(new Date());
let years = reactive([]);
let year = ref(date.value.getFullYear());
let months = reactive([]);
let month = ref(date.value.getMonth() + 1);
// let days = reactive([]);
let monthDays = ref(30);
let days = computed(() => {
  let arr = [];
  for (let i = 1; i <= monthDays.value; i++) {
    arr.push(i);
  }
  return arr;
});
// console.log('daysdays,',days,days.value);

let day = ref(date.value.getDate());
for (let i = 1990; i <= date.value.getFullYear(); i++) {
  years.push(i);
}
for (let i = 1; i <= 12; i++) {
  months.push(i);
}

const dateVal = reactive([9999, month.value - 1]);
const dateRangeVal = reactive([9999, month.value - 1, day.value - 1]);
const indicatorStyle = ref(`height: 76rpx;`);

let isRangeStartActive = ref(true);
let startDateDesc = ref(`${year.value}年${month.value}月${day.value}日`);
let startDateVal = ref(new Date(`${year.value}-${month.value}-${day.value}`));
let endDateDesc = ref(`${year.value}年${month.value}月${day.value}日`);
let endDateVal = ref(new Date(`${year.value}-${month.value}-${day.value}`));
// let endDateDesc = ref("");
// let endDateVal = ref(null);

let recentActiceVal = ref(0);

// 切换开始日期、结束日期时
let isSame = ref(false);
const focusRangeStart = (flag) => {
  console.log(
    "点击开始/结束时间",
    isSame.value,
    flag,
    startDateVal.value,
    endDateVal.value
  );
  isRangeStartActive.value = flag;
  nextTick(() => {
    // 开始时间
    if (flag) {
      const endY = startDateVal.value.getFullYear();
      const endM = startDateVal.value.getMonth() + 1;
      monthDays.value = getDaysInMonth(endY, endM); // 先更新
      dateRangeVal[0] = years.findIndex((item) => item === endY);
      dateRangeVal[1] = months.findIndex((item) => item === endM);
      dateRangeVal[2] = days.value.findIndex(
        (item) => item === startDateVal.value.getDate()
      );
      // debugger;
    } else {
      if (endDateVal.value) {
        const endY = endDateVal.value.getFullYear();
        const endM = endDateVal.value.getMonth() + 1;
        monthDays.value = getDaysInMonth(endY, endM); // 先更新

        dateRangeVal[0] = years.findIndex((item) => item === endY);
        dateRangeVal[1] = months.findIndex((item) => item === endM);
        dateRangeVal[2] = days.value.findIndex(
          (item) => item === endDateVal.value.getDate()
        );
        // debugger;
      }
    }
    isSame.value = startDateDesc.value == endDateDesc.value;
    console.log(
      "点击开始/结束时间2",
      isSame.value,
      dateRangeVal[0],
      dateRangeVal[1],
      dateRangeVal[2]
    );
  });
};

// 手动滑动选择时间
const dateChange = (e) => {
  recentActiceVal.value = 0;
  const val = e.detail.value;
  year.value = years[val[0]];
  month.value = months[val[1]];
  day.value = days.value[val[2]];
  console.log("查看val", e, month.value, day.value);

  // 自定义时间，选择时间范围时
  if (curTab.value === 1) {
    // 切换月份之后，需要重置days日期天数
    monthDays.value = getDaysInMonth(year.value, month.value);
    // 选开始时间
    if (isRangeStartActive.value) {
      startDateDesc.value = `${year.value}年${month.value}月${day.value}日`;
      startDateVal.value = new Date(
        `${year.value}-${month.value}-${day.value}`
      );
    } else {
      // 选结束时间
      endDateDesc.value = `${year.value}年${month.value}月${day.value}日`;
      endDateVal.value = new Date(`${year.value}-${month.value}-${day.value}`);
    }
  }
  focusRangeStart(isRangeStartActive.value)
};

// 计算前一年日期，返回格式为 YYYY-MM-DD 的字符串
const getPreviousYearDate = (date) => {
  date.setFullYear(date.getFullYear() - 1);
  return date;
};
// 计算前半年日期，直接减去6个月，JavaScript会自动处理年份的减少（如果需要）
const getPreviousHalfYearDate = (date) => {
  date.setMonth(date.getMonth() - 6);
  return date;
};
// 计算前三个月的日期，直接减去3个月，JavaScript会自动处理年份的减少（如果需要）
const getPreviousThreeMonthsDate = (date) => {
  date.setMonth(date.getMonth() - 3);
  return date;
};
const formatDate = (date) => {
  let year = date.getFullYear();
  let month = (date.getMonth() + 1).toString().padStart(2, "0"); // 月份从0开始，所以要+1
  let day = date.getDate().toString().padStart(2, "0");
  return `${year}年${month}月${day}日`;
};

// 快捷选择日期
// 3：近三个月
// 6：近半年
// 12：近一年
const quickChooseDate = (scene) => {
  recentActiceVal.value = scene;
  // 1）先计算当前日期，当天，作为结束日期
  let curDate = ref(new Date());
  let curYear = ref(curDate.value.getFullYear());
  let curMonth = ref(curDate.value.getMonth() + 1);
  let curDay = ref(curDate.value.getDate());
  endDateDesc.value = formatDate(curDate.value);
  endDateVal.value = new Date(
    `${curYear.value}-${curMonth.value}-${curDay.value}`
  );

  // 2）再计算距离当前日期 前三个月/前半年/前一年 的开始日期
  switch (scene) {
    case 12:
      startDateVal.value = getPreviousYearDate(curDate.value);
      startDateDesc.value = formatDate(startDateVal.value);
      dateRangeVal[0] = years.findIndex(
        (item) => item === startDateVal.value.getFullYear()
      );
      dateRangeVal[1] = months.findIndex(
        (item) => item === startDateVal.value.getMonth() + 1
      );
      break;
    case 6:
      startDateVal.value = getPreviousHalfYearDate(curDate.value);
      startDateDesc.value = formatDate(startDateVal.value);
      dateRangeVal[0] = years.findIndex(
        (item) => item === startDateVal.value.getFullYear()
      );
      dateRangeVal[1] = months.findIndex(
        (item) => item === startDateVal.value.getMonth() + 1
      );
      break;
    case 3:
      startDateVal.value = getPreviousThreeMonthsDate(curDate.value);
      startDateDesc.value = formatDate(startDateVal.value);
      dateRangeVal[0] = years.findIndex(
        (item) => item === startDateVal.value.getFullYear()
      );
      dateRangeVal[1] = months.findIndex(
        (item) => item === startDateVal.value.getMonth() + 1
      );
      break;
    default:
      break;
  }
};

// 确定
const emit = defineEmits(["confirmDate", "cancelDate"]);
let toast = ref(null);
const comfirm = () => {
  if (curTab.value === 0) {
    // 情况1：选择月份
    // console.log('选择月份',year.value,month.value);
    // month.value = month.value<10?`0${month.value}`:month.value;
    emit("confirmDate", {
      dateType: "date",
      year: year.value,
      month: month.value,
      startDate: `${year.value}-${month.value}`,
      endDate: `${year.value}-${month.value}`,
    });
  } else {
    // 情况2：选择时间范围
    if (startDateVal.value > endDateVal.value) {
      toast.value.show({
        type: "warning",
        message: "结束日期不能小于开始日期！",
      });
      return;
    }
    emit("confirmDate", {
      dateType: "daterange",
      startDate: dayjs(startDateVal.value).format("YYYY-MM-DD 00:00:00"),
      endDate: dayjs(endDateVal.value).format("YYYY-MM-DD 23:59:59"),
    });
  }
};
// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const change = (e) => {
  console.log("弹窗状态改变：", e);
  emit("cancelDate");
};

const setDefaultDate = (dateData) => {
  // dateData 格式应与 confirmDate 事件传出的结构一致，例如:
  // { dateType: "daterange", startDate: "2025-04-09 00:00:00", endDate: "2025-04-11 23:59:59" }
  if (dateData.dateType === "daterange") {
    // 注意：这里根据你的现有逻辑来更新内部状态
    // 假设你要显示的格式与 confirmDate 里展示的一致
    startDateDesc.value = dayjs(dateData.startDate).format("YYYY年M月D日");
    endDateDesc.value = dayjs(dateData.endDate).format("YYYY年M月D日");
    startDateVal.value = new Date(dateData.startDate);
    endDateVal.value = new Date(dateData.endDate);
    // 同时，更新 dateRangeVal 数组，使其指向对应的年份、月份和日期索引
    dateRangeVal[0] = years.findIndex(
      (item) => item === startDateVal.value.getFullYear()
    );
    dateRangeVal[1] = months.findIndex(
      (item) => item === startDateVal.value.getMonth() + 1
    );
    dateRangeVal[2] = days.value.findIndex(
      (item) => item === startDateVal.value.getDate()
    );
    focusRangeStart(isRangeStartActive.value ? true : false);
    // focusRangeStart(false);
  } else if (dateData.dateType === "date") {
    // 处理单个日期选择 —— 通常对应“选择月份”的情况
    // 假设 dateData 中有 year 和 month 字段（例如 2025, 4 ）
    year.value = dateData.year;
    month.value = dateData.month;

    // 更新 picker-view 使用的默认索引数组（只有两列：年和月）
    dateVal[0] = years.findIndex((item) => item === dateData.year);
    dateVal[1] = months.findIndex((item) => item === dateData.month);
    console.log("回显月份", dateVal[0], dateVal[1]);
  }
  // 你也可以在这里处理 dateType==='date' 的情况
};

// 从描述中得到年、月、日的数字
const parseChineseDate = (str) => {
  // 匹配数字+年、数字+月、数字+日
  const pattern = /(\d+)年(\d+)月(\d+)日/;
  const match = str.match(pattern);
  if (match) {
    // match[1] 为年份, match[2] 为月份, match[3] 为日份
    return {
      year: Number(match[1]),
      month: Number(match[2]),
      day: Number(match[3]),
    };
  }
  // 如果不匹配，返回 null 或者抛出错误
  return null;
};
const startYear = computed(
  () => parseChineseDate(startDateDesc.value)?.year || null
);
const startMonth = computed(
  () => parseChineseDate(startDateDesc.value)?.month || null
);
const startDay = computed(
  () => parseChineseDate(startDateDesc.value)?.day || null
);
const endYear = computed(
  () => parseChineseDate(endDateDesc.value)?.year || null
);
const endMonth = computed(
  () => parseChineseDate(endDateDesc.value)?.month || null
);
const endDay = computed(() => parseChineseDate(endDateDesc.value)?.day || null);

defineExpose({
  open,
  close,
  changeT,
  setDefaultDate,
});
</script>
<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  // height: calc(100vh - env(safe-area-inset-bottom) - 400rpx);
  height: 900rpx;
  width: 100%;
  padding: 40rpx;
  // margin-bottom: 100rpx;
  .tabs_box {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: stretch;
    align-content: flex-start;
    .item {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: #a09f9f;
      line-height: 44rpx;
      padding: 0 0 20rpx 0;
      margin-right: 64rpx;
    }
    .active {
      color: #4378ff;
    }
    .bar_line {
      position: absolute;
      bottom: 0;
      width: 128rpx;
      height: 4rpx;
      background: #4378ff;
      transform: translate(0rpx);
      transition-duration: 300ms;
    }
    .change_line {
      transform: translate(206rpx);
    }
  }
  .date_picker_box {
    margin: 40rpx 0 50rpx 0;
    .quick_btns_box {
      display: flex;
      align-items: center;
      font-family: PingFang SC, PingFang SC;
      .quick_btn {
        margin-right: 28rpx;
        padding: 6rpx 16rpx;
        box-sizing: border-box;
        background: #fff;
        border: 2rpx solid #a09f9f;
        border-radius: 16rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #a09f9f;
        line-height: 40rpx;
      }
      .recent_active_btn {
        border: 2rpx solid #4378ff;
        color: #4378ff;
      }
    }
    .dateval_range {
      margin-top: 32rpx;
      display: flex;
      align-items: center;
      font-family: PingFang SC, PingFang SC;
      .start_val,
      .end_val {
        width: 288rpx;
        height: 76rpx;
        box-sizing: border-box;
        border-bottom: 2rpx solid #d9d9d9;
        font-weight: 400;
        font-size: 32rpx;
        color: #a09f9f;
        line-height: 76rpx;
        text-align: center;
      }
      .active_val {
        border-bottom: 2rpx solid #4378ff;
      }
      .to {
        margin: 0 32rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #373737;
        line-height: 40rpx;
      }
    }
    .picker_view {
      width: 100%;
      // height: calc(100% - 352rpx);
      // height: 380rpx;
      height: 580rpx;
      margin-top: 20rpx;
    }
    .date_item {
      line-height: 76rpx;
      text-align: center;
      font-weight: 500;
      font-size: 32rpx;
      color: #a09f9f;
      line-height: 76rpx;
    }
    .active_date_item {
      color: #404040;
    }
  }
}
.btn_box {
  // padding: 0 40rpx 40rpx 0rpx;
  display: flex;
  width: 100%;
  position: fixed;
  bottom: 40rpx;
  z-index: 9;
  .btn {
    width: 670rpx;
    height: 84rpx;
    line-height: 84rpx;
    box-sizing: border-box;
    text-align: center;
    border-radius: 8rpx;
    background: #4378ff;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #fff;
  }
}
</style>
