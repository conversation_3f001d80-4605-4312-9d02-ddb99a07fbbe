<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-14 15:06:22
 * @Description: 
-->
<template>
  <view class="page_container">
    <view class="top_bg"></view>
    <view class="page_content">
      <view class="main_info_card">
        <view class="top_card">
          <view
            :class="[
              'card_top_status',
              'card_top_status' + detailData.eventStatus,
            ]"
          >
            {{ detailData.eventStatusName }}
          </view>
          <view class="card_top">
            <image
              class="card_top_icon"
              :src="geteventSourceIcon(detailData.eventSource)"
            />
            <view v-if="detailData.eventType === '2'" class="card_top_title">
              {{ detailData.remark || "-" }}
            </view>
          </view>
          <view class="card_content">
            <view class="content_item">
              <view class="content_val">{{ detailData.eventCode || "-" }}</view>
            </view>
          </view>
        </view>
        <view class="detail_title flex">
          <view class="left_bar flex">
            <view class="bar"></view>
            <view class="text">事件详情 </view>
          </view>
        </view>
        <ImageSwiper :swiperList="swiperList" />
        <view v-if="isShowDetail">
          <view class="info_box">
            <view class="left_title">上报时间：</view>
            <view class="right_content">
              {{
                detailData?.createTime +
                "（" +
                detailData?.submitUserName +
                "）"
              }}
            </view>
          </view>
          <view class="info_box">
            <view class="left_title">事件说明：</view>
            <view class="right_content">{{ detailData?.remark || "-" }}</view>
          </view>
          <view class="info_box">
            <view class="left_title">所属路段：</view>
            <view class="right_content">{{
              detailData?.sectionName || "-"
            }}</view>
          </view>
          <view class="info_box">
            <view class="left_title">事件对象： </view>
            <view class="right_content">{{
              detailData?.eventObjectTypeName +
                "（" +
                detailData?.eventObjectName +
                "）" || "-"
            }}</view>
          </view>
          <view class="info_box">
            <view class="left_title">事件位置：</view>
            <view class="right_content">
              {{
                (detailData?.stakeName || "--") +
                "/" +
                (detailData?.endStakeName || "--") +
                "（" +
                detailData?.updownMarkName +
                "） "
              }}
            </view>
          </view>
          <view class="info_box">
            <view class="left_title" style="display: flex">
              地
              <view style="width: 56rpx"></view>
              址：
            </view>
            <view
              class="right_content"
              style="color: #4378ff"
              @click="viewLocation"
            >
              {{ detailData?.address || "-" }}
            </view>
          </view>
          <view v-if="detailData?.inspectTask?.taskCode">
            <view class="detail_title flex">
              <view class="left_bar flex">
                <view class="bar"></view>
                <view class="text">关联检查任务 </view>
              </view>
            </view>
            <view class="info_box">
              <view class="left_title">任务编码：</view>
              <view class="right_content">
                {{ detailData?.inspectTask?.taskCode }}
              </view>
            </view>
          </view>
        </view>
        <uv-skeleton
          class="skeleton"
          rows="12"
          title
          :loading="pageLoading"
        ></uv-skeleton>
        <view class="touch_bar" @click="toggleTopCard">
          <image
            class="down_img"
            src="../../../../../static/icon/touch_down_icon.png"
          ></image>
        </view>
      </view>
      <ProgressNodes :nodes="detailData.nodes" />
      <view style="height: 200rpx"> </view>
    </view>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      bgColor="rgba(255,255,255,0.5)"
      font-size="24rpx"
    ></uv-loading-page>
    <view v-if="detailData.eventStatus === '2'" class="bottom_box">
      <view class="place_box"></view>
      <view class="btn_box">
        <view class="btn" @click="openPopup">重新处置</view>
      </view>
    </view>
    <HandlePopup
      style="z-index: 9999"
      ref="handlePopupRef"
      :taskId="taskId"
      :eventObjectType="detailData.eventObjectType"
    />
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { eventHandlingApi } from "@/service";
import eventHandling_report_person from "@/static/icon/eventHandling_report_person.png";
import eventHandling_report_car from "@/static/icon/eventHandling_report_car.png";
import eventHandling_report_machie from "@/static/icon/eventHandling_report_machie.png";
import eventHandling_report_active from "@/static/icon/eventHandling_report_active.png";
import ImageSwiper from "@/pages/eventHandling/diseaseHandling/components/ImageSwiper.vue";
import ProgressNodes from "./ProgressNodes.vue";

const geteventSourceIcon = (eventSource) => {
  if (!eventSource) return "";
  const iconMap = {
    1: eventHandling_report_person,
    2: eventHandling_report_active,
    3: eventHandling_report_car,
    4: eventHandling_report_machie,
  };
  return iconMap[eventSource];
};

// 查看定位
const viewLocation = () => {
  uni.navigateTo({
    url: `/pages/eventHandling/diseaseHandling/components/ViewLocation?isDetail=${true}&curLongitude=${String(
      detailData.value.longitude
    )}&curLatitude=${String(detailData.value.latitude)}&curAddress=${
      detailData.value.address
    }`,
  });
};
// popup
import HandlePopup from "./HandlePopup.vue";

const handlePopupRef = ref();
const openPopup = () => {
  handlePopupRef.value.open(detailData.value);
};
const taskId = ref("");
const pageLoading = ref(false);
onLoad((options) => {
  taskId.value = options?.id;
});

onShow(async () => {
  // 获取任务详情
  await getDetail();
});

const detailData = ref({});
const swiperList = ref([]);
import { showImg } from "@/utils";
const isShowDetail = ref(false);
const toggleTopCard = () => {
  isShowDetail.value = !isShowDetail.value;
};
const getDetail = async () => {
  try {
    pageLoading.value = true;
    const { code, data } = await eventHandlingApi.detailWithTask(taskId.value);
    console.log("任务详情", code, data);
    if (code == 200) {
      detailData.value = data;
      swiperList.value =
        data?.fileAttributes &&
        data?.fileAttributes.map((item) => {
          return { title: item.source, url: showImg(item.path) };
        });

      // topCardHeight.value = data.inspectStartTime ? "244rpx" : "1000rpx";
    }
    pageLoading.value = false;
  } catch (error) {
    pageLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page_container {
  background-color: #f2f2f2f2;
  min-height: 100vh;
  overflow: hidden;
  position: relative;
  .top_bg {
    position: fixed;
    top: 0;
    width: 100vw;
    height: 100rpx;
    background-color: #065bff;
  }
  .page_content {
    position: relative;
    top: 20rpx;
    padding: 0 40rpx;
    box-sizing: border-box;
  }
  .main_info_card {
    position: relative;
    box-sizing: border-box;
    //   padding: 28rpx;
    width: 670rpx;
    // height: calc(100vh - 184rpx);
    // height: v-bind(topCardHeight);
    //   transition: height 0.8s ease;

    // overflow: auto;
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
    padding-top: 1px;
    padding-bottom: 40rpx;
    .card_top_status {
      position: absolute;
      top: -50rpx;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 44rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      border-radius: 0 16rpx 0 16rpx;
      &.card_top_status1 {
        color: #5c9ef7;
        background: #e2eeff;
      }
      &.card_top_status2 {
        color: #4a7ef6;
        background: #dfe8ff;
      }
      &.card_top_status3 {
        color: #61dc53;
        background: #ebfff2;
      }
    }
    .top_card {
      width: 100%;
      position: relative;
      text-align: center;
      box-sizing: border-box;
      margin-top: 48rpx;
      border-radius: 16rpx;
      background: #fff;

      .card_top {
        display: flex;
        position: relative;
        padding-right: 100rpx;
        .card_top_icon {
          display: block;
          width: 132rpx;
          height: 54rpx;
          margin: 0 20rpx 0 -16rpx;
        }
        .card_top_title {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          text-align: left;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          font-size: 32rpx;
          color: #373737;
        }
      }
      .card_content {
        margin-top: 28rpx;
        padding: 0rpx 28rpx;
        font-family:
          PingFang SC,
          PingFang SC;
        .content_item {
          margin-bottom: 20rpx;
          display: flex;
          align-items: baseline;
          font-weight: 400;
          font-size: 28rpx;
          line-height: 40rpx;
          .content_val {
            flex: 1;
            text-align: left;
            color: #404040;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
    }
    .title {
      font-weight: 600;
      font-size: 36rpx;
      color: #373737;
      line-height: 50rpx;
    }
    .detail_title {
      margin-top: 28rpx;
      margin-bottom: 20rpx;
      padding: 0 28rpx;
      .left_bar {
        .bar {
          margin-right: 8rpx;
          width: 6rpx;
          height: 28rpx;
          background: #4378ff;
          border-radius: 4rpx;
        }
        .text {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: bold;
          font-size: 32rpx;
          color: #404040;
        }
      }
      .right_img {
        display: block;
        width: 88rpx;
        height: 42rpx;
      }
    }

    .info_box {
      display: flex;
      // flex-wrap: wrap;
      margin-top: 24rpx;
      padding: 0 28rpx;
      .left_title {
        width: 140rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #a09f9f;
        line-height: 40rpx;
      }
      .right_content {
        flex: 1;
        font-weight: 400;
        font-size: 28rpx;
        color: #373737;
        line-height: 40rpx;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // overflow: hidden;
      }
      .right_excel {
        margin-top: 20rpx;
        width: 100%;
        border-radius: 4rpx;
        border: 2rpx solid #f2f2f2;
        .table_header {
          height: 50%;
          line-height: 48rpx;
          background-color: rgba(242, 242, 242, 1);
          font-weight: 400;
          font-size: 28rpx;
          color: #636363;
        }
        .table_body {
          height: 50%;
          line-height: 48rpx;
          font-weight: 400;
          font-size: 28rpx;
          color: #404040;
          border-bottom: 2rpx solid #f2f2f2;
        }
        .table_body:last-child {
          border-bottom: none;
        }
        .table_item_text {
          width: 50%;
          text-align: center;
        }
      }
    }
    .touch_bar {
      .down_img {
        position: absolute;
        bottom: 8rpx;
        left: 50%;
        transform: translateX(-40rpx);
        display: block;
        width: 80rpx;
        height: 8rpx;
      }
    }
  }
  .bottom_box {
    width: 100%;
    position: fixed;
    bottom: 0rpx;
    .place_box {
      width: 100%;
      height: 26rpx;
      background: #f2f2f2;
    }
    .btn_box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 160rpx;
      background: #ffffff;
      box-shadow: 0rpx -6rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
      .btn {
        width: 670rpx;
        height: 84rpx;
        background: #4378ff;
        border-radius: 8rpx;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        font-size: 40rpx;
        color: #ffffff;
        line-height: 84rpx;
        text-align: center;
      }
    }
  }
}
</style>
