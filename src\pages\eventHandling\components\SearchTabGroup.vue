<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-14 15:05:07
 * @Description: 
-->
<template>
  <view class="tabs-container">
    <view class="tabs-box1">
      <view
        :class="['tab', activeTab === item.value ? 'tab-active' : '']"
        v-for="(item, index) in eventTypes"
        :key="index"
        @click="changeTab(item.value)"
      >
        {{ item.label }}·{{ item.num }}
      </view>
    </view>
    <view class="tabs-box2">
      <uv-tabs
        :list="tabTypes"
        :current="tabType"
        @click="clickTab"
        lineColor="transparent"
        :activeStyle="{
          marginLeft: tabType === '' ? '2px' : '0px',
          height: '24px',
          lineHeight: '24px',
          padding: '4px 12px',
          borderRadius: '16px',
          backgroundColor: '#EDF2FF',
          color: '#4378FF',
          transform: 'scale(1.05)',
        }"
        :inactiveStyle="{
          padding: '4px 12px',
          borderRadius: '16px',
          backgroundColor: '#F2F2F2',
          color: '#83858B',
          transform: 'scale(1)',
        }"
        :itemStyle="searchTabsItemStyle"
      >
        <template v-slot:right>
          <view style="padding-left: 4px" @tap="showAllTabs">
            <image
              class="tabbar_more_icon"
              src="../../../static/icon/tabbar_more_icon2.png"
            />
          </view>
        </template>
      </uv-tabs>
    </view>
    <HomeTabsPicker
      style="z-index: 9999"
      ref="tabsPicker"
      :types="tabTypes"
      :dataStatus="tabDataStatus"
      :curType="pickerType"
      :curState="pickerState"
      @tabPickerCallback="tabPickerCallback"
    />
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import { computed, reactive, ref } from "vue";
import { onLoad, onPageScroll, onShow } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { DataAcquisitionService, eventHandlingApi } from "@/service";
import { useProjectStore } from "@/store/project";
import HomeTabsPicker from "./HomeTabsPicker.vue";

const projectInfo = useProjectStore();

const emits = defineEmits(["queryList"]);
const activeTab = ref("1");
const eventTypes = computed(() => {
  return [
    {
      value: "1",
      label: "病害事件",
      num: statisticData.diseasesNum,
    },
    {
      value: "2",
      label: "其他事件",
      num: statisticData.otherNum,
    },
  ];
});
const changeTab = (value) => {
  if (value === activeTab.value) return;
  activeTab.value = value;
  queryList();
};
// 获取顶部的统计数据
const statisticData = reactive({});
const getTopData = async () => {
  try {
    const params = {
      projectId: projectInfo.projectId,
      eventStatus: "1",
      eventSource: queryParams.value.eventSource,
      objectType: queryParams.value.objectType,
      sectionId: queryParams.value.sectionId,
    };
    const { code, data } = await eventHandlingApi.eventNum(params);
    if (code == 200) {
      statisticData.diseasesNum = data.diseasesNum;
      statisticData.otherNum = data.otherNum;
    }
    console.log("getTopData", params);
  } catch (error) {
    console.log(error);
  }
};
// 筛选条件
// 数据类型type
const tabType = ref("");
const tabTypes = reactive([
  {
    name: "全部",
    type: "",
  },
  {
    name: "人巡",
    type: "1",
  },
  {
    name: "主动上报",
    type: "2",
  },
  {
    name: "车巡",
    type: "3",
  },
  {
    name: "机巡",
    type: "4",
  },
]);
const tabDataStatus = ref([]);
const searchTabsItemStyle = reactive({
  height: "26px",
  boxSizing: "border-box",
  padding: "0px 0px",
  backgroundColor: "#fff",
  borderRadius: "8px",
  marginRight: "12px",
  fontSize: "14px",
});
const pickerType = ref("");
const pickerState = ref("");
const pickerRoad = ref("");
const pageInfo = reactive({
  page: 1,
  limit: 10,
});
const tabStatus = ref("");

const clickTab = (env) => {
  tabType.value = env.type;
  pickerType.value = env.type;
  pageInfo.page = 1;
  queryList();
};
const tabsPicker = ref(null);
const stickyZIndex = ref(999);
const showAllTabs = () => {
  stickyZIndex.value = 99;
  tabsPicker.value.open();
  pickerType.value = tabType.value;
  pickerState.value = tabStatus.value;
};
// ************************************
const getDicts = async () => {
  const { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes: "yhBusType",
  });
  if (code == 200) {
    tabDataStatus.value = [
      {
        label: "全部",
        value: "",
      },
    ].concat(
      data.yhBusType.map((item) => {
        return {
          label: item.dictValue,
          value: item.dictKey,
        };
      })
    );
  }
};
const queryParams = computed(() => {
  return {
    eventType: activeTab.value,
    eventSource: pickerType.value,
    objectType: pickerState.value,
    sectionId: pickerRoad.value,
  };
});
// 分类&状态选择组件，回调
const tabPickerCallback = (envRes) => {
  console.log(envRes);
  switch (envRes.type) {
    case "changeType":
      pickerType.value = envRes.val;
      break;
    case "changeState":
      pickerState.value = envRes.val;
      break;
    case "changeRoad":
      pickerRoad.value = envRes.val;
      break;
    case "confirmTabs":
      console.log("选择回调", envRes, pickerType.value, pickerState.value);
      tabType.value = pickerType.value;
      tabStatus.value = pickerState.value;
      tabsPicker.value?.close();
      stickyZIndex.value = 999;
      pageInfo.page = 1;
      queryList();
      break;
    case "reset":
      pickerType.value = "";
      pickerState.value = "";
      pickerRoad.value = "";
      break;
    case "cancelTabs":
      stickyZIndex.value = 999;
      break;
    default:
      break;
  }
};
const queryList = () => {
  getTopData();
  emits("queryList", queryParams.value);
};
onShow(() => {
  getDicts();
});
defineExpose({
  queryList,
});
</script>
<style lang="scss" scoped>
.tabs-container {
  padding: 0 28rpx;
  .tabs-box1 {
    display: flex;
    align-items: center;
    gap: 0 64rpx;
    height: 110rpx;
    .tab {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: #404040;
    }
    .tab-active {
      font-weight: bold;
      background: linear-gradient(
          to right,
          rgba(51, 109, 255, 1),
          rgba(124, 161, 255, 0.8),
          rgba(67, 120, 255, 0.1),
          rgba(67, 120, 255, 0.05)
        )
        no-repeat bottom / 100% 8rpx;
    }
  }
  .tabs-box2 {
    .tabbar_more_icon {
      display: inline-block;
      width: 120rpx;
      height: 48rpx;
      vertical-align: middle;
    }
  }
}
</style>
