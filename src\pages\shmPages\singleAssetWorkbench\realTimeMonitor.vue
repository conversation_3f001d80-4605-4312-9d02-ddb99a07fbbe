<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-05-21 10:26:08
 * @Description: 
-->
<template>
  <view class="video_container">
    <uv-loading-page :loading="isPageLoading" loading-mode="spinner"></uv-loading-page>
    <view>
      <!-- 视频播放区域 -->
      <view v-show="!isPageLoading && deviceList.length">
        <web-view
          class="webview_box"
          src="../../../static/jessibuca-demo-3/index.html"
          ref="webviewRef"
          :webview-styles="webviewStyles"
          @onPostMessage="handlePostMessage"
          @message="handlePostMessage"
        ></web-view>
      </view>
      <!-- 设备列表 -->
      <swiper
        class="swiper_box"
        :circular="true"
        previous-margin="136rpx"
        next-margin="86rpx"
        :indicator-dots="true"
        :duration="500"
        :current="currentIndex"
        @change="handleChange"
      >
        <swiper-item
          class="swiper_item"
          v-for="(deviceItem, index) in deviceList"
          :key="index"
        >
          <image
            v-if="deviceItem.devicePicture"
            :class="[
              'device_item_img',
              currentIndex === index ? 'active_device_img' : '',
            ]"
            :src="showImg(deviceItem.devicePicture)"
          ></image>
          <image
            v-else
            :class="[
              'device_item_img',
              currentIndex === index ? 'active_device_img' : '',
            ]"
            src="../../../static/shmStatic/image/line_echart_point_nodata.png"
          ></image>
          <view class="device_info_bar">
            <image
              v-if="deviceItem.deviceStatus == '1'"
              class="monitor_status_icon"
              src="../../../static/shmStatic/icon/monitor_online_icon_20250514.png"
              alt=""
            />
            <image
              v-else
              class="monitor_status_icon"
              src="../../../static/shmStatic/icon/monitor_outline_icon_20250516.png"
              alt=""
            />
            <view class="monitor_device_name">{{ deviceItem.name }}</view>
          </view>
        </swiper-item>
      </swiper>
      <!-- 控制云台 -->
      <view v-show="!isPageLoading && deviceList.length">
        <view v-if="currentDevice.streamContent?.ws_flv" class="control_box">
          <!-- 放大 -->
          <image
            class="control_add_btn"
            src="../../../static/shmStatic/icon/monitor_active_add_control_20250519.png"
            alt=""
            @click="ptzCamera('zoomin')"
          />
          <!-- 上下左右 -->
          <view class="control_center_btn">
            <view class="btn btn_up">
              <image
                src="../../../static/shmStatic/icon/monitor_active_up_control_20250519.png"
                alt=""
                @click="ptzCamera('up')"
              />
            </view>
            <view class="btn btn_down">
              <image
                src="../../../static/shmStatic/icon/monitor_active_down_control_20250519.png"
                alt=""
                @click="ptzCamera('down')"
              />
            </view>
            <view class="btn btn_left">
              <image
                src="../../../static/shmStatic/icon/monitor_active_left_control_20250519.png"
                alt=""
                @click="ptzCamera('left')"
              />
            </view>
            <view class="btn btn_right">
              <image
                src="../../../static/shmStatic/icon/monitor_active_right_control_20250519.png"
                alt=""
                @click="ptzCamera('right')"
              />
            </view>
          </view>
          <!-- 减小 -->
          <image
            class="control_reduce_btn"
            src="../../../static/shmStatic/icon/monitor_active_reduce_control_20250519.png"
            alt=""
            @click="ptzCamera('zoomout')"
          />
        </view>
        <view v-else class="control_box">
          <!-- 放大 -->
          <image
            class="control_add_btn"
            src="../../../static/shmStatic/icon/monitor_inactive_add_control_20250514.png"
            alt=""
          />
          <!-- 上下左右 -->
          <view class="control_center_btn">
            <view class="btn btn_up">
              <image
                src="../../../static/shmStatic/icon/monitor_inactive_up_control_20250514.png"
                alt=""
              />
            </view>
            <view class="btn btn_down">
              <image
                src="../../../static/shmStatic/icon/monitor_inactive_down_control_20250514.png"
                alt=""
              />
            </view>
            <view class="btn btn_left">
              <image
                src="../../../static/shmStatic/icon/monitor_inactive_left_control_20250514.png"
                alt=""
              />
            </view>
            <view class="btn btn_right">
              <image
                src="../../../static/shmStatic/icon/monitor_inactive_right_control_20250514.png"
                alt=""
              />
            </view>
          </view>
          <!-- 减小 -->
          <image
            class="control_reduce_btn"
            src="../../../static/shmStatic/icon/monitor_inactive_reduce_control_20250514.png"
            alt=""
          />
        </view>
      </view>
    </view>
    <ylgNodata
      v-show="!isPageLoading && !deviceList.length"
      noticeTip="暂无监控设备"
      :imgStyle="{
        width: '400rpx',
        height: '304rpx',
      }"
      textSize="28rpx"
      textColor="#A09F9F"
      imgUrl="../../../static/shmStatic/image/monitor_nodata_20250519.png"
    />
  </view>
</template>
<script setup>
import { ref, computed, nextTick } from "vue";
import { onLoad, onReady } from "@dcloudio/uni-app";
import ylgNodata from "@/components/ylg-nodata.vue";
import { ShmService } from "@/service";
import { showImg } from "@/utils";

let webviewTop = ref('0')
let webviewStyles = computed(()=>{
  return {
    position: "fixed",
    left: "0",
    width: "100%",
    height: '30%',
    top: webviewTop.value,
    backgroundColor: "antiquewhite",
  }
});

// 监听webview发来的信息
const handlePostMessage = (data) => {
  console.log("webview天地图发来的数据", data.detail);
};

let assetId = ref("");
onLoad((options)=>{
  assetId.value = options.assetId || "1867091080912158721";
})

// 顶部设备列表轮播
let currentIndex = ref(0);
let currentDevice = ref({});
const handleChange = (e) => {
  console.log("切换卡片", e);
  currentIndex.value = e.detail.current;
  console.log('wv.value', wv.value);
  currentDevice.value = deviceList.value[currentIndex.value]
  wv.value.evalJS(
      `
        window.dispatchEvent(new MessageEvent('message', {
          data: ${JSON.stringify(currentDevice.value)}
        }));
      ` 
    );
};

// 获取监控设备列表
let deviceList = ref([]);
let isPageLoading = ref(true)
let controlBoxBottom = ref('200rpx')
let swiperTop = ref('28rpx')
const getMonitorDeviceList = async () => {
  try {
    isPageLoading.value = true
    let { code,data } = await ShmService.getMonitorDevicePageApi({
      assetId: assetId.value,
      page: -1,
      limit: -1,
    });
    console.log('查看监控设备结果', code,data);
    isPageLoading.value = false
    if (code == 200 && data?.length) {
      // data = [data[0]]
      // 设置设备列表为1个或多个时的页面样式
      webviewTop.value = data.length>1?'30%':'10.5%'
      controlBoxBottom.value = data.length>1?'200rpx':'500rpx'
      swiperTop.value = data.length>1?'28rpx':'12%'
      deviceList.value = data
      console.log('查看webview高度', deviceList.value.length);

      wv.value.evalJS(
        `
          window.dispatchEvent(new MessageEvent('message', {
            data: ${JSON.stringify(deviceList.value[0])}
          }));
        ` 
      );
    }
  } catch (error) {
    isPageLoading.value = false
    console.error("获取监控详情失败:", error);
    uni.showToast({
      title: "获取监控设备列表失败",
      icon: "none",
      duration: "2000",
    });
  }
};

// 云台控制
let ptzLoading = ref(false);
const ptzCamera = async (command) => {
  console.log("云台控制", command);
  if(ptzLoading.value) return
  try {
    ptzLoading.value = true;
    await ShmService.sendMonitorPtzApi(currentDevice.value.deviceId, { command })
    uni.showToast({
      title: "命令发送成功",
      icon: "none",
      duration: "2000",
    });
  } finally {
    ptzLoading.value = false
  }

};

// webview实例
const pages = getCurrentPages();
let wv = ref(null);

const getWebView = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const webView = pages[pages.length - 1].$getAppWebview().children()[0];
      console.log("webView", webView);
      resolve(webView);
    }, 2000);
  });
};

onReady(async () => {
  wv.value = await getWebView();
  getMonitorDeviceList();
});
</script>

<style scoped lang="scss">
.video_container {
  width: 100%;
  padding-top: 28rpx;
  // padding-top: v-bind(swiperTop);
  height: 100vh;
  background: #f4f8ff;
  overflow: hidden;
}
.black{
  width: 100%;
  height: 40rpx;
}
.swiper_box {
  position: fixed;
  top: v-bind(swiperTop);
  left: 0;
  width: 100vw;
  // z-index: 9999;
  .swiper_item {
    // margin-right: 45rpx;
    position: relative;
    width: 482rpx !important;
    height: 244rpx !important;
    // background: #ffffff;
    text-align: center;
    .device_item_img {
      box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.2);
      border-radius: 16rpx;
      width: 432rpx;
      height: 98%;
    }
    .active_device_img {
      border: 3rpx solid #4378ff;
    }
    .device_info_bar {
      position: absolute;
      bottom: 2rpx;
      left: 25rpx;
      width: 432rpx;
      height: 48rpx;
      background: rgba(10, 10, 10, 0.4);
      display: flex;
      align-items: center;
      border-bottom-left-radius: 10rpx;
      border-bottom-right-radius: 10rpx;
      .monitor_status_icon {
        width: 24rpx;
        height: 24rpx;
        margin: 0 12rpx 0 20rpx;
      }
      .monitor_device_name {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #ffffff;
        line-height: 32rpx;
      }
    }
  }
}
.control_box {
  position: fixed;
  bottom: v-bind(controlBoxBottom);
  width: 100%;
  z-index: 10000;
  display: flex;
  align-items: center;
  .control_add_btn {
    margin: 0 64rpx 0 86rpx;
  }
  .control_reduce_btn {
    margin: 0 86rpx 0 64rpx;
  }
  .control_add_btn,
  .control_reduce_btn {
    width: 80rpx;
    height: 80rpx;
  }
  .control_center_btn {
    position: relative;
    width: 300rpx; /* 整个控制器大小，可按需调整 */
    height: 300rpx;
    // background: #f0f4f8; /* 可选背景色，帮助看布局 */
  }
  /* 公共 btn 样式 */
  .btn {
    position: absolute;
    cursor: pointer;
  }
  .btn image {
    width: 100%;
    height: 100%;
    display: block;
  }

  /* 分别定位 */
  .btn_up {
    width: 194rpx; /* 按钮尺寸 */
    height: 96rpx;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }
  .btn_down {
    width: 194rpx; /* 按钮尺寸 */
    height: 96rpx;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
  .btn_left {
    width: 96rpx; /* 按钮尺寸 */
    height: 194rpx;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  .btn_right {
    width: 96rpx; /* 按钮尺寸 */
    height: 194rpx;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
// 设置nodata组件向下移一点
:deep(.content) {
  margin-top: 200rpx;
}
</style>
