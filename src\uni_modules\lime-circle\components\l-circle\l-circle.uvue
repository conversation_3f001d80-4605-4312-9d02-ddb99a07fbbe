<template>
	<view ref="circleRef" class="l-circle" :class="classes" :style="[styles]">
		<!-- #ifndef APP -->
		<view class="check"></view>
		<view v-if="!useCanvas" class="l-circle__trail" :style="trailStyles">
			<view class="cap start"></view>
			<view class="cap end"></view>
		</view>
		<view v-if="!useCanvas" class="l-circle__stroke" :style="strokeStyles">
			<view class="l-circle__stroke-line"></view>
			<view class="cap start" v-if="current"></view>
			<view class="cap end" v-if="current"></view>
		</view>
		<!-- #endif -->
		<view class="l-circle__inner" :style="contentStyles">
			<slot></slot>
		</view>
	</view>
</template>

<script lang="uts" setup>
	import { nextTick, PropType } from 'vue';
	import { unitConvert } from '@/uni_modules/lime-shared/unitConvert';
	import { addUnit } from '@/uni_modules/lime-shared/addUnit';
	import { useTransition, UseTransitionOptions } from '@/uni_modules/lime-shared/animation/useTransition'
	// import { useTransition, UseTransitionOptions } from '@/uni_modules/lime-use/useTransition'
	// #ifndef APP
	import { Circle } from './circle.esm'
	import { getCircleStyle } from './utils';
	// #endif
	// #ifdef APP
	import { Circle, CircleOptions } from './circle.uts'
	// #endif
	const emits = defineEmits(['update:current'])
	const props = defineProps({
		percent: {
			type: Number,
			default: 0
		},
		lineCap: {
			type: String,
			default: 'round'
		},
		// #ifdef APP-ANDROID
		size: {
			type: Object,
			default: '120px'
		},
		strokeWidth: {
			type: Object,
			default: '6'
		},
		strokeColor: {
			type: Object,
			default: '#2db7f5'
		},
		trailWidth: {
			type: Object,
			default: '6'
		},
		// #endif
		// #ifndef APP-ANDROID
		size: {
			type: [String, Number],
			default: '120px'
		},
		strokeWidth: {
			type: [String, Number],
			default: '6'
		},
		strokeColor: {
			type: [String, Array] as PropType<string|string[]>,
			default: '#2db7f5'
		},
		trailWidth: {
			type: [String, Number],
			default: '6'
		},
		canvas: Boolean,
		// #endif
		trailColor: {
			type: String,
			default: '#ddd'
		},
		dashboard: {
			type: Boolean,
			default: false
		},
		clockwise: {
			type: Boolean,
			default: true
		},
		duration: {
			type: Number,
			default: 300
		},
		max: {
			type: Number,
			default: 100
		},
		/**缺口角度 */
		gapDegree: {
			type: Number,
			default: 90
		},
		/**缺口位置 */
		gapPosition: {
			type: String,
			default: 'bottom'
		},
	})
	
	const classes = computed<Map<string, boolean>>(() :Map<string, boolean> => {
		const cls = new Map<string, boolean>()
		cls.set('clockwise', !props.clockwise)
		cls.set('is-'+props.lineCap, true)
		return cls //!props.clockwise ? 'clockwise' : ''
	})
	const contentStyles = computed<string>(() : string => {
		return !props.clockwise ? `transform: scale(-1,1)` : ''
	})
	const styles = computed<Map<string, string>>(() : Map<string, string> => {
		const style = new Map<string, string>()
		const size = addUnit(props.size)
		if(size!=null){
			style.set('width', size)
			style.set('height', size)
		}
		return style
	})
		
	
	const context = getCurrentInstance()
	const circleRef = ref<UniElement | null>(null)
	let circle : Circle | null = null
	// #ifndef APP
	const useCanvas = ref(props.canvas)
	// #endif
	const percent = ref<number>(0)
	const current = useTransition(percent, {
		// context: this,
		duration: props.duration,
		immediate: true,
	} as UseTransitionOptions);
	const stopCurrent = watch(current, (v:number) =>{
		const value = Math.floor(v*100)/100;
		// #ifdef APP
		circle?.play(value)
		// #endif
		//.toFixed(2)
		emits('update:current', value)
	})
	
	const stopPercent = watch(():number => props.percent, (v:number) => {
		percent.value = Math.min(v, props.max)
		// circleCanvas && circleCanvas.play(v)
	})
	// #ifndef APP
	const trailStyles = computed(() => {
		const { size, trailWidth, trailColor, dashboard, gapDegree, gapPosition } = props
		return getCircleStyle('trail', unitConvert(props.size), 1, dashboard ? gapDegree : 0, gapPosition, trailColor, unitConvert(trailWidth))
	})
	const strokeStyles = computed(() => {
		const { size, strokeWidth, strokeColor, dashboard, max, gapDegree, gapPosition } = props
		return getCircleStyle('stroke', unitConvert(props.size), Math.min(current.value / props.max, 1), dashboard ? gapDegree : 0, gapPosition, strokeColor, unitConvert(strokeWidth))
	})
	
	// #endif
	onMounted(() => {
		nextTick(() => {
			//  #ifdef APP
			const ctx = circleRef.value!.getDrawableContext()
			circle = new Circle(ctx!)
			circle!.setOption({
				size: unitConvert(props.size),
				lineCap: props.lineCap,
				strokeWidth: unitConvert(props.strokeWidth),
				strokeColor: props.strokeColor,
				trailWidth: unitConvert(props.trailWidth),
				trailColor: props.trailColor,
				dashboard: props.dashboard,
				max: props.max,
				gapDegree: props.gapDegree,
				gapPosition: props.gapPosition,
			} as CircleOptions)
			// #endif
			//  #ifndef APP
			uni.createSelectorQuery().in(context).select('.check').boundingClientRect((res)=>{
				useCanvas.value = !(res['height'] > 0 && !props.canvas)
			}).exec()
			
			// #endif
			percent.value = props.percent
		})
	})
	
	onUnmounted(()=>{
		stopCurrent()
		stopPercent()
	})
</script>
<style lang="scss">
	@import './index-x.scss';
</style>