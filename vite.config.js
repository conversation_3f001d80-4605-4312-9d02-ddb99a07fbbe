/*
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-06-27 09:09:39
 * @Description:
 */
import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import { resolve } from "path";
import { readFileSync } from "fs";

// 新增：读取 manifest.json 的 description
const manifest = JSON.parse(
  readFileSync(resolve(__dirname, "src/manifest.json"), "utf-8")
);

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => ({
  plugins: [uni()],
  envDir: "./env",
  // 新增：注入环境变量
  define: {
    "process.env.APP_DESCRIPTION": JSON.stringify(manifest.description || ""),
  },
  // 开发服务器配置
  server: {
    host: "0.0.0.0",
    port: 8080,
    // 请求代理
    proxy: {
      "/api": {
        // target: "https://platform.ylgsz.com/api", // 正式
        // target: "http://*************:8080", // dev
        target: "http://*************:8080", // test
        // target: "http://*************:8080", // 森珂本地
        // target: "http://*************:8080", // 邓哥本地
        // target: "http://*************:8080", // 兴炜本地
        // target: "http://************:8080/", // 刘行
        // target: "http://*************:8080/", // 庆边
        // target: "http://*************:8080/", // 彪哥
        // target: "http://*************:8080/", // 陈维平
        // target: 'http://*************:8080/', // 陈宇昊
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
  resolve: {
    alias: {
      "@": resolve("src"),
    },
  },
  build:
    command === "build" && mode === "production"
      ? {
          minify: "terser", // 混淆器，terser构建后文件体积更小
          terserOptions: {
            compress: {
              // 生产环境时移除console.log()
              drop_console: true,
              drop_debugger: true,
            },
          },
        }
      : {},
}));
