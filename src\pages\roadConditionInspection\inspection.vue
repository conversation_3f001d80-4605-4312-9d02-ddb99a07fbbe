<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-01-23 15:03:02
 * @Description: 
-->
<template>
  <view class="container">
    <view class="top_bg"></view>
    <view class="content">
      <view class="main_info_card">
        <view class="top_mark">{{
          taskDetail?.inspectPlan?.inspectTypeName || "日常巡查"
        }}</view>
        <view class="title">{{ taskDetail?.inspectPlan?.planName }}</view>
        <view class="detail_title flex">
          <view class="left_bar flex">
            <view class="bar"></view>
            <view class="text">任务详情</view>
          </view>
          <image
            class="right_img"
            :style="{
              width:
                taskDetail?.taskStatus == 5 || taskDetail?.taskStatus == 6
                  ? '136rpx'
                  : '88rpx',
            }"
            :src="statusUrls[taskDetail?.taskStatus]"
          ></image>
        </view>
        <view class="message_notice" v-if="taskDetail?.inspectPlan?.remark">{{
          taskDetail?.inspectPlan?.remark
        }}</view>
        <view class="info_box">
          <view class="left_title">任务时效：</view>
          <view class="right_content">{{ taskDetail?.cycleTime }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">检查路段：</view>
          <view class="right_content">{{
            taskDetail?.inspectPlan?.sectionName || "-"
          }}</view>
        </view>
        <view class="info_box wrap_info_box">
          <view class="left_title">检查对象：</view>
          <view class="right_excel">
            <view class="table_header flex">
              <view class="table_item_text">对象名称</view>
              <view class="table_item_text">养护检查等级</view>
            </view>
            <view
              class="table_body flex"
              v-for="(item, index) in taskDetail?.inspectPlan?.objectList"
              :key="index"
            >
              <view class="table_item_text">{{
                item.objectName || "-"
              }}</view>
              <view class="table_item_text">{{
                item.inspectGradeName || "-"
              }}</view>
            </view>
          </view>
        </view>
        <view class="info_box">
          <view class="left_title">关联计划：</view>
          <view class="right_content">{{
            taskDetail?.inspectPlan?.planName || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">检查类型：</view>
          <view class="right_content">{{
            taskDetail?.inspectPlan?.inspectTypeName || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">巡查类型：</view>
          <view class="right_content">{{
            taskDetail?.inspectPlan?.watchTypeName || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">检查频率：</view>
          <view class="right_content">{{
            taskDetail?.inspectPlan?.inspectFrequencyName || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">检 查 人：</view>
          <view class="right_content">{{
            taskDetail?.inspectorNames || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title w200">任务完成判定：</view>
          <view class="right_content">{{
            taskDetail?.inspectPlan?.taskFinishRuleName || "-"
          }}</view>
        </view>
        <view class="touch_bar" v-if="!isWait" @click="toggleTopCard">
          <image
            class="down_img"
            src="../../static/icon/touch_down_icon.png"
          ></image>
        </view>
      </view>
      <!-- <view v-if="isWait" class="bottom_btn" @click="handleStart"
        >开始检查</view
      > -->
      <uv-button
        v-if="isWait"
        :loading="btnLoading"
        :custom-style="startBtnStyle"
        :customTextStyle="btnTextStyle"
        text="开始检查"
        @click="handleStart"
      ></uv-button>
      <!-- 任务完成情况 -->
      <view v-if="!isWait" class="task_finish_card">
        <view class="detail_title flex">
          <view class="left_bar flex">
            <view class="bar"></view>
            <view class="text">任务完成情况</view>
          </view>
        </view>
        <!-- <template> -->
        <view class="info_box">
          <view class="left_title">开始时间：</view>
          <view class="right_content">{{
            taskCompleteInfo?.inspectStartTime || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">开始定位：</view>
          <view class="right_content">{{
            taskCompleteInfo?.inspectStartAddress || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">结束时间：</view>
          <view class="right_content">{{
            taskCompleteInfo?.inspectEndTime || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">结束定位：</view>
          <view class="right_content">{{
            taskCompleteInfo?.inspectEndAddress || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">任务用时：</view>
          <view class="right_content">{{
            taskCompleteInfo?.taskTakes || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">上报事件：</view>
          <view class="right_content">{{ taskCompleteInfo?.eventNum }}</view>
        </view>
        <view
          class="tab_box"
          v-if="
            taskCompleteInfo?.diseaseEventList?.length ||
            taskCompleteInfo?.otherEventList?.length
          "
        >
          <view class="tab_bar">
            <view
              :class="['bar_item', diseaseTab === 0 ? 'active_bar' : '']"
              @click="handleDiseaseTab(0)"
              >病害（{{
                taskCompleteInfo?.diseaseEventList?.length || 0
              }}）</view
            >
            <view
              :class="['bar_item', diseaseTab === 1 ? 'active_bar' : '']"
              @click="handleDiseaseTab(1)"
              >其他（{{ taskCompleteInfo?.otherEventList?.length || 0 }}）</view
            >
          </view>
          <view class="tab_content_box">
            <!-- 病害列表 -->
            <template v-if="diseaseTab === 0">
              <template v-if="taskCompleteInfo?.diseaseEventList?.length">
                <view
                  class="content_item"
                  @click="toDiseaseDetail(eventIem)"
                  v-for="(eventIem, index) in taskCompleteInfo?.diseaseEventList"
                  :key="index"
                >
                  <view class="main_info flex">
                    <!-- 病害：病害类型 -->
                    <view class="title">{{ eventIem?.diseasesTypeName || '-' }}</view>
                    <view class="time">{{ eventIem?.submitTime || '-' }}</view>
                  </view>
                  <!-- 病害：事件对象名称 -->
                  <view class="desc">{{ eventIem?.eventObjectName || '-' }}</view>
                </view>
              </template>
              <view v-else class="empty_box">暂无数据~</view>
            </template>
            <!-- 其他列表 -->
            <template v-else>
              <template v-if="taskCompleteInfo?.otherEventList?.length">
                <view
                  class="content_item"
                  @click="toDiseaseDetail(eventIem)"
                  v-for="(eventIem, index) in taskCompleteInfo.otherEventList"
                  :key="index"
                >
                  <view class="main_info flex">
                    <!-- 其他：事件描述 -->
                    <view class="title">{{ eventIem.remark || '-' }}</view>
                    <view class="time">{{ eventIem.submitTime || '-' }}</view>
                  </view>
                  <!-- 其他：事件对象名称 -->
                  <view class="desc">{{ eventIem.eventObjectName || '-' }}</view>
                </view>
              </template>
              <view v-else class="empty_box">暂无数据~</view>
            </template>      
          </view>
        </view>
        <!-- </template> -->
        <view class="btns" v-if="taskStatus == 3 || taskStatus == 6">
          <view class="event_sub_btn" @click="toReport">事件上报</view>
          <uv-button
            :loading="btnLoading"
            :custom-style="endBtnStyle"
            :customTextStyle="btnTextStyle"
            text="结束检查"
            @click="handleEnd"
          ></uv-button>
        </view>
      </view>
    </view>
    <inspectionModal
      ref="inspectionModalRef"
      :modalType="inspectionType"
      :curTime="curTime"
      :curRemark="remarkVal"
      :curAddress="myPosition.address"
      :isCancelDisabled="isCancelDisabled"
      :isRefreshLoading="isRefreshLoading"
      @onInspectionCallback="onInspectionCallback"
      ></inspectionModal>
    <remarkModal
      ref="remarkModalRef"
      :curRemark="remarkVal"
      @onRemarkCallback="onRemarkCallback"
    ></remarkModal>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      bgColor="rgba(255,255,255,0.5)"
      font-size="24rpx"
    ></uv-loading-page>
  </view>
</template>
<script setup>
import { computed, onUnmounted, reactive, ref } from "vue";
import inspectionModal from "./components/inspectionModal.vue";
import remarkModal from "@/components/ylg-remark-modal.vue";
import { onHide, onLoad, onShow, onUnload } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { RoadInspectionService } from "@/service";
import {
  getCurLocation,
  reverseGeocode,
  transformLngLat,
} from "@/utils/location";

// 状态图映射
// 1 待开始
// 2 待检查
// 3 进行中
// 4 已完成
// 5 超时已完成
// 6 超时未完成（需判断有无开始时间）
const statusUrls = {
  1: "/static/icon/waiting_start_icon20241008.png",
  2: "/static/icon/waiting_inspect_icon20241008.png",
  3: "/static/icon/on_going_icon20241008.png",
  4: "/static/icon/finished_icon20241008.png",
  5: "/static/icon/timeout_finished_icon20241008.png",
  6: "/static/icon/timeout_icon20241008.png",
};

let taskStatus = ref("2");
let taskId = ref("");
let pageLoading = ref(false);
// 是否还没有开始
// 2-true；
// 3，4，5-false；
// 6，需要判断有无开始时间，无开始时间-true；有开始时间-false
let isWait = ref(true);
let topCardHeight = ref("244rpx");
let btnLoading = ref(false);
let pageFrom = ref("");
onLoad((options) => {
  taskStatus.value = options?.taskStatus;
  pageFrom.value = options?.pageFrom;
  if (taskStatus.value == "2") {
    isWait.value = true;
    topCardHeight.value = "1000rpx";
  } else if (["3", "4", "5"].includes(taskStatus.value)) {
    isWait.value = false;
    topCardHeight.value = "244rpx";
  }
  taskId.value = options?.taskId;
});

onShow(async () => {
  // 获取任务详情
  await getDetail();
  if (!isWait.value) {
    await getCompleteInfo();
  }
  // todo....... 超时未完成需结合开始时间判断
  if (taskStatus.value == "3" || (taskStatus.value == "6"&&!isWait.value)) {
    watchLocation();
  }
});

let taskDetail = ref({});
const getDetail = async () => {
  try {
    pageLoading.value = true;
    let { code, data } = await RoadInspectionService.getTaskDetail(
      taskId.value
    );
    console.log("任务详情", code, data);
    if (code == 200) {
      taskDetail.value = data;
      taskStatus.value = data.taskStatus;
      isWait.value = data.inspectStartTime ? false : true;
      topCardHeight.value = data.inspectStartTime ? "244rpx" : "1000rpx";
    }
    pageLoading.value = false;
  } catch (error) {
    pageLoading.value = false;
  }
};
let taskCompleteInfo = ref({});
const getCompleteInfo = async () => {
  try {
    let { code, data } = await RoadInspectionService.getTaskCompleteInfo(
      taskId.value
    );
    console.log("任务完成情况", code, data);
    taskCompleteInfo.value = data;
  } catch (error) {}
};
const startBtnStyle = {
  position: "fixed",
  bottom: "40rpx",
  width: "670rpx",
  height: "84rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378ff",
  color: " #ffffff",
};
const btnTextStyle = {
  fontSize: "40rpx",
};
const endBtnStyle = {
  marginTop: "28rpx",
  width: "616rpx",
  height: "84rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#FFFFFF",
  border: "2rpx solid #4378ff",
  color: " #4378FF",
};

// 切换顶部详情卡片展示高度
const toggleTopCard = () => {
  console.log("切换高度", topCardHeight.value);
  topCardHeight.value =
    topCardHeight.value === "1000rpx" ? "244rpx" : "1000rpx";
};

let inspectionModalRef = ref(null);
let inspectionType = ref("start");
let curTime = ref("");
let myPosition = reactive({
  // longitude: "115.109334",
  // latitude: "39.633542",
  // address: "河北省保定市涞水县其中口乡108国道",
  longitude: "",
  latitude: "",
  address: "",
});
const handleStart = async () => {
  inspectionType.value = "start";
  curTime.value = dayjs().format("HH:mm");
  btnLoading.value = true;
  await getAddress();
  inspectionModalRef.value.open();
};
const getAddress = async () => {
  try {
    let locationRes = await getCurLocation();
    if (locationRes.errMsg == "getLocation:ok") {
      myPosition.longitude = locationRes.longitude;
      myPosition.latitude = locationRes.latitude;
      myPosition.address = await reverseGeocode(
        locationRes.longitude,
        locationRes.latitude
      );
    }
    btnLoading.value = false;
  } catch (error) {
    console.log("获取定位失败", error);
    btnLoading.value = false;
    uni.showToast({
      icon: "none",
      title: "获取定位失败" + error,
    });
  }
};

const handleEnd = async () => {
  inspectionType.value = "end";
  curTime.value = dayjs().format("HH:mm");
  btnLoading.value = true;
  await getAddress();
  inspectionModalRef.value.open();
};
// 开始检查/结束检查 弹窗回调事件
let remarkModalRef = ref(null);
let isRefreshLoading = ref(false);
const onInspectionCallback = async (e) => {
  switch (e.type) {
    case "onAction":
      // 开始检查
      if (isWait.value) {
        console.log("开始检查");
        onStart();
      } else if (taskStatus.value == 3||taskStatus.value == 6) {
        console.log("结束检查");
        onEnd();
      }
      break;
    case "onRefreshLocation":
      isRefreshLoading.value = true;
      await getAddress();
      isRefreshLoading.value = false;
      break;
    case "onCacel":
      inspectionModalRef.value.close();
      remarkVal.value = "";
      break;
    case "onRemark":
      console.log("添加备注");
      remarkModalRef.value.open();
      break;
    default:
      break;
  }
};

let remarkVal = ref("");
const onRemarkCallback = (envRes) => {
  console.log("remark", envRes.remark);
  if (!envRes.remark) {
    uni.showToast({
      icon: "none",
      title: "请填写备注~",
    });
    return;
  }
  remarkVal.value = envRes.remark;
  remarkModalRef.value.close();
};
let isCancelDisabled = ref(false);
const onStart = async () => {
  try {
    isCancelDisabled.value = true;
    // await getAddress();
    // isLocationLoading.value = false;
    if(!myPosition.longitude||!myPosition.latitude){
      uni.showToast({
        icon: 'none',
        title: '请先获取当前定位~'
      })
      return false;
    }
    pageLoading.value = true;
    let params = {
      inspectTaskId: taskId.value,
      remark: remarkVal.value,
      ...myPosition,
    };
    let { code, data } = await RoadInspectionService.startInspectTask(params);
    console.log("开始检查结果", code, data);

    if (code == 200) {
      uni.showToast({
        icon: "none",
        title: "路况检查开始",
      });
      remarkVal.value = "";
      await getDetail();
      await getCompleteInfo();
      inspectionModalRef.value.close();
      // 开始监听实时定位
      watchLocation();
    }
    pageLoading.value = false;
    isCancelDisabled.value = false;
  } catch (error) {
    pageLoading.value = false;
    isCancelDisabled.value = false;
    console.log("开始检查失败");
  }
};
const onEnd = async () => {
  console.log('触发结束检查事件');
  try {
    isCancelDisabled.value = true;
    // await getAddress();
    // isLocationLoading.value = false;
    if(!myPosition.longitude||!myPosition.latitude){
      uni.showToast({
        icon: 'none',
        title: '请先获取当前定位~'
      })
      return false;
    }
    pageLoading.value = true;
    let params = {
      inspectTaskId: taskId.value,
      remark: remarkVal.value,
      ...myPosition,
    };
    let { code, data } = await RoadInspectionService.endInspectTask(params);
    console.log("结束检查结果", code, data);

    if (code == 200) {
      uni.showToast({
        icon: "none",
        title: "路况检查结束",
      });
      remarkVal.value = "";
      // 停止监听实时定位
      // uni.stopLocationUpdate();
      stopLocationUpdate();
      // await getDetail();
      // await getCompleteInfo();
      inspectionModalRef.value.close();
      if(pageFrom.value=='home'){
        uni.redirectTo({
          url:"/pages/roadConditionInspection/index"
        })
      }else{
        uni.navigateBack({
          delta: 1,
        });
      }
    }
    pageLoading.value = false;
    isCancelDisabled.value = false;
  } catch (error) {
    pageLoading.value = false;
    isCancelDisabled.value = false;
    console.log("结束检查失败");
  }
};

// 事件上报
const toReport = () => {
  uni.navigateTo({
    url: `/pages/roadConditionInspection/eventReporting?pageFrom=inspection&inspectTaskId=${taskDetail.value.id}&sectionId=${taskDetail.value.inspectPlan.sectionId}&sectionName=${taskDetail.value.inspectPlan.sectionName}&inspectPlanId=${taskDetail.value.inspectPlanId}&inspectObject=${taskDetail.value.inspectPlan.inspectObject}`,
  });
};

// 切换查看病害/其他
let diseaseTab = ref(0);
const handleDiseaseTab = (tabType) => {
  diseaseTab.value = tabType;
  // 查询对应数据
};

// 查看病害/其他详情
const toDiseaseDetail = (item) => {
  uni.navigateTo({
    url: `/pages/roadConditionInspection/eventReportingDetail?id=${item.id}`,
  });
};

onHide(() => {
  console.log("hide页面",);
  // uni.stopLocationUpdate();
  stopLocationUpdate();
});
onUnload(() => {
  console.log("onUnload页面",);
  // uni.stopLocationUpdate();
  stopLocationUpdate();
});

const lastTimestamp = ref(0);
const watchLocation = () => {
  console.log(
    "taskDetail.value.inspectPlan.inspectTrajectory",
    taskDetail.value.inspectPlan.inspectTrajectory
  );

  if (taskDetail.value.inspectPlan.inspectTrajectory != 2) {
    return;
  }
  startLocationUpdate();
};

// 开始实时定位
const startLocationUpdate = () => {
  uni.startLocationUpdate({
    type:'wgs84',
    success: () => {
      console.log('开始实时定位更新');
      // 监听位置变化
      uni.onLocationChange(handleLocationChange);
    },
    fail: (err) => {
      console.error('启动定位更新失败', err);
    },
  });
};

const handleLocationChange = (res) => {
  console.log('监听位置改变',res);
  
  if(res.accuracy && res.accuracy < 5){
    getLocationAndReport(res);
  }
}

// 获取用户位置
const getLocationAndReport = (res) => {
  // 获取当前时间
  const currentTimestamp = Date.now();
  // 确保上次上报时间距离当前时间至少5秒
  if (currentTimestamp - lastTimestamp.value >= 5000) {
    lastTimestamp.value = currentTimestamp; // 更新上次上报的时间
    const { latitude, longitude } = res;
    uploadLocationToServer(latitude, longitude); // 上报位置到后端
  }
};
// 上传位置到服务器
let isUploading = ref(false);
const uploadLocationToServer = async (latitude, longitude) => {
  if (isUploading.value) return;  // 如果上一次请求还没完成，直接返回
  isUploading.value = true;
  try {
    let params = {
      inspectTaskId: taskDetail.value.id,
      longitude: String(longitude),
      latitude: String(latitude),
    };
    let res = await RoadInspectionService.updateLocation(params);
    console.log('实时上报位置',String(longitude),String(latitude));
    
    if (res.code != 200) {
      console.log("监听位置失败");
      uni.showToast({
        icon: "none",
        title: res.msg || "上报轨迹请求失败~",
      });
    }
  } catch (error) {
    console.error('位置上传失败:', error);
  } finally {
    isUploading.value = false;  // 完成后重置标识位
  }

};

// 停止实时定位
const stopLocationUpdate = () => {
  uni.offLocationChange(handleLocationChange);
  uni.stopLocationUpdate({
    success: () => {
      console.log('停止实时定位更新');
    },
    fail: (err) => {
      console.error('停止定位更新失败', err);
    },
  });
};


</script>
<style lang="scss" scoped>
.container {
  background-color: #f4f8ff;
  min-height: 100vh;
  overflow: hidden;
  position: relative;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top_bg {
  position: absolute;
  top: 0;
  width: 100vw;
  height: 100rpx;
  background-color: #065bff;
}
.content {
  position: relative;
  top: 20rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
}
.main_info_card {
  position: relative;
  box-sizing: border-box;
  padding: 28rpx;
  width: 670rpx;
  // height: calc(100vh - 184rpx);
  height: v-bind(topCardHeight);
  transition: height 0.8s ease;
  // height: 80vh;
  // height: 244rpx;
  overflow: scroll;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
  .top_mark {
    position: absolute;
    right: 0;
    top: 0;
    background-color: rgba(207, 238, 255, 1);
    width: 136rpx;
    height: 52rpx;
    border-radius: 0 24rpx 0 24rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #48A9E6;
    line-height: 52rpx;
    text-align: center;
  }
  .title {
    font-weight: 600;
    font-size: 36rpx;
    color: #373737;
    line-height: 50rpx;
  }
  .detail_title {
    margin-top: 28rpx;
    .left_bar {
      .bar {
        margin-right: 8rpx;
        width: 6rpx;
        height: 24rpx;
        background: #4378ff;
        border-radius: 4rpx;
      }
      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 32rpx;
        color: #404040;
        line-height: 44rpx;
      }
    }
    .right_img {
      display: block;
      width: 88rpx;
      height: 42rpx;
    }
  }
  .message_notice {
    margin-top: 28rpx;
    padding: 6rpx 18rpx;
    background-color: rgba(254, 230, 228, 1);
    background: #fee6e4;
    border-radius: 8rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #ff3838;
    line-height: 40rpx;
  }
  .wrap_info_box{
    flex-wrap: wrap;
  }
  .info_box {
    display: flex;
    // flex-wrap: wrap;
    align-items: center;
    margin-top: 24rpx;
    display: flex;
    .left_title {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #b0b0b0;
      line-height: 40rpx;
    }
    .right_content {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      line-height: 40rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .right_excel {
      margin-top: 20rpx;
      width: 100%;
      border-radius: 4rpx;
      border: 2rpx solid #f2f2f2;
      .table_header {
        height: 50%;
        line-height: 48rpx;
        background-color: rgba(242, 242, 242, 1);
        font-weight: 400;
        font-size: 28rpx;
        color: #636363;
      }
      .table_body {
        height: 50%;
        line-height: 48rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #404040;
        border-bottom: 2rpx solid #f2f2f2;
      }
      .table_body:last-child {
        border-bottom: none;
      }
      .table_item_text {
        width: 50%;
        text-align: center;
      }
    }
  }
}
.touch_bar {
  .down_img {
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-40rpx);
    display: block;
    width: 80rpx;
    height: 8rpx;
  }
}
.bottom_btn {
  position: fixed;
  bottom: 40rpx;
  // left: 0;
  // right: 0;
  width: 670rpx;
  height: 84rpx;
  line-height: 84rpx;
  text-align: center;
  background: #4378ff;
  border-radius: 8rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 40rpx;
  color: #ffffff;
}

.task_finish_card {
  margin: 34rpx 0 80rpx 0;
  box-sizing: border-box;
  padding: 28rpx;
  width: 670rpx;
  background-color: #fff;
  border-radius: 24rpx;
  .detail_title {
    .left_bar {
      .bar {
        margin-right: 8rpx;
        width: 6rpx;
        height: 24rpx;
        background: #4378ff;
        border-radius: 4rpx;
      }
      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 32rpx;
        color: #404040;
        line-height: 44rpx;
      }
    }
    .right_img {
      display: block;
      width: 88rpx;
      height: 42rpx;
    }
  }
  .info_box {
    display: flex;
    align-items: center;
    // flex-wrap: wrap;
    margin-top: 24rpx;
    display: flex;
    .left_title {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #b0b0b0;
      line-height: 40rpx;
    }
    .right_content {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      line-height: 40rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .right_excel {
      margin-top: 20rpx;
      height: 96rpx;
      width: 100%;
      border-radius: 4rpx;
      border: 2rpx solid #f2f2f2;
      .table_header {
        height: 50%;
        line-height: 48rpx;
        background-color: rgba(242, 242, 242, 1);
        font-weight: 400;
        font-size: 28rpx;
        color: #636363;
      }
      .table_body {
        height: 50%;
        line-height: 48rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #404040;
      }
      .table_item_text {
        width: 50%;
        text-align: center;
      }
    }
  }
  .tab_box {
    margin: 40rpx 0;
    .tab_bar {
      display: flex;
      align-content: center;
      justify-content: center;
      border-radius: 4rpx;
      .bar_item {
        width: 50%;
        height: 56rpx;
        background: #f2f2f2;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 30rpx;
        color: #636363;
        line-height: 56rpx;
        text-align: center;
      }
      .active_bar {
        color: #fff;
        background: #4378ff;
      }
    }
    .tab_content_box {
      .content_item {
        margin-top: 20rpx;
        padding: 12rpx 24rpx;
        box-sizing: border-box;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        border: 2rpx solid #d9d9d9;
        .main_info {
          .title {
            flex: 1;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #404040;
            line-height: 40rpx;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
          .time {
            width: 266rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 26rpx;
            color: #636363;
            line-height: 36rpx;
          }
        }
        .desc {
          margin-top: 8rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #a09f9f;
          line-height: 34rpx;
        }
      }
      .empty_box{
        margin-top: 20rpx;
        box-sizing: border-box;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        border: 2rpx solid #d9d9d9;
        height: 108rpx;
        line-height: 108rpx;
        text-align: center;
        color: #636363;
      }
    }
  }
  .btns {
    padding: 20rpx 0;
    .event_sub_btn {
      box-sizing: border-box;
      width: 616rpx;
      height: 84rpx;
      line-height: 84rpx;
      text-align: center;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      background: #4378ff;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 40rpx;
      color: #ffffff;
    }
  }
}
.w200{
  width: 200rpx !important;
}
</style>
