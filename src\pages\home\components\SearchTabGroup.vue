<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-16 13:56:27
 * @Description: 
-->
<template>
  <view class="tabs-container">
    <view class="tabs-box1">
      <view
        :class="['tab', activeTab === item.value ? 'tab-active' : '']"
        v-for="(item, index) in toDoTypes"
        :key="index"
        @click="changeTab(item.value)"
      >
        {{ item.label }}·{{ item.num }}
      </view>
    </view>
    <view class="tabs-box2">
      <uv-tabs
        :list="tabTypes"
        :current="tabType"
        @click="clickTab"
        lineColor="transparent"
        :activeStyle="{
          marginLeft: tabType === 0 ? '2px' : '0px',
          height: '24px',
          lineHeight: '24px',
          padding: '4px 12px',
          borderRadius: '16px',
          backgroundColor: '#3C5FFA',
          color: '#fff',
          transform: 'scale(1.05)',
        }"
        :inactiveStyle="{
          padding: '4px 12px',
          borderRadius: '16px',
          backgroundColor: '#F2F2F2',
          color: '#83858B',
          transform: 'scale(1)',
        }"
        :itemStyle="searchTabsItemStyle"
      >
      </uv-tabs>
    </view>
  </view>
</template>
<script setup>
import { computed, reactive, ref, watch } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { useProjectStore } from "@/store/project";
import { homepageApi } from "@/service";

const emits = defineEmits(["queryList"]);
const projectInfo = useProjectStore();
watch(
  () => projectInfo.projectId,
  (newValue) => {
    if (newValue) {
      queryList();
    }
  }
);
const activeTab = ref("1");
const toDoTypes = computed(() => {
  return [
    {
      value: "1",
      label: "作业单",
      num: statisticData.taskNum,
    },
    {
      value: "2",
      label: "审批单",
      num: statisticData.approveNum,
    },
  ];
});
const changeTab = (value) => {
  if (value === activeTab.value) return;
  activeTab.value = value;
  tabType.value = 0;
  queryList();
};
// 获取顶部的统计数据
const statisticData = reactive({});
const getCount = async () => {
  try {
    const { code, data } = await homepageApi.homeTodayTodoCount(
      queryParams.value
    );
    if (code == 200) {
      statisticData.taskNum = data.taskNum;
      statisticData.approveNum = data.approveNum;
    }
    console.log("getCount", queryParams.value);
  } catch (error) {
    console.log(error);
  }
};
const tabType = ref(0);
const tabTypes = computed(() => {
  switch (activeTab.value) {
    case "1":
      return [
        {
          name: "全部作业",
          type: 0,
        },
        {
          name: "路况检查",
          type: 1,
        },
        {
          name: "日常保养",
          type: 2,
        },
        {
          name: "日常维修",
          type: 3,
        },
      ];

    case "2":
      return [
        {
          name: "全部审批",
          type: 0,
        },
        {
          name: "事件处置",
          type: 1, // 4
        },
        {
          name: "维修确认",
          type: 2, // 5
        },
        {
          name: "维修验收",
          type: 3, // 6
        },
      ];

    default:
      break;
  }
});
const searchTabsItemStyle = reactive({
  height: "26px",
  boxSizing: "border-box",
  padding: "0px 0px",
  backgroundColor: "transparent",
  borderRadius: "8px",
  marginRight: "12px",
  fontSize: "14px",
});
const pageInfo = reactive({
  page: 1,
  limit: 10,
});
const clickTab = (env) => {
  tabType.value = env.type;
  pageInfo.page = 1;
  queryList();
};

const queryParams = computed(() => {
  return {
    toDoType: activeTab.value,
    workType: getFormatWorkType(),
    projectId: projectInfo.projectId,
  };
});
const getFormatWorkType = () => {
  if (activeTab.value == 2) {
    const formatMap = {
      0: 0,
      1: 4,
      2: 5,
      3: 6,
    };
    return formatMap[tabType.value];
  } else {
    return tabType.value;
  }
};
const queryList = () => {
  getCount();
  // todo：父页面缓存参数 下拉刷新
  emits("queryList", queryParams.value);
};
onShow(() => {
  if (queryParams.value.projectId) {
    queryList();
  }
});
defineExpose({ queryList });
</script>
<style lang="scss" scoped>
.tabs-container {
  .tabs-box1 {
    display: flex;
    align-items: center;
    gap: 0 64rpx;
    margin-bottom: 28rpx;
    .tab {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: #404040;
    }
    .tab-active {
      font-weight: bold;
      background: linear-gradient(
          to right,
          rgba(51, 109, 255, 1),
          rgba(124, 161, 255, 0.8),
          rgba(67, 120, 255, 0.1),
          rgba(67, 120, 255, 0.05)
        )
        no-repeat bottom / 100% 8rpx;
    }
  }
  .tabs-box2 {
    margin-bottom: 40rpx;
    .tabbar_more_icon {
      display: inline-block;
      width: 120rpx;
      height: 48rpx;
      vertical-align: middle;
    }
  }
}
</style>
