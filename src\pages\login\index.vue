<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-05-23 16:03:27
 * @Description: 
-->
<template>
  <view class="container">
    <view class="hello_box">
      <view class="left_text">
        <view class="main_text">您好！</view>
        <view class="info_text mt12">欢迎来到</view>
        <view class="info_text">解决方案平台~</view>
      </view>
      <image class="right_img" src="../../static/image/system_logo.png"></image>
    </view>
    <view class="content">
      <!-- 登录方式切换 -->
      <view class="method_picker">
        <view class="picker_box">
          <view
            :class="['item', currentLogin == 1 ? 'active' : 'default1']"
            @click="changeMethod(1)"
            >手机号登录
          </view>
          <view
            :class="['item', currentLogin == 2 ? 'active' : 'default2']"
            @click="changeMethod(2)"
            >密码登录</view
          >
        </view>
        <view class="active_bar" :style="activeBarStyle"></view>
      </view>
      <!-- 登录信息表单 -->
      <uv-form :model="formInfo" :rules="rules" ref="formRef">
        <template v-if="currentLogin == 1">
          <uv-form-item prop="phone">
            <uv-input
              fontSize="14px"
              color="8a8a8a"
              :customStyle="inputStyle"
              border="none"
              type="number"
              maxlength="11"
              placeholder="请输入手机号"
              prefixIcon="../../static/icon/phone_num_icon.png"
              prefixIconStyle="font-size: 44rpx;color: #909399"
              v-model="formInfo.phone"
            ></uv-input>
          </uv-form-item>
          <uv-form-item prop="code">
            <uv-input
              border="none"
              fontSize="14px"
              color="8a8a8a"
              type="number"
              maxlength="6"
              :customStyle="inputStyle"
              placeholder="请输入验证码"
              prefixIcon="../../static/icon/code_icon.png"
              prefixIconStyle="font-size: 44rpx;color: #909399"
              v-model="formInfo.code"
            >
              <!-- vue3模式下必须使用v-slot:suffix -->
              <template v-slot:suffix>
                <uv-code
                  ref="uCode"
                  @change="codeChange"
                  seconds="60"
                  changeText="X秒重新获取"
                ></uv-code>
                <text class="code_btn" @click="getCode">{{ tips }}</text>
              </template>
            </uv-input>
          </uv-form-item>
        </template>
        <template v-else>
          <uv-form-item prop="account">
            <uv-input
              fontSize="14px"
              color="8a8a8a"
              :customStyle="inputStyle"
              border="none"
              maxlength="20"
              placeholder="请输入登录账号"
              prefixIcon="../../static/icon/account_icon.png"
              prefixIconStyle="font-size: 44rpx;color: #909399"
              v-model="formInfo.account"
            ></uv-input>
          </uv-form-item>
          <uv-form-item prop="pwd">
            <uv-input
              :type="showPwd ? 'text' : 'password'"
              border="none"
              fontSize="14px"
              color="8a8a8a"
              :customStyle="inputStyle"
              maxlength="20"
              placeholder="请输入密码"
              prefixIcon="../../static/icon/pwd_icon.png"
              prefixIconStyle="font-size: 44rpx;color: #909399"
              v-model="formInfo.pwd"
            >
              <template v-slot:suffix>
                <uv-icon
                  v-if="showPwd"
                  name="../../static/icon/<EMAIL>"
                  size="18"
                  color="#909399"
                  @click="togglePwd(false)"
                ></uv-icon>
                <uv-icon
                  v-else
                  name="../../static/icon/pwd_notlook_icon.png"
                  size="18"
                  color="#909399"
                  @click="togglePwd(true)"
                ></uv-icon>
              </template>
            </uv-input>
          </uv-form-item>
          <view class="forget_pwd_box">
            <view class="forget_pwd" @click="toResetPwd">忘记密码？</view>
          </view>
        </template>
        <uv-button
          type="primary"
          text="登 录"
          :loading="btnLoading"
          :customStyle="submitBtnStyle"
          :customTextStyle="submitTextStyle"
          @click="onLogin"
        ></uv-button>
      </uv-form>
      <view class="bottom_notice"
        >登录即代表您已阅读并同意 《用户协议》与《隐私政策》</view
      >
      <uv-toast ref="toast"></uv-toast>
    </view>
  </view>
</template>
<script setup>
import { getSysteminfo, getRsaCode } from "@/utils";
import { computed, onMounted, reactive, ref } from "vue";
import { onLoad, onReady } from "@dcloudio/uni-app";
import { UserService } from "@/service";
import { useUserStore } from "@/store/user";
const userInfo = useUserStore();
import { useSystemStore } from "@/store/system";
const systemInfo = useSystemStore();

// 获取手机系统栏高度
const systemBarHeight = `${Number(getSysteminfo().systemBarHeight) + 66}px`;
const contentTop = `${Number(getSysteminfo().systemBarHeight) + 66 + 144}px`;

let toast = ref(null);
onLoad(async (options) => {
  console.log(
    "登录页onLoad进入首页必备条件！！！！！",
    userInfo.token,
    systemInfo.systemCode
  );
  if (userInfo.token && systemInfo.systemCode?.length) {
    console.log("000");
    uni.navigateTo({
      url: "/pages/platform/index",
    });
  }
//   if (!userInfo?.token && !systemInfo?.systemCode?.length) {
//     console.log("111");
//     uni.showToast({
//       icon: "none",
//       title: "token和sysCode都失效，请重新登录~",
//     });
//     return
//   }
//   if (!userInfo?.token) {
//     console.log("111");
//     uni.showToast({
//       icon: "none",
//       title: "token已失效，请重新登录~",
//     });
//   }
//   if (!systemInfo?.systemCode?.length) {
//     console.log("222");
//     uni.showToast({
//       icon: "none",
//       title: "sysCode已失效，请重新登录~",
//     });
//   }
});

// 登录方式选择
let formRef = ref(null);
let currentLogin = ref(1);
const changeMethod = (index) => {
  currentLogin.value = index;
};
const activeBarStyle = computed(() => {
  let widthPercentage = currentLogin.value === 1 ? 160 : 538;
  return {
    left: `${widthPercentage}rpx`,
  };
});
// 登录信息
const inputStyle = reactive({
  width: "670rpx",
  height: "100rpx",
  background: "#F4F8FF",
  boxSizing: "border-box",
  padding: "28rpx 32rpx",
  borderRadius: "16rpx 16rpx 16rpx 16rpx",
});
const submitBtnStyle = reactive({
  marginTop: "144rpx",
  height: "100rpx",
});
const submitTextStyle = reactive({
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 500,
  fontSize: "40rpx",
  color: "#FFFFFF",
  lineHeight: "47rpx",
});

let formInfo = reactive({
  phone: "",
  code: "",
  account: "",
  pwd: "",
});
const rules = reactive({
  phone: {
    type: "string",
    required: true,
    len: 11,
    message: "请填写11位手机号",
    trigger: ["blur", "change"],
  },
  code: {
    type: "string",
    required: true,
    len: 6,
    message: "请填写6位验证码",
    trigger: ["blur", "change"],
  },
  account: {
    type: "string",
    required: true,
    min: 3,
    max: 20,
    message: "请填写3-20位登录账户",
    trigger: ["blur", "change"],
  },
  pwd: {
    type: "string",
    required: true,
    min: 8,
    max: 20,
    message: "请填写8-20位密码",
    trigger: ["blur", "change"],
  },
});
let tips = ref("");
const codeChange = (text) => {
  tips.value = text;
};
// 获取验证码
const uCode = ref(null);
const getCode = async () => {
  if (!formInfo.phone) {
    uni.showToast({
      title: "请填写手机号~",
      duration: 2000,
      icon: "none",
    });
    return;
  }
  if (uCode.value.canGetCode) {
    // uni.showLoading({
    //   title: "正在获取验证码",
    // });
    const { code, data } = await UserService.sendLoginCode(formInfo.phone);
    console.log("验证码，", data);
    // uni.hideLoading();
    // 通知验证码组件内部开始倒计时
    uCode.value.start();
  }
  //  else {
  //   uni.showToast({
  //     title: "倒计时结束后再发送",
  //     duration: 2000,
  //     icon: "none",
  //   });
  // }
};

// 切换密码可见/不可见
let showPwd = ref(false);
const togglePwd = (falg) => {
  showPwd.value = falg;
};

// 点击登录按钮
let btnLoading = ref(false);
const onLogin = () => {
  formRef.value
    .validate()
    .then((res) => {
      btnLoading.value = true;
      if (currentLogin.value === 1) {
        // 手机验证码登录
        phoneNumCodeLogin();
      } else {
        // 密码登录
        pwdLogin();
      }
    })
    .catch((errors) => {
      console.log("error validate!!!");
    });
};
// 手机验证码登录
const phoneNumCodeLogin = async () => {
  try {
    const { code, data } = await UserService.verifyCode(
      formInfo.phone,
      formInfo.code
    );
    // 0.5.0版本首次登录选租户
    toLogin(data);
  } catch (error) {
    btnLoading.value = false;
  }
};
// 密码登录
const pwdLogin = async () => {
  try {
    // 1获取加密key
    const { code: publicKeyCode, data: publicKey } =
      await UserService.getAuthPublicKey();
    const rsaPwd = getRsaCode(formInfo.pwd, publicKey);
    // 2登录login
    const { code: loginCode, data: loginData } = await UserService.login({
      username: formInfo.account,
      password: rsaPwd,
    });
    // 0.5.0版本首次登录选租户
    toLogin(loginData);
  } catch (error) {
    btnLoading.value = false;
  }
};
/**
 * @description: 账号、手机号登录
 * @param {*} data 登录后获取的信息
 * @return {*}
 */
const toLogin = async (data) => {
  console.log("处理登录逻辑!!!!!!", data);
  let rightCodes = ["4", "5", "7"];
  let isNoRight = rightCodes.every(
    (val) => !data.user.userAuthority.systemCodes.includes(val)
  );
  if (isNoRight) {
    btnLoading.value = false;
    toast.value.show({
      type: "warning",
      message: `暂无系统权限，请联系管理员`,
    });
    return false;
  }
  userInfo.updateToken(data.access_token);
  // 先判断该账号有无多个租户
  // 0租户----toast提示;
  // 1个租户-----自动选取租户进入系统
  // 多个租户,再判断该账号有无登录历史
  const {
    code,
    msg,
    data: tenantList = [],
  } = await UserService.getUserTenantList();
  if (code == 200) {
    if (Array.isArray(tenantList) && tenantList.length == 1) {
      console.log("只有一个租户11111111111", tenantList);
      successLogin(data, tenantList[0]);
    } else if (Array.isArray(tenantList) && tenantList.length > 1) {
      console.log("有多个租户nnnnnnnnnnnn", tenantList);
      // 1.判断该账号有登陆历史，自动选取最后一次切换的租户进入系统，
      // 2.有登陆历史的话,进一步判断最后一次切换租户id是否在租户列表中
      let curTenant = tenantList.find(
        (item) => item.tenantId == data.user.tenantId
      );
      if (data?.user?.tenantId && curTenant) {
        console.log("有登陆历史,并且在租户列表中");
        successLogin(data, curTenant);
      } else {
        console.log("有登陆历史,没在租户列表中,或者无登录历史");
        // 有登录历史,但是最后一次切换的租户id不在现有租户列表中
        // 或者 该账号无登陆历史，去选择租户
        userInfo.updateToken(data.access_token);
        btnLoading.value = false;
        firstLogin(data.user.userTenantList);
      }
    } else {
      btnLoading.value = false;
      toast.value.show({
        type: "warning",
        message: `暂无可用组织，请联系系统管理员`,
      });
    }
  }
};

// 更新token,进入系统
const successLogin = async (data, curTenant) => {
  try {
    const { code, data: authChangeData } = await UserService.authChange(
      curTenant.tenantId
    );
    console.log("进入成功登录逻辑啦!!!!", data, authChangeData, curTenant);
    if (code == 200) {
      // userInfo.updateToken(data.access_token);
      userInfo.updateToken(authChangeData.access_token);
      const postName = curTenant.postName || null;
      const { id, userFace, userFullName, tenantId, tenantName } =
        authChangeData.user;
      // const { userFace, userFullName } = data.user;
      userInfo.updateUser({
        id,
        userFace,
        userFullName,
        postName,
        tenantId,
        tenantName,
      });
      // 获取资源列表
      await getResourceList(data.user.userAuthority.systemCodes.join(","));
      btnLoading.value = false;
      let loginSysCode = data.user.userAuthority.systemCodes;
      systemInfo.updateSystemCode(loginSysCode);
      console.log("登陆后查看code", systemInfo.systemCode);
      uni.navigateTo({
        url: `/pages/platform/index`,
      });
    }
  } catch (error) {
    console.log("不正常!!!", error);
  }
};

// 首次选择租户进入的逻辑
const firstLogin = (userTenantList) => {
  if (userTenantList.length) {
    uni.navigateTo({
      url: `/pages/home/<USER>
    });
  }
};

const getResourceList = async (systemCode) => {
  try {
    let { code, data } = await UserService.authResource(systemCode);
    console.log("登录权限资源", code, data);
    if (code == 200) {
      uni.setStorageSync("permissions", data);
    }
  } catch (error) {
    console.log("获取权限失败");
  }
};

// 忘记密码
const toResetPwd = () => {
  console.log("忘记密码");
  uni.navigateTo({
    url: "/pages/login/editPwd/checkPhone",
  });
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding-top: v-bind(systemBarHeight);
  background-image: url("../../static/image/login_bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.hello_box {
  display: flex;
  justify-content: space-between;
  // align-items: baseline;
  padding-left: 42rpx;
  .left_text {
    // margin-right: 34rpx;
    .main_text {
      font-family: HelloFont WenYiHei, HelloFont WenYiHei;
      font-weight: 400;
      font-size: 64rpx;
      color: #3871ff;
      line-height: 75rpx;
    }
    .info_text {
      font-family: Source Han Sans SC, Source Han Sans SC;
      font-weight: 500;
      font-size: 36rpx;
      color: #5e8cff;
      line-height: 42rpx;
    }
  }
  .right_img {
    width: 442rpx;
    height: 302rpx;
  }
}
.content {
  width: 100%;
  // min-height: calc(100vh - 484rpx);
  min-height: calc(100vh - v-bind(contentTop));
  // position: fixed;
  // bottom: 0;
  background: #fff;
  border-radius: 64rpx 64rpx 0rpx 0rpx;
  .method_picker {
    position: relative;
    width: 100%;
    .picker_box {
      display: flex;
      .item {
        width: 50%;
        height: 108rpx;
        line-height: 108rpx;
        text-align: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: #a09f9f;
        background: #f2f2f2;
      }
      .default1 {
        border-radius: 64rpx 0rpx 64rpx 0rpx;
      }
      .default2 {
        border-radius: 0rpx 64rpx 0rpx 64rpx;
      }
      .active {
        color: #4378ff;
        background: transparent;
      }
    }
    .active_bar {
      position: absolute;
      top: 84rpx;
      left: 160rpx;
      width: 48rpx;
      height: 8rpx;
      background: #4378ff;
      border-radius: 8rpx;
      transition: left 1 ease-in-out;
      // animation: activeBar 500 ease;
    }
    // @keyframes activeBar {

    // }
  }
  :deep(.uv-form) {
    position: relative;
    margin-top: 80rpx;
    padding: 0 40rpx;
    .code_btn {
      font-family: PingFang SC-Medium;
      font-size: 28rpx;
      color: #4378ff;
    }
    .forget_pwd_box {
      display: flex;
      justify-content: flex-end;
      .forget_pwd {
        height: 34rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #4378ff;
        line-height: 28rpx;
      }
    }
  }
  .bottom_notice {
    position: absolute;
    left: 50%;
    width: 608rpx;
    margin-left: -304rpx;
    bottom: 48rpx;
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #a09f9f;
    line-height: 28rpx;
  }
}
</style>
