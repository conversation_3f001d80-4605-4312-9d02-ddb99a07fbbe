<template>
  <view class="rejected-page">
    <!-- 选择区：全选+已选数量 -->
    <view class="select-wrap">
      <view class="selected-count">
        <text>已选中 </text>
        <text class="count">{{ selectedCount }}</text>
        <text> 个工单</text>
      </view>
      <view class="select-all" @click="toggleSelectAll">
        <view class="checkbox" :class="{ checked: isAllSelected }">
          <uv-icon
            v-if="isAllSelected"
            name="checkmark"
            color="#FFFFFF"
            size="16"
          ></uv-icon>
        </view>
        <text class="select-all-text">全选</text>
      </view>
    </view>

    <!-- 工单列表区 -->

    <view class="order-list">
      <view
        v-for="(item, index) in dataList"
        :key="index"
        class="order-item"
        :class="{ selected: item.selected, disabled: !item.selected }"
        @click="toggleOrderSelection(index)"
      >
        <view
          class="checkbox"
          :class="{ checked: item.selected, disabled: !item.selected }"
        >
          <uv-icon
            v-if="item.selected"
            name="checkmark"
            color="#FFFFFF"
            size="16"
          ></uv-icon>
        </view>
        <view class="order-content">
          <view class="order-header">
            <text class="order-type">{{ item.diseasesTypeName }}</text>
            <text class="order-time">{{ item.createTime }}</text>
          </view>
          <view class="order-footer">
            <view class="order-address">{{ item.eventObjectName }}</view>
            <text class="detail-link">详情</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 驳回原因区块 -->
    <view class="reject-reason-section">
      <view class="reason-header">
        <text class="reason-title">驳回原因</text>
        <image class="required-icon" src="/static/icon/required_icon.png" />
      </view>
      <uv-textarea
        v-model="rejectReason"
        :maxlength="150"
        height="120px"
        placeholder="请输入驳回原因（最多150字）"
        count
        class="reason-input"
      />
    </view>

    <!-- 底部按钮区块 -->
    <view class="bottom-buttons">
      <uv-button
        class="cancel-btn"
        :custom-style="cancelBtnStyle"
        @click="cancel"
        >取 消</uv-button
      >
      <uv-button
        class="confirm-btn"
        :custom-style="confirmBtnStyle"
        :disabled="!canConfirm"
        @click="confirmReject"
        >确认驳回</uv-button
      >
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { RoutineMaintenanceService } from "@/service";
import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";

// store
const workOrderInfo = useRepairWorkOrderStore();

// 业务类型
let rejectType = "";
// 业务对应MAP
const rejectApiMap = {
  repairWorkOrder: {
    getData: (...args) => RoutineMaintenanceService.getChildOrder(...args),
    submit: (...args) => RoutineMaintenanceService.workOrderReject(...args),
  },
};

const dataList = ref([]);
const getDataList = async () => {
  const api = rejectApiMap[rejectType]?.getData;
  const { data } = await api(workOrderInfo?.id);
  dataList.value = data;
};

const rejectReason = ref("");
const selectedCount = computed(
  () => dataList.value.filter((item) => item.selected).length
);
const isAllSelected = computed(() =>
  dataList.value.every((item) => item.selected)
);
const canConfirm = computed(
  () => selectedCount.value > 0 && rejectReason.value.trim().length > 0
);

const cancelBtnStyle = {
  height: "96rpx",
  lineHeight: "96rpx",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#DDDDDD",
  color: "#62697B",
  fontSize: "40rpx",
};

const confirmBtnStyle = {
  height: "96rpx",
  lineHeight: "96rpx",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378ff",
  color: "#ffffff",
  fontSize: "40rpx",
};

const toggleSelectAll = () => {
  const shouldSelectAll = !isAllSelected.value;
  dataList.value.forEach((item) => {
    item.selected = shouldSelectAll;
  });
};

const toggleOrderSelection = (index) => {
  dataList.value[index].selected = !dataList.value[index].selected;
};

const cancel = () => {
  uni.navigateBack && uni.navigateBack();
};

const confirmReject = async () => {
  if (!canConfirm.value) {
    uni.showToast &&
      uni.showToast({
        title: "请选择工单并填写驳回原因",
        icon: "none",
      });
    return;
  }
  const api = rejectApiMap[rejectType]?.submit;
  await api({
    reason: rejectReason.value,
    workId: workOrderInfo?.id,
    rejectChildIds: dataList.value
      ?.filter((item) => item.selected)
      ?.map((select) => select.id),
  });
  uni.showToast &&
    uni.showToast({
      title: "驳回成功",
      icon: "success",
    });
  // 使用事件总线通知上一页刷新
  uni.$emit("refreshWorkOrderBaseInfo");
  // 延迟返回上一页，让用户看到提示
  setTimeout(() => {
    uni.navigateBack();
  }, 2000);
};

// 页面加载
onLoad(async (options) => {
  uni.setNavigationBarTitle &&
    uni.setNavigationBarTitle({
      title: "驳回工单确认",
    });

  // 获取类型
  if (options?.type) {
    rejectType = options.type;
  }
  getDataList();
});
</script>

<style lang="scss" scoped>
.rejected-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.select-wrap {
  background-color: #ffffff;
  padding: 0 24rpx;
  .selected-count {
    padding: 24rpx 0;
    font-size: 28rpx;
    color: #666666;

    .count {
      color: #4378ff;
      font-weight: bold;
    }
  }

  .select-all {
    display: flex;
    align-items: center;
    padding-bottom: 24rpx;

    .checkbox {
      width: 40rpx;
      height: 40rpx;
      border: 2rpx solid #cccccc;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      &.checked {
        background-color: #4378ff;
        border-color: #4378ff;
      }
    }

    .select-all-text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}

.order-list {
  flex: 1;
  padding: 0 24rpx;
  margin-top: 24rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: flex-start;
  border: 2rpx solid transparent;
  position: relative;

  &.selected {
    border-color: #4378ff;
  }

  &.disabled {
    border-color: #d9d9d9;
  }

  .checkbox {
    width: 40rpx;
    height: 40rpx;
    border: 2rpx solid #cccccc;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -1px;
    left: -1px;

    &.checked {
      background-color: #4378ff;
      border-color: #4378ff;
    }

    &.disabled {
      background-color: #bdbdbd;
      border-color: #bdbdbd;
    }
  }

  .order-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    position: relative;
    gap: 16rpx;
    margin-left: 32rpx;
    .order-header,
    .order-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .order-type {
      font-size: 28rpx;
      font-weight: bold;
      color: #404040;
    }
    .order-time {
      font-size: 28rpx;
      color: #636363;
    }
    .order-address {
      font-size: 24rpx;
      color: #a09f9f;
      margin-bottom: 0;
    }
    .detail-link {
      flex-shrink: 0;
      font-size: 28rpx;
      color: #4378ff;
      align-self: unset;
      margin-top: 0;
    }
  }
}

.reject-reason-section {
  padding: 0 24rpx;
  margin-top: 48rpx;

  .reason-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .reason-title {
      position: relative;
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #404040;
      margin-right: 8rpx;

      &:before {
        content: "";
        display: inline-block;
        width: 6rpx;
        height: 28rpx;
        background: #4378ff;
        border-radius: 4rpx;
        margin-right: 8rpx;
        margin-top: 4rpx;
      }
    }

    .required-icon {
      width: 84rpx;
      height: 40rpx;
    }
  }

  .reason-input {
    width: 100%;
    background-color: #ffffff;
    border-radius: 16rpx;
    font-size: 28rpx;
    color: #333333;
    border: 2rpx solid #eeeeee;
    box-sizing: border-box;

    &::placeholder {
      color: #cccccc;
    }
  }
}

.bottom-buttons {
  flex-shrink: 0;
  display: flex;
  padding: 24rpx;
  gap: 24rpx;
  .cancel-btn {
    flex: 1;
  }
  .confirm-btn {
    flex: 2;
  }
}
</style>
