<template>
  <view class="container">
    <uv-search
      height="36"
      shape="square"
      searchIcon="../../../static/icon/search_icon.png"
      searchIconSize="16"
      placeholder="输入项目名称搜索"
      placeholderColor="#C1C1C1;"
      bgColor="#fff"
      :showAction="false"
      :boxStyle="searchStyle"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <!-- 列表 -->
    <template v-if="!pageLoading">
      <view class="list_box" v-if="filterList.length">
        <view
          :class="['card', curItem.id === item.id ? 'active_card' : '']"
          v-for="item in filterList"
          :key="item.id"
          @click="chooseItem(item)"
        >
          <view class="item_title">{{ item.projectName }}</view>
        </view>
      </view>
      <no-data class="nodata" v-else></no-data>
    </template>
    <uv-loading-page :loading="pageLoading" loading-text="加载中..." font-size="24rpx"></uv-loading-page>
    <view class="bottom_box">
      <view class="choosed_data_show" v-if="curItem.projectName"
        >已选 {{ curItem.projectName }}</view
      >
      <view class="btn" @click="confirm">确 定</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { ProjectService } from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

onLoad(async (options) => {
  // 获取项目列表
  await getList();
  // 当前已选项目回显
  handelShowCurItem(options?.curProjectId);
});

// 查询项目列表
let dataList = ref([]);
let filterList = ref([]);
let pageLoading = ref(false);
const getList = async () => {
  pageLoading.value = true;
  const res = await ProjectService.getProjectList();
  console.log("请求项目", res);
  dataList.value = res.data;
  filterList.value = res.data;
  pageLoading.value = false;
};

// 回显当前已选择的项目，并将其置顶
const handelShowCurItem = (curProjectId) => {
  // filterList.value.forEach((item) => {
  //   if (item.projectId == curProjectId) {
  //     curItem.value = item;
  //   }
  // });

  let curItemIndex = ref(-1);
  filterList.value.forEach((item, index) => {
    if (item.projectId == curProjectId) {
      curItem.value = item;
      curItemIndex.value = index;
    }
  });
  if (curItemIndex.value !== -1) {
    let [it] = filterList.value.splice(curItemIndex.value, 1);
    filterList.value.unshift(it);
  }

};

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  marginBottom: "28rpx",
});

let inpVal = ref("");
const inpChange = (val) => {
  inpVal.value = val;
  handleFilterProject();
};
const inpSearch = (val) => {
  inpVal.value = val;
  handleFilterProject();
};

const handleFilterProject = () =>{
  filterList.value = dataList.value.filter(item=>item.projectName.indexOf(inpVal.value)>-1)
}

// 选择项目
let curItem = ref({});
const chooseItem = (item) => {
  curItem.value = item;
};

// 提交
const confirm = () => {
  projectInfo.updateProject({
    projectId: curItem.value.projectId,
    projectName: curItem.value.projectName,
    projectPermission: curItem.value.projectPermission,
  });
  uni.navigateBack({
    data: 1,
  });
};
</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 20rpx 40rpx;
  background: #f4f8ff;
  box-sizing: border-box;
}
.list_box {
  margin: 12rpx 0 200rpx 0;
  // height: calc(100% - 302rpx);
  .card {
    margin-bottom: 28rpx;
    padding: 22rpx 28rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 16rpx;
    font-family: PingFang SC, PingFang SC;
    .item_title {
      font-weight: 400;
      color: #373737;
      font-size: 32rpx;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .active_card {
    background: #4378ff;
    .item_title {
      color: #ffffff;
    }
  }
}
.nodata {
  margin-top: 136rpx;
}
.bottom_box {
  width: calc(100% - 80rpx);
  position: fixed;
  bottom: 0rpx;
  padding-bottom: 20rpx;
  background: #f4f8ff;
  .choosed_data_show {
    padding-top: 20rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .btn {
    width: 670rpx;
    height: 84rpx;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
}
</style>
