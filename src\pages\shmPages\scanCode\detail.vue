<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-02 16:43:57
 * @Description: 
-->
<template>
  <view class="container">
    <view class="subsection_box">
      <uv-subsection
        :list="tabList"
        :current="currentTab"
        custom-style="height: 64rpx;border-radius: 32rpx;"
        custom-item-style="border-radius: 32rpx;"
        bgColor="#fff"
        activeColor="#fff"
        inactiveColor="#9C9C9C"
        @change="onTabChange"
      ></uv-subsection>
    </view>
    <!-- 数据详情 -->
    <view class="data_detail_box" v-show="currentTab == 0">
      <!-- 测点选择 -->
      <view class="point_picker flex-start-center" @click="toChoosePoint">
        <view class="cur_point">{{ curChoosedPoint?.pointCode || "-" }}</view>
        <image
          class="picker_arrow_icon"
          src="/static/shmStatic/icon/picker_arrow_down_20250318.png"
        ></image>
      </view>
      <!-- 实时数据折线图 -->
      <view class="echarts_box">
        <view class="echart_title same-add-title">实时数据</view>
        <LineEchart
          :assetId="assetId"
          :pointId="curChoosedPoint?.id || ''"
          :curPointMonitorUnit="curPointMonitorUnit"
        />
      </view>
      <!-- 测点基础信息 -->
      <PointBaseInfo :detailData="pointDetailData" :showDeviceInfo="false" />
    </view>
    <!-- 设备详情 -->
    <view class="device_detail_box" v-show="currentTab == 1">
      <view class="info_title same-add-title">设备信息</view>
      <view class="info_content">
        <view class="info_item flex-between-center">
          <view class="info_item_left">设备名称</view>
          <view class="info_item_right">{{
            deviceDetailData.deviceName || "-"
          }}</view>
        </view>
        <view class="info_item flex-between-center">
          <view class="info_item_left">设备状态</view>
          <image
            v-if="deviceDetailData.status == '1'"
            class="info_item_icon"
            src="/static/shmStatic/icon/device_online_icon_20250318.png"
          ></image>
          <image
            v-else
            class="info_item_icon"
            src="/static/shmStatic/icon/device_outline_icon_20250326.png"
          ></image>
        </view>
        <view class="info_item flex-between-center">
          <view class="info_item_left">所属资产</view>
          <view class="info_item_right">{{
            deviceDetailData.assetLabel || "-"
          }}</view>
        </view>
        <view class="info_item flex-between-center">
          <view class="info_item_left">设备类型</view>
          <view class="info_item_right">{{
            deviceDetailData.deviceTypeLabel || "-"
          }}</view>
        </view>
        <view class="info_item flex-between-center">
          <view class="info_item_left">安装位置</view>
          <view class="info_item_right">{{
            deviceDetailData.installLocation || "-"
          }}</view>
        </view>
        <view class="info_item flex-between-center">
          <view class="info_item_left">安装日期</view>
          <view class="info_item_right">{{
            deviceDetailData.installDate || "-"
          }}</view>
        </view>

        <view class="info_item flex-between-center">
          <view class="info_item_left">设备照片</view>
          <view class="info_item_imgs" v-if="deviceDetailData.picture">
            <image
              class="img"
              :src="showImg(deviceDetailData.picture)"
              @click="previewImg(0, [showImg(deviceDetailData.picture)])"
            ></image>
          </view>
          <view v-else class="info_item_right">-</view>
        </view>
      </view>
    </view>
    <!-- 测点选择弹窗 -->
    <ylg-dicts-picker
      ref="pointPickerRef"
      :curChoosedKey="curChoosedPointKey"
      title="测点选择"
      :options="pointList"
      @onSelect="onSelectPoint"
    ></ylg-dicts-picker>
  </view>
</template>

<script setup>
import { onBackPress, onLoad } from "@dcloudio/uni-app";
import { ref, reactive } from "vue";
import { ShmService } from "@/service";
import { showImg } from "@/utils";
import LineEchart from "../components/LineEchart.vue";
import PointBaseInfo from "../components/PointBaseInfo.vue";

let assetId = ref("");
let deviceId = ref("");
let pageFrom = ref("");
onLoad((options) => {
  assetId.value = options.assetId;
  deviceId.value = options.deviceId;
  pageFrom.value = options.pageFrom;
  getPointList();
  getDeviceDetail();
});

// 返回上一页
onBackPress((backOptions) => {
  if (backOptions.from === "backbutton") {
    if(pageFrom.value == 'scan'){
      return false;
    }else if(pageFrom.value == 'createForm'){
      uni.navigateBack({
        delta: 2
      })
    }
    return true;
  } else if (backOptions.from === "navigateBack") {
    return false;
  }
});

// 设备详情
let deviceDetailData = ref({});
const getDeviceDetail = async () => {
  try {
    let { code, data } = await ShmService.getDeviceInfoDetail(deviceId.value);
    if (code == 200) {
      deviceDetailData.value = data;
    } else {
      deviceDetailData.value = {};
    }
  } catch (error) {
    deviceDetailData.value = {};
  }
};

// 顶部tab相关
const tabList = ref(["数据详情", "设备详情"]);
let currentTab = ref(0);
const onTabChange = (ind) => {
  currentTab.value = ind;
};

// 测点选择相关
let pointPickerRef = ref(null);
let curChoosedPoint = ref({});
let curPointMonitorUnit = ref("");
const pointList = ref([]);
const getPointList = async () => {
  console.log("查询测点参数", deviceId.value);
  try {
    // let params = {
    //   assetId: assetId.value,
    //   fieldTypes: "int,double,long,float",
    // };
    let { code, data } = await ShmService.getPointListByDeviceId(
      deviceId.value
    );
    // 处理测点数据
    if (code == 200) {
      pointList.value = data.map((point) => {
        point.dictValue = point.pointCode;
        point.dictKey = point.id;
        return point;
      });
      curChoosedPoint.value = pointList.value[0];
      curPointMonitorUnit.value = pointList.value[0]?.monitorItemUnit || "-";
      curChoosedPointKey.value = pointList.value[0]
        ? `${pointList.value[0]?.dictValue}${pointList.value[0]?.dictKey}`
        : "";
      // 获取测点详情
      getPointDetail();
    }
  } catch (error) {
    console.log("请求测点 error", error);
  }
};
const toChoosePoint = () => {
  pointPickerRef.value.open();
};
let curChoosedPointKey = ref("");
const onSelectPoint = (e) => {
  curChoosedPoint.value = e;
  curChoosedPointKey.value = `${e.dictValue}${e.dictKey}`;
  curPointMonitorUnit.value = e.monitorItemUnit || "";
  pointPickerRef.value.close();
  getPointDetail();
};

// 测点基础信息
const pointDetailData = ref({});
const getPointDetail = async () => {
  try {
    console.log("当前选中项id", curChoosedPoint.value.id, typeof curChoosedPoint.value.id);

    let { code, data } = await ShmService.getMonitoringPointDetail(
      curChoosedPoint.value.id
    );
    if (code == 200 && data) {
      data.deviceDesc = data.deviceName
        ? `${data.deviceName}(${data.iotId})`
        : "";
      sectionImgUrls = [];
      data.assetContentDTO.sectionalInfoList?.forEach((item) => {
        sectionImgUrls.push(showImg(item.drawSectionalPath));
      });
      pointDetailData.value = data;
    } else {
      pointDetailData.value = data;
    }
  } catch (error) {
    pointDetailData.value = {};
    console.log("测点详情 error", error);
  }
};

let sectionImgUrls = reactive([]);
const previewImg = (cur, urls) => {
  uni.previewImage({
    urls: urls, // 需要预览的图片HTTP链接列表
    current: cur, // 当前显示图片的链接索引
  });
};
</script>
<style lang="scss" scoped>
.container {
  background: rgba(244, 248, 255, 1);
  min-height: 100vh;
  padding: 28rpx 0;
  box-sizing: border-box;
}
.subsection_box {
  width: 670rpx;
  margin: 0 auto;
  :deep(.uv-subsection--button__bar) {
    background: rgba(67, 120, 255, 1);
  }
}
.data_detail_box {
  margin-top: 28rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
  .point_picker {
    // background: rgba($color: #fff, $alpha: 1);
    .cur_point {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #373737;
      line-height: 44rpx;
    }
    .picker_arrow_icon {
      display: block;
      width: 28rpx;
      height: 28rpx;
      margin-left: 16rpx;
    }
  }
  .echarts_box {
    margin-top: 28rpx;
    padding: 28rpx 46rpx;
    box-sizing: border-box;
    width: 100%;
    height: 428rpx;
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 0 16rpx 0rpx rgba(0, 0, 0, 0.1);
    .echart_title {
      padding: "";
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #404040;
      line-height: 40rpx;
    }
  }
}
.device_detail_box {
  background: #fff;
  margin-top: 40rpx;
  .info_title {
    padding: 28rpx 40rpx 20rpx 40rpx;
    margin-left: 40rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 28rpx;
    color: #404040;
    line-height: 40rpx;
  }
  .info_content {
    .info_item {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      line-height: 40rpx;
      padding: 24rpx 40rpx;
      border-bottom: 2rpx solid #f0f0f0;
      &_left {
        width: 150rpx;
      }
      &_right {
        width: calc(100% - 190rpx);
      }
      &_icon {
        width: 100rpx;
        height: 48rpx;
        position: absolute;
        left: 230rpx;
      }
      &_imgs {
        white-space: nowrap;
        width: calc(100% - 190rpx);
        overflow-x: scroll;
        .img {
          display: inline-block;
          width: 200rpx;
          height: 200rpx;
          margin-right: 20rpx;
        }
      }
    }
    .info_item:last-child {
      border-bottom: none;
    }
  }
}
</style>
