<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-16 16:49:43
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="智慧养护"
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      leftIconColor="#fff"
      :bgColor="bgColor"
      @leftClick="goBack"
    >
    </uv-navbar>
    <!-- 选择项目 -->
    <view class="project_picker" @click="toChooseProject">
      <template v-if="curProjectName">
        <image class="left_icon" :src="project_picker_icon"></image>
        <view class="cur_project">{{ curProjectName }}</view>
        <image
          class="right_icon"
          src="../../static/icon/right_icon_white.png"
        />
      </template>
    </view>
    <!-- 统计数据 -->
    <view class="data_statistics">
      <view
        class="data_item"
        v-for="(item, index) in dataStatistics"
        :key="index"
      >
        <view :class="['data_val', item.redColor ? 'red_val' : '']">{{
          item.val
        }}</view>
        <view class="data_title">{{ item.title }}</view>
      </view>
    </view>
    <!-- 菜单列表 -->
    <view class="menu_list">
      <template v-for="item in menuList" :key="item.id">
        <view class="menu_item" @click="toMenuPage(item)">
          <image class="menu_icon" :src="item.iconPath" mode="widthFix" />
          <view class="menu_title">{{ item.title }}</view>
        </view>
      </template>
    </view>
    <view class="todo_title">
      <view class="todo_title_bar"></view>
      <view class="todo_title_text">我的待办</view>
    </view>
    <SearchTabGroup ref="searchTabGroupRef" @queryList="queryList" />
    <view
      v-if="
        isShowRoadInspectionCard ||
        isShowDailyMaintainCard ||
        isShowDailyRepairCard ||
        isShowEventHandlingCard ||
        isShowDailyRepairCard_5 ||
        isShowDailyRepairCard_6
      "
    >
      <!-- 路况检查 -->
      <RoadInspectionCard
        v-if="isShowRoadInspectionCard"
        :dataList="todoMap.inspectTaskVO"
      />
      <!-- 日常保养 -->
      <DailyMaintainCard
        v-if="isShowDailyMaintainCard"
        :dataList="todoMap.dailyMaintainTaskVO"
      />
      <!-- 日常维修 -->
      <DailyRepairCard
        v-if="isShowDailyRepairCard"
        :dataList="todoMap.workOrderMobileTodayVO"
      />
      <!-- 事件处理 -->
      <EventHandlingCard
        v-if="isShowEventHandlingCard"
        :dataList="todoMap.inspectEventVO"
      />
      <!-- 维修确认 -->
      <DailyRepairCard
        v-if="isShowDailyRepairCard_5"
        :dataList="todoMap.workOrderMobileApprove"
      />
      <!-- 维修验收 -->
      <DailyRepairCard
        v-if="isShowDailyRepairCard_6"
        :dataList="todoMap.workOrderMobileApprove"
      />
    </view>
    <NoData v-else />
    <view class="black"></view>
    <!-- 底部切换按钮 -->
    <ylg-my-btns
      :switchActive="switchActive"
      @myBtnCallback="myBtnCallback"
    ></ylg-my-btns>
  </view>
</template>
<script setup>
import {
  onLoad,
  onShow,
  onPageScroll,
  onPullDownRefresh,
  onBackPress,
} from "@dcloudio/uni-app";
import { ref, computed, reactive } from "vue";
import { DataAcquisitionService, ProjectService, homepageApi } from "@/service";
import { getSysteminfo } from "@/utils";
import { hasAuth } from "@/config/permissions.js";
import project_picker_icon from "@/static/icon/project_picker_icon.png";
import SearchTabGroup from "./components/SearchTabGroup.vue";
import RoadInspectionCard from "./components/RoadInspectionCard.vue";
import DailyMaintainCard from "./components/DailyMaintainCard.vue";
import DailyRepairCard from "./components/DailyRepairCard.vue";
import EventHandlingCard from "./components/EventHandlingCard.vue";
import NoData from "@/components/ylg-nodata.vue";
import { useProjectStore } from "@/store/project";

const projectInfo = useProjectStore();
const queryParams = ref({});
const searchTabGroupRef = ref();
const todoMap = reactive({
  inspectTaskVO: [], // 路况检查
  dailyMaintainTaskVO: [], // 日常保养
  workOrderMobileTodayVO: [], // 日常维修
  inspectEventVO: [], // 事件处置
  workOrderMobileApprove: [], // 日常维修-维修确认、维修验收
});
const isShowRoadInspectionCard = ref(false);
const isShowDailyMaintainCard = ref(false);
const isShowDailyRepairCard = ref(false);
const isShowEventHandlingCard = ref(false);
const isShowDailyRepairCard_5 = ref(false); // 维修确认
const isShowDailyRepairCard_6 = ref(false); // 维修验收

const queryList = (params) => {
  console.log("queryListqueryParams", params);
  queryParams.value = params;
  getTodoList();
};
const getTodoList = async () => {
  try {
    const { code, data } = await homepageApi.homeTodayTodoList({
      ...queryParams.value,
      projectId: projectInfo.projectId,
    });
    console.log(code, data, "code, data");
    // 路况检查list
    todoMap.inspectTaskVO =
      (data.inspectTaskVO && data.inspectTaskVO?.list) || [];
    if (
      (queryParams.value.workType == 1 ||
        (queryParams.value.toDoType == 1 && queryParams.value.workType == 0)) &&
      todoMap.inspectTaskVO.length
    ) {
      isShowRoadInspectionCard.value = true;
    } else {
      isShowRoadInspectionCard.value = false;
    }
    // 日常保养list
    todoMap.dailyMaintainTaskVO =
      (data.dailyMaintainTaskVO && data.dailyMaintainTaskVO?.list) || [];
    if (
      (queryParams.value.workType == 2 ||
        (queryParams.value.toDoType == 1 && queryParams.value.workType == 0)) &&
      todoMap.dailyMaintainTaskVO.length
    ) {
      isShowDailyMaintainCard.value = true;
    } else {
      isShowDailyMaintainCard.value = false;
    }
    // 日常维修list
    todoMap.workOrderMobileTodayVO =
      (data.workOrderMobileTodayVO && data.workOrderMobileTodayVO?.toDo) || [];
    if (
      (queryParams.value.workType == 3 ||
        (queryParams.value.toDoType == 1 && queryParams.value.workType == 0)) &&
      todoMap.workOrderMobileTodayVO.length
    ) {
      isShowDailyRepairCard.value = true;
    } else {
      isShowDailyRepairCard.value = false;
    }
    // 事件处置list
    todoMap.inspectEventVO =
      (data.inspectEventVO && data.inspectEventVO?.list) || [];
    if (
      (queryParams.value.workType == 4 ||
        (queryParams.value.toDoType == 2 && queryParams.value.workType == 0)) &&
      todoMap.inspectEventVO.length
    ) {
      isShowEventHandlingCard.value = true;
    } else {
      isShowEventHandlingCard.value = false;
    }
    // 维修确认
    todoMap.workOrderMobileApprove =
      (data.workOrderMobileApprove && data.workOrderMobileApprove?.toDo) || [];
    if (
      (queryParams.value.workType == 5 ||
        (queryParams.value.toDoType == 2 && queryParams.value.workType == 0)) &&
      todoMap.workOrderMobileApprove.length
    ) {
      isShowDailyRepairCard_5.value = true;
    } else {
      isShowDailyRepairCard_5.value = false;
    }
    // 维修验收
    if (
      (queryParams.value.workType == 6 ||
        (queryParams.value.toDoType == 2 && queryParams.value.workType == 0)) &&
      todoMap.workOrderMobileApprove.length
    ) {
      isShowDailyRepairCard_6.value = true;
    } else {
      isShowDailyRepairCard_6.value = false;
    }
  } catch (error) {
    console.log(error);
  }
};
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;
const bgColor = ref("transparent");
onPageScroll((e) => {
  if (e.scrollTop > 35) {
    bgColor.value = "#4383F8";
  } else {
    bgColor.value = "transparent";
  }
});
const goBack = () => {
  try {
    uni.navigateBack({
      delta: 1,
    });
  } catch (error) {
    console.log("日常养护返回 error", error);
  }
};

onBackPress((backOptions) => {
  console.log("触发home页返回1");
  if (backOptions.from === "backbutton") {
    console.log("触发home页返回2");
    return false;
  } else if (backOptions.from === "navigateBack") {
    console.log("触发home页返回3");
    return false;
  }
});
onLoad(async () => {
  console.log("homei请求数据");
  await getProjects();
});
onShow(() => {
  switchActive.value = false;
  curProjectName.value = projectInfo.projectName || "";
  console.log("onshow curProjectName.value", projectInfo.projectName);
  if (projectInfo.projectId) {
    getStatisticData();
  }
});

onPullDownRefresh(async () => {
  console.log("onPullDownRefresh");
  await getProjects();
  uni.stopPullDownRefresh();
});

const curProjectName = ref("");
const noData = ref(true);
const getProjects = async () => {
  try {
    const { code, data } = await ProjectService.getProjectList();
    console.log("请求项目", data);
    if (code == 200 && data.length > 0) {
      if (!projectInfo.projectId && !projectInfo.projectName) {
        await projectInfo.updateProject({
          projectId: data[0].projectId,
          projectName: data[0].projectName,
          projectPermission: data[0].projectPermission,
        });
      }
      curProjectName.value = projectInfo.projectName || "";
      console.log("请求项目 curProjectName.value", projectInfo.projectId);
      getStatisticData();
      searchTabGroupRef.value?.queryList();
    }
    noData.value = false;
  } catch (error) {
    console.log("catch", error);
    noData.value = false;
  }
};

const toChooseProject = () => {
  uni.navigateTo({
    url: `/pages/home/<USER>
  });
};

const switchActive = ref(false);
const myBtnCallback = (envRes) => {
  switch (envRes.type) {
    case "toggoleSwitch":
      switchActive.value = !switchActive.value;
      break;
    case "toReportEvent":
      uni.navigateTo({
        url: `/pages/roadConditionInspection/eventReporting?pageFrom=index&inspectObject=1`,
      });
      break;
    default:
      break;
  }
};

// 顶部统计数据
const dataStatistics = ref([
  {
    val: 0,
    title: "今日待办",
  },
  {
    val: 0,
    title: "超时待办",
    redColor: true,
  },
  {
    val: 0,
    title: "今日完成",
  },
  {
    val: 0,
    title: "今日上报",
  },
]);
const getStatisticData = async () => {
  const { code, data } = await DataAcquisitionService.getHomeStatisticData({
    projectId: projectInfo.projectId,
  });
  console.log("统计数据", data);
  if (code === 200) {
    dataStatistics.value = [
      {
        val: data.todoNum || 0,
        title: "今日待办",
      },
      {
        val: data.timeoutNum || 0,
        title: "超时待办",
        redColor: true,
      },
      {
        val: data.finishNum || 0,
        title: "今日完成",
      },
      {
        val: data.reportNum || 0,
        title: "今日上报",
      },
    ];
  }
};

const menuList = computed(() => {
  return [
    {
      id: "dataAcquisition",
      url: "/pages/dataAcquisition/index",
      iconPath: "../../static/icon/data_acquisition_icon.png",
      title: "数据采集",
      // 当projectPermission>2时，才展示“数据采集”的入口，1-查看；2-编辑；3-管理
      isPermission:
        Number(projectInfo.projectPermission) >= 2 &&
        hasAuth("dataAcquisition"),
    },
    {
      id: "roadConditionInspection",
      url: "/pages/roadConditionInspection/index",
      iconPath: "../../static/icon/road_survey_icon.png",
      title: "路况检查",
      isPermission: hasAuth("roadConditionInspection"),
    },
    {
      id: "routinePreserve",
      url: "/pages/routinePreserve/index",
      iconPath: "../../static/icon/inspection_task_icon.png",
      title: "日常保养",
      isPermission: hasAuth("routinePreserve"),
    },
    {
      id: "routineMaintenance",
      url: "/pages/routineMaintenance/index",
      iconPath: "../../static/icon/maintenance_tasks_icon.png",
      title: "日常维修",
      isPermission: hasAuth("routinePreserve"),
    },
    {
      id: "eventHandling",
      url: "/pages/eventHandling/index",
      iconPath: "../../static/icon/eventHandling_home_icon.png",
      title: "事件处置",
      isPermission: hasAuth("eventHandling"),
    },
  ];
});
// 菜单页面跳转
const toMenuPage = (item) => {
  console.log("toMenuPage", item);
  if (!item.isPermission) {
    // const msg =
    //   item.id === "dataAcquisition" && Number(projectInfo.projectPermission) < 2
    //     ? "当前项目暂无编辑权限,请切换项目"
    //     : "当前账号无该功能权限,请联系管理员~";
    const msg = "暂无当前功能权限，如有需使用请联系系统管理员";
    uni.showToast({
      title: msg,
      duration: 3000,
      icon: "none",
    });
    return;
  }
  if (item.url) {
    uni.navigateTo({
      url: item.url,
    });
  }
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  padding: 0 28rpx;
  padding-top: v-bind(systemBarHeight);
  background-image: url("../../static/image/home_bg_new.png");
  background-color: #fff;
  background-size: 100% auto;
  background-repeat: no-repeat;
}
.page_title {
  text-align: center;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
  line-height: 42rpx;
}
.project_picker {
  margin-top: 84rpx;
  display: flex;
  align-items: center;
  .left_icon {
    display: block;
    margin-right: 12rpx;
    width: 36rpx;
    height: 36rpx;
  }
  .cur_project {
    font-family:
      PingFang SC,
      PingFang SC;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 40rpx;
  }
  .right_icon {
    display: block;
    margin-left: 16rpx;
    width: 20rpx;
    height: 20rpx;
  }
}
.data_statistics {
  margin-top: 32rpx;
  padding: 32rpx 38rpx;
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-radius: 24rpx;
  .data_item {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #373737;
    line-height: 40rpx;
    text-align: center;
    .data_val {
      font-family:
        Bakbak One,
        Bakbak One;
      font-weight: bold;
      font-size: 48rpx;
      color: #373737;
      line-height: 68rpx;
    }
    .red_val {
      color: #ff2d2d;
    }
    .data_title {
      margin-top: 8rpx;
      color: #909090;
    }
  }
}
.menu_list {
  margin: 40rpx 0 48rpx 0;
  // display: flex;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 36rpx 0;
  height: 336rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 28rpx;
  box-shadow: 0 0 20rpx 0 rgba(146, 153, 180, 0.2);
  .menu_item {
    // margin-right: 36rpx;
    width: 140rpx;
    height: 140rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 16rpx 22rpx 18rpx 22rpx;
    box-sizing: border-box;
    text-align: center;
    .menu_icon {
      width: 80rpx;
      height: 80rpx;
      margin-bottom: 12rpx;
    }
    .menu_title {
      font-family: PingFang SC-Regular;
      font-size: 24rpx;
      font-weight: 500;
      color: #5f6374;
      line-height: 32rpx;
    }
  }
  .menu_item:last-child {
    margin-right: 0;
  }
}
.todo_title {
  display: flex;
  align-items: center;
  gap: 0 12rpx;
  height: 52rpx;
  margin-bottom: 28rpx;
  .todo_title_bar {
    width: 8rpx;
    height: 32rpx;
    background: #3c62fa;
    border-radius: 4rpx 4rpx 4rpx 4rpx;
  }
  .todo_title_text {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #373737;
  }
}
.black {
  height: 200rpx;
}
</style>
