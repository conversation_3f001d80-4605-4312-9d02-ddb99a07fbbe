<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-14 10:14:59
 * @Description: 
-->
<!doctype html>
<html style="">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <title></title>
    <style>
      body,
      html {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
      }
      #mapDiv {
        position: relative;
      }
      img {
        object-fit: contain !important;
      }
    </style>
    <script
      type="text/javascript"
      src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js"
    ></script>
    <script
      src="https://api.tianditu.gov.cn/api?v=4.0&tk=307b00f916df23b531cf212d1e73216b"
      type="text/javascript"
    ></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dom-to-image/2.6.0/dom-to-image.min.js"></script>
    <body style="width: 100%; height: 100%" onLoad="onLoad()">
      <div id="mapDiv" style="width: 100%; height: 100%"></div>
    </body>
    <script>
      const url = location.href;
      const urlParams = new URLSearchParams(url.split("?")[1]);

      let stakeLongitude = urlParams.get("stakeLongitude");
      let stakeLatitude = urlParams.get("stakeLatitude");
      let stakeAddress = urlParams.get("stakeAddress");

      let endStakeLongitude = urlParams.get("endStakeLongitude");
      let endStakeLatitude = urlParams.get("endStakeLatitude");
      let endStakeAddress = urlParams.get("endStakeAddress");

      let activePlan = urlParams.get("activePlan");

      function receiveData(data) {
        switch (data.type) {
          case "changeRoute":
            changeRoute(data.data.routeType);
            break;
          case "getPng":
            getPng();
            break;
          default:
            break;
        }
      }
      let map;
      let zoom = 15;
      let roadLine = null;
      let routeList = [];
      let baseCopyData = [];
      function onLoad() {
        map = new T.Map("mapDiv", "EPSG:4326");
        map.clearOverLays();
        const startPoint = new T.LngLat(stakeLongitude * 1, stakeLatitude * 1);
        const endPoint = new T.LngLat(
          endStakeLongitude * 1,
          endStakeLatitude * 1
        );
        createMaker({
          point: startPoint,
          type: "start",
        });
        createMaker({
          point: endPoint,
          type: "end",
        });
        map.setViewport([startPoint, endPoint]);
        map.isDrag(true);
        map.disableDoubleClickZoom();
        map.setZoom(map.getZoom() - 1);
        const config = {
          policy: 0,
          onSearchComplete: searchResult,
        };
        let drivingRoute = new T.DrivingRoute(map, config);
        for (let i = 0; i < 4; i++) {
          drivingRoute.setPolicy(i);
          drivingRoute.search(startPoint, endPoint);
        }
        document.addEventListener("UniAppJSBridgeReady", () => {
          uni.postMessage({
            data: {
              title: "天地图",
              msg: "我滴任务完成啦",
            },
          });
        });
      }

      function searchResult(result) {
        const routes = result.getNumPlans();
        const tipMap = {
          0: "最少时间",
          1: "最短距离",
          2: "避开高速",
          3: "步行",
        };
        for (let i = 0; i < routes; i++) {
          let plan = result.getPlan(i);
          let obj = {
            time: plan.getDuration(),
            distance: plan.getDistance(),
            policy: result.styles,
            tip: tipMap[result.styles],
            planPath: plan.getPath(),
            type: result.styles,
          };
          routeList.push(obj);
          if (result.styles == activePlan) {
            createRoute(plan.getPath());
            map.setViewport(plan.getPath());
          }
        }
        routeList.sort((a, b) => a.policy - b.policy);
        uni.postMessage({ data: { type: "routeList", routeList } });
      }

      function createRoute(lnglats) {
        roadLine = new T.Polyline(lnglats, {
          color: "#1C89ED",
          weight: 4,
          opacity: 1,
        });
        map.addOverLay(roadLine);
      }

      function changeRoute(routeType) {
        console.log("changeRoute", routeType);
        const filterItem = routeList.find((item) => item.type === routeType);
        if (filterItem) {
          roadLine.onRemove();
          createRoute(filterItem.planPath);
          map.setViewport(filterItem.planPath);
          if (routeType == 1) {
          } else {
            map.setZoom(map.getZoom() - 1);
          }
        }
      }

      function createMaker({ point, type }) {
        const markerObj = {
          icon: new T.Icon({
            iconUrl:
              type === "start"
                ? "../icon/eventHandling_route_plan_start.png"
                : "../icon/eventHandling_route_plan_end.png",
            iconSize: new T.Point(30, 40),
            iconAnchor: new T.Point(15, 30),
          }),
        };
        const pointMarker = new T.Marker(point, markerObj);
        map.addOverLay(pointMarker);
      }

      function getPng() {
        const target = document.getElementById("mapDiv");
        const options = {
          width: 414,
          height: 360,
          style: {
            "background-color": "white",
          },
        };
        domtoimage
          .toPng(target, options)
          .then(function (dataUrl) {
            const img = new Image();
            img.src = dataUrl;
            uni.postMessage({ data: { screenshot: dataUrl } });
          })
          .catch(function (error) {
            console.error("截图失败:", error);
          });
      }

      function base64ToBlob(base64, name) {
        if (typeof base64 != "string") {
          return;
        }
        var arr = base64.split(",");
        var type = arr[0].match(/:(.*?);/)[1];
        var fileExt = type.split("/")[1];
        var bstr = atob(arr[1]);
        var n = bstr.length;
        var u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], `${name}.` + fileExt, {
          type: type,
        });
      }
    </script>
  </head>
</html>
