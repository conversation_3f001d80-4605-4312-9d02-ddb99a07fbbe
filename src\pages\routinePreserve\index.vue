<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-23 14:00:57
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="日常保养"
      leftIconColor="#fff"
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      :autoBack="true"
      bgColor="#065BFF"
    >
      <template v-slot:right>
        <view class="uv-nav-slot" @click="toHistoryPage">历史保养</view>
      </template>
    </uv-navbar>
    <!-- 页面内容 -->
    <view class="content">
      <!-- 顶部日历 -->
      <view class="calendar_box">
        <zjy-calendar
          class="calendar uni-calendar--hook"
          :selected="calendarInfo.selected"
          :showMonth="false"
          :endDate="calendarInfo.endDate"
          :calendarTop="calendarTop"
          @watchWeeks="watchWeeks"
          @change="change"
          @monthSwitch="monthSwitch"
        />
        <!-- @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd" -->
        <view class="touch_bar" @click="toggleCalendar">
          <image
            v-if="!isCalendarExpanded"
            class="down_img"
            src="../../static/icon/touch_down_icon.png"
          ></image>
          <image
            v-else
            class="up_img"
            src="../../static/icon/touch_up_icon.png"
          ></image>
        </view>
      </view>
      <!-- 今日列表 -->
      <template v-if="isToday">
        <!-- 今日待办列表 -->
        <view class="list_box" v-if="todoList.length">
          <view class="title_box">
            <view class="title">
              <text>{{ choosedDate || "今日待办" }}</text>
              <text class="num"
                >（{{ finishedNum }}/{{ todoList.length }}）</text
              >
            </view>
            <view class="more" @click="toHistoryPage">
              <text>更多</text>
              <image
                class="more_icon"
                src="../../static/icon/more_primary_icon.png"
                mode="widthFix"
              />
            </view>
          </view>
          <view class="card_box" v-for="item in todoList" :key="item.id">
            <listItemCard
              :cardItem="item"
              @onCardCallBack="handleCardClick"
            ></listItemCard>
          </view>
        </view>
        <!-- 超时列表 -->
        <view class="list_box mt0" v-if="timeoutList.length">
          <view class="title_box">
            <view class="title">
              <text>超时任务</text>
              <text class="num">（{{ timeoutList.length }}）</text>
            </view>
          </view>
          <view class="card_box" v-for="item in timeoutList" :key="item.id">
            <listItemCard
              :cardItem="item"
              @onCardCallBack="handleCardClick"
            ></listItemCard>
          </view>
        </view>
        <ylg-nodata
          class="nodata"
          v-show="!todoList?.length && !timeoutList?.length && !pageLoading"
        ></ylg-nodata>
      </template>
      <!--过去/未来列表 -->
      <template v-else>
        <view class="list_box" v-if="newOrOldList?.length">
          <view class="title_box">
            <view class="title">
              <text>{{ choosedDate }}</text>
              <text class="">（共计{{ newOrOldList.length }}条）</text>
            </view>
          </view>
          <view class="card_box" v-for="item in newOrOldList" :key="item.id">
            <listItemCard
              :cardItem="item"
              @onCardCallBack="handleCardClick"
            ></listItemCard>
          </view>
        </view>
        <ylg-nodata
          class="nodata"
          v-show="!newOrOldList?.length && !pageLoading"
        ></ylg-nodata>
      </template>
      <uv-loading-icon
        :show="pageLoading"
        text="加载中..."
        textSize="30rpx"
      ></uv-loading-icon>
      <!-- 底部切换按钮 -->
      <ylg-my-btns
        :switchActive="switchActive"
        @myBtnCallback="myBtnCallback"
      ></ylg-my-btns>
    </view>
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import { computed, reactive, ref } from "vue";
import { onPageScroll, onShow, onReady } from "@dcloudio/uni-app";
import zjyCalendar from "@/uni_modules/zjy-calendar/components/zjy-calendar/zjy-calendar.vue";
import listItemCard from "./components/listItemCard.vue";
import { RoutinePreserveService } from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 86
}rpx`;

// 查看历史记录
const toHistoryPage = () => {
  uni.navigateTo({
    url: "/pages/routinePreserve/historyPreserve",
  });
};

// 日历相关
let calendarConentHeight = ref("270rpx"); // 初始高度
let isCalendarExpanded = ref(false);
let startY = ref(null);
let moveY = ref(null);
let choosedDate = ref("");
let isToday = ref(true);
const handleTouchStart = (e) => {
  startY.value = e.touches[0].clientY;
};
const handleTouchMove = (e) => {
  moveY.value = e.touches[0].clientY;
};
const handleTouchEnd = (e) => {
  // 下拉
  if (moveY.value > startY.value) {
    calendarConentHeight.value = "588rpx";
    isCalendarExpanded.value = true;
  } else {
    // 上拉
    calendarConentHeight.value = "270rpx";
    isCalendarExpanded.value = false;
  }
};
let calendarTop = ref("0rpx");
const toggleCalendar = () => {
  isCalendarExpanded.value = !isCalendarExpanded.value;
  calendarConentHeight.value = isCalendarExpanded.value ? "588rpx" : "270rpx";
  // 设置收起日历时露出当前选中日期所在行
  calendarTop.value = isCalendarExpanded.value
    ? "0rpx"
    : `${Number(curItemIndex.value) * -80}rpx`;
};

const getDate = (date, AddDayCount = 0) => {
  if (!date) {
    date = new Date();
  }
  if (typeof date !== "object") {
    date = date.replace(/-/g, "/");
  }
  const dd = new Date(date);
  dd.setDate(dd.getDate() + AddDayCount); // 获取AddDayCount天后的日期
  const y = dd.getFullYear();
  const m =
    dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1; // 获取当前月份的日期，不足10补0
  const d = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate(); // 获取当前几号，不足10补0
  return {
    fullDate: y + "-" + m + "-" + d,
    year: y,
    month: m,
    date: d,
    day: dd.getDay(),
  };
};

let calendarInfo = ref({
  lunar: true,
  range: false,
  insert: false,
  selected: [],
  // startDate: getDate(new Date(), -60).fullDate
  // endDate: getDate(new Date(), 6).fullDate,
});
onShow(() => {
  switchActive.value = false;
  getTodayData();

});
onReady(()=>{
  calendarTop.value = isCalendarExpanded.value
    ? "0rpx"
    : `${Number(curItemIndex.value) * -80}rpx`;
})
const monthSwitch = (e) => {
  console.log("monthSwitch", e);
};

let curItemIndex = ref(0);
const watchWeeks = (e) =>{
  console.log('切换周',e.curItemIndex);
  curItemIndex.value = e.curItemIndex;
}
const change = (e) => {
  console.log("选择日期回调", e);
  // 设置收起日历时露出当前选中日期所在行
  curItemIndex.value = e.curItemIndex;

  choosedDate.value = e.fulldate;
  isToday.value = !!(getDate().fullDate == e.fulldate);
  if (isToday.value) {
    getTodayData();
  } else {
    getNotTodayData(e.fulldate);
  }
};

let pageLoading = ref(false);
let todoList = ref([]);
let finishedNum = ref(0);
let timeoutList = ref([]);
const getTodayData = async () => {
  try {
    pageLoading.value = true;
    let { code, data } = await RoutinePreserveService.getTodayList(
      projectInfo.projectId
    );
    console.log("查询今日列表", code, data);

    if (code == 200) {
      todoList.value = data.todoList || [];
      finishedNum.value = data.todoList.filter(
        (item) => item.taskStatus == "4" || item.taskStatus == "5"
      ).length;
      timeoutList.value = data.timeoutList || [];
    }
    pageLoading.value = false;
  } catch (error) {
    pageLoading.value = false;
    console.log("请求今日列表失败", error);
  }
};

const newOrOldList = ref([]);
const getNotTodayData = async (searchDate) => {
  try {
    let params = {
      date: searchDate,
      projectId: projectInfo.projectId || "1818986789899177986",
    };
    pageLoading.value = true;
    let { code, data } = await RoutinePreserveService.getNewOrOldList(params);
    if (code == 200) {
      newOrOldList.value = data || [];
    }
    pageLoading.value = false;
  } catch (error) {
    console.log("请求过去或未来列表失败", error);
    pageLoading.value = false;
  }
};

const handleCardClick = (envRes) => {
  console.log("卡片回调", envRes);
  uni.navigateTo({
    url: `/pages/routinePreserve/preserve?taskStatus=${envRes.taskStatus}&taskId=${envRes.id}`,
  });
};

let switchActive = ref(false);
const myBtnCallback = (envRes) => {
  switch (envRes.type) {
    case "toggoleSwitch":
      switchActive.value = !switchActive.value;
      break;
    case "toReportEvent":
      uni.navigateTo({
        url: `/pages/roadConditionInspection/eventReporting?pageFrom=index&inspectObject=1`,
      });
      break;
    default:
      break;
  }
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: v-bind(systemBarHeight);
  background-color: #f0f5ff;
}
.content {
  padding-bottom: 200rpx;
}
.uv-nav-slot {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}
.calendar_box {
  position: relative;
  height: v-bind(calendarConentHeight);
  overflow: hidden;
  transition: height 0.8s ease;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(0, 0, 0, 0.08);
  .calendar {
    :deep(.uni-calendar__backtoday) {
      display: none;
    }
    :deep(.uni-calendar__header) {
      height: 84rpx;
      border-bottom: none;
    }
    :deep(.uni-calendar__header-text) {
      font-weight: 600;
      font-size: 36rpx;
      color: #404040;
      line-height: 50rpx;
    }
    // 周描述
    :deep(.uni-calendar__weeks-day) {
      height: 84rpx;
      border-bottom: 2rpx solid #f0f0f0;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #606060;
      line-height: 44rpx;
    }
    // 日期 item
    :deep(.uni-calendar__weeks-item) {
      height: 80rpx;
      width: 107rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    :deep(.uni-calendar-item__weeks-box) {
      flex: none;
      text-align: center;
      font-size: 32rpx;
      line-height: 44rpx;
      color: #404040;
    }
    :deep(.uni-calendar-item--isDay-text:nth-child(2)) {
      display: none;
    }
    :deep(.uni-calendar-item__weeks-box-circle) {
      position: absolute;
      top: 80rpx;
      left: 46rpx;
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
    }
    :deep(.uni-calendar-item--disable) {
      font-size: 32rpx;
      color: #a09f9f;
      line-height: 44rpx;
    }
    :deep(.uni-calendar-item--isDay),
    :deep(.uni-calendar-item--checked) {
      border-radius: 50%;
      width: 60rpx;
      height: 60rpx;
      font-weight: 600;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 60rpx;
      background: #4378ff;
      opacity: 1;
    }
  }
  .touch_bar {
    width: 100%;
    height: 40rpx;
    // background: rgb(182, 140, 140);
    position: absolute;
    bottom: 0rpx;
    .down_img {
      position: absolute;
      bottom: 16rpx;
      left: 50%;
      transform: translateX(-40rpx);
      display: block;
      width: 80rpx;
      height: 8rpx;
    }
    .up_img {
      position: absolute;
      bottom: 6rpx;
      left: 50%;
      transform: translateX(-26rpx);
      display: block;
      width: 52rpx;
      height: 24rpx;
    }
  }
}
.list_box {
  margin-top: 64rpx;
  padding: 0 40rpx 64rpx 40rpx;
  .title_box {
    display: flex;
    justify-content: space-between;
    .title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #373737;
      line-height: 44rpx;
      .num {
        color: #ff3132;
      }
    }
    .more {
      text {
        font-family: PingFang SC-Medium;
        font-size: 28rpx;
        font-weight: 600;
        color: #4378ff;
        line-height: 40rpx;
      }
      .more_icon {
        margin-left: 8rpx;
        width: 20rpx;
        height: 20rpx;
      }
    }
  }
}
.mt0 {
  margin-top: 0;
}
.bottom_btn {
  position: fixed;
  right: 40rpx;
  bottom: 28rpx;
  .mine_switch_btn {
    width: 76rpx;
    height: 76rpx;
    transition: transform 2 ease;
  }
  .rotated {
    transform: rotate(45deg);
  }
  .option_btn_box {
    position: absolute;
    top: -130rpx; /* 根据需要调整 */
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    transition: opacity 0.8s ease, transform 0.8s ease;
    opacity: 0;
    transform: translateY(20px); /* 初始位置在下方 */
  }
  .option_btn_box.show {
    opacity: 1;
    transform: translateY(0); /* 动画效果: 从下方移入 */
  }
  .event_report_btn {
    margin-top: 28rpx;
    width: 76rpx;
    height: 76rpx;
  }
}
.nodata {
  margin-top: 58rpx;
}
.uv-loading-icon {
  margin-top: 168rpx;
}
</style>
