<template>
  <view class="swiper_box">
    <uv-swiper
      :list="props.swiperList"
      keyName="url"
      height="200"
      imgMode="aspectFit"
      @change="(e) => (current1 = e.current)"
      :autoplay="false"
      :showTitle="true"
    >
      <template v-slot:indicator>
        <view class="indicator">
          <view
            class="indicator__dot"
            v-for="(item, index) in props.swiperList"
            :key="index"
            :class="[index === current1 && 'indicator__dot--active']"
          >
          </view>
        </view>
      </template>
    </uv-swiper>
  </view>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  swiperList: {
    type: Array,
    default: () => [],
  },
});
const current1 = ref(0);
</script>

<style lang="scss" scoped>
.swiper_box {
  padding: 0 28rpx;
  height: 400rpx;
  .indicator {
    display: flex;
    justify-content: center;
    &__dot {
      height: 16rpx;
      width: 16rpx;
      border-radius: 100px;
      background-color: #d9d9d9;
      margin: 0 5px;
      transition: background-color 0.3s;
      &--active {
        background-color: #4378ff;
      }
    }
  }
}
:deep(.uv-swiper__wrapper__item__wrapper__title) {
  display: flex !important;
  align-items: center;
  justify-content: center;
  bottom: unset !important;
  right: unset !important;
  width: 148rpx;
  height: 44rpx;
  border-radius: 8rpx 0rpx 16rpx 0rpx;
  background-color: rgba(37, 37, 37, 0.8);
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 32rpx;
  margin: 0 2rpx;
}
</style>
