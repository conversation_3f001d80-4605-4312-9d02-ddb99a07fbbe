'use strict';  
const uniPush = uniCloud.getPushManager({appId:"__UNI__8A196AA"}) //注意这里需要传入你的应用appId  
exports.main = async (event, context) => {  
	let obj = JSON.parse(event.body)
	console.log(obj)
    return await uniPush.sendMessage({  
        "push_clientid": obj.cids,     //填写上一步在uni-app客户端获取到的客户端推送标识push_clientid  
        "force_notification":true,  //填写true，客户端就会对在线消息自动创建“通知栏消息”。  
        "title": obj.title,      
        "content": obj.content,
		"request_id": obj.requestId || Date.now().toString(),
        "payload": {
            "page": "pages/shmPages/alarmEvents/detail",
            "params": {
              "id": obj.alarmEventId
            }
        },  
    })  
};
