<!--
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-02-21 14:37:52
 * @Description: 
-->
<template>
  <view class="container">
    <scroll-view scroll-y="true" class="content">
      <ylg-auto-form
        ref="autoFormRef"
        :isAllDisabled="false"
        :isFromDetail="false"
        :formConfig="formConfig"
        :formData="formData"
        :rules="rules"
        :btnLoading="btnLoading"
        :labelStyle="labelStyle"
        :formItemStyle="formItemStyle"
        :placeholderStyle="placeholderStyle"
        cancelBtnText="暂 存"
        confirmBtnText="上 报"
        @onChange="onFormInpChange"
        @onRightSlot="onGetLocation"
        @onSubmit="onFormSubmit"
      ></ylg-auto-form>
      <view class="black" style="height: 100rpx"></view>
      <uv-toast ref="toast"></uv-toast>
    </scroll-view>
    <ylg-temporary-modal
      ref="temporaryModal"
      title="退出前是否需要暂存当前进度"
      notice="之后可选择该工单继续补录"
      btnText="暂存当前进度"
      :btnLoading="btnLoading"
      @onModalCallback="onBackModal"
    ></ylg-temporary-modal>
  </view>
</template>
<script setup>
import { computed, ref, reactive } from "vue";
import { onLoad, onShow, onBackPress } from "@dcloudio/uni-app";
import { getSysteminfo, uploadFilePromise, showImg } from "@/utils";
import {
  getCurLocation,
  reverseGeocode,
  transformLngLat,
  reTransformLngLat,
} from "@/utils/location";
// import ylgAutoForm from "@/components/ylg-auto-form.vue";
import { DataAcquisitionService, RoutinePreserveService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

// 获取手机底部安全距离
const systemBottomSafeArea = `${
  Number(getSysteminfo().bottomSafeArea) * 2 || 40
}rpx`;
console.log("手机底部安全距离", systemBottomSafeArea);

let maintainTaskId = ref(""); // 日常养护任务id
let maintainPlanId = ref(""); // 日常养护计划id
let id = ref(""); // 当前作业id（已经上报的草稿状态）
let sectionId = ref(""); // 所属路段id

onLoad(async (options) => {
  // 1清空pinia中缓存的之前选择的数据项：如所属路线、所属路段等；
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: "",
      value: "",
      label: "",
    },
  });
  // 2处理从上个页面带过来的值
  console.log("optionsssss", options);
  maintainTaskId.value = options?.maintainTaskId;
  maintainPlanId.value = options?.maintainPlanId;
  id.value = options?.id || "";
  sectionId.value = options?.sectionId;
  setNestedValue(formData.value, "sectionId".split("."), options?.sectionId);
  // 3查询字典枚举库
  await getDicts();

  // 如果是草稿，需要回显详情数据
  if (id.value) {
    getDetail();
  } else {
    getAddress();
  }
});

onShow(() => {
  // 处理从别的页面中选择之后的数据显示，如所属路线选择
  onFormSelChange();
});

const getDetail = async () => {
  try {
    let { code, data } = await RoutinePreserveService.getHomeworkDetail(
      id.value
    );
    console.log("作业详情", code, data);
    if (code == 200) {
      // formData.value = data
      formData.value.maintainType = data.maintainType || "";
      formData.value.maintainTypeLabel = data.maintainTypeName || "";
      formData.value.number = data.number || ""; // 作业数量
      formData.value.updownMark = data.updownMark || "";
      formData.value.updownMarkLabel = data.updownMarkName || "";
      formData.value.stakeIdLabel = data.stakeName || "";
      // let reTrans = reTransformLngLat(data.longitude, data.latitude);
      // formData.value.longitude = String(reTrans[0]) || "";
      // formData.value.latitude = String(reTrans[1]) || "";
      formData.value.longitude = data.longitude || "";
      formData.value.latitude = data.latitude || "";
      formData.value.reportAddress = data.reportAddress || "";
      formData.value.remark = data.remark || "";
      data.beforePicture &&
        (formData.value.beforePictureArr = [
          { url: showImg(data.beforePicture) },
        ]);
      data.doingPicture &&
        (formData.value.doingPictureArr = [
          { url: showImg(data.doingPicture) },
        ]);
      data.afterPicture &&
        (formData.value.afterPictureArr = [
          { url: showImg(data.afterPicture) },
        ]);
      console.log("详情回显", formData.value);
    }
  } catch (error) {
    console.log("获取作业详情失败", error);
  }
};

const getAddress = async () => {
  try {
    let locationRes = await getCurLocation();
    if (locationRes.errMsg == "getLocation:ok") {
      formData.value.longitude = locationRes.longitude;
      formData.value.latitude = locationRes.latitude;
      formData.value.reportAddress = await reverseGeocode(
        locationRes.longitude,
        locationRes.latitude
      );
    }
  } catch (error) {
    console.log("获取定位失败", error);
    uni.showToast({
      icon: "none",
      title: "获取当前定位失败" + error,
    });
  }
};

// form表单样式配置
const labelStyle = reactive({
  width: "208rpx",
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  lineHeight: "33rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  overflow: "hidden",
});
const placeholderStyle = ref("color: #C1C1C1");

let dicts = ref({});
const getDicts = async () => {
  let { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes: "maintainType,updownMark",
  });
  if (code == 200) {
    dicts.value = data;
  }
};

let temporaryModal = ref(null);
onBackPress((backOptions) => {
  if (backOptions.from === "backbutton") {
    temporaryModal.value.open();
    return true;
  } else if (backOptions.from === "navigateBack") {
    return false;
  }
});
// 返回上一页modal组件回调事件
const onBackModal = (envRes) => {
  switch (envRes.type) {
    case "onCacel":
      temporaryModal.value.close();
      break;
    case "onNotSave":
      temporaryModal.value.close();
      uni.navigateBack({
        delta: 1,
      });
      break;
    case "onTemporaryDraft":
      onFormSubmit({ data: formData.value, status: 0 });
      break;
    default:
      break;
  }
};

// 表单数据配置
let formData = ref({
  // 项目id
  projectId: projectInfo.projectId,
  // 作业类型
  maintainType: "",
  maintainTypeLabel: "",

  // 位置方向
  updownMark: "",
  updownMarkLabel: "",

  // 路段id
  sectionId: sectionId.value,

  // 附近桩号
  stakeName: "",
  stakeId: "", // 1820375081585016833
  stakeIdLabel: "", // K130+900

  // 地址信息
  reportAddress: "", // 随便地址。。。。啦啦啦
  longitude: "", // 104.897869
  latitude: "", // 38.457587

  // 描述
  remark: "",

  // 照片
  beforePictureArr: [],
  doingPictureArr: [],
  afterPictureArr: [],
});
const rules = computed(() => {
  return {
    maintainTypeLabel: {
      type: "string",
      required: true,
      message: "请选择作业类型",
      trigger: ["blur", "change"],
    },
    // number: [
    //   {
    //     // type: "number",
    //     required: false,
    //     validator: (rule, value, callback) => {
    //       // 正则表达式匹配最大7位整数和小数点后2位的浮点数
    //       const regex = /^-?\d{1,7}(\.\d{1,2})?$/;
    //       if (!regex.test(value)) {
    //         callback(new Error("请输入最大7位整数和小数点后2位的浮点数"));
    //       } else {
    //         callback();
    //       }
    //     },
    //     message: "请输入最大7位整数和小数点后2位的浮点数",
    //     trigger: ["blur", "change"],
    //   },
    // ],
    updownMarkLabel: {
      type: "string",
      required: true,
      message: "请选择位置方向",
      trigger: ["blur", "change"],
    },
    reportAddress: {
      type: "string",
      required: true,
      message: "请选择地址信息",
      trigger: ["blur", "change"],
    },
    beforePictureArr: [
      {
        validator: (rule, value, callback) => {
          return value.length == 1;
        },
        message: "请上传照片",
        trigger: ["blur", "change"],
      },
    ],
    doingPictureArr: [
      {
        validator: (rule, value, callback) => {
          return value.length == 1;
        },
        message: "请上传照片",
        trigger: ["blur", "change"],
      },
    ],
    afterPictureArr: [
      {
        validator: (rule, value, callback) => {
          return value.length == 1;
        },
        message: "请上传照片",
        trigger: ["blur", "change"],
      },
    ],
  };
});

const formConfig = computed(() => {
  return [
    {
      items: [
        {
          type: "select",
          label: "作业类型",
          placeholder: "请选择",
          unionKey: "maintainTypeLabel",
          // options: dicts.value.maintainType || [],
          optionsPath: `/pages/routinePreserve/relationInfoChoose/maintainTypeChoose?maintainPlanId=${maintainPlanId.value}`,
        },
        {
          type: "input",
          inputType: "number",
          maxlen: 10,
          label: "作业数量",
          placeholder: "请输入（非必填）",
          unionKey: "number",
        },
        {
          type: "select",
          label: "位置方向",
          placeholder: "请选择",
          unionKey: "updownMarkLabel",
          options: dicts.value.updownMark || [],
        },
        {
          type: "select",
          label: "附近桩号",
          placeholder: "请选择（非必填）",
          unionKey: "stakeIdLabel",
          pathKey: "sectionId",
          isNeedPathKeyValue: false,
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose?noNew=true`,
        },
        {
          type: "custom",
          label: "地址信息",
          placeholder: "请获取定位",
          slotText: "坐标拾取",
          unionKey: "reportAddress",
        },
        {
          type: "textarea",
          label: "作业备注",
          maxlen: 150,
          placeholder: "请输入（非必填）",
          unionKey: "remark",
        },
        {
          type: "upload",
          label: "作业前照片",
          fileList: ref(formData.value.beforePictureArr),
          maxCount: 1,
          formItemHeight: "244rpx",
          unionKey: "beforePictureArr",
        },
        {
          type: "upload",
          label: "作业中照片",
          fileList: ref(formData.value.doingPictureArr),
          maxCount: 1,
          formItemHeight: "244rpx",
          unionKey: "doingPictureArr",
        },
        {
          type: "upload",
          label: "作业后照片",
          fileList: ref(formData.value.afterPictureArr),
          maxCount: 1,
          formItemHeight: "244rpx",
          unionKey: "afterPictureArr",
        },
      ],
    },
  ];
});

const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
  console.log("setNestedValue", formData.value);
};
// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  console.log("formItem数据修改回调", unionKey, val);
  setNestedValue(formData.value, unionKey.split("."), val);
};
// formItem 从选择页面回调的 select 数据修改
let autoFormRef = ref(null);
const onFormSelChange = () => {
  console.log(
    "dataAcquisitionInfo.stateData.key",
    dataAcquisitionInfo.stateData.key
  );
  // 1处理select选择的label
  setNestedValue(
    formData.value,
    dataAcquisitionInfo.stateData.key.split("."),
    dataAcquisitionInfo.stateData.label
  );
  // 2处理select选择的value
  let str = dataAcquisitionInfo.stateData.key.replace(/Label$/, "");
  setNestedValue(
    formData.value,
    str.split("."),
    dataAcquisitionInfo.stateData.value
  );

  // 3如果选择的是 “地址信息”
  // if (formData.value.reportAddressLabel) {
  if (dataAcquisitionInfo.stateData.key === "reportAddressLabel") {
    console.log("选择更改1", formData.value, dataAcquisitionInfo.stateData);
    formData.value.longitude = formData.value.reportAddressLabel.split(",")[0];
    formData.value.latitude = formData.value.reportAddressLabel.split(",")[1];
    delete formData.value.reportAddressLabel;
    autoFormRef.value.formRef.validateField("reportAddress");
    console.log("选择更改", formData.value);
  }
  if (autoFormRef.value?.formRef) {
    autoFormRef.value.formRef.validateField(dataAcquisitionInfo.stateData.key);
  }
};

// 获取定位、地址
const onGetLocation = async () => {
  uni.navigateTo({
    url: `/pages/roadConditionInspection/coordinatePicking?unionKey=reportAddressLabel`,
  });
};

// form表单提交回调
let toast = ref(null);
let btnLoading = ref(""); // '0'-暂存按钮；'1'-提交按钮
const onFormSubmit = async ({ data, status }) => {
  console.log("事件上报", data, status);

  // 暂存时校验是否有上传照片
  if (status == 0) {
    autoFormRef.value.formRef.clearValidate();
    if (
      !data.beforePictureArr.length &&
      !data.doingPictureArr.length &&
      !data.afterPictureArr.length
    ) {
      toast.value.show({
        type: "warning",
        message: `最少需要上传一张照片才可暂存`,
      });
      return;
    }
  }

  // 上报
  try {
    btnLoading.value = String(status);
    let params = {};
    params.projectId = data.projectId;
    params.id = id.value;
    // 日常养护计划id；
    params.maintainTaskId = maintainTaskId.value;
    // 是否草稿
    params.draft = status == "1" ? 0 : 1;
    // 作业类型
    params.maintainType = data.maintainType;
    // 作业数量
    params.number = data.number;
    // 位置方向
    params.updownMark = data.updownMark;
    // 附近桩号id
    params.stakeId = data.stakeId;
    // 作业地址
    params.reportAddress = data.reportAddress;
    params.longitude = data.longitude;
    params.latitude = data.latitude;
    // 作业备注
    params.remark = data.remark;
    // 照片
    // params.beforePicture = data.beforePictureArr[0]?.url;
    if (data?.beforePictureArr?.length) {
      let startIndex = data.beforePictureArr[0]?.url.indexOf("/file");
      if (startIndex !== -1) {
        params.beforePicture =
          data.beforePictureArr[0]?.url.substring(startIndex);
      } else {
        console.log("未找到 '/file' 子字符串");
      }
    } else {
      params.beforePicture = "";
    }
    // params.doingPicture = data.doingPictureArr[0]?.url;
    if (data?.doingPictureArr?.length) {
      let startIndex = data.doingPictureArr[0]?.url.indexOf("/file");
      if (startIndex !== -1) {
        params.doingPicture =
          data.doingPictureArr[0]?.url.substring(startIndex);
      } else {
        console.log("未找到 '/file' 子字符串");
      }
    } else {
      params.doingPicture = "";
    }
    // params.afterPicture = data.afterPictureArr[0]?.url;
    if (data?.afterPictureArr?.length) {
      let startIndex = data.afterPictureArr[0]?.url.indexOf("/file");
      if (startIndex !== -1) {
        params.afterPicture =
          data.afterPictureArr[0]?.url.substring(startIndex);
      } else {
        console.log("未找到 '/file' 子字符串");
      }
    } else {
      params.afterPicture = "";
    }
    // delete data.beforePictureArr;
    // delete data.doingPictureArr;
    // delete data.afterPictureArr;
    let { code, data: resData } = await RoutinePreserveService.submitPreserve(
      params
    );
    btnLoading.value = "";
    console.log("res", resData);
    if (code == 200) {
      toast.value.show({
        type: "success",
        message: `作业${status == "0" ? "暂存" : "上报"}成功`,
        complete() {
          uni.navigateBack({
            delta: 1,
          });
        },
      });
    }
  } catch (error) {
    console.log("上报失败", error);
    btnLoading.value = "";
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f4f8ff;
}
.content {
  background-color: #fff;
}
.uv-form {
  .right_slot_btn {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 24rpx;
    color: #4378ff;
    line-height: 34rpx;
  }
  :deep(.uv-textarea) {
    padding: 0;
  }
  :deep(.uni-input-input) {
    color: #404040;
  }
  :deep(.uni-textarea-textarea) {
    color: #404040;
  }
  :deep(.uv-line) {
    // border-bottom: 2rpx solid #F0F0F0 !important;
  }
}
.btn_box {
  background-color: #f4f8ff;
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx 40rpx v-bind(systemBottomSafeArea) 40rpx;
  position: fixed;
  bottom: 0;
  // bottom: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
