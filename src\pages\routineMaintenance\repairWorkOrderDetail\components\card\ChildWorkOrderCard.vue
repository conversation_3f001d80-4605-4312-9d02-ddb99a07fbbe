<template>
  <view :class="['card', { active }]">
    <view class="image-section">
      <ImageSwiper
        :swiperList="imageList"
      />
    </view>
    <view class="detail-section">
      <view
        v-for="item in detailList"
        :key="item.key"
        class="info-row"
      >
        <view
          class="info-label"
          :class="{ 'address-label': item.key === 'address' }"
        >
          <template v-if="item.key === 'address'">
            地
            <view class="address-spacing"></view>
            址：
          </template>
          <template v-else>
            {{ item.label }}
          </template>
        </view>
        <view
          class="info-value"
          :class="{ 'address-link': item.key === 'address' }"
          @click="item.key === 'address' && handleAddressClick()"
        >
          <template v-if="item.key === 'diseasesTypeName'">
            <view class="flex-row">
              <view>{{ item.value }}</view>
              <view
                :class="[
                  'status-label',
                  getWorkStatusClass(childWorkOrderDetail?.workStatus),
                ]"
                >{{ childWorkOrderDetail.workStatusName }}</view
              >
            </view>
          </template>
          <template v-else-if="item.key === 'address'">
            <view class="address-text">
              {{ item.value || "-" }}
            </view>
          </template>
          <template v-else>
            {{ item.value || "-" }}
          </template>
        </view>
      </view>
    </view>
    <view v-if="showReportBtn && hasPermission" class="report-btn-wrap">
      <button class="report-btn" @click="$emit('report')">作业上报</button>
    </view>
  </view>
</template>

<script setup>
import { computed, inject } from "vue";
import ImageSwiper from "../ImageSwiper.vue";
import { useUserStore } from "@/store/user";
const getWorkStatusClass = inject("getWorkStatusClass");

// 用户store
const userInfo = useUserStore();

const props = defineProps({
  // 子工单详情
  childWorkOrderDetail: {
    type: Object,
    required: true,
  },
  // 是否激活状态
  active: {
    type: Boolean,
    default: false,
  },
  // 是否显示上报按钮
  showReportBtn: {
    type: Boolean,
    default: false,
  },
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 工单配置详情
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
});

// 权限检查
const hasPermission = computed(() => {
  const hasResource = props.repairWorkOrderBaseInfo?.resourceList?.includes("dailyRepairWork") || false;
  const isUserInOrder = props.workOrderConfigDetail?.users?.some(
    (user) => user.relevancyId === userInfo.id
  ) || false;
  return hasResource && isUserInOrder;
});

// 图片列表
const imageList = computed(() =>
  props.childWorkOrderDetail.fileAttributes?.map((file, index) => ({
    title: `作业图片${index + 1}`,
    url: file.path
  })) || []
);

// 详情信息列表
const detailList = computed(() => [
  {
    key: "diseasesTypeName",
    label: "病害类型：",
    value: `${props.childWorkOrderDetail.diseasesTypeName || '-'}(${props.childWorkOrderDetail.diseasesCount || '-'}${props.childWorkOrderDetail.diseasesUnit || ''})`,
  },
  {
    key: "remark",
    label: "事件说明：",
    value: props.childWorkOrderDetail.remark,
  },
  {
    key: "updownMarkName",
    label: "所属路段：",
    value: props.childWorkOrderDetail.updownMarkName,
  },
  {
    key: "eventObjectName",
    label: "事件对象：",
    value: props.childWorkOrderDetail.eventObjectName,
  },
  {
    key: "sectionName",
    label: "事件位置：",
    value: `${props.childWorkOrderDetail.stakeName || '-'}/${props.childWorkOrderDetail.endStakeName || '-'}(${props.childWorkOrderDetail.sectionName || '-'})`,
  },
  {
    key: "address",
    label: "地址：",
    value: props.childWorkOrderDetail.address,
  },
]);

// 地址点击处理
const handleAddressClick = () => {
  const { longitude, latitude, address } = props.childWorkOrderDetail;
  if (!longitude || !latitude) {
    uni.showToast({ title: "暂无位置信息", icon: "none" });
    return;
  }
  uni.navigateTo({
    url: `/pages/common/viewLocation?isDetail=true&curLongitude=${longitude}&curLatitude=${latitude}&curAddress=${address}`,
  });
};
</script>

<style lang="scss" scoped>
@import "../../common.scss";

.flex-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card {
  width: 100%;
  border-radius: 24rpx;
  border: 2rpx solid transparent;
  background-color: #fff;
  box-sizing: border-box;
  padding: 28rpx 24rpx 32rpx 24rpx;

  &.active {
    border: 2rpx solid $primary-color;
  }
}

.image-section {
  position: relative;
}

.detail-section {
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.status-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 40rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  margin-left: 8rpx;
}

.info-row {
  display: flex;
  line-height: 40rpx;

  .info-label {
    width: 140rpx;
    font-size: 28rpx;
    color: $text-muted;
  }

  .info-value {
    flex: 1;
    font-size: 28rpx;
    color: $text-primary;
  }
}

.address-label {
  display: flex;

  .address-spacing {
    width: 56rpx;
  }
}

.address-text {
  color: $primary-color;
}

.report-btn-wrap {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;

  .report-btn {
    width: 80%;
    height: 84rpx;
    line-height: 84rpx;
    background: $primary-color;
    color: #fff;
    border: none;
    border-radius: 16rpx;
    font-size: 40rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 16rpx 0 rgba(67, 120, 255, 0.1);
  }
}
</style>
