<template>
  <view class="progress-box">
    <view class="detail_title">
      <view class="bar"></view>
      <view class="text">处置记录 </view>
    </view>
    <view class="nodes-container">
      <view class="node-item" v-for="(item, index) in props.nodes" :key="index">
        <view
          :class="[
            'node-icon',
            lastContentIndex == index + 1 ? 'last-active-icon' : '',
            !item.content ? 'inactive-icon' : '',
          ]"
        >
          {{ item.title }}
        </view>
        <view class="node-name">{{ item.content }}</view>
        <view class="node-date">{{ item.date }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from "vue";
const props = defineProps({
  nodes: Array,
});
const lastContentIndex = computed(() => {
  const reversedArray = props.nodes.slice();
  const index = reversedArray.findIndex((item) => !item.content);
  return index;
});
</script>

<style lang="scss" scoped>
.progress-box {
  height: 364rpx;
  margin-top: 32rpx;
  border-radius: 24rpx;
  background: #ffffff;
  box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
  padding: 40rpx 28rpx;
  .detail_title {
    margin: 2rpx 0;
    display: flex;
    align-items: center;
    height: 44rpx;
    .bar {
      display: flex;
      align-items: center;
      margin-right: 8rpx;
      width: 6rpx;
      height: 28rpx;
      background: #4378ff;
      border-radius: 4rpx;
    }
    .text {
      display: flex;
      align-items: center;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      font-size: 32rpx;
      color: #404040;
    }
  }
  .nodes-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    position: relative;
    margin: 48rpx 0 16rpx;
    // padding: 0 24rpx;
    .node-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 8rpx;
      width: 30%;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      color: #404040;
      text-align: center;
      .node-icon,
      .last-active-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 76rpx;
        height: 76rpx;
        border-radius: 50%;
        background: #3c62fa;
        position: relative;
        font-size: 32rpx;
        color: #ffffff;
        line-height: 32rpx;
        margin-bottom: 8rpx;
        &::after {
          content: "";
          position: absolute;
          top: 50%;
          left: 84rpx;
          width: 120rpx;
          height: 2rpx;
          background-color: #3c62fa;
          transform: translateY(-50%);
        }
      }
      .last-active-icon {
        &::after {
          background-color: #f2f2f2;
        }
      }
      .inactive-icon {
        background: transparent;
        border: 2rpx solid #f2f2f2;
        color: #606060;
        &::after {
          background-color: #fff;
        }
      }
      &:last-child {
        .node-icon,
        .last-active-icon {
          &::after {
            display: none;
          }
        }
      }
      .node-name {
        // width: 112rpx;
        height: 40rpx;
        color: #404040;
        font-size: 28rpx;
      }
      .node-date {
        width: 134rpx;
        height: 40rpx;
        color: #606060;
        font-size: 28rpx;
      }
    }
    .item:last-child .icon::after {
      display: none;
    }
  }
}
</style>
