<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-11-08 14:57:03
 * @Description: 
-->
<template>
  <view class="container">
    <uv-search
      height="36"
      shape="square"
      searchIcon="../../../static/icon/search_icon.png"
      searchIconSize="16"
      placeholder="输入路段名称搜索"
      placeholderColor="#C1C1C1;"
      bgColor="#fff"
      :showAction="false"
      :boxStyle="searchStyle"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <!-- 列表 -->
    <template v-if="!pageLoading">
      <view class="list_box" v-if="dataList.length">
        <view
          :class="['card', curItem.id === item.id ? 'active_card' : '']"
          v-for="item in dataList"
          :key="item.id"
          @click="chooseItem(item)"
        >
          <view class="item_title">{{ item.sectionName }}</view>
          <view class="item_info">{{ item.startStakeName }}-{{ item.endStakeName }}</view>
        </view>
      </view>
      <no-data class="nodata" v-else></no-data>
    </template>
    <uv-loading-page :loading="pageLoading" loading-text="加载中..." font-size="24rpx"></uv-loading-page>
    <view class="bottom_box">
      <view class="choosed_data_show" v-if="curItem.sectionName"
        >已选 {{ curItem.sectionName }}({{ curItem.startStakeName }}-{{
          curItem.endStakeName
        }})</view
      >
      <view class="btn" @click="confirm">确 定</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { onLoad, onReachBottom } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { DataAcquisitionService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

let unionKey = ref("");
onLoad(async (options) => {
  console.log("options", options);
  unionKey.value = options.unionKey;
  // 获取路线列表
  await getList();
  await handleCurChoosed(options.curItemName);
});

// 处理回显当前已经选择的路线
const handleCurChoosed = (curItemName) => {
  dataList.value.forEach((item) => {
    if (item.sectionName === curItemName) {
      curItem.value = item;
    }
  });
};

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  marginBottom: "28rpx",
});

let inpVal = ref("");
const inpChange = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  dataList.value = [];
  getList();
};
const inpSearch = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  dataList.value = [];
  getList();
};

// 获取路段列表
let dataList = ref([]);
let pageInfo = reactive({
  page: 1,
  limit: 10,
});
let noData = ref(false);
let pageLoading = ref(false);
const getList = async () => {
  let params = {
    searchVal: inpVal.value,
    projectId: projectInfo.projectId,
    page: pageInfo.page,
    limit: pageInfo.limit,
  };
  pageLoading.value = true;
  noData.value = false;
  const { code, data, total } = await DataAcquisitionService.getRoadPartList(
    params
  );
  pageLoading.value = false;
  // 如果接口异常，page-1，不执行后续操作
  if (code != 200) {
    pageInfo.page--;
    return;
  }
  dataList.value = dataList.value.concat(data);
  // 小于5条，加载完成
  if (data.length < 5) {
    noData.value = true;
  } else if (data.length === 5 && total === 5) {
    noData.value = true;
  } else {
    noData.value = false;
  }
  console.log("请求路段", dataList.value);
};

// 选择项目
let curItem = ref({});
const chooseItem = (item) => {
  curItem.value = item;
};

// 提交
const confirm = () => {
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: unionKey.value,
      value: curItem.value.id,
      label: curItem.value.sectionName,
    },
  });
  uni.navigateBack({
    data: 1,
  });
};

/** 页面上拉 **/
onReachBottom(() => {
  if (noData.value) {
    uni.showToast({
      icon: "none",
      title: "没有更多了~",
    });
    return;
  }
  pageInfo.page++;
  getList();
});
</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 20rpx 40rpx;
  background: #f4f8ff;
}
.list_box {
  margin: 12rpx 0 200rpx 0;
  .card {
    margin-bottom: 28rpx;
    padding: 22rpx 28rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 16rpx;
    font-family: PingFang SC, PingFang SC;
    .item_title {
      font-weight: 400;
      color: #373737;
      font-size: 32rpx;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .item_info {
      font-size: 28rpx;
      color: #a09f9f;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .active_card {
    background: #4378ff;
    .item_title {
      color: #ffffff;
    }
    .item_info {
      color: #ffffff;
    }
  }
}
.nodata {
  margin-top: 136rpx;
}
.bottom_box {
  width: calc(100% - 80rpx);
  position: fixed;
  bottom: 0rpx;
  padding: 20rpx 0;
  background: #f4f8ff;
  .choosed_data_show {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .btn {
    width: 670rpx;
    height: 84rpx;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
}
</style>
