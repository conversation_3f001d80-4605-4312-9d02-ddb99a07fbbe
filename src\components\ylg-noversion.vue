<!--
 * @Author: yjw
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2024-12-06 14:46:19
 * @Description: 
-->
<template>
  <view class="content">
    <image class="img" :src="imgUrl" mode="widthFix" />
    <view class="noice" :style="{ fontSize: textSize }">{{ noticeTip }}</view>
  </view>
</template>
<script setup>
import { ref } from "vue";
const props = defineProps({
  imgUrl: {
    type: String,
    default: "/static/image/version_nodata.png",
  },
  noticeTip: {
    type: String,
    default: "当前已是最新版本",
  },
  textSize: {
    type: String,
    default: "32rpx",
  },
});
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.img {
  display: block;
  width: 560rpx;
  height: 560rpx;
}
.noice {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: #404040;
  line-height: 38rpx;
  text-align: center;
}
</style>
