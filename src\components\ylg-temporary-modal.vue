<template>
  <view>
    <uv-popup ref="popup" mode="center" round="8" :customStyle="customStyle">
      <view class="box">
        <view class="title">{{ title }}</view>
        <view class="content">{{ notice }}</view>
        <view class="btn_box">
          <view class="cancel btn" @click="onCacel">取消</view>
          <view class="not_save btn" @click="onNotSave">不保存</view>
          <uv-button
            :loading="btnLoading === '0'"
            :custom-style="btnStyle"
            :customTextStyle="btnTextStyle"
            :text="btnText"
            @click="onTemporaryDraft"
          ></uv-button>
        </view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
const props = defineProps({
  title:{
    type: String,
    default: '退出前是否需要暂存当前数据'
  },
  notice:{
    type: String,
    default: '之后可在历史采集中继续补录'
  },
  btnText:{
    type: String,
    default: '暂存为草稿'
  },
  btnLoading: {
    type: String,
    default: '',
  }
})


const customStyle = reactive({
  width: '608rpx',
  height: '536rpx',
  padding: '46rpx 44rpx',
  boxSizing: 'border-box'
})

// 按钮样式
const btnStyle = reactive({
  width: "520rpx",
  height: "72rpx",
  lineHeight: "44rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 500,
  background: '#4378FF',
  color:' #FFFFFF',
  border: "2rpx solid #4378ff",
});
const btnTextStyle = reactive({
  fontSize: "32rpx",
});


const emit = defineEmits(['confirmTabs','onModalCallback']);
// 取消
const onCacel = () => {
  emit('onModalCallback',{type:'onCacel'});
}
// 不保存
const onNotSave = () => {
  emit('onModalCallback',{type:'onNotSave'});
}
// 暂存为草稿
const onTemporaryDraft = () =>{
  emit('onModalCallback',{type:'onTemporaryDraft'});
}
// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};

defineExpose({
  open,
  close
});
</script>
<style lang="scss" scoped>
.box {
  text-align: center;
  box-sizing: border-box;
  .title{
    font-weight: 500;
    font-size: 36rpx;
    color: #373737;
    line-height: 50rpx;
  }
  .content{
    margin-top: 20rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #A09F9F;
    line-height: 40rpx;
  }
  .btn_box{
    margin-top: 60rpx;
    .btn{
      width: 520rpx;
      height: 72rpx;
      border-radius: 8rpx;
      padding: 14rpx 0;
      font-weight: 500;
      font-size: 32rpx;
      line-height: 44rpx;
      text-align: center;
      margin-bottom: 28rpx;
      box-sizing: border-box;
    }
    .btn:last-child{
      margin-bottom: 0 !important;
    }
    .cancel{
      background: #E4E4E4;
      color: #373737; 
    }
    .not_save{
      background: #E4E4E4;
      color: #373737;
    }
    .temporary_draft{
      background: #4378FF;
      color: #FFFFFF;
    }

  }
}
</style>
