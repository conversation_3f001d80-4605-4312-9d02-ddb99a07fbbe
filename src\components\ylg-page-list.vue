<template>
    <scroll-view
      class="page-list"
      :scroll-top="scrollTop"
      scroll-y
      :scroll-anchoring="true"
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="!isFlexScroll"
      @scroll="onScroll"
      @scrolltolower="onReachBottom"
    >
      <!-- 列表内容 -->
      <view class="list-content">
        <slot name="content" :list="list"></slot>
      </view>
      <!-- 空状态 -->
      <view v-if="list.length === 0 && !loading" class="empty-state">
        <!-- <text>{{ emptyText }}</text> -->
        <ylgNodata :imgStyle="{
                width: '244rpx',
                height: '168rpx'
            }" textSize="32rpx" imgUrl="/static/shmStatic/image/asset-nodata-img.png" />
      </view>
  
      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <uv-loading-icon mode="circle" />
        <text>加载中...</text>
      </view>
      <!-- 加载更多 -->
      <view v-if="list.length > 0" class="load-more">
        <text v-if="hasMore && !loading">上拉加载更多</text>
        <text v-if="!hasMore">没有更多数据了</text>
      </view>
    </scroll-view>
  </template>
  
  <script setup>
  import { ref, onMounted, watch, nextTick } from 'vue'
  import { throttle } from '@/utils/index'
  import ylgNodata from '@/components/ylg-nodata.vue'
  const props = defineProps({
    // 请求方法
    fetchData: {
      type: Function,
      required: true
    },
    // 请求参数
    params: {
      type: Object,
      default: () => ({})
    },
    // 空状态文本
    emptyText: {
      type: String,
      default: '暂无数据~'
    },
    // 是否自动加载第一页
    autoLoad: {
      type: Boolean,
      default: true
    },
    // 顶部滚动多余
    scrollExtraHeight: {
      type: Number,
      default: 0
    },
    // 滚动布局方式
    isFlexScroll: {
      type: Boolean,
      default: false
    }
  })
  
  // 数据相关
  const list = ref([])
  const page = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const hasMore = ref(true)
  const loading = ref(false)
  const scrollTop = ref(0)
  
  // 重置列表
  const reset = () => {
    list.value = []
    page.value = 1
    total.value = 0
    hasMore.value = true
  }
  
  // 加载数据
  const loadData = async (isRefresh = false) => {
    if (loading.value || (!hasMore.value && !isRefresh)) return
  
    loading.value = true
    try {
      const params = {
        ...props.params,
        page: page.value,
        limit: pageSize.value
      }
      const data = await props.fetchData(params)
      const { list: newList, total: newTotal } = data
      total.value = newTotal
      if (isRefresh) {
        list.value = newList
        nextTick(() => {
          scrollTop.value = 0
        })
      } else {
        list.value = [...list.value, ...newList]
      }
      hasMore.value = list.value.length < total.value
      if (hasMore.value) {
        page.value++
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      loading.value = false
      // 停止下拉刷新
      uni.stopPullDownRefresh()
    }
  }
  
  // 刷新列表
  const refresh = () => {
    reset()
    loadData(true)
  }
  
  // 滚动事件
  const onScroll = (e) => {
  }
  
  // 滚动到底部
  const onReachBottom = throttle(() => {
    if (!hasMore.value || loading.value) return
    loadData()
  })
  
  // 监听参数变化
  watch(
    () => props.params,
    () => {
      refresh()
    },
    { deep: true }
  )
  
  // 加载数据
  onMounted(() => {
    if (props.autoLoad) {
      loadData(true)
    }
  })
  
  // 暴露方法给父组件
  defineExpose({
    refresh,
    loadData,
    reset
  })
  </script>
  
  <style lang="scss" scoped>
  .page-list {
    // background-color: #f0f0f0;
    flex: 1 !important;
    height: 100% !important;
  
    .list-content {
      padding: 10rpx;
    //   box-sizing: border-box;
    //   height: 100%;
    }
  
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60rpx 0;
  
      text {
        font-size: 40rpx;
        color: #999;
      }
    }
  
    .loading,
    .load-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 40rpx;
      color: #999;
      font-size: 24rpx;
  
      text {
        margin-left: 12rpx;
      }
    }
  }
  </style>