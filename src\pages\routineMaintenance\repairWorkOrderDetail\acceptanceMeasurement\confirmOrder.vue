<template>
  <view class="work-order-confirm-container">
    <view class="work-order-confirm-content">
      <WorkOrderWrapCard :base-info="workOrderBaseInfo" :is-show-bar="true">
        <template #header>
          <view class="work-order-confirm-title">
            <view class="work-order-confirm-title-bar">
              <view class="title-indicator"></view>
              <view class="title-text">维修工单接受</view>
            </view>
          </view>
        </template>
        <template #body>
          <!-- 工单基础信息展示 -->
          <view class="child-work-order-detail-wrap">
            <view
              v-for="item in baseInfoList"
              :key="item.key"
              class="work-order-info-row"
            >
              <view class="work-order-info-label">{{ item.label }}</view>
              <view class="work-order-info-value">
                {{ item.value || "-" }}
              </view>
            </view>
            <!-- 子工单详情区域 -->
            <view
              :style="{
                paddingBottom: '8rpx',
                paddingTop: '40rpx',
              }"
            >
              <!-- 子工单详情组件 -->
              <ChildWorkOrderDetail
                :child-work-order-list="childWorkOrderList"
                :child-order-detail="childWorkOrderDetail"
                :current-page="currentChildOrderPage"
                @page-change="handleChildOrderPageChange"
              />
              <!-- 子工单页码切换器（多个子工单时显示） -->
              <view
                v-if="childWorkOrderList.length > 1"
                class="page-switcher-container"
              >
                <NumberSwitcher
                  :current-page="currentChildOrderPage"
                  :total-pages="childWorkOrderList.length"
                  @change="handleChildOrderPageChange"
                />
              </view>
            </view>
          </view>
        </template>
      </WorkOrderWrapCard>
      <!-- 维修描述表单 -->
      <view style="margin-top: 32rpx">
        <RepairDescriptionForm ref="repairDescriptionFormRef" />
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="footer-bottom-wrap">
      <!-- 完成确认操作栏 -->
      <view class="completed-footer">
        <!-- 预计花费显示 -->
        <view class="completed-footer-cost">
          <view class="cost-label">预计花费</view>
          <view class="cost-value-wrap">
            <view class="cost-value" :class="{ 'cost-dash': !estimateCost }">{{
              estimateCost || "—"
            }}</view>
            <view class="cost-unit">元</view>
          </view>
        </view>
        <!-- 确认完成按钮 -->
        <uv-button
          class="confirm-button"
          :class="{ 'confirm-button--disabled': !isFormValid }"
          :disabled="!isFormValid || isConfirmLoading"
          :loading="isConfirmLoading"
          text="确认完成"
          @click="handleConfirmOrder"
        ></uv-button>
      </view>
    </view>

    <!-- 自定义退出确认弹窗 -->
    <view
      v-if="showExitDialog"
      class="exit-dialog-overlay"
      @click="hideExitDialog"
    >
      <view class="exit-dialog" @click.stop>
        <view class="exit-dialog-title">是否确认退出</view>
        <view class="exit-dialog-content">退出后，已录入数据将丢失</view>
        <view class="exit-dialog-buttons">
          <button
            class="exit-dialog-button exit-dialog-button--confirm"
            @click="confirmExit"
          >
            确认退出
          </button>
          <button
            class="exit-dialog-button exit-dialog-button--cancel"
            @click="hideExitDialog"
          >
            我再想想
          </button>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
// Vue相关导入
import { ref, computed, provide } from "vue";
import { onLoad, onBackPress } from "@dcloudio/uni-app";

// 组件导入
import ChildWorkOrderDetail from "../components/ChildWorkOrderDetail.vue";
import RepairDescriptionForm from "../components/repairDescriptionForm/index.vue";
import NumberSwitcher from "../components/NumberSwitcher.vue";
import WorkOrderWrapCard from "../components/card/WorkOrderWrapCard.vue";

// 服务和Store导入
import { RoutineMaintenanceService } from "@/service";
import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";

// ==================== Store实例 ====================
const workOrderStore = useRepairWorkOrderStore();

// ==================== 响应式数据 ====================
const repairDescriptionFormRef = ref(null); // 维修描述表单引用
const isConfirmLoading = ref(false); // 确认按钮加载状态
const currentChildOrderPage = ref(1); // 当前子工单页码
const showExitDialog = ref(false); // 控制退出确认弹窗显示

// 子工单相关数据
const childWorkOrderList = ref([]); // 子工单列表
const childWorkOrderDetail = ref({}); // 当前子工单详情

// ==================== 计算属性 ====================
// 从store获取工单基础信息
const workOrderBaseInfo = computed(() => workOrderStore || {});

// ==================== 工单状态配置 ====================
/**
 * 工单状态映射配置
 * 定义工单各个状态的名称和对应的CSS样式类名
 */
const WORK_STATUS_MAP = {
  1: { name: "待确认", class: "pending" }, // 工单创建后等待确认
  2: { name: "待施工", class: "to-do" }, // 工单已确认，等待开始施工
  3: { name: "施工中", class: "in-progress" }, // 工单正在施工中
  4: { name: "待核验", class: "verify" }, // 施工完成，等待验收核验
  5: { name: "已完成", class: "completed" }, // 工单已完成所有流程
};

/**
 * 获取工单状态对应的CSS样式类名
 * @param {number} status - 工单状态码 (1-5)
 * @returns {string|undefined} 对应的CSS类名，如果状态码无效则返回undefined
 */
const getWorkStatusClass = (status) => WORK_STATUS_MAP[status]?.class;

// ==================== 依赖注入 ====================
/**
 * 向子组件提供工单状态样式类获取方法
 * 子组件可以通过inject("getWorkStatusClass")来获取此方法
 */
provide("getWorkStatusClass", getWorkStatusClass);

// ==================== 数据获取方法 ====================
/**
 * 获取子工单列表
 * 从工单基础信息中获取工单ID，请求子工单列表数据
 */
const getChildWorkOrderList = async () => {
  const workOrderId = workOrderBaseInfo.value?.id;
  if (!workOrderId) return;

  try {
    const { data } = await RoutineMaintenanceService.getChildOrder(workOrderId);
    childWorkOrderList.value = data;

    // 自动获取第一个子工单的详情
    if (data.length > 0) {
      await getChildWorkOrderDetail(data[0]?.eventId);
    }
  } catch (error) {
    console.error("获取子工单列表失败:", error);
  }
};

/**
 * 获取子工单详情
 * @param {string} eventId - 子工单事件ID
 */
const getChildWorkOrderDetail = async (eventId) => {
  if (!eventId) return;

  try {
    const { data } =
      await RoutineMaintenanceService.getInspectEventDetail(eventId);
    childWorkOrderDetail.value = data;
  } catch (error) {
    console.error("获取子工单详情失败:", error);
  }
};

/**
 * 初始化页面数据
 */
const initializeData = async () => {
  await getChildWorkOrderList();
};

// ==================== 工具函数 ====================
/**
 * 格式化日期时间
 * @param {string} dateTime - ISO格式的日期时间字符串
 * @returns {string} 格式化后的日期时间字符串 (YYYY-MM-DD HH:mm)
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return "";
  return dateTime.replace(/T/, " ").substring(0, 16);
};

// ==================== 计算属性 ====================
/**
 * 工单基础信息列表
 * 用于页面顶部信息展示
 */
const baseInfoList = computed(() => [
  {
    key: "createTime",
    label: "接收时间：",
    value: formatDateTime(workOrderBaseInfo.value?.createTime),
  },
  {
    key: "createUserName",
    label: "工单来源：",
    value: workOrderBaseInfo.value?.createUserName,
  },
]);

/**
 * 预计花费金额
 * 从维修描述表单中获取总费用
 */
const estimateCost = computed(() => {
  const cost = repairDescriptionFormRef.value?.totalCost;
  return cost && parseFloat(cost) > 0 ? cost : "-";
});

/**
 * 表单验证状态
 * 用于控制确认完成按钮的可用状态
 */
const isFormValid = computed(() => {
  return repairDescriptionFormRef.value?.isFormValid || false;
});

// ==================== 事件处理方法 ====================
/**
 * 子工单页码切换处理
 * @param {number} page - 目标页码
 */
const handleChildOrderPageChange = (page) => {
  currentChildOrderPage.value = page;
  // 获取对应页码的子工单详情
  getChildWorkOrderDetail(childWorkOrderList.value[page - 1]?.eventId);
};

/**
 * 隐藏退出确认弹窗
 */
const hideExitDialog = () => {
  showExitDialog.value = false;
};

/**
 * 确认退出操作
 * 退出前通知上一个页面刷新工单基础信息
 */
const confirmExit = () => {
  showExitDialog.value = false;
  uni.navigateBack();
};

/**
 * 确认工单处理
 * 提交维修描述表单数据，完成工单确认流程
 */
const handleConfirmOrder = async () => {
  // 表单验证检查
  if (!isFormValid.value) {
    return;
  }

  // 获取表单数据
  const repairForm = repairDescriptionFormRef.value.getRepairFormData();
  if (!repairForm) {
    // 表单数据获取失败或验证不通过
    return;
  }

  isConfirmLoading.value = true;

  try {
    /**
     * 数字字段类型转换工具函数
     * @param {Array} list - 需要转换的数据列表
     * @param {Array} fields - 需要转换为数字的字段名数组
     * @returns {Array} 转换后的数据列表
     */
    const convertToNumber = (list, fields) => {
      return list.map((item) => {
        const converted = { ...item };
        fields.forEach((field) => {
          if (
            converted[field] !== undefined &&
            converted[field] !== null &&
            converted[field] !== ""
          ) {
            converted[field] = Number(converted[field]);
          }
        });
        return converted;
      });
    };

    // 构建提交参数
    const params = {
      projectId: workOrderBaseInfo.value.projectId, // 项目ID
      workId: workOrderBaseInfo.value.id, // 工单ID
      remark: repairForm.remark, // 备注信息
      filePath: repairForm.filePath, // 附件文件路径

      // 人员费用数据（转换planNum字段为数字类型）
      users: convertToNumber(repairForm.users, ["planNum"]).map((user) => ({
        workId: workOrderBaseInfo.value.id,
        relevancyId: user.relevancyId,
        relevancyType: user.relevancyType,
        planNum: user.planNum, // 计划数量
        planTotal: user.planTotal, // 计划总价
        unitPrice: user.unitPrice, // 单价
        units: user.units, // 单位
      })),

      // 作业内容数据（转换planNumber字段为数字类型）
      settlements: convertToNumber(repairForm.settlements, ["planNumber"]).map(
        (settlement) => ({
          workOrderId: workOrderBaseInfo.value.id,
          configId: settlement.configId,
          content: settlement.content, // 作业内容
          planNumber: settlement.planNumber, // 计划数量
          planTotal: settlement.planTotal, // 计划总价
          unitPrice: settlement.unitPrice, // 单价
          units: settlement.units, // 单位
        })
      ),

      // 材料费用数据（转换planNum字段为数字类型）
      materials: convertToNumber(repairForm.materials, ["planNum"]).map(
        (material) => ({
          workId: workOrderBaseInfo.value.id,
          relevancyId: material.relevancyId,
          relevancyType: material.relevancyType,
          planNum: material.planNum, // 计划数量
          planTotal: material.planTotal, // 计划总价
          unitPrice: material.unitPrice, // 单价
          unit: material.units, // 单位
        })
      ),

      // 机械费用数据（转换planNum字段为数字类型）
      machines: convertToNumber(repairForm.machines, ["planNum"]).map(
        (machine) => ({
          workId: workOrderBaseInfo.value.id,
          relevancyId: machine.relevancyId,
          relevancyType: machine.relevancyType,
          planNum: machine.planNum, // 计划数量
          planTotal: machine.planTotal, // 计划总价
          unitPrice: machine.unitPrice, // 单价
          units: machine.units, // 单位
        })
      ),
    };

    // 提交确认工单请求
    const { data } = await RoutineMaintenanceService.addOrdinary(params);

    // 处理提交结果
    if (data?.allowCommit) {
      // 提交成功
      uni.showToast({
        icon: "none",
        title: "确认工单成功",
      });
      // 使用事件总线通知上一页刷新
      uni.$emit("refreshWorkOrderBaseInfo");
      // 延迟返回上一页，让用户看到成功提示
      setTimeout(() => {
        uni.navigateBack();
      }, 2000);
    } else {
      // 提交失败，显示服务器返回的错误信息
      uni.showToast({
        icon: "none",
        title: data?.msg || "确认工单失败",
      });
    }
  } finally {
    // 无论成功失败都要重置加载状态
    isConfirmLoading.value = false;
  }
};

// ==================== 生命周期钩子 ====================
/**
 * 页面加载时初始化
 */
onLoad(() => {
  initializeData();
});

/**
 * 页面返回拦截处理
 * 当用户点击左上角返回按钮或使用手势返回时触发
 */
onBackPress((backOptions) => {
  if (backOptions.from === "backbutton") {
    // 用户手动点击返回按钮时
    showExitDialog.value = true; // 显示确认弹窗
    return true; // 拦截返回操作
  } else if (backOptions.from === "navigateBack") {
    // 代码调用uni.navigateBack()时
    return false; // 允许返回操作
  }
});
</script>
<style lang="scss">
@import "../common.scss";

.work-order-confirm-container {
  background-color: #f4f8ff;
  height: 100vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .work-order-confirm-content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    padding: 0 40rpx;
    box-sizing: border-box;
  }

  .work-order-confirm-title {
    margin-top: 28rpx;
    display: flex;
    align-items: center;

    .work-order-confirm-title-bar {
      display: flex;
      align-items: center;

      .title-indicator {
        margin-right: 8rpx;
        width: 6rpx;
        height: 28rpx;
        background: $primary-color;
        border-radius: 4rpx;
      }

      .title-text {
        font-family: $font-family;
        font-weight: 500;
        font-size: 32rpx;
        color: $text-secondary;
      }
    }
  }

  .work-order-info-row {
    display: flex;
    margin-top: 24rpx;

    .work-order-info-label {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-muted;
      line-height: 40rpx;
    }

    .work-order-info-value {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: $text-primary;
      line-height: 40rpx;
    }
  }

  .page-switcher-container {
    margin-top: 32rpx;
    padding: 0 28rpx;
  }

  .footer-bottom-wrap {
    height: 160rpx;
    margin-top: 24rpx;
    flex-shrink: 0;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent;

    .completed-footer {
      width: 100%;
      height: 100%;
      padding: 0 32rpx;
      box-sizing: border-box;
      background: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .completed-footer-cost {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;

        .cost-label {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .cost-value-wrap {
          display: flex;
          align-items: baseline;

          .cost-value {
            font-size: 40rpx;
            font-weight: bold;
            color: #4378ff;
            margin-right: 8rpx;

            &.cost-dash {
              color: #bfc4cc;
              font-weight: normal;
            }
          }

          .cost-unit {
            font-size: 24rpx;
            color: #bfc4cc;
          }
        }
      }

      :deep(.confirm-button) {
        width: 260rpx;
        height: 96rpx;

        .uv-button {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 600;
          background: #4378ff;
          color: #ffffff;
          border: none;
          outline: none;
          transition: all 0.2s ease;
          box-shadow: 0 2rpx 8rpx rgba(67, 120, 255, 0.2);

          .uv-button__text {
            font-size: 40rpx !important;
            color: #ffffff;
          }

          .uv-button__loading-text {
            font-size: 40rpx !important;
            color: #ffffff;
          }
        }

        &.confirm-button--disabled .uv-button {
          background: #e4e4e4;
          color: #999999;
          box-shadow: none;

          .uv-button__text {
            color: #999999;
          }

          .uv-button__loading-text {
            color: #999999;
          }
        }
      }
    }
  }

  // ==================== 退出确认弹窗样式 ====================
  .exit-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .exit-dialog {
    width: 80%;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 48rpx 40rpx 32rpx;
    box-sizing: border-box;

    .exit-dialog-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
      text-align: center;
      margin-bottom: 24rpx;
      line-height: 50rpx;
    }

    .exit-dialog-content {
      font-size: 28rpx;
      color: #999999;
      text-align: center;
      margin-bottom: 80rpx;
      line-height: 40rpx;
    }

    .exit-dialog-buttons {
      display: flex;
      flex-direction: column;
      gap: 24rpx;

      .exit-dialog-button {
        width: 100%;
        height: 78rpx;
        line-height: 78rpx;
        border-radius: 8rpx;
        font-size: 32rpx;
        font-weight: 500;
        border: none;
        outline: none;
        transition: all 0.2s ease;

        &--confirm {
          background: #e4e4e4;
          color: #373737;

          &:active {
            background: #e8e8e8;
            transform: scale(0.99);
          }
        }

        &--cancel {
          background: #4378ff;
          color: #ffffff;

          &:active {
            background: #3366ee;
            transform: scale(0.99);
          }
        }
      }
    }
  }
}
</style>
