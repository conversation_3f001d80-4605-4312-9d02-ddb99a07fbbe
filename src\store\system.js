/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-03-11 15:48:09
 * @Description:
 */
import { defineStore } from "pinia";
export const useSystemStore = defineStore({
  id: "systemInfo",
  state: () => ({
    systemCode: [], // 系统编码
  }),
  // 存储数据（同步|异步）
  actions: {
    async updateSystemCode(flag) {
      this.systemCode = flag;
    }
  },
  persist: {
    // 开启持久化
    enabled: true,
    H5Storage: window?.localStorage || ""
  },
});
