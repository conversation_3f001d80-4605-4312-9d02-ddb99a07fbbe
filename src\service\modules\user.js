/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-22 14:43:28
 * @Description:
 */
import BaseService from "../request";

class UserService extends BaseService {
  constructor() {
    super();
  }

  // 账号密码登录
  getAuthPublicKey() {
    return this.get("/auth/publicKey");
  }
  // 账号密码登录
  login(data) {
    return this.post("/auth/login", data);
  }

  // 验证码登录-获取验证码
  sendLoginCode(phoneNumber) {
    return this.post(`/sms/ali/send/loginCode/${phoneNumber}`);
  }
  // 验证码登录-校验验证码&手机号
  verifyCode(phoneNumber, code) {
    return this.get(
      `/sms/ali/login/verifyCode?phoneNumber=${phoneNumber}&code=${code}`
    );
  }

  // 切换租户
  authChange(tenantId) {
    return this.put(`/auth/change/${tenantId}`);
  }

  // 根据当前用户获取租户列表
  getUserTenantList() {
    // return this.get("/system/user/tenant/list?systemCode=5");
    return this.get("/system/user/tenant/list");
  }
  // 根据当前登录人获取的资源编码列表
  authResource(systemCode) {
    // return this.get(`/system/user/resource/code/list?systemCode=4`);
    return this.get(`/system/user/resource/code/list?systemCode=${systemCode}`);
  }

  // 重置密码-获取验证码
  resetPasswordCode(phoneNumber) {
    return this.post(`/sms/ali/send/resetPasswordCode/${phoneNumber}`);
  }

  // 重置密码
  resetPassword(data) {
    return this.put(`/sms/ali/resetPassword/verifyCode`, data);
  }

  // 重置密码
  logoutLogin() {
    return this.post(`/auth/logout`);
  }

  /**
   * 获取用户信息
   * @param accessToken
   */
  getUserInfo(accessToken) {
    return this.get("/user/userinfo", { accessToken });
  }
}
export default new UserService();
