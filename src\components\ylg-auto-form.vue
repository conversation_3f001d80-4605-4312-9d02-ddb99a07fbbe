<!--
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-01 10:21:07
 * @Description: 
-->
<template>
  <div class="container">
    <uv-form
      labelPosition="left"
      :model="formData"
      :rules="rules"
      :labelWidth="labelStyle.width || '252rpx'"
      :labelStyle="labelStyle"
      ref="formRef"
    >
      <view
        class="info_card"
        v-for="(cardItem, index) in formConfig"
        :key="index"
      >
        <view class="info_title" v-if="cardItem.title">
          <view class="card_bar"></view>
          <view class="">{{ cardItem.title }}</view>
        </view>
        <view
          class="form_item"
          v-for="formItem in cardItem.items"
          :key="formItem.unionKey"
        >
          <!-- 主字段-input -->
          <uv-form-item
            v-if="formItem.type === 'mainInput'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="formItemStyle"
          >
            <uv-input
              class="main_inp"
              :disabled="isAllDisabled"
              disabledColor="#ffffff"
              placeholderStyle="fontSize: 32rpx;color:#8e8e8e;"
              fontSize="32rpx"
              border="none"
              :maxlength="formItem.maxlen"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              :value="getValue(formItem.unionKey)"
              @blur="
                onChange(
                  $event,
                  formItem.unionKey,
                  formItem.inputType,
                  formItem.maxlen
                )
              "
              @touchmove.stop="handleTouchMove"
            >
            </uv-input>
          </uv-form-item>
          <!-- 选择select组件 -->
          <uv-form-item
            v-if="formItem.type === 'select'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            :borderBottom="
              String(formItem.borderBottom) === 'false' ? false : true
            "
            :customStyle="formItemStyle"
          >
            <uv-input
              v-if="formItem.modelType === 'model'"
              :placeholderStyle="placeholderStyle"
              fontSize="28rpx"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              disabled
              @click="toSelect(formItem)"
              v-model="formData[formItem.unionKey]"
              disabledColor="#ffffff"
              border="none"
              @touchmove.stop="handleTouchMove"
            >
            </uv-input>
            <uv-input
              v-else
              :placeholderStyle="placeholderStyle"
              fontSize="28rpx"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              disabled
              @click="toSelect(formItem)"
              :value="
                getValue(
                  isFromDetail ? formItem.unionKeyDetail : formItem.unionKey
                )
              "
              disabledColor="#ffffff"
              border="none"
              @touchmove.stop="handleTouchMove"
            >
            </uv-input>
            <template v-if="!isAllDisabled && !formItem.disabled" v-slot:right>
              <uv-icon
                name="arrow-right"
                size="12"
                @click="toSelect(formItem)"
              ></uv-icon>
            </template>
          </uv-form-item>
          <!-- 选择 日期 datePicker 组件 -->
          <uv-form-item
            v-if="formItem.type === 'datePicker'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="formItemStyle"
          >
            <uv-input
              v-if="formItem.modelType === 'model'"
              :placeholderStyle="placeholderStyle"
              fontSize="28rpx"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              disabled
              @click="toPickDate(formItem)"
              v-model="formData[formItem.unionKey]"
              disabledColor="#ffffff"
              border="none"
              @touchmove.stop="handleTouchMove"
            >
            </uv-input>
            <uv-input
              v-else
              :placeholderStyle="placeholderStyle"
              fontSize="28rpx"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              disabled
              @click="toPickDate(formItem)"
              :value="getValue(formItem.unionKey)"
              disabledColor="#ffffff"
              border="none"
              @touchmove.stop="handleTouchMove"
            >
            </uv-input>

            <template v-if="!isAllDisabled" v-slot:right>
              <uv-icon
                name="calendar"
                color="#606266"
                size="18"
                @click="toPickDate(formItem)"
              ></uv-icon>
            </template>
          </uv-form-item>
          <!-- 输入input组件 -->
          <uv-form-item
            v-if="formItem.type === 'input'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="formItemStyle"
          >
            <uv-input
              v-if="formItem.modelType === 'model'"
              :type="formItem.inputType || 'text'"
              :maxlength="formItem.maxlen"
              :disabled="isAllDisabled || formItem.disabled"
              disabledColor="#ffffff"
              :placeholderStyle="placeholderStyle"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              border="none"
              fontSize="28rpx"
              v-model="formData[formItem.unionKey]"
              @input="onKeyInput($event, formItem.unionKey, formItem.inputType)"
              @blur="
                onChange(
                  $event,
                  formItem.unionKey,
                  formItem.inputType,
                  formItem.maxlen
                )
              "
              @touchmove.stop="handleTouchMove"
            >
            </uv-input>
            <uv-input
              v-else
              :type="formItem.inputType || 'text'"
              :maxlength="formItem.maxlen"
              :disabled="isAllDisabled"
              disabledColor="#ffffff"
              :placeholderStyle="placeholderStyle"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              border="none"
              fontSize="28rpx"
              :value="getValue(formItem.unionKey)"
              @input="onKeyInput($event, formItem.unionKey, formItem.inputType)"
              @blur="
                onChange(
                  $event,
                  formItem.unionKey,
                  formItem.inputType,
                  formItem.maxlen
                )
              "
              @touchmove.stop="handleTouchMove"
            >
            </uv-input>
            <template v-slot:right>
              <text v-if="formItem.sideInfo">{{ formItem.sideInfo }}</text>
              <template v-else>
                <view
                  v-if="formItem?.slotText"
                  :class="[
                    'right_slot_text',
                    formItem?.slotTextDis ? 'disabeld_slot_text' : '',
                  ]"
                  @click="onRightSlot"
                  >{{ formItem?.slotText }}</view
                >
                <uv-icon
                  v-if="formItem?.slotIcon"
                  :name="formItem?.slotIcon"
                  @click="onRightSlot"
                ></uv-icon>
              </template>
            </template>
          </uv-form-item>
          <!-- 输入textarea组件 -->
          <uv-form-item
            v-if="formItem.type === 'textarea'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="{
              background: '#FFF',
              boxSizing: 'border-box',
              padding: '24rpx 40rpx',
              borderBottom: 'none',
            }"
          >
            <uv-textarea
              v-if="formItem.modelType === 'model'"
              :disabled="isAllDisabled"
              disabledColor="#ffffff"
              :placeholderStyle="placeholderStyle"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              border="none"
              :textStyle="{ fontSize: '28rpx' }"
              :count="true"
              :autoHeight="true"
              :maxlength="formItem.maxlen"
              v-model="formData[formItem.unionKey]"
              @blur="
                onChange(
                  $event,
                  formItem.unionKey,
                  formItem.type,
                  formItem.maxlen
                )
              "
              @touchmove.stop="handleTouchMove"
            >
            </uv-textarea>
            <uv-textarea
              v-else
              :disabled="isAllDisabled"
              disabledColor="#ffffff"
              :placeholderStyle="placeholderStyle"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              border="none"
              :textStyle="{ fontSize: '28rpx' }"
              :count="true"
              :autoHeight="true"
              :maxlength="formItem.maxlen"
              :value="getValue(formItem.unionKey)"
              @blur="
                onChange(
                  $event,
                  formItem.unionKey,
                  formItem.type,
                  formItem.maxlen
                )
              "
              @touchmove.stop="handleTouchMove"
            >
            </uv-textarea>
          </uv-form-item>
          <!-- 自定义组件 点击事件获取值 -->
          <uv-form-item
            v-if="formItem.type === 'custom'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="formItemStyle"
          >
            <uv-input
              :placeholderStyle="placeholderStyle"
              fontSize="28rpx"
              :placeholder="isAllDisabled ? '-' : formItem.placeholder"
              disabled
              :value="
                getValue(
                  isFromDetail ? formItem.unionKeyDetail : formItem.unionKey
                )
              "
              disabledColor="#ffffff"
              border="none"
              @touchmove.stop="handleTouchMove"
            >
            </uv-input>
            <template v-if="!isAllDisabled" v-slot:right>
              <view
                v-if="formItem?.slotText"
                :class="[
                  'right_slot_text',
                  formItem?.slotTextDis ? 'disabeld_slot_text' : '',
                ]"
                @click="onRightSlot"
                >{{ formItem?.slotText }}</view
              >
              <uv-icon
                v-if="formItem?.slotIcon"
                :name="formItem?.slotIcon"
                @click="onRightSlot"
              ></uv-icon>
            </template>
          </uv-form-item>
          <!-- upload组件 -->
          <uv-form-item
            v-if="formItem.type === 'upload'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="{
              ...formItemStyle,
              minHeight: formItem.formItemHeight || '304rpx',
            }"
          >
            <uv-upload
              :name="formItem.unionKey"
              :maxCount="formItem.maxCount"
              :fileList="formItem.fileList.value"
              @afterRead="
                afterRead($event, formItem.unionKey, formItem.fileList.value)
              "
              @delete="deletePic($event, formItem.fileList.value)"
              :previewFullImage="true"
            ></uv-upload>
          </uv-form-item>
          <!-- 纯文本展示组件 -->
          <uv-form-item
            v-if="formItem.type === 'disText'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="{
              background: '#FFF',
              boxSizing: 'border-box',
              padding: '24rpx 44rpx',
              borderBottom: 'none',
            }"
          >
            <view
              class="dis_text"
              :style="{
                fontSize: '28rpx',
                color: getValue(formItem.unionKey) ? '#909090' : '#C1C1C1',
              }"
              @touchmove.stop="handleTouchMove"
              >{{ getValue(formItem.unionKey) || formItem.placeholder }}</view
            >
          </uv-form-item>
          <!-- 纯图片展示组件 -->
          <uv-form-item
            v-if="formItem.type === 'additionalImg'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="additionalImgItemStyle"
          >
            <template v-if="!isAllDisabled" v-slot:right>
              <view class="additional_img">
                <image class="step_item_icon" :src="formItem.urlPath"></image>
              </view>
            </template>
          </uv-form-item>
          <!-- 级联数据选择组件 -->
          <uv-form-item
            class="tree_picker_formItem"
            v-if="formItem.type === 'treeSelect'"
            :prop="formItem.unionKey"
            :label="formItem.label"
            borderBottom
            :customStyle="formItemStyle"
          >
            <view class="toast_msg" v-if="!formItem.hasOptions">{{
              formItem.toastMsg
            }}</view>
            <uni-data-picker
              v-else
              class="tree_picker"
              :ellipsis="true"
              popup-title="请选择"
              :clear-icon="false"
              :readonly="props.isAllDisabled || formItem.disabled"
              :localdata="formItem.options"
              v-slot:default="{ data, error }"
              :value="
                getValue(
                  isFromDetail ? formItem.unionKeyDetail : formItem.unionKey
                )
              "
              :map="formItem.map"
              @change="(e) => toTreeSelect(formItem, e, false)"
              @touchmove.stop="handleTouchMove"
            >
              <view v-if="error" class="error">
                <text>{{ error }}</text>
              </view>
              <view v-else-if="data.length" class="tree_picker_box">
                <view
                  class="tree_picker_text"
                  :style="{ width: formItem.pickerTextW || '380rpx' }"
                  >{{ data.map((item) => item.text).join("/") }}</view
                >
                <uv-icon
                  v-if="!isAllDisabled && !formItem.disabled"
                  class="picker_arrow_icon"
                  name="arrow-right"
                  size="12"
                ></uv-icon>
              </view>
              <view v-else class="tree_picker_placeholder_box">
                <view class="tree_picker_placeholder">{{
                  formItem.placeholder
                }}</view>
                <uv-icon name="arrow-right" size="12"></uv-icon>
              </view>
            </uni-data-picker>
          </uv-form-item>
        </view>
      </view>
    </uv-form>
    <slot name="bottomBtns">
      <!-- 如果父组件未传入 bottomBtns 插槽，则显示此默认内容 -->
      <view class="bottom_btns">
        <view v-if="isAllDisabled" class="btn edit_btn" @click="toEdit"
          >编 辑</view
        >
        <template v-else>
          <uv-button
            v-if="customBtnText"
            :loading="btnLoading === '0'"
            :custom-style="primaryBtnStyle"
            :customTextStyle="btnTextStyle"
            :text="customBtnText"
            @click="submit(1)"
          ></uv-button>
          <template v-else>
            <uv-button
              :loading="btnLoading === '0'"
              :custom-style="btnStyle"
              :customTextStyle="btnTextStyle"
              :text="cancelBtnText"
              @click="submit(0)"
            ></uv-button>
            <uv-button
              :disabled="subBtnDisable"
              :loading="btnLoading === '1'"
              :custom-style="primaryBtnStyle"
              :customTextStyle="btnTextStyle"
              :text="confirmBtnText"
              @click="submit(1)"
            ></uv-button>
          </template>
        </template>
      </view>
    </slot>

    <!-- 底部选择弹窗 -->
    <!-- <uv-action-sheet ref="selectPop" round="24" :actions="selectActions" :title="selectPopTitle" @select="selectConfirm">
		</uv-action-sheet> -->
    <ylgDictsPicker
      ref="selectPop"
      :curChoosedKey="curChoosedItem"
      :unionKey="curSelKey"
      :title="selectPopTitle"
      :options="dictsOptions"
      @onSelect="selectConfirm"
    ></ylgDictsPicker>
    <uv-datetime-picker
      ref="datetimePicker"
      v-model="dateVal"
      mode="date"
      :minDate="0"
      :maxDate="maxDate"
      @confirm="confirmDate"
    >
    </uv-datetime-picker>
  </div>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref, reactive } from "vue";
import dayjs from "dayjs";
import ylgDictsPicker from "@/components/ylg-dicts-picker.vue";
import { uploadFilePromise, imgCompress, imgCompress2, showImg } from "@/utils";
// import ylgHelangCompress from '@/components/ylg-helang-compress';
// import llCompressImg from "../uni_modules/ll-compress-img/components/ll-compress-img/ll-compress-img"
const props = defineProps({
  isAllDisabled: {
    type: Boolean,
    required: true,
    default: false,
  },
  isFromDetail: {
    type: Boolean,
    required: true,
    default: false,
  },
  formConfig: {
    type: Object,
    required: true,
    default: () => {},
  },
  formData: {
    type: Object,
    required: true,
    default: () => {},
  },
  rules: {
    type: Object,
    required: true,
    default: () => {},
  },
  labelStyle: {
    type: Object,
    required: true,
    default: () => {},
  },
  formItemStyle: {
    type: Object,
    required: true,
    default: () => {},
  },
  placeholderStyle: {
    type: String,
    required: true,
    default: "",
  },
  customBtnText: {
    type: String,
    required: false,
    default: "",
  },
  cancelBtnText: {
    type: String,
    required: false,
    default: "暂 存",
  },
  confirmBtnText: {
    type: String,
    required: false,
    default: "提 交",
  },
  btnLoading: {
    type: String,
    required: true,
    default: "",
  },
});
// 动态设置校验文案的margin-left
let validMsgMarginL = ref("300rpx");
onLoad(() => {
  console.log("created", props.formData);
  if (props.labelStyle.width) {
    let num = 252 - Number(props.labelStyle.width.replace("rpx", ""));
    validMsgMarginL.value = `${300 - num}rpx`;
  }
});

const handleTouchMove = (event) => {
  // 阻止事件冒泡，不让 swiper 处理
  event.stopPropagation();
  emit("handleTouchMove", { e: event });
};

const additionalImgItemStyle = reactive({
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
  display: "flex",
  justifyContent: "end",
});
// 按钮样式
const btnStyle = reactive({
  width: "256rpx",
  height: "96rpx",
  lineHeight: "96rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 500,
  color: "#4378ff",
  background: " #fff",
  border: "2rpx solid #4378ff",
});
const primaryBtnStyle = reactive({
  width: props.customBtnText ? "670rpx" : "256rpx",
  height: "96rpx",
  lineHeight: "96rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 500,
  color: "#fff",
  background: "#4378ff",
});
const btnTextStyle = reactive({
  fontSize: "40rpx",
});

// 使用递归或路径分割来访问formData嵌套属性
const getValue = (unionKey) => {
  if (!unionKey) return false;
  let arr = unionKey.split(".");
  if (unionKey === "deviceTypeLabel") {
    console.log(
      "sweweweew",
      arr.reduce(
        (acc, key) => (acc && acc[key] !== "undefined" ? acc[key] : undefined),
        props.formData
      )
    );
  }
  return arr.reduce(
    (acc, key) => (acc && acc[key] !== "undefined" ? acc[key] : undefined),
    props.formData
  );
};

// select选择
let selectActions = ref([]);
let selectPopTitle = ref("");
let dictsOptions = ref([]);
let selectPop = ref(null);
let curSelKey = ref("");
let backShowKel = ref("");
const toSelect = (item) => {
  // 查看详情状态，整个表单禁用，不可编辑；
  if (props.isAllDisabled || item.disabled) return;
  // 主要针对选择桩号，需要所属路段id，若无，则不允许选择桩号；
  // if (item.isNeedSectionId && !props.formData.sectionId) {
  //   uni.showToast({
  //     icon: "none",
  //     title: item.toastMsg,
  //   });
  //   return;
  // }
  // 针对需要关联选择的场景，必须上一个选择之后，当前项才能选择
  if (item.isNeedPathKeyValue && !getValue(item.pathKey)) {
    uni.showToast({
      icon: "none",
      title: item.toastMsg,
    });
    return;
  }
  curSelKey.value = item.unionKey;
  backShowKel.value =
    item.type === "select" && props.isFromDetail
      ? item.unionKeyDetail
      : item.unionKey;
  console.log("选择", curSelKey.value);
  console.log("选择options", item.options);
  // 跳转页面选择
  if (item.optionsPath) {
    console.log("选择111", item);
    // let urlParams = new URLSearchParams();
    // urlParams.append("unionKey", item.unionKey);
    // urlParams.append("curItemName", getValue(backShowKel.value));
    // urlParams.append(
    //   "curItemId",
    //   getValue(curSelKey.value.replace("Label", ""))
    // );

    // /pages/dataAcquisition/relationInfoChoose/pileNumberChoose?noNew=true
    let strArr = [];
    let urlStr = "";
    if (item.optionsPath.indexOf("?")) {
      strArr = item.optionsPath.split("?");
    }

    // 处理optionsPath中本身就携带了页面参数的情况
    if (strArr.length > 1) {
      urlStr = `${strArr[0]}?unionKey=${item.unionKey}&curItemName=${getValue(
        backShowKel.value
      )}&curItemId=${getValue(curSelKey.value.replace("Label", ""))}&${
        strArr[1]
      }`;
    } else {
      urlStr = `${item.optionsPath}?unionKey=${
        item.unionKey
      }&curItemName=${getValue(backShowKel.value)}&curItemId=${getValue(
        curSelKey.value.replace("Label", "")
      )}`;
    }

    item.pathKey &&
      getValue(item.pathKey) &&
      (urlStr = `${urlStr}&${item.pathKey}=${getValue(item.pathKey)}`);
    uni.navigateTo({
      // url: `${item.optionsPath}?${urlParams.toString()}`,
      url: urlStr,
    });
  } else if (item.options) {
    // 在当前页底部弹窗选择
    selectPopTitle.value = `请选择${item.label}`;
    dictsOptions.value = item.options;
    selectActions.value = item.options;
    curChoosedItem.value = `${getValue(curSelKey.value)}${getValue(
      curSelKey.value.replace(/Label$/, "")
    )}`;
    selectPop.value.open();
  }
};
// select弹窗事件
let curChoosedItem = ref("");
const selectConfirm = (e) => {
  console.log("选择", e);
  selectPop.value.close();
  // 处理select选择的label
  setNestedValue(props.formData, curSelKey.value.split("."), e.dictValue);
  // 处理select选择的key
  let str = curSelKey.value.replace(/Label$/, "");
  emit("onDictChange", { val: e.dictKey, unionKey: curSelKey.value });
  setNestedValue(props.formData, str.split("."), e.dictKey);
  curChoosedItem.value = `${getValue(curSelKey.value)}${e.dictKey}`;
  console.log("弹窗选择后", props.formData);
  formRef.value.validateField(curSelKey.value);
};

// 树级级联选择
const toTreeSelect = (item, e) => {
  try {
    console.log("toTreeSelect选择", item, e);
    // 针对需要关联选择的场景，必须上一个选择之后，当前项才能选择
    if (item.isNeedPathKeyValue && !getValue(item.pathKey)) {
      uni.showToast({
        icon: "none",
        title: item.toastMsg,
      });
      return;
    }
    curSelKey.value = item.unionKey;
    backShowKel.value =
      item.type === "treeSelect" && props.isFromDetail
        ? item.unionKeyDetail
        : item.unionKey;

    console.log("选择options", item.options);
    // 在当前页底部弹窗选择
    // 处理select选择的label
    let valueArr = [];
    let labelArr = [];
    e.detail.value.forEach((item) => {
      valueArr.push(item.value);
      labelArr.push(item.text);
    });
    setNestedValue(props.formData, curSelKey.value.split("."), valueArr);
    // 处理select选择的key
    let str = curSelKey.value.replace(/Label$/, "");
    emit("onDictChange", { val: valueArr, unionKey: curSelKey.value });
    setNestedValue(props.formData, str.split("."), valueArr);
    curChoosedItem.value = `${getValue(curSelKey.value)}$ valueArr}`;
    console.log("弹窗选择后", props.formData);
    formRef.value.validateField(curSelKey.value);
  } catch (error) {
    console.log("toTreeSelect error", error);
  }
};

// datePick事件
let datetimePicker = ref(null);
let dateVal = ref("");
let maxDate = ref(dayjs().valueOf());
const toPickDate = (item) => {
  curSelKey.value = item.unionKey;
  dateVal.value = getValue(item.unionKey);
  datetimePicker.value.open();
  if (item.noMaxLimit) {
    maxDate.value = dayjs().add(10, "year").valueOf();
  } else {
    maxDate.value = dayjs().valueOf();
  }
};
const confirmDate = (e) => {
  console.log("确定选择日期", e);
  datetimePicker.value.close();
  let dateStr = dayjs(e.value).format("YYYY-MM-DD");
  setNestedValue(props.formData, curSelKey.value.split("."), dateStr);
};

// 设置formData嵌套属性
const setNestedValue = (obj, path, value) => {
  console.log("赋值时的formConfig1", props.formConfig);
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
};

// 触发数据修改
const emit = defineEmits([
  "onChange",
  "onEdit",
  "onSubmit",
  "onRightSlot",
  "onDictChange",
  "handleTouchMove",
]);
const onChange = (val, unionKey, inputType, maxlen) => {
  function truncateToTwoDecimals(num) {
    const str = num.toString();
    const decimalIndex = str.indexOf(".");
    if (decimalIndex === -1) {
      return num; // 如果没有小数点，直接返回
    }
    return str.slice(0, decimalIndex + 3); // 截取小数点后两位
  }
  if (inputType === "number" && val) {
    // console.log('格式化',val);
    // val = Number(val).toFixed(2);
    // console.log('格式化后',val);
    // v1.3.0 2025年3月27日 修改为 99999.99 - 不特殊说明按此统一标准
    const limitNum = 99999.99;
    if (val > limitNum) {
      val = limitNum;
    } else {
      val = truncateToTwoDecimals(val);
    }
    setTimeout(() => {
      emit("onChange", { val, unionKey });
    }, 0);
  } else if (inputType === "textarea") {
    val = val.detail.value;
    emit("onChange", { val, unionKey });
  } else {
    emit("onChange", { val, unionKey });
  }
  console.log("onChange", val, typeof val, unionKey);
};
// inputType === "number"的情况下去触发父组件更新
const onKeyInput = (val, unionKey, inputType) => {
  if (inputType === "number" && val) {
    // console.log("onInput", val, unionKey);
    emit("onChange", { val, unionKey });
  }
};
// 删除图片
let uploadUnionkey = ref("");
// let fileList = ref([]);
const deletePic = (event, fileList) => {
  fileList.splice(event.index, 1);
  uploadUnionkey.value = event.name;
  formRef.value.validateField(uploadUnionkey.value);
  console.log("删除图片", event, uploadUnionkey.value);
};
let subBtnDisable = ref(false);
// 上传图片
const afterRead = async (event, unionKey, fileList) => {
  console.log("afterRead图片", event, unionKey);
  uploadUnionkey.value = unionKey;
  // // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  let lists = [].concat(event.file);
  let fileListLen = fileList.length;
  subBtnDisable.value = true;
  lists.map((item) => {
    fileList.push({
      ...item,
      status: "uploading",
      message: "上传中",
    });
  });
  console.log("打印lists", lists);
  let abc1 = ref(null);
  for (let i = 0; i < lists.length; i++) {
    try {
      // const compressRes = await imgCompress(lists[i].url);
      const compressRes = await imgCompress2(lists[i].url);
      let itemUrl =
        compressRes.code === "1" ? compressRes.imgUrl : lists[i].url;
      const { code, data } = await uploadFilePromise(itemUrl);
      console.log("上传图片结果", data);
      if (code == 200) {
        let item = fileList[fileListLen];
        // 处理字段名
        item.size = item.size / 1024 / 1024;
        data.newName = data.name;
        data.url = showImg(data.url);
        data.path = data.url;
        delete data.size;
        fileList.splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: "success",
            message: "",
            ...data,
            // url: showImg(lists[i].url)
          })
        );
        fileListLen++;
        console.log("afterRead", fileList);

        setNestedValue(props.formData, unionKey.split("."), fileList);
        formRef.value.validateField(uploadUnionkey.value);
        console.log("上传图片后", fileList);

        let uploadingItem = fileList.find(
          (item) => item.status === "uploading"
        );
        if (!uploadingItem) {
          subBtnDisable.value = false;
        }
      }
    } catch (error) {
      console.log("上传结果返回，上传失败", fileList);
      fileList.splice(fileListLen, 1);
      let uploadingItem = fileList.find((item) => item.status === "uploading");
      if (!uploadingItem) {
        subBtnDisable.value = false;
      }
      uni.showToast({
        icon: "none",
        title: "上传图片失败，请稍后重试~",
      });
    }
  }
};

// 提交/暂存
const formRef = ref(null);
const submit = (status) => {
  console.log("提交", props.formData, props.isFromDetail);

  if (status === 0) {
    emit("onSubmit", { data: props.formData, status });
  } else {
    formRef.value
      .validate()
      .then((res) => {
        emit("onSubmit", { data: props.formData, status });
      })
      .catch((errors) => {
        console.log("校验失败", errors);
      });
  }
};

// 自定义组件，点击右侧插槽内容时触发
const onRightSlot = () => {
  emit("onRightSlot");
};

// 编辑
const toEdit = () => {
  emit("onEdit");
};

defineExpose({
  formRef,
});
</script>
<style lang="scss" scoped>
.info_card {
  background: #fff;
  margin-bottom: 48rpx;
  .info_title {
    display: flex;
    align-items: center;
    height: 88rpx;
    padding: 22rpx 40rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #404040;
    line-height: 44rpx;
    box-sizing: border-box;
    border-bottom: 2rpx solid #f0f0f0;
    .card_bar {
      width: 6rpx;
      height: 28rpx;
      border-radius: 4rpx;
      background: #4378ff;
      margin-right: 12rpx;
    }
  }
}
:deep(.input-placeholder) {
  color: #c1c1c1;
}
.bottom_btns {
  position: fixed;
  bottom: 0rpx;
  width: 100vw;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 40rpx;
  background: #f4f8ff;
  box-shadow: -8rpx 0rpx 20rpx 0 rgba(0, 0, 0, 0.1);
  .btn {
    width: 256rpx;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    box-sizing: border-box;
    border-radius: 8rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .save_btn {
    color: #4378ff;
    background: #fff;
    border: 2rpx solid #4378ff;
  }
  .sub_btn {
    color: #fff;
    background: #4378ff;
  }
  .edit_btn {
    width: 670rpx;
    height: 84rpx;
    background: #4378ff;
    color: #fff;
  }
}

.right_slot_text {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #4378ff;
  line-height: 40rpx;
}
.disabeld_slot_text {
  color: #909090;
}
:deep(.uni-input-input) {
  color: #909090;
}
:deep(.uni-textarea-textarea) {
  color: #909090;
}
:deep(.uv-form-item__body) {
  align-items: center;
}
:deep(.uv-form-item__body__right__message) {
  // margin-left: 300rpx !important;
  margin-left: v-bind(validMsgMarginL) !important;
}
.main_inp {
  :deep(.uni-input-input) {
    color: #404040;
  }
}
.additional_img {
  width: 260rpx;
  height: 260rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
.toast_msg {
  color: #c1c1c1;
  margin-left: 8rpx;
  font-size: 28rpx;
  line-height: 48rpx;
  font-weight: 400;
}
.tree_picker_formItem {
  :deep(.uv-form-item__body__right) {
    width: calc(100% - 6.5rem);
    white-space: nowrap !important;
    overflow-x: scroll;
  }
}

.tree_picker {
  width: 100%;
  white-space: nowrap !important;
  overflow-x: scroll;
  .tree_picker_box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 6rpx;
    position: relative;
    .tree_picker_text {
      width: 380rpx;
      white-space: nowrap !important;
      overflow-x: scroll;
      color: #909090;
    }
    .picker_arrow_icon {
      position: absolute;
      right: 2rpx;
    }
  }
  .tree_picker_placeholder_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 6rpx;
    .tree_picker_placeholder {
      color: #c1c1c1 !important;
      font-size: 28rpx !important;
    }
  }
  :deep(.input-value) {
    // flex: 1;
    width: 100%;
    // height: 100%;
    padding: 0;
    // .selected-area {
    //   .selected-list {
    //     .selected-item {
    //       .text-color {
    //         color: #909090 !important;
    //         white-space: nowrap;
    //       }
    //     }
    //   }
    // }
    // placeholderStyle="fontSize: 32rpx;color:#8e8e8e;"
    // font-size: 32rpx !important;
  }
}
:deep(.uni-data-pickerview) {
  .selected-area {
    .selected-item {
      max-width: 112rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      uni-text {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
</style>
