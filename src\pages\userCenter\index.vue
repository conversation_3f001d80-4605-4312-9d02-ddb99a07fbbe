<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="个人中心"
      leftIcon=""
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      :autoBack="false"
      bgColor="#065BFF"
    >
    </uv-navbar>
    <view class="top-bg"></view>
    <view class="content">
      <!-- 背景颜色 -->
      <div class="user-msg">
        <image
          class="userFace"
          v-if="userInfo?.userFace"
          :src="showImg(userInfo.userFace)"
          mode="widthFix"
        />
        <view class="default-avater" v-else>{{ shortUserName }}</view>
        <view class="user-account">
          <view class="name">
            <view class="userFullName">
              {{ userInfo.userFullName }}
            </view>
            <view class="postName" v-if="userInfo && userInfo.postName">
              {{ userInfo.postName }}
            </view>
          </view>
          <view class="time">
            {{ getGreeting() }}
          </view>
        </view>
      </div>
      <view class="menu_list">
        <view
          class="menu_item"
          v-for="(item, index) of menu"
          :key="index"
          @click="onClickMenu(item)"
        >
          <view class="left-name">
            <image class="icon" :src="item.icon" mode="widthFix" />
            <view class="name">
              {{ item.name }}
            </view>
          </view>
          <view class="right-name">
            <view class="icon-tip">
              <image
                class="icon-img"
                v-if="item.tip"
                :src="item.tip"
                mode="widthFix"
              />
            </view>
            <view class="tenant-name">
              {{ item.tenantName }}
            </view>
            <image
              v-if="item.path"
              class="icon-down"
              src="../../static/icon/gary-down.png"
              mode="widthFix"
            />
          </view>
        </view>
      </view>
      <view class="log-out" @click="open">退出登录</view>
      <!-- <view class="show-img-box">
        <image
          class="show-img"
          src="../../static/image/user-no-img.png"
          mode="widthFix"
        />
        <view class="show-tip"> 道路养护，有您更美好！ </view>
      </view> -->
    </view>
    <!-- 退出登录弹窗 -->
    <uv-popup ref="popupRef" mode="bottom" round="16">
      <view class="bottom-popup">
        <uv-button
          :custom-style="endBtnStyle"
          :customTextStyle="btnTextStyle"
          text="退出登录"
          @click="logout"
        ></uv-button>
        <uv-button
          :custom-style="startBtnStyle"
          :customTextStyle="btnTextStyle"
          text="取消"
          @click="closePopus"
        ></uv-button>
      </view>
    </uv-popup>
    <ylg-tabBar />
  </view>
</template>

<script setup>
import { onBackPress, onShow } from "@dcloudio/uni-app";
import { computed, reactive, ref } from "vue";
import { useUserStore } from "@/store/user";
import { showImg, getSysteminfo } from "@/utils";
import dayjs from "dayjs";
const userInfo = useUserStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import reset_password_icon from "/static/icon/reset_password_icon.png";
import tenant_icon from "/static/icon/tenant_icon.png";
import system_version from "/static/icon/system_version.png";
import version_tip from "/static/icon/version_tip.png";
import { VersionService } from "@/service";
import { useTabBarStore } from "@/store/tabBar"; // 引入状态管理
const tabBarStoreInfo = useTabBarStore();
import { useSystemStore } from "@/store/system";
const systemInfo = useSystemStore();
console.log("userInfo", userInfo);
const shortUserName = ref("");
shortUserName.value = userInfo.userFullName.slice(-2);

// 获取手机底部安全距离
const systemBottomSafeArea = `${
  Number(getSysteminfo().bottomSafeArea) * 2 || 40
}rpx`;

const getGreeting = () => {
  const hour = dayjs().hour(); // 获取当前小时 (0-23)
  if (hour >= 6 && hour < 12) {
    return "上午好～";
  } else if (hour >= 12 && hour < 18) {
    return "下午好～";
  } else {
    return "晚上好～";
  }
};
onShow(() => {
  tabBarStoreInfo.updateActiveTab(1);
  // 4.15号的版本不上版本更新
  hasVersion();
});
// 禁止左右划出当前页
onBackPress(() => {
  return true;
});
// 展示新版本icon
const showNewUpdateTip = ref(false);
const showNowVersion = ref("");
const hasVersion = async () => {
  showNewUpdateTip.value = false;
  const { sysVersion, appVersion, sysPlatform } = uni.getStorageSync("version");
  showNowVersion.value = sysVersion || appVersion;
  const { code, data } = await VersionService.isAppUpdateAvailable({
    business: "yhPublishPlatform",
    version: showNowVersion.value,
    publishPlatform: sysPlatform || process.env.APP_DESCRIPTION,
    publishPlatforms: uni.getStorageSync("platforms").join(','),
  });
  if (code === 200 && data) {
    console.log(data, "data");
    const { version } = data;
    if (version) {
      showNewUpdateTip.value = true;
    }
  }
  console.log("个人中心-icon显示", showNewUpdateTip.value);
};
const menu = computed(() => {
  return [
    {
      name: "重置密码",
      icon: reset_password_icon,
      action: "goPage",
      path: "/pages/login/editPwd/checkPhone?scene=resetPwd",
    },
    {
      name: "组织切换",
      icon: tenant_icon,
      action: "goPage",
      path: `/pages/home/<USER>
      tenantName: userInfo.tenantName || "组织名称",
    },
    {
      name: "系统版本",
      icon: system_version,
      action: "goPage",
      path: `/pages/userCenter/SystemVersion?tenantId=${userInfo.tenantId}&pageType=change`,
      tenantName: `版本号：${showNowVersion.value}`,
      tip: showNewUpdateTip.value ? version_tip : "",
    },
  ];
});

// 退出登录
const logout = async () => {
  // 退出登录前，清空项目信息
  await projectInfo.updateProject({
    projectId: "",
    projectName: "",
    projectPermission: "",
  });
  systemInfo.updateSystemCode([]);
  userInfo.logout();
};

const onClickMenu = (item) => {
  if (item.action === "goPage") {
    uni.navigateTo({
      url: item.path,
    });
  }
};
const popupRef = ref();
const open = () => {
  popupRef.value.open();
};
const closePopus = () => {
  popupRef.value.close();
};

const btnTextStyle = {
  fontSize: "40rpx",
};
const endBtnStyle = {
  width: "100%",
  height: "100rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  marginBottom: "40rpx",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378FF",
  color: " #fff",
};
const startBtnStyle = {
  width: "100%",
  height: "100rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#fff",
  border: "2rpx solid #4378ff",
  color: " #4378FF",
};
</script>

<style lang="scss" scoped>
.bottom-popup {
  padding: 76rpx 40rpx 68rpx 40rpx;
}
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  background: #f4f8ff;
  .top-bg {
    position: absolute;
    top: 84rpx;
    width: 100vw;
    background: #065bff;
    width: 100%;
    height: 160rpx;
  }
}

.uv-nav-slot {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}

.content {
  position: relative;
  top: 0rpx;
  // padding: 0 40rpx;
  box-sizing: border-box;
  height: calc(100% - 208rpx);
  // margin-top: 84rpx;
  .user-msg {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: absolute;
    left: 50%;
    top: 160rpx;
    transform: translate(-50%, 0);
    padding: 36rpx 40rpx;
    border-radius: 24rpx;
    box-sizing: border-box;
    width: calc(100% - 80rpx);
    height: 192rpx;
    background: #fff;
    box-shadow: 4rpx 4rpx 20rpx 0px rgba(0, 0, 0, 0.08),
      -4rpx -4rpx 20rpx 0px rgba(0, 0, 0, 0.08);

    .userFace {
      width: 120rpx;
      height: 120rpx !important;
      border-radius: 50%;
      overflow: hidden;
    }
    .default-avater {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      text-align: center;
      line-height: 120rpx;
      background: rgba(77, 138, 255, 1);
      font-size: 36rpx;
      font-weight: 600;
      color: #fff;
    }

    .user-account {
      margin-left: 40rpx;
      flex: 1;
      .name {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 28rpx;

        .userFullName {
          max-width: 218rpx;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
          color: #373737;
          font-size: 36rpx;
          margin-right: 28rpx;
        }

        .postName {
          background: #f2f2f2;
          border-radius: 4rpx;
          padding: 4rpx 10rpx;
          color: #a09f9f;
          font-size: 24rpx;
        }
      }

      .time {
        color: #8e8e8e;
        font-size: 28rpx;
      }
    }
  }
  .menu_list {
    padding-top: 386rpx;
  }
  .menu_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    padding: 22rpx 40rpx;
    margin-bottom: 14px;
    .left-name {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 16rpx;
      }

      .name {
        color: #404040;
        font-size: 32rpx;
      }
    }
    .right-name {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .icon-tip {
        width: 36rpx;
        height: 36rpx;
        margin-right: 8rpx;
        .icon-img {
          width: 36rpx;
          height: 36rpx;
        }
      }
      .tenant-name {
        color: #a09f9f;
        font-size: 28rpx;
        font-family: PingFang SC-Regular;
        margin-right: 16rpx;
        line-height: 28rpx;
        max-width: 400rpx;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .icon-down {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
  .log-out {
    width: 100%;
    background: rgba(255, 255, 255, 1);
    text-align: center;
    padding: 24rpx 0;
    box-sizing: border-box;
    color: #404040;
    font-size: 32rpx;
    line-height: 44rpx;
  }
  .show-img-box {
    margin-top: 160rpx;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .show-img {
      width: 486rpx;
      height: 444rpx;
    }

    .show-tip {
      color: #d0d0d0;
      font-size: 36rpx;
      text-align: center;
      margin-top: 48rpx;
    }
  }

  .btn-box {
    width: 100%;
    position: fixed;
    bottom: 0;
    box-sizing: border-box;
    padding: 40rpx 40rpx v-bind(systemBottomSafeArea) 40rpx;
  }
}
</style>
