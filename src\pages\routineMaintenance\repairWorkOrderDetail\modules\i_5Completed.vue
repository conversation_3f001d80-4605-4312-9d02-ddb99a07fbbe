<template>
  <view class="completed-container">
    <view class="completed-content">
      <view class="content-wrapper">
        <ConstructionCard
          :base-info="repairWorkOrderBaseInfo"
          :detail-info="repairWorkOrderDetail"
          :child-order-list="repairChildWorkOrderList"
          :config-detail="workOrderConfigDetail"
          :is-show-bar="true"
          :is-show-mark="true"
          :is-pass="true"
        />

        <view class="review-wrap">
          <ReviewRecordCard
            :base-info="repairWorkOrderBaseInfo"
            :check-log-list="repairWorkOrderDetail.vericaContent || []"
            @go-acceptance-measurement="handleGoAcceptance"
          />
          <TaskCompletionCard
            :complete-info="childWorkOrderCompleteInfo"
            @go-detail="handleGoDetail"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import ConstructionCard from "../components/card/ConstructionCard.vue";
import ReviewRecordCard from "../components/card/ReviewRecordCard.vue";
import TaskCompletionCard from "../components/card/TaskCompletionCard.vue";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 工单详情信息
  repairWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Array,
    default: () => [],
  },
  // 工单配置详情
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单完成信息
  childWorkOrderCompleteInfo: {
    type: Object,
    default: () => ({}),
  },
});

/**
 * 跳转到验收详情页
 * @param {number} index - 验收项索引
 */
const handleGoAcceptance = (index) => {
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/acceptanceMeasurement/acceptanceDetail?isViewMode=true&index=${index}`,
  });
};

/**
 * 跳转到子工单详情页
 * @param {Object} info - 工单信息
 */
const handleGoDetail = (info) => {
  const id = props.repairWorkOrderBaseInfo?.id;
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/childWorkOrderDetail/index?workId=${id}&childWorkId=${info.childWorkId}`,
  });
};
</script>

<style lang="scss" scoped>
.completed-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f4f8ff;

  .completed-content {
    flex: 1;
    overflow-y: auto;

    .content-wrapper {
      padding: 0 40rpx;
    }

    .review-wrap {
      margin-top: 40rpx;
      display: flex;
      flex-direction: column;
      gap: 40rpx;
    }
  }
}
</style>
