<template>
  <view class="container">
    <!-- 地图组件 -->
    <view>
      <web-view
        class="webview_box"
        :src="webviewMapUrl"
        @onPostMessage="handlePostMessage"
        @message="handlePostMessage"
        ref="webview"
        :webview-styles="webviewStyles"
      >
      </web-view>
    </view>
    <view v-if="!isDetail" class="search_bar" @click="toSearchAddress">
      <uv-icon name="search" color="#A09F9F" size="22"></uv-icon>
      <view class="search_text">地点搜索</view>
      <!-- <view class="search_text">定位次数：{{getLocationCount}}，{{ myPosition.longitude }}，{{ myPosition.latitude }}</view> -->
    </view>
    <view class="bottom_btn">
      <template v-if="!isDetail">
        <view class="cur_address_desc">已选地址：{{ myPosition.address }}</view>
        <view class="comfrim_btn" @click="confirmAddress">确 定</view>
      </template>
      <template v-else>
        <view class="detail_address">
          <view class="cur_address_desc mb0">{{ myPosition.address }}</view>
          <image class="img" @click="goMap" :src="to_navigation_icon" />
        </view>
      </template>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref, computed } from "vue";
import { DataAcquisitionService } from "@/service";
import { onLoad, onReady, onShow } from "@dcloudio/uni-app";
import {
  getCurLocation,
  reverseGeocode,
  reTransformLngLat,
} from "@/utils/location";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { systemInfo } from "@/static/system.js";
const { uniPlatform, platform } = systemInfo();
import { getSysteminfo } from "@/utils";
import to_navigation_icon from "@/static/icon/to_navigation_icon.png";
// webview实例
const pages = getCurrentPages();
let wv = ref(null);

const handlePostMessage = (data) => {
  console.log("webview天地图发来的数据", data.detail.data[0]);
  uni.setNavigationBarTitle({
    // title: isDetail.value ? "事件详情" : "定位拾取",
    title: "查看定位",
  });
  switch (data.detail.data[0].action) {
    // 地图被拖动，中心点发生变化
    case "moveMap":
      console.log("data.detail.data[0].action", data.detail.data[0].center);
      myPosition.latitude = data.detail.data[0].center.lat;
      myPosition.longitude = data.detail.data[0].center.lng;
      flagStr.value += "g";
      break;
    // 得到逆解析中文地址
    case "reverseGeocode":
      if (!isDetail.value) {
        myPosition.address = data.detail.data[0].address;
      }
      break;
    case "resetClick":
      getLocation();
      break;
    default:
      break;
  }
};

// 获取当前定位
let myPosition = reactive({
  latitude: "",
  longitude: "",
  address: "",
});

let mapTop = ref(`0px`);
let mapHeight = ref(`1200rpx`);
let webviewMapUrl = ref("");
let curFormItemKey = ref("");
let isDetail = ref(false);
let flagStr = ref(""); // 用于判断当前的地址是拖动地图得到的（高德地图返回的），还是搜索列表里选择的（天地图api返回的）
onLoad((options) => {
  console.log("查看地址options", options);
  curFormItemKey.value = options?.unionKey;
  // isDetail.value = options?.isDetail || false;
  isDetail.value = options?.isDetail === "true" ? true : false;
  // 查看详情
  if (isDetail.value) {
    mapTop.value = `0px`;
    mapHeight.value = `75%`;
    webviewMapUrl.value = `./static/tianditu/map.html?isDetail=${isDetail.value}&longitude=${options.curLongitude}&latitude=${options.curLatitude}&pageFrom=coordinatePicking&ControlTop=85vh`;
    myPosition.longitude = options.curLongitude;
    myPosition.latitude = options.curLatitude;
    myPosition.address = options.curAddress;
  } else {
    // 测试清除上一次缓存数据
    myPosition.longitude = "";
    myPosition.latitude = "";
    mapTop.value = `40px`;
    mapHeight.value = `70%`;
    webviewMapUrl.value = `./static/tianditu/map.html?isDetail=${isDetail.value}&pageFrom=coordinatePicking&ControlTop=85vh`;
  }
});

const getWebView = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const webView = pages[pages.length - 1].$getAppWebview().children()[0];
      console.log("webView", webView);
      resolve(webView);
    }, 1000);
  });
};

onReady(async () => {
  wv.value = await getWebView();
  console.log("wv.value", wv.value);
  if (isDetail.value) {
    uni.setNavigationBarTitle({
      title: "查看定位",
    });
    // 通知webview更新定位
    // wv.value = pages[pages.length - 1].$getAppWebview().children()[0];
    wv.value.evalJS(
      `receiveData({type:'changeLocation',data:{lng:${myPosition.longitude},lat:${myPosition.latitude}}})`
    );
  } else {
    uni.setNavigationBarTitle({
      title: "定位拾取",
    });
    getLocation();
  }
});

onShow(async () => {
  let storagePosition = JSON.parse(uni.getStorageSync("myPosition") || "{}");
  console.log("storagePosition!!", storagePosition);
  let { type, longitude, latitude, address } = storagePosition;
  if (type === "coordinatePicking" && longitude && latitude) {
    myPosition.longitude = String(longitude);
    myPosition.latitude = String(latitude);
    myPosition.address = address;
    flagStr.value += "t";
    // 通知webview更新定位
    wv.value.evalJS(
      `receiveData({type:'changeLocation',data:{lng:${myPosition.longitude},lat:${myPosition.latitude}}})`
    );
    uni.removeStorageSync("myPosition");
  }
});

const toSearchAddress = () => {
  uni.navigateTo({
    url: "/pages/dataAcquisition/relationInfoChoose/addressChoose?pageFrom=coordinatePicking",
  });
};
let getLocationCount = ref(0);
const getLocation = async () => {
  // 定位开启状态 true=开启，false=未开启
  let bool = false;
  // android平台
  if (uni.getSystemInfoSync().platform == "android") {
    var context = plus.android.importClass("android.content.Context");
    var locationManager = plus.android.importClass(
      "android.location.LocationManager"
    );
    var main = plus.android.runtimeMainActivity();
    var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
    bool = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER);

    uni.getLocation({
      // type: "gcj02", //返回可以用于uni.openLocation的经纬度
      type: "wgs84", //返回可以用于uni.openLocation的经纬度
      success: function (res) {
        myPosition.latitude = res.latitude;
        myPosition.longitude = res.longitude;
        console.log("获取定位成功", wv.value);
        // 通知webview更新定位
        wv.value = pages[pages.length - 1].$getAppWebview().children()[0];
        // wv.value.evalJS(`receiveData({type:'initLocation',data:{lng:${res.longitude},lat:${res.latitude}}})`);
        // wv.value.evalJS(`receiveData({type:'initLocation',data:{}})`);
        wv.value.evalJS(
          `receiveData({type:'initLocation',data:{type: 'getPosition',lng:${myPosition.longitude},lat:${myPosition.latitude}}})`
        );
        getLocationCount.value++;
      },
    });
  }

  // 未开启定位功能
  if (bool === false) {
    uni.showModal({
      title: "提示",
      content: "请打开定位服务",
      success: ({ confirm, cancel }) => {
        if (confirm) {
          // android平台
          if (uni.getSystemInfoSync().platform == "android") {
            var Intent = plus.android.importClass("android.content.Intent");
            var Settings = plus.android.importClass(
              "android.provider.Settings"
            );
            var intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            var main = plus.android.runtimeMainActivity();
            main.startActivity(intent); // 打开系统设置GPS服务页面
          }
          // ios平台
          if (uni.getSystemInfoSync().platform == "ios") {
            var UIApplication = plus.ios.import("UIApplication");
            var application2 = UIApplication.sharedApplication();
            var NSURL2 = plus.ios.import("NSURL");
            var setting2 = NSURL2.URLWithString(
              "App-Prefs:root=Privacy&path=LOCATION"
            );
            application2.openURL(setting2);
            plus.ios.deleteObject(setting2);
            plus.ios.deleteObject(NSURL2);
            plus.ios.deleteObject(application2);
          }
        }
        // 用户取消前往开启定位服务
        if (cancel) {
          console.log("用户取消前往开启定位服务");
          // do sth...
        }
      },
    });
  }
};

// 确定选择的地址
const confirmAddress = () => {
  if (!myPosition.address) {
    uni.showToast({
      icon: "none",
      title: "请选择地址~",
    });
    return;
  }
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: curFormItemKey.value,
      value: myPosition.address,
      label: `${myPosition.longitude},${myPosition.latitude}`,
    },
  });
  uni.navigateBack({
    delta: 1,
  });
};

// 唤起本机地图软件
let curPosition = reactive({
  longitude: "",
  latitude: "",
});
const goMap = async () => {
  if (!myPosition.latitude || !myPosition.longitude || !myPosition.address) {
    uni.showToast({ title: "定位信息不完整", icon: "none" });
    return;
  }

  // 获取用户当前所处位置
  let locationRes = await getCurLocation();
  if (locationRes.errMsg == "getLocation:ok") {
    curPosition.longitude = locationRes.longitude;
    curPosition.latitude = locationRes.latitude;
  }

  // 定义地图应用的名称和对应的包名/URL Scheme
  const mapNames = [
    {
      title: "高德地图",
      name: "amap",
      androidName: "com.autonavi.minimap",
      iosName: "iosamap://",
    },
    {
      title: "百度地图",
      name: "baidumap",
      androidName: "com.baidu.BaiduMap",
      iosName: "baidumap://",
    },
    {
      title: "腾讯地图",
      name: "qqmap",
      androidName: "com.tencent.map",
      iosName: "qqmap://",
    },
  ];

  // 获取系统信息
  const platform = uni.getSystemInfoSync().platform;

  // 判断设备上已安装的地图应用
  const buttons = mapNames.filter((item) => {
    return platform === "android"
      ? plus.runtime.isApplicationExist({ pname: item.androidName })
      : platform === "ios" &&
          plus.runtime.isApplicationExist({ action: item.iosName });
  });

  if (buttons.length) {
    // 显示选择菜单
    uni.showActionSheet({
      itemList: buttons.map((item) => item.title),
      success: async (res) => {
        const selectedMap = buttons[res.tapIndex];
        await openURL(selectedMap, platform);
      },
      fail: (error) => {
        if (error.errMsg.indexOf("cancel")) {
          console.log("取消", error);
        } else {
          uni.showToast({ title: "操作失败~", icon: "none" });
        }
      },
    });
  } else {
    uni.showToast({ title: "请安装地图软件", icon: "none" });
  }
};
// 打开指定的地图应用
const openURL = async (map, platform) => {
  const urls = {
    android: {
      amap: `amapuri://route/plan/?sid=&did=&dlat=${myPosition.latitude}&dlon=${myPosition.longitude}&dname=${myPosition.address}&dev=0&t=0`,
      baidumap: `baidumap://map/direction?origin=${curPosition.latitude},${curPosition.longitude}&destination=name:${myPosition.address}|latlng:${myPosition.latitude},${myPosition.longitude}&coord_type=wgs84&mode=driving`,
      qqmap: `qqmap://map/routeplan?type=drive&from=我的位置&to=${myPosition.address}&at=${myPosition.latitude},${myPosition.longitude}`,
    },
    ios: {
      amap: `iosamap://path?sourceApplication=myApp&dlat=${myPosition.latitude}&dlon=${myPosition.longitude}&dname=${myPosition.address}&dev=0&t=0`,
      baidumap: `baidumap://map/direction?origin=${curPosition.latitude},${curPosition.longitude}&destination=name:${myPosition.address}|latlng:${myPosition.latitude},${myPosition.longitude}&mode=driving`,
      qqmap: `qqmap://map/routeplan?type=drive&from=我的位置&to=${myPosition.address}&at=${myPosition.latitude},${myPosition.longitude}`,
    },
  };

  const url = encodeURI(urls[platform][map.name]);
  plus.runtime.openURL(
    url,
    (res) => {
      if (res.message) {
        uni.showModal({
          title: "错误",
          content: res.message,
        });
      }
    },
    map.androidName || ""
  );
};

const webviewStyles = computed(() => {
  return {
    position: "fixed",
    width: "100%",
    // height: '70%',
    height: mapHeight.value,
    top: mapTop.value,
    // top: '40px',
    left: "0",
  };
});
</script>

<style lang="scss" scoped>
.search_bar {
  position: absolute; /* 让搜索框悬浮在页面上 */
  top: 0; /* 调整到你需要的位置 */
  left: 0;
  z-index: 999;
  width: 100vw;
  height: 80rpx;
  display: flex;
  align-items: center;
  background: rgb(255, 255, 255);
  padding: 0 20rpx;
  color: #a09f9f;
  font-size: 28rpx;
}
.container {
  min-height: 100vh;
  background: #f4f8ff;
}

.bottom_btn {
  width: 100%;
  position: fixed;
  bottom: 0rpx;
  box-sizing: border-box;
  padding: 32rpx 40rpx 20rpx 40rpx;
  background: #f4f8ff;
  .cur_address_desc {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .mb0 {
    margin-bottom: 0rpx;
  }
  .comfrim_btn {
    width: 670rpx;
    height: 84rpx;
    box-sizing: border-box;
    background: #4378ff;
    border-radius: 8rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 600;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
  .plain_btn {
    margin-bottom: 28rpx;
    border: 2rpx solid #4378ff;
    background: #f4f8ff;
    color: #4378ff;
  }
  .detail_address {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 60rpx;
    .cur_address_desc {
      width: 520rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #4378ff;
      line-height: 40rpx;
    }
    .img {
      display: block;
      width: 72rpx;
      height: 72rpx;
    }
  }
}
.address_list {
  position: absolute;
  top: 80rpx;
  left: 0;
  padding: 0 24rpx;
  width: 100%;
  height: calc(100vh - 80rpx);
  box-sizing: border-box;
  background: #fff;
  .top_line {
    height: 2rpx;
    background: #d8d6d6;
  }
  .address_item {
    padding: 24rpx 0rpx;
    display: flex;
    justify-content: space-between;
    align-content: center;
    .left {
      .address_name {
        color: #373737;
        font-size: 32rpx;
      }
      .address_detail {
        color: #a09f9f;
        font-size: 22rpx;
        margin-top: 8rpx;
      }
    }
    .right {
      color: #666;
      font-size: 14rpx;
    }
  }
}
</style>
