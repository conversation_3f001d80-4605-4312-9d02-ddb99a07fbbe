<template>
  <scroll-view @touchmove.stop.prevent>
    <uv-modal
      ref="updateModalRef"
      title=""
      content=""
      :closeOnClickOverlay="false"
      :zIndex="9999999"
    >
      <view class="version-header">
        <view class="version-left"></view>
        <view class="version-right">
          <view>
            <view class="title">更新版本号</view>
            <view class="version">{{ baseInfo.version }}</view>
          </view>
        </view>
      </view>
      <view class="version-content">
        <view class="title">版本更新说明：</view>
        <scroll-view class="paragraphs" :scroll-y="true">
          <view
            v-for="(item, index) in paragraphs"
            :key="index"
            class="paragraph"
          >
            {{ item }}
          </view>
        </scroll-view>
      </view>
      <uv-loading-page
        :loading="pageLoading"
        loading-text="更新中..."
        font-size="24rpx"
      ></uv-loading-page>
      <template v-slot:confirmButton>
        <view class="btns-box">
          <!-- 根据是否强更来判断 -->
          <view
            v-if="baseInfo.apkPath && !baseInfo.mandatoryUpdate"
            class="btn ghost"
            @click="notPrompting"
            >不再提示</view
          >
          <view
            v-if="baseInfo.apkPath && !baseInfo.mandatoryUpdate"
            class="btn ghost"
            @click="notUpdating"
            >暂不更新</view
          >
          <!-- 必有 -->
          <view v-if="baseInfo.apkPath" class="btn primary" @click="updateNow"
            >立即更新</view
          >
          <!-- todo 安装包 -->
          <view v-if="!baseInfo.apkPath" class="btn primary" @click="IKnew"
            >我知道了</view
          >
        </view>
      </template>
    </uv-modal>
    <UpdateVersionModal
      ref="UpdateVersionModalRef"
      v-if="UpdateVersionModalVisible"
      :url="url"
      :baseInfo="baseInfo"
      @openModal="openModal"
    />
  </scroll-view>
</template>
<script setup>
import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
import { ref, computed, nextTick } from "vue";
// import { showImg } from "@/utils";
import UpdateVersionModal from "./UpdateVersionModal.vue";
import { VersionService } from "@/service";
import { useSystemStore } from "@/store/system";
const systemInfo = useSystemStore();

const pageLoading = ref(false);
const updateModalRef = ref();
const baseInfo = ref({});
const downloadTask = ref(null);
const UpdateVersionModalVisible = ref(false); // 全量更新modal
const url = ref("");
// 获取段落
const paragraphs = computed(() => {
  const ps = baseInfo.value.releaseNote?.split("\n") || [];
  return ps;
});
const UpdateVersionModalRef = ref(); // 更新弹框

// 处理版本更新时要传递的参数，系统模块编码list
const platformMenu = {
  4: "androidYangdaoApp",
  5: "androidShmApp",
  7: "androidComPlatformApp",
};
let sCode = systemInfo.systemCode || [];
console.log("查看当前登陆人的sysCode", sCode);

let platforms = ref([]);
const handlePlatforms = () => {
    console.log("处理平台参数", sCode);
  sCode.forEach((code) => {
    console.log('查看sysCode类型!!!!!!!!!', typeof code, Object.keys(platformMenu)[0], typeof Object.keys(platformMenu)[0]);
    
    if (Object.keys(platformMenu).includes(code)) {
      platforms.value.push(platformMenu[code]);
    }
  });
  if ((sCode.includes("4") || sCode.includes("5")) && !sCode.includes("7")) {
    platforms.value.push(platformMenu[7]);
  }
  uni.setStorageSync("platforms", platforms.value);
};

// 不再提示
const notPrompting = () => {
  console.log("不再提示");
  notPrompRequest(false);
  closeModal();
};
// 暂不更新
const notUpdating = () => {
  console.log("暂不更新");
  notPrompRequest(true);
  closeModal();
};

const notPrompRequest = async (blo) => {
  await VersionService.isPrompt({
    business: "yhPublishPlatform",
    version: baseInfo.value.version,
    prompt: blo,
  });
};
// 我知道了
const IKnew = () => {
  // 更新sysVersion
  const sysVersion = baseInfo.value.version.toLowerCase();
  const sysPlatform = baseInfo.value.publishPlatform;
  const cacheVersion = uni.getStorageSync("version");
  setTimeout(() => {
    uni.setStorageSync("version", {
      ...cacheVersion,
      sysVersion,
      sysPlatform,
    });
  }, 1);
  closeModal();
};

// 立即更新
const updateNow = () => {
  console.log("立即更新", baseInfo.value.apkPath);
  if (!baseInfo.value.apkPath) return;
  const filePath = baseInfo.value.apkPath;
  const firstIndex = filePath.indexOf("/");
  const firstPart = filePath.substring(0, firstIndex);
  const secondPart = filePath.substring(firstIndex);
  // https://ylg-dev.obs.cn-southwest-2.myhuaweicloud.com/file/20241211/%E6%B5%8B%E8%AF%95%E4%B8%8A%E4%BC%A0d.docx
  url.value =
    "https://" +
    firstPart +
    ".obs.cn-southwest-2.myhuaweicloud.com" +
    secondPart;
  console.log(firstPart);
  console.log(secondPart);
  console.log(url.value);
  console.log("版本更新方式", baseInfo.value.updateMethod);
  // 热更
  if (baseInfo.value.updateMethod === "hotUpdate") {
    console.log("热更");
    comfirmUpdate(url.value);
  } else {
    // 全量更新
    closeModal();
    UpdateVersionModalVisible.value = true;
  }
};
// 关闭弹窗
const closeModal = () => {
  updateModalRef.value.close();
};
// 取消 全量更新 回调
const openModal = () => {
  UpdateVersionModalVisible.value = false;
  nextTick(() => {
    initVersion();
  });
};

const initVersion = () => {
  // uni.setStorageSync("version", {
  //   appVersion: "v4.1.0",
  //   dlVersion: "",
  //   sysVersion: "v4.1.0",
  //   sysPlatform: "androidYangdaoApp",
  // });

  // 获取登录程序的版本信息
  console.log("uni.getSystemInfoSync()", uni.getSystemInfoSync());

  const { appWgtVersion } = uni.getSystemInfoSync();
  const orgAppVersion = "v" + appWgtVersion;
  console.log("appVersion", orgAppVersion);
  // 获取缓存的版本对象
  const version = uni.getStorageSync("version");
  console.log("缓存的version", version);
  // 更新缓存字段
  let newDlVersion = "";
  let newSysVersion = "";
  // 缓存版本对象存在
  if (version) {
    const { dlVersion, sysVersion, sysPlatform } = version;
    // apk安装完成
    if (orgAppVersion === dlVersion) {
      console.log("安装完成1");
      newDlVersion = "";
      newSysVersion = orgAppVersion;
      newSysPlatform = process.env.APP_DESCRIPTION;
    } else {
      console.log("安装中断2");
      // 安装中断/非强制更新
      newDlVersion = "";
      newSysVersion = sysVersion;
      newSysPlatform = sysPlatform || "";
    }
  } else {
    console.log("初次安装3");
    // 初次安装
    newDlVersion = "";
    newSysVersion = orgAppVersion;
    newSysPlatform = process.env.APP_DESCRIPTION;
  }
  console.log("newSysVersion", newSysVersion);
  console.log("newSysPlatform", newSysPlatform);

  // 缓存版本对象 - 统一转换成小写对比
  const newVersion = {
    appVersion: orgAppVersion.toLowerCase(),
    dlVersion: newDlVersion.toLowerCase(),
    sysVersion: newSysVersion.toLowerCase() || orgAppVersion.toLowerCase(),
    sysPlatform: newSysPlatform,
  };
  console.log("要缓存的newVersion对象", newVersion);
  uni.setStorageSync("version", newVersion);
  // 检查更新 - 先用系统记录的sysVersion||读取的appVersion
  const requestVersion =
    newSysVersion.toLowerCase() || orgAppVersion.toLowerCase(); // 统一通过sys去请求版本，不论版本高低 2024年12月17日16:36:12
  console.log("**********请求的requestVersion", requestVersion);

  checkUpdate(requestVersion, newSysPlatform);

  // 获取当前app的版本
  // const version = plus.runtime;
  // console.log(version, "获取当前app的版本");
};

// 检查版本
const checkUpdate = async (requestVersion, requestPlatform) => {
  console.log("updateModalRef.value", updateModalRef.value);
  // 拉取接口检查更新
  try {
    let params = {
      business: "yhPublishPlatform",
      publishPlatforms: platforms.value.join(","),
      version: requestVersion,
      publishPlatform: requestPlatform || process.env.APP_DESCRIPTION,
      // todo 增加参数 当前登录用户权限code
    };
    console.log("查看isAppUpdateAvailable参数", params);
    const { code, data } = await VersionService.isAppUpdateAvailable(params);
    if (code == 200 && data) {
      console.log("查看接口isAppUpdateAvailable data", data);
      const {
        version,
        updateAvailable,
        mandatoryUpdate,
        prompt,
        lastVersionChecked,
      } = data;
      baseInfo.value = data;
      // 是否有可用更新
      if (updateAvailable && updateModalRef.value) {
        // 是否有可用更新版本
        if (version) {
          // 是否强制更新
          if (mandatoryUpdate) {
            updateModalRef.value.open();
          } else {
            // 是否提示 - prompt == 要提示
            if (prompt) {
              // 判断时间是否超过10h
              const now = new Date().getTime();
              const lastTime = new Date(lastVersionChecked).getTime();
              const time = now - lastTime;
              console.log({
                now,
                lastTime,
                time,
              });

              if (time > 36000000) {
                updateModalRef.value.open();
              }
            }
          }
        }
      }
    } else {
      console.log("查看接口isAppUpdateAvailable", code, data);
    }
  } catch (e) {
    //TODO handle the exception
    console.log("app版本更新请求失败");
  }
};
// 热更
const comfirmUpdate = (url) => {
  pageLoading.value = true;
  downloadTask.value = uni.downloadFile({
    // url: showImg('/file/2024-11-15/__UNI__8A196AA_20241115154611A027.wgt'),
    // url: showImg('/file/2024-11-18/__UNI__8A196AA_20241118103705A030.wgt'),
    // url: showImg("/file/2024-11-18/__UNI__8A196AA_20241118170157A035.wgt"),
    url,
    success: (res) => {
      if (res.statusCode === 200) {
        // 更新dl
        const dlVersion = baseInfo.value.version.toLowerCase();
        const cacheVersion = uni.getStorageSync("version");
        setTimeout(() => {
          uni.setStorageSync("version", {
            ...cacheVersion,
            dlVersion,
          });
        }, 100);
        // 写入缓存后安装
        setTimeout(() => {
          installWgt(res.tempFilePath);
          console.log("热更下载完成", baseInfo.value);
        }, 200);
        pageLoading.value = false;
      } else {
        uni.showToast({
          title: "下载失败",
          icon: "none",
        });
        pageLoading.value = false;
      }
    },
    fail: (err) => {
      uni.showToast({
        title: "下载失败",
        icon: "none",
      });
      pageLoading.value = false;
    },
  });
  // 监听下载进度
  downloadTask.value.onProgressUpdate((progress) => {
    console.log("热更下载进度", progress.progress);
  });
};

const installWgt = (filePath) => {
  plus.runtime.install(
    filePath,
    {
      // true表示强制安装，不进行版本号的校验；
      // false则需要版本号校验，如果要安装应用的版本号不高于现有应用的版本号则终止安装，并返回安装失败。
      // 仅安装wgt和wgtu时生效，默认值 false。
      force: true,
    },
    () => {
      console.log("安装成功！！！！");
      plus.runtime.restart();
    },
    (err) => {
      console.log("安装失败！！！！", err);
      uni.showToast({
        icon: "none",
        title: "安装失败~" + err,
      });
    }
  );
};
onReady(() => {
});
onLoad(() => {
    handlePlatforms();
});
onShow(() => {
  // console.log("检查版本onShow", UpdateVersionModalRef.value);
  // if (!UpdateVersionModalRef.value) {
  //   nextTick(() => {
  //     initVersion(); // 初始化版本信息
  //   });
  // }
});
</script>
<style lang="scss" scoped>
// 升级弹窗
:deep(.uv-modal__content) {
  display: block !important;
  padding: 6rpx 28rpx;
}

.version-header {
  display: flex;
  margin-top: -30rpx;

  .version-left {
    width: 322rpx;
    height: 322rpx;
    // border: 1px solid red;
    background-image: url("../../static/image/version_update_bg1.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  .version-right {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 300rpx;
    height: 300rpx;
    // border: 1px solid green;
    background-image: url("../../static/image/version_update_bg2.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;

    .title,
    .version {
      transform: rotate(8deg);
      font-family: PingFang SC, PingFang SC;
      color: #ffffff;
      font-weight: 500;
    }

    .title {
      margin-bottom: 10rpx;
      height: 50rpx;
      font-size: 36rpx;
      line-height: 42rpx;
    }

    .version {
      height: 68rpx;
      font-size: 48rpx;
      line-height: 56rpx;
    }
  }
}

.version-content {
  height: 634rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 2rpx solid #e1e1e1;
  margin-top: 20rpx;
  padding: 0 24rpx;

  .title {
    min-width: 224rpx;
    height: 44rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #404040;
    line-height: 38rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 28rpx 0;
  }

  .paragraphs {
    height: calc(100% - 100rpx);
    overflow-y: auto;

    .paragraph {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #606060;
      line-height: 32rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 32rpx;
    }
  }
}

.btns-box {
  margin-top: 48rpx;
  padding: 0 24rpx;

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 72rpx;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    line-height: 38rpx;
    margin-bottom: 28rpx;
    border: 1px solid #4378ff;
  }

  .ghost {
    background: #ffffff;
    color: #4378ff;
  }

  .primary {
    background: #4378ff;
    color: #ffffff;
  }
}
</style>
