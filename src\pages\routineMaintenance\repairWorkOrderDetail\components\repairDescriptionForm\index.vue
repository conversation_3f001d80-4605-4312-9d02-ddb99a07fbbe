<template>
  <view class="repair-form-container">
    <!-- 维修说明/计量备注输入区域 -->
    <RepairFormCard
      :title="isShowAcceptanceMeasurement ? '计量备注' : '维修说明'"
      :required="true"
    >
      <view class="repair-desc-input-wrap">
        <uv-textarea
          v-model="repairForm.remark"
          :placeholder="
            isShowAcceptanceMeasurement ? '请输入计量备注' : '请输入维修说明'
          "
          maxlength="150"
          count
          height="120px"
          class="repair-desc-input"
          :disabled="isViewMode"
        />
      </view>
    </RepairFormCard>

    <!-- 维修人员管理区域 -->
    <RepairFormCard
      title="维修人员"
      :required="true"
      :show-improve-icon="showUsersImproveIcon"
      :show-add-button="true"
      :is-view-mode="isViewMode"
      @add="handleAddClick('user')"
    >
      <!-- 空状态展示 -->
      <RepairEmptyState v-if="repairForm.users.length === 0" />
      <!-- 人员列表 -->
      <RepairList v-else>
        <RepairListItem
          v-for="(item, idx) in repairForm.users"
          :key="item.id"
          :show-avatar="true"
          :avatar-text="item.userName?.slice(-2) || item.name?.slice(-2)"
          :title="item.userName || item.name"
          :subtitle="item.deptName"
          :price-text="`¥${item.unitPrice || '-'}/${getUnitLabel(item.units, USER_UNIT_LIST)}`"
          :count-value="getNum(item, isShowAcceptanceMeasurement)"
          @click="openUserEditModal(item, idx)"
        />
      </RepairList>
    </RepairFormCard>
    <!-- 作业内容管理区域 -->
    <RepairFormCard
      title="作业内容"
      :show-improve-icon="showSettlementsImproveIcon"
      :show-add-button="true"
      :is-view-mode="isViewMode"
      @add="handleAddClick('settlement')"
    >
      <!-- 空状态展示 -->
      <RepairEmptyState v-if="repairForm.settlements.length === 0" />
      <!-- 作业内容列表 -->
      <RepairList v-else>
        <RepairListItem
          v-for="(item, idx) in repairForm.settlements"
          :key="item.id"
          :title="item.content"
          :price-text="`¥${item.unitPrice || '-'}/${item.unitName || '-'}`"
          :count-value="
            getNum(
              item,
              isShowAcceptanceMeasurement,
              'planNumber',
              'realNumber'
            )
          "
          @click="openSettlementsEditModal(item, idx)"
        />
      </RepairList>
    </RepairFormCard>

    <!-- 使用材料管理区域 -->
    <RepairFormCard
      title="使用材料"
      :show-improve-icon="showMaterialsImproveIcon"
      :show-add-button="true"
      :is-view-mode="isViewMode"
      @add="handleAddClick('material')"
    >
      <!-- 空状态展示 -->
      <RepairEmptyState v-if="repairForm.materials.length === 0" />
      <!-- 材料列表 -->
      <RepairList v-else>
        <RepairListItem
          v-for="(item, idx) in repairForm.materials"
          :key="item.id"
          :title="item.materialName"
          :subtitle="item.specification"
          :price-text="`¥${item.unitPrice || '-'}/${item.unitName || '-'}`"
          :count-value="getNum(item, isShowAcceptanceMeasurement)"
          @click="openMaterialEditModal(item, idx)"
        />
      </RepairList>
    </RepairFormCard>
    <!-- 使用机械管理区域 -->
    <RepairFormCard
      title="使用机械"
      :show-improve-icon="showMachinesImproveIcon"
      :show-add-button="true"
      :is-view-mode="isViewMode"
      @add="handleAddClick('machine')"
    >
      <!-- 空状态展示 -->
      <RepairEmptyState v-if="repairForm.machines.length === 0" />
      <!-- 机械列表 -->
      <RepairList v-else>
        <RepairListItem
          v-for="(item, idx) in repairForm.machines"
          :key="item.id"
          :title="item.name"
          :price-text="`¥${item.unitPrice || '-'}/${getUnitLabel(item.units, MACHINE_UNIT_LIST)}`"
          :count-value="getNum(item, isShowAcceptanceMeasurement)"
          @click="openMachineEditModal(item, idx)"
        />
      </RepairList>
    </RepairFormCard>

    <!-- 附件上传区域 -->
    <RepairFormCard
      :title="isShowAcceptanceMeasurement ? '验收计量附件' : '维修方案附件'"
    >
      <view class="repair-upload-area">
        <!-- 查看模式：显示已上传的附件 -->
        <template v-if="isViewMode">
          <view v-if="repairForm.files && repairForm.files.length > 0">
            <a
              class="repair-file-link"
              href="javascript:void(0);"
              @click="viewFile({ filePath: repairForm.files[0].url })"
              >{{
                detail.vericaContent.filePath?.split("/").pop() || "附件"
              }}</a
            >
          </view>
          <view v-else style="color: #a0a0a0">无附件</view>
        </template>
        <!-- 编辑模式：文件上传组件 -->
        <template v-else>
          <uv-upload
            ref="uploadRef"
            :maxCount="1"
            :fileList="previewFileList"
            :accept="currentAcceptType"
            @afterRead="handleFileChange"
            @delete="handleFileDelete"
          >
            <view class="repair-upload-box-custom" @click.stop="handleAcceptTypeCheck">
              <image
                src="@/static/image/repair_from_upload.png"
                class="repair-upload-icon"
              />
              <view class="repair-upload-tip"
                >支持上传图片、视频格式</view
              >
            </view>
          </uv-upload>
        </template>
      </view>
    </RepairFormCard>
    <!-- 通用编辑弹窗 -->
    <CommonEditPopup
      :visible="editModalVisible"
      :title="editModalTitle"
      :fields="editModalFields"
      :model="editModalModel"
      :unitList="editModalUnitList"
      :showDelete="editModalShowDelete"
      :info="editModalInfo"
      :isAcceptance="isShowAcceptanceMeasurement"
      :isViewMode="isViewMode"
      @close="handleEditModalClose"
      @confirm="handleEditModalConfirm"
      @delete="handleEditModalDelete"
    />
  </view>
</template>

<script setup>
/**
 * 维修描述表单组件
 * 功能：管理维修工单的详细信息，包括维修说明、人员、作业内容、材料、机械设备和附件
 */

// Vue 核心功能导入
import { ref, reactive, onMounted, onUnmounted, computed, watch, nextTick } from "vue";

// 工具函数导入
import {
  uploadFilePromise,
  showLoading,
  hideLoading,
  showToast,
  showImg,
} from "@/utils";
import { viewFile } from "@/utils/fileViewer";

// 组件导入
import CommonEditPopup from "../popup/CommonEditPopup.vue";
import RepairFormCard from "./components/RepairFormCard.vue";
import RepairEmptyState from "./components/RepairEmptyState.vue";
import RepairList from "./components/RepairList.vue";
import RepairListItem from "./components/RepairListItem.vue";

// 工具函数和常量导入
import { getUnitLabel, getNum } from "./utils";
import {
  USER_UNIT_LIST,
  MACHINE_UNIT_LIST,
  RELEVANCY_TYPE_MAP,
} from "./constants";
import {
  createUserEditConfig,
  createSettlementEditConfig,
  createMaterialEditConfig,
  createMachineEditConfig,
} from "./config/editModal.js";

// 第三方库导入
import Decimal from "decimal.js";
import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";

// 事件定义
const emit = defineEmits(["onConfirm"]);
// 全局状态管理
const workOrderInfo = useRepairWorkOrderStore();

const props = defineProps({
  // 是否显示验收计量模式
  isShowAcceptanceMeasurement: {
    type: Boolean,
    default: false,
  },
  // 是否为查看模式
  isViewMode: {
    type: Boolean,
    default: false,
  },
  // 工单详情数据
  detail: {
    type: Object,
    default: () => ({}),
  },
});

// 表单数据结构
const repairForm = reactive({
  remark: "", // 维修说明/计量备注
  users: [], // 维修人员列表
  settlements: [], // 作业内容列表
  materials: [], // 使用材料列表
  machines: [], // 使用机械列表
  files: [], // 附件列表
  old_users: [], // 原始人员数据（用于编辑对比）
  old_settlements: [], // 原始作业内容数据
  old_materials: [], // 原始材料数据
  old_machines: [], // 原始机械数据
});

// 文件上传相关状态
const currentAcceptType = ref('image'); // 当前接受的文件类型
const uploadRef = ref(null); // 上传组件引用
const maxSize = ref(1 * 1024 * 1024); // 最大文件大小（1MB）

/**
 * 维修人员图标(完善数据)
 * 根据 isShowAcceptanceMeasurement 模式检查对应的必填字段：
 */
const showUsersImproveIcon = computed(() => {
  if (!repairForm.users || repairForm.users.length === 0) return false;
  console.log(repairForm.users);
  return repairForm.users.some(
    (item) =>
      props.isShowAcceptanceMeasurement
        ? !item.realNum // 验收模式：实际数量
        : !item.units || !item.planNum // 计划模式：检查单位和计划数量
  );
});

/**
 * 作业内容图标(完善数据)
 * 根据 isShowAcceptanceMeasurement 模式检查对应的数量字段：
 */
const showSettlementsImproveIcon = computed(() => {
  if (!repairForm.settlements || repairForm.settlements.length === 0)
    return false;
  return repairForm.settlements.some(
    (item) =>
      props.isShowAcceptanceMeasurement
        ? !item.realNumber // 验收模式：实际数量
        : !item.planNumber // 计划模式：计划数量
  );
});

/**
 * 使用材料图标(完善数据)
 * 根据 isShowAcceptanceMeasurement 模式检查对应的必填字段：
 */
const showMaterialsImproveIcon = computed(() => {
  if (!repairForm.materials || repairForm.materials.length === 0) return false;
  return repairForm.materials.some(
    (item) =>
      props.isShowAcceptanceMeasurement
        ? !item.realNum // 验收模式：实际数量
        : !item.planNum // 计划模式：计划数量
  );
});

/**
 * 使用机械图标(完善数据)
 * 根据 isShowAcceptanceMeasurement 模式检查对应的必填字段：
 */
const showMachinesImproveIcon = computed(() => {
  if (!repairForm.machines || repairForm.machines.length === 0) return false;
  return repairForm.machines.some(
    (item) =>
      props.isShowAcceptanceMeasurement
        ? !item.realNum // 验收模式：实际数量
        : !item.units || !item.planNum // 计划模式：检查单位和计划数量
  );
});

/**
 * 监听工单详情数据变化，初始化表单数据
 * 主要用于验收计量模式下的数据回显
 */
watch(
  () => props.detail,
  (newDetail) => {
    if (
      newDetail &&
      Object.keys(newDetail).length &&
      props.isShowAcceptanceMeasurement
    ) {
      // 回显维修人员数据
      repairForm.users = (newDetail.users || []).map((item) => ({
        ...item,
        originId: item.id, // 保存原始ID用于编辑
        id: item.relevancyId, // 使用关联ID作为新ID
      }));
      // 保存原始数据用于编辑回填
      repairForm.old_users = JSON.parse(JSON.stringify(repairForm.users));

      // 回显机械设备数据
      repairForm.machines = (newDetail.machines || []).map((item) => ({
        ...item,
        originId: item.id,
        id: item.relevancyId,
      }));
      repairForm.old_machines = JSON.parse(JSON.stringify(repairForm.machines));

      // 回显材料数据（字段名转换：name -> materialName）
      repairForm.materials = (newDetail.materials || []).map((item) => ({
        ...item,
        materialName: item.name,
        originId: item.id,
        id: item.relevancyId,
        units: item.unit,
      }));
      repairForm.old_materials = JSON.parse(
        JSON.stringify(repairForm.materials)
      );

      // 回显作业内容数据（字段名转换：content -> settlementName）
      repairForm.settlements = (newDetail.settlements || []).map((item) => ({
        ...item,
        settlementName: item.content,
        originId: item.id,
        id: item.configId, // 作业内容使用configId
        unitName: item.units,
      }));
      repairForm.old_settlements = JSON.parse(
        JSON.stringify(repairForm.settlements)
      );

      //备注
      repairForm.remark = newDetail.vericaContent?.remark || "";

      //附件
      repairForm.files = newDetail.vericaContent?.filePath
        ? [{ url: newDetail.vericaContent.filePath }]
        : [];

      // 设置页面标题为验收计量
      uni.setNavigationBarTitle &&
        uni.setNavigationBarTitle({
          title: "验收计量",
        });
    }
  },
  { immediate: true, deep: true }
);

// ==================== 编辑弹窗管理 ====================

/** 编辑弹窗状态管理 */
const editModalVisible = ref(false); // 弹窗显示状态
const editModalTitle = ref(""); // 弹窗标题
const editModalFields = ref([]); // 表单字段配置
const editModalModel = ref({}); // 表单数据模型
const editModalUnitList = ref([]); // 单位选项列表
const editModalShowDelete = ref(false); // 是否显示删除按钮
const editModalInfo = ref(null); // 编辑项信息
let editType = ""; // 当前编辑类型
let editIdx = -1; // 当前编辑项索引

/**
 * 打开人员编辑弹窗
 * @param {Object} item - 人员数据
 * @param {number} idx - 人员索引
 */
function openUserEditModal(item, idx) {
  const config = createUserEditConfig(
    item,
    idx,
    props.isShowAcceptanceMeasurement,
    props.isViewMode
  );
  editType = config.type;
  editIdx = config.idx;
  editModalTitle.value = config.title;
  editModalFields.value = config.fields;
  editModalModel.value = config.model;
  editModalUnitList.value = config.unitList;
  editModalShowDelete.value = config.showDelete;
  editModalInfo.value = config.info;
  editModalVisible.value = true;
}

/**
 * 打开作业内容编辑弹窗
 * @param {Object} item - 作业内容数据
 * @param {number} idx - 作业内容索引
 */
function openSettlementsEditModal(item, idx) {
  const config = createSettlementEditConfig(
    item,
    idx,
    props.isShowAcceptanceMeasurement,
    props.isViewMode
  );
  editType = config.type;
  editIdx = config.idx;
  editModalTitle.value = config.title;
  editModalFields.value = config.fields;
  editModalModel.value = config.model;
  editModalUnitList.value = [{ value: item.unitName, label: item.unitName }];
  editModalShowDelete.value = config.showDelete;
  editModalInfo.value = config.info;
  editModalVisible.value = true;
}
/**
 * 打开材料编辑弹窗
 * @param {Object} item - 材料数据
 * @param {number} idx - 材料索引
 */
function openMaterialEditModal(item, idx) {
  const config = createMaterialEditConfig(
    item,
    idx,
    props.isShowAcceptanceMeasurement,
    props.isViewMode
  );
  editType = config.type;
  editIdx = config.idx;
  editModalTitle.value = config.title;
  editModalFields.value = config.fields;
  editModalModel.value = config.model;
  editModalUnitList.value = [{ value: item.units, label: item.unitName }];
  editModalShowDelete.value = config.showDelete;
  editModalInfo.value = config.info;
  editModalVisible.value = true;
}

/**
 * 打开机械设备编辑弹窗
 * @param {Object} item - 机械设备数据
 * @param {number} idx - 机械设备索引
 */
function openMachineEditModal(item, idx) {
  const config = createMachineEditConfig(
    item,
    idx,
    props.isShowAcceptanceMeasurement,
    props.isViewMode
  );
  editType = config.type;
  editIdx = config.idx;
  editModalTitle.value = config.title;
  editModalFields.value = config.fields;
  editModalModel.value = config.model;
  editModalUnitList.value = config.unitList;
  editModalShowDelete.value = config.showDelete;
  editModalInfo.value = config.info;
  editModalVisible.value = true;
}

/** 关闭编辑弹窗 */
function handleEditModalClose() {
  editModalVisible.value = false;
}

/**
 * 确认编辑操作
 * @param {Object} newData - 新的表单数据
 */
function handleEditModalConfirm(newData) {
  if (editType && editIdx > -1) {
    const fromData = repairForm[editType][editIdx];
    const { countCost, ...rest } = newData;
    let toData = {
      ...fromData,
      ...rest,
    };
    // 验收模式下需要设置实际花费
    if (props.isShowAcceptanceMeasurement) {
      toData.realTotal = countCost || 0;
    } else {
      toData.planTotal = countCost || 0;
    }
    repairForm[editType][editIdx] = toData;
  }
  editModalVisible.value = false;
}

/**
 * 删除编辑项
 */
function handleEditModalDelete() {
  if (editType && editIdx > -1) {
    repairForm[editType].splice(editIdx, 1);
  }
  editModalVisible.value = false;
}

// ==================== 选择页面数据处理 ====================

/**
 * 处理选择页面回传的数据
 * @param {Object} params - 回传参数
 * @param {string} params.type - 选择类型（user/material/machine/settlement）
 * @param {Array} params.list - 选择的数据列表
 */
function handleSelectPageConfirm({ type, list }) {
  // 合并已选择的数据（用表单中已有的数据替换列表中的相同项）
  const newList = list.map((item) => {
    const existed = repairForm[`${type}s`].find((old) => old.id === item.id);
    return existed ? { ...existed } : item;
  });

  // 验收模式下需要保留原有的计划数据
  const planList = props.isShowAcceptanceMeasurement
    ? repairForm[`${type}s`].filter((item) =>
        repairForm[`old_${type}s`]?.find((old) => old.id === item.id)
      )
    : [];

  // 获取对应的单位列表
  let unitList = [];
  if (type === "user") unitList = USER_UNIT_LIST;
  if (type === "machine") unitList = MACHINE_UNIT_LIST;
  // 为新添加的项目设置默认值和关联信息
  newList.forEach((item) => {
    const existed = repairForm[`${type}s`].find((old) => old.id === item.id);
    if (!existed && !item.units) {
      // 设置默认单位
      if (unitList.length > 0) {
        item.units = unitList[0].value;
      } else {
        item.units = type == "settlement" ? item.unitName : item.unit;
        item.unitName = item.unitName;
      }
      // 设置关联信息
      if (type !== "settlement") {
        item.relevancyId = item.id;
        item.relevancyType = RELEVANCY_TYPE_MAP[type];
        item.wordId = workOrderInfo.id;
      } else {
        item.configId = item.id;
        item.workOrderId = workOrderInfo.id;
      }
    }
  });

  // 更新表单数据
  repairForm[`${type}s`] = props.isShowAcceptanceMeasurement
    ? [...planList, ...newList]
    : newList;
}

// 组件生命周期：监听选择页面事件
onMounted(() => {
  uni.$on("selectPageConfirm", handleSelectPageConfirm);
});

onUnmounted(() => {
  uni.$off("selectPageConfirm", handleSelectPageConfirm);
});

// ==================== 页面跳转处理 ====================

/**
 * 处理添加按钮点击，跳转到选择页面
 * @param {string} type - 选择类型（user/material/machine/settlement）
 */
function handleAddClick(type) {
  // 获取需要排除的ID列表（原有数据，避免重复选择）
  const exclude = repairForm[`old_${type}s`]?.map((item) => item.id) || [];
  // 获取已选择的ID列表（当前表单中的数据）
  const selected = repairForm[`${type}s`]?.map((item) => item.id) || [];

  // 使用 events 方式传递数据，解决安卓打包后URL参数接收不到的问题
  uni.navigateTo({
    url: `/pages/common/selectPage?type=${type}`,
    events: {
      // 监听选择页面返回的确认数据
      selectPageConfirm: function(data) {
        console.log('接收到选择页面返回的数据:', data);
        // 直接调用原有的处理函数
        handleSelectPageConfirm(data);
      }
    },
    success: function(res) {
      // 通过eventChannel向被打开页面传送数据
      res.eventChannel.emit('initSelectPageData', {
        exclude,
        selected,
        isEdit: props.isShowAcceptanceMeasurement
      });
    }
  });
}

// ==================== 文件上传处理 ====================
const handleAcceptTypeCheck = () => {
  uni.showActionSheet({
    itemList: ['选择图片', '选择视频'],
    success: (res) => {
      const type = res.tapIndex === 0 ? 'image' : 'video';
      currentAcceptType.value = type;
      // 使用 nextTick 确保 accept 属性更新后再触发选择
      nextTick(() => {
        if (uploadRef.value) {
          uploadRef.value.chooseFile();
        }
      });
    },
  });
}

/**
 * 处理文件上传
 * @param {Object} event - 上传事件对象
 */
const handleFileChange = async (event) => {
  const file = event.file;
  if (!file) return;

  // 额外的文件大小检查，确保安全
  const fileSize = file.size || file.file?.size;
  console.log('handleFileChange - 文件信息:', {
    fileName: file.name,
    fileSize: fileSize,
    maxSize: maxSize.value,
    file: file
  });

  if (fileSize && fileSize > maxSize.value) {
    console.log('文件大小超限，阻止上传');
    showToast("文件不能超过1MB");
    return;
  }

  showLoading("上传中...");
  try {
    const fileUrl = file.url;
    if (!fileUrl) {
      showToast("文件路径获取失败");
      return;
    }

    // 调用上传接口
    const res = await uploadFilePromise(fileUrl);

    // 更新文件列表（只支持单文件）
    repairForm.files = [
      {
        ...file,
        url: res.data?.url || res.url,
        name: file.name || res.data?.name,
      },
    ];
  } catch (e) {
    showToast("文件上传失败");
  } finally {
    hideLoading();
  }
};

/**
 * 处理文件删除
 */
const handleFileDelete = () => {
  repairForm.files = [];
};

/**
 * 预览文件列表计算属性
 * 将文件URL转换为可预览的格式
 */
const previewFileList = computed(() => {
  return repairForm.files.map((f) => ({
    ...f,
    url: showImg(f.url),
  }));
});

// ==================== 费用计算 ====================

/**
 * 计算单项费用总和
 * @param {Array} list - 数据列表
 * @returns {Decimal} 费用总和
 */
function calcTotal(list) {
  return list.reduce((sum, item) => {
    const price = new Decimal(item.unitPrice == null ? 0 : item.unitPrice);

    // 根据验收模式选择对应的数量字段
    const val = props.isShowAcceptanceMeasurement
      ? item.realNumber || item.realNum
      : item.planNum || item.planNumber;
    const count = new Decimal(val == null ? 0 : val);

    return sum.plus(price.times(count));
  }, new Decimal(0));
}

/**
 * 计算总费用
 * 包含人员、作业内容、材料、机械的所有费用
 */
const totalCost = computed(() => {
  const total = calcTotal(repairForm.users)
    .plus(calcTotal(repairForm.settlements))
    .plus(calcTotal(repairForm.materials))
    .plus(calcTotal(repairForm.machines));
  return total.toFixed(2); // 返回两位小数的字符串
});

// ==================== 表单验证 ====================

/**
 * 表单数据验证
 * 复用完善数据图标逻辑来简化验证
 * @returns {boolean} 验证是否通过
 */
const validateForm = () => {
  // 验证维修说明/计量备注
  if (!repairForm.remark.trim()) {
    uni.showToast({
      title: `请填写${props.isShowAcceptanceMeasurement ? "计量备注" : "维修说明"}`,
      icon: "none",
    });
    return false;
  }

  // 验证维修人员信息（必须有人员且没有完善提示）
  if (repairForm.users.length === 0) {
    uni.showToast({ title: "请完善维修人员信息", icon: "none" });
    return false;
  }
  if (showUsersImproveIcon.value) {
    uni.showToast({ title: "请填完善维修人员信息", icon: "none" });
    return false;
  }

  // 验证作业内容信息（如果有作业内容且显示完善提示则无效）
  if (repairForm.settlements.length > 0 && showSettlementsImproveIcon.value) {
    uni.showToast({ title: "请填完善作业内容信息", icon: "none" });
    return false;
  }

  // 验证材料信息（如果有材料且显示完善提示则无效）
  if (repairForm.materials.length > 0 && showMaterialsImproveIcon.value) {
    uni.showToast({ title: "请填完善材料信息", icon: "none" });
    return false;
  }

  // 验证机械信息（如果有机械且显示完善提示则无效）
  if (repairForm.machines.length > 0 && showMachinesImproveIcon.value) {
    uni.showToast({ title: "请填完善机械信息", icon: "none" });
    return false;
  }

  return true;
};
// ==================== 数据导出方法 ====================

/**
 * 获取新建维修工单的表单数据
 * @returns {Object|undefined} 表单数据对象，验证失败时返回undefined
 */
const getRepairFormData = () => {
  if (!validateForm()) return;
  // 过滤掉临时ID字段的辅助函数
  const omitId = (item) => {
    const { id, relevancyId, ...rest } = item;
    return {
      ...rest,
      relevancyId: id,
    };
  };

  return {
    users: repairForm.users.map(omitId),
    materials: repairForm.materials.map(omitId),
    machines: repairForm.machines.map(omitId),
    settlements: repairForm.settlements.map(omitId),
    filePath: repairForm.files[0]?.url || "",
    remark: repairForm.remark,
  };
};

/**
 * 获取编辑维修工单的表单数据
 * @returns {Object|undefined} 表单数据对象，验证失败时返回undefined
 */
const getRepairEditFormData = () => {
  if (!validateForm()) return;

  // 根据模式还原ID的辅助函数
  const formatItem = (item) => {
    const { id, originId, ...rest } = item;
    return {
      ...rest,
      id: originId, // 使用原始ID
    };
  };

  return {
    users: repairForm.users.map(formatItem),
    materials: repairForm.materials.map(formatItem),
    machines: repairForm.machines.map(formatItem),
    settlements: repairForm.settlements.map(formatItem),
    filePath: repairForm.files[0]?.url || "",
    remark: repairForm.remark,
  };
};

// ==================== 表单验证状态 ====================

/**
 * 表单是否有效（所有必填项都已填写）
 * 用于控制外部按钮的启用/禁用状态
 */
const isFormValid = computed(() => {
  // 验证维修说明/计量备注
  if (!repairForm.remark.trim()) {
    return false;
  }

  // 验证维修人员信息（必须有人员且没有完善提示）
  if (repairForm.users.length === 0 || showUsersImproveIcon.value) {
    return false;
  }

  // 验证作业内容信息（如果有作业内容且显示完善提示则无效）
  if (repairForm.settlements.length > 0 && showSettlementsImproveIcon.value) {
    return false;
  }

  // 验证材料信息（如果有材料且显示完善提示则无效）
  if (repairForm.materials.length > 0 && showMaterialsImproveIcon.value) {
    return false;
  }

  // 验证机械信息（如果有机械且显示完善提示则无效）
  if (repairForm.machines.length > 0 && showMachinesImproveIcon.value) {
    return false;
  }

  return true;
});

// ==================== 组件对外接口 ====================

defineExpose({
  totalCost, // 总费用计算属性
  isFormValid, // 表单验证状态
  getRepairFormData, // 获取新建表单数据
  getRepairEditFormData, // 获取编辑表单数据
});
</script>

<style lang="scss" scoped>
.repair-form-container {
  border-radius: 24rpx;
  position: relative;
  background-color: #fff;

  .repair-desc-input-wrap {
    margin-top: 12rpx;
    box-sizing: border-box;
  }
  .repair-upload-area {
    margin-top: 12rpx;

    :deep(.uv-upload__wrap) {
      width: 100%;
      display: block;
    }

    .repair-upload-box-custom {
      width: 100%;
      box-sizing: border-box;
      border: 2rpx dashed #d9d9d9;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 8rpx;
      padding: 32rpx 0 16rpx 0;

      .repair-upload-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 8rpx;
      }

      .repair-upload-tip {
        font-size: 24rpx;
        color: #a0a0a0;
        margin-top: 8rpx;
      }
    }
  }

  .repair-file-link {
    color: #4378ff;
    text-decoration: none;
    font-size: 28rpx;
  }
}
</style>
