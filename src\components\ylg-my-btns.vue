<template>
  <view class="bottom_btn">
    <image
      class="mine_switch_btn"
      :class="{ rotated: switchActive }"
      mode="widthFix"
      src="../static/icon/mine_switch_btn20240912.png"
      @click.stop="toggoleSwitch"
    />
    <view class="option_btn_box" :class="{ show: switchActive }">
      <image
        class="event_report_btn"
        src="../static/icon/event_report_icon20240912.png"
        @click.stop="toReportEvent"
      />
      <!-- <image
        class="mine_btn"
        src="../static/icon/to_mine_icon20240912.png"
        mode="widthFix"
        @click.stop="goUserCenter"
      /> -->
    </view>
  </view>
</template>
<script setup>
const props = defineProps({
  switchActive: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const emit = defineEmits(["myBtnCallback"]);
const toggoleSwitch = () => {
  emit("myBtnCallback", { type: "toggoleSwitch" });
};
const toReportEvent = () => {
  emit("myBtnCallback", { type: "toReportEvent" });
};
// const goUserCenter = () => {
//   emit("myBtnCallback", { type: "goUserCenter" });
// };
</script>

<style lang="scss" scoped>
.bottom_btn {
  position: fixed;
  right: 40rpx;
  bottom: 28rpx;
  z-index: 999;
  .mine_switch_btn {
    width: 100rpx;
    height: 100rpx;
    transition: transform 0.2s ease;
  }
  .rotated {
    transform: rotate(45deg);
  }
  .option_btn_box {
    position: absolute;
    top: 0rpx; /* 根据需要调整 */
    left: 0;
    right: 0;
    height: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
    transform: translateY(20px); /* 初始位置在下方 */
  }
  .option_btn_box.show {
    opacity: 1;
    height: 266rpx;
    // top: -290rpx; /* 根据需要调整 */
    top: -224rpx; /* 根据需要调整 */
    transform: translateY(0); /* 动画效果: 从下方移入 */
  }
  .event_report_btn,
  .mine_btn,
  .ai_btn {
    margin-top: 28rpx;
    width: 100rpx;
    height: 100rpx;
  }
}
</style>
