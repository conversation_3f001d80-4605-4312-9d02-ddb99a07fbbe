/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-01-08 16:12:24
 * @Description: 
 */

import BaseService from "../request";

class RoadInspectionService extends BaseService {
  constructor() {
    super();
  }  

  // 首页今日待办任务
  getHomeTodayList(projectId) {
    return this.get(`/yh/app/home/<USER>/inspect/list?projectId=${projectId}`);
  }

  // 今日待办和超时任务列表
  getTodayList(projectId) {
    return this.get(`/yh/inspectTask/today/todo?projectId=${projectId}`);
  }
  // 过去或未来任务列表
  getNewOrOldList(params) {
    return this.get(`/yh/inspectTask/newOrOld/list?date=${params.date}&projectId=${params.projectId}`);
  }
  // 任务详情
  getTaskDetail(id) {
    return this.get(`/yh/inspectTask/detail/${id}`);
  }
  // 任务完成情况
  getTaskCompleteInfo(id) {
    return this.get(`/yh/inspectTask/complete/situation/${id}`);
  }
  // 开始检查任务
  startInspectTask(params) {
    return this.put(`/yh/inspectTask/start/${params.inspectTaskId}`,params);
  }
  // 新增轨迹-路况检查事件上报
  updateLocation(params) {
    return this.post(`/yh/inspectTaskTrajectory/save`,params);
  }
  // 结束检查任务
  endInspectTask(params) {
    return this.put(`/yh/inspectTask/end/${params.inspectTaskId}`,params);
  }


  // 历史任务查询
  getHistoryData(params) {
    return this.get(`/yh/inspectTask/history/list?projectId=${params.projectId}&planNameOrCode=${params.planNameOrCode}&inspectType=${params.inspectType}&taskStatus=${params.taskStatus}&startInspectTime=${params.startInspectTime}&endInspectTime=${params.endInspectTime}&page=${params.page}&limit=${params.limit}`);
  }
  // 上报事件详情
  getEventDetail(id) {
    return this.get(`/yh/inspectEvent/detail/${id}`);
  }



  // 事件上报-查询事件对象类型选项
  getEventObjType(inspectPlanId) {
    return this.get(`/yh/inspectPlan/object/type/list?inspectPlanId=${inspectPlanId}`);
  }
  // 事件上报-查询事件对象名称选项1
  getEventObjName1(params) {
    return this.get(`/project-resource/assetData/page?type=${params.objectType}&page=${params.page}&limit=${params.limit}&sectionId=${params.sectionId}`);
  }
  // 事件上报-查询事件对象名称选项2
  getEventObjName2(params) {
    return this.get(`/yh/inspectPlan/object/list?inspectPlanId=${params.inspectPlanId}&objectType=${params.objectType}`);
  }
  // 事件上报
  submitEvent(params) {
    return this.post(`/yh/inspectEvent/save`,params);
  }


}
export default new RoadInspectionService();