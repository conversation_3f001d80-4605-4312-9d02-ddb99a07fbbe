<!--
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-03-27 16:53:52
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="路段采集"
      leftIconColor="#fff"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      :autoBack="false"
      @leftClick="onNavBack"
      bgColor="#4d8bff"
    >
      <template v-if="isViewDetail" v-slot:right>
        <view class="uv-nav-slot" @click="onDel">删除</view>
      </template>
    </uv-navbar>
    <scroll-view scroll-y="true" class="content">
      <ylg-auto-form
        ref="autoFormRef"
        :isAllDisabled="isViewDetail"
        :isFromDetail="isFromDetail"
        :formConfig="formConfig"
        :formData="formData"
        :rules="rules"
        :btnLoading="btnLoading"
        :labelStyle="labelStyle"
        :formItemStyle="formItemStyle"
        :placeholderStyle="placeholderStyle"
        @onEdit="onFormEdit"
        @onChange="onFormInpChange"
        @onSubmit="onFormSubmit"
      ></ylg-auto-form>
      <uv-toast ref="toast"></uv-toast>
      <del-data-modal
        ref="delModal"
        :delMessage="delMessage"
         :delBtnLoading="delBtnLoading"
        :delMode="delMode"
        @onDelCallback="onDelModal"
      ></del-data-modal>
    </scroll-view>
    <back-page-modal
      ref="backModal"
      :btnLoading="btnLoading"
      @onModalCallback="onBackModal"
    ></back-page-modal>
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import { computed, reactive, ref } from "vue";
import ylgAutoForm from "@/components/ylg-auto-form.vue";
import delDataModal from "./components/delDataModal.vue";
import backPageModal from "./components/backPageModal.vue";
import { onLoad, onShow,onBackPress } from "@dcloudio/uni-app";
import {
  reverseGeocode,
  reTransformLngLat,
  transformLngLat,
} from "@/utils/location";
import { DataAcquisitionService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { usePileNumberStore } from "@/store/pileNumber";
const pileNumberInfo = usePileNumberStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;

// form表单样式配置
const labelStyle = reactive({
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  lineHeight: "40rpx",
  marginLeft: "12rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
});
const placeholderStyle = ref("color: #C1C1C1 !important;");

let isViewDetail = ref(false); // 是否查询详情状态，控制form表单禁用
let isFromDetail = ref(false); // 是否从详情进入、再编辑，控制unionkey配置
onLoad((options) => {
  // 1清空pinia中缓存的之前选择的数据项：如所属路线、所属路段等；
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: "",
      value: "",
      label: "",
    },
  });
  // 2.查询详情数据，并修改状态变量
  if (options.id) {
    getDetail(options.id);
    isViewDetail.value = !!Number(options.status); // 0-草稿；1-正式
    isFromDetail.value = true;
  } else {
    isFromDetail.value = false;
  }
  // 3查询字典枚举库
  getDicts();
});
onShow(() => {
  // 处理从别的页面中选择之后的数据显示，如所属路线选择
  onFormSelChange();
});

let backModal = ref(null);
onBackPress((backOptions) => {
  if(backOptions.from === 'backbutton'){
    if (isViewDetail.value) {
      return false;
    } else {
      backModal.value.open();
      return true;
    }
  }else if(backOptions.from === 'navigateBack'){
		return false;
  }
})

const onNavBack = () => {
  console.log("返回上一页", isViewDetail.value);
  if (isViewDetail.value) {
    uni.navigateBack({
      delta: 1,
    });
  } else {
    backModal.value.open();
  }
};
// 返回上一页modal组件回调事件
const onBackModal = (envRes) => {
  switch (envRes.type) {
    case "onCacel":
      backModal.value.close();
      break;
    case "onNotSave":
      backModal.value.close();
      uni.navigateBack({
        delta: 1,
      });
      break;
    case "onTemporaryDraft":
      onFormSubmit({ data: formData.value, status: 0 });
      break;
    default:
      break;
  }
};

// 查看详情时，点击删除
let delModal = ref(null);
let delMessage = ref("");
let delBtnLoading = ref(false);
let delMode = ref("nomal");
const onDel = async () => {
  delMessage.value = formData.value.sectionName;
  delModal.value.open();
};
const onDelModal = (envRes) => {
  switch (envRes.type) {
    case "onCacel":
      delMode.value = "nomal";
      delModal.value.close();
      break;
    case "onComfirm":
      delCurData();
      break;
    default:
      break;
  }
};
const delCurData = async () => {
  try {
    delBtnLoading.value = true;
    const { code, data, msg } = await DataAcquisitionService.roadPartDel(
      formData.value.id
    );
    delBtnLoading.value = false;
    console.log("删除", code, data);
    if (code == 200) {
      toast.value.show({
        type: "success",
        message: `删除成功`,
        complete() {
          uni.navigateBack({
            delta: 1,
          });
        },
      });
    } else if (code == 500) {
      delMessage.value = msg;
      delMode.value = "error";
    } else {
      toast.value.show({
        type: "error",
        message: `删除失败，请稍后重试~`,
      });
    }
  } catch (error) {
    console.log("删除失败", error);
    delBtnLoading.value = false;
  }
};

// 查询字典枚举
let dicts = ref({});
const getDicts = async () => {
  let { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes: "laneNum,laneDirection,shoulderType",
  });
  console.log("字典", res);
  if (code == 200) {
    dicts.value = data;
  }
};

// 表单数据配置
let formData = ref({
  // 项目id
  projectId: projectInfo.projectId,
  // 路段名称
  sectionName: "",
  // 路段编码
  sectionCode: "",

  // 所属路线
  routeId: "",
  routeName: "",
  routeIdLabel: "",
  // 起点桩号
  startStake: "",
  startStakeId: "",
  startStakeIdLabel: "",
  // 起点桩号名称
  startStakeName: "",
  // 终点桩号
  endStake: "",
  endStakeId: "",
  endStakeIdLabel: "",
  // 终点桩号名称
  endStakeName: "",
  // 路段长度
  sectionLength: "",
  // 养护长度
  maintainLength: "",
  // 可绿化里程
  canAfforestLength: "",
  // 已绿化里程
  afforestLength: "",

  // 行车道
  // 设计行车道数
  designLaneNum: "",
  designLaneNumLabel: "",
  // designLaneNumName: "",
  // 使用行车道数
  useLaneNum: "",
  useLaneNumLabel: "",
  // useLaneNumName: "",
  // 行车道方向属性
  laneDirection: "",
  laneDirectionLabel: "",
  laneDirectionName: "",
  // 实际行车道宽度
  laneWidth: "",
  // 设计行车道宽度
  designLaneWidth: "",

  // 路肩
  sectionGeometryData: {
    // 路肩类型
    shoulderTypeLabel: "",
    shoulderTypeName: "",
    shoulderType: "",
    // 左路肩宽度
    leftShoulderWidth: "",
    // 右路肩宽度
    rightShoulderWidth: "",
  },
});
const rules = computed(() => {
  return {
    sectionName: {
      type: "string",
      required: true,
      message: "请填写路段名称",
      trigger: ["blur"],
    },
    routeIdLabel: {
      type: "string",
      required: isFromDetail.value ? false : true,
      // required:  false,
      message: "请选择所属路线",
      trigger: ["blur", "change"],
    },
    startStakeName: {
      type: "string",
      required: true,
      message: "请填写起点桩号名称",
      trigger: ["blur"],
    },
    endStakeName: {
      type: "string",
      required: true,
      message: "请填写终点桩号名称",
      trigger: ["blur"],
    },
    sectionLength: {
      type: "string",
      required: true,
      message: "请填写路段长度",
      trigger: ["blur"],
    },
    // maintainLength: {
    //   type: "string",
    //   required: true,
    //   message: "请填写养护里程",
    //   trigger: ["blur"],
    // },
    useLaneNumLabel: {
      type: "string",
      required: isFromDetail.value && formData.value.useLaneNum ? false : true,
      message: "请选择使用行车道数",
      trigger: ["blur", "change"],
    },
    laneDirectionLabel: {
      type: "string",
      required: isFromDetail.value && formData.value.laneDirection ? false : true,
      message: "请选择行车道方向属性",
      trigger: ["blur", "change"],
    },
    laneWidth: {
      type: "string",
      required: true,
      message: "请填写实际行车道宽度",
      trigger: ["blur"],
    },
    "sectionGeometryData.shoulderTypeLabel": {
      type: "string",
      required: isFromDetail.value && formData.value.sectionGeometryData.shoulderType ? false : true,
      message: "请选择路肩类型",
      trigger: ["blur", "change"],
    },
    "sectionGeometryData.leftShoulderWidth": {
      type: "string",
      required: true,
      message: "请填写左路肩宽度",
      trigger: ["blur"],
    },
    "sectionGeometryData.rightShoulderWidth": {
      type: "string",
      required: true,
      message: "请填写右路肩宽度",
      trigger: ["blur"],
    },
  };
});
const formConfig = computed(() => {
  return [
    {
      items: [
        {
          type: "mainInput",
          maxlen: 50,
          placeholder: "请输入路段名称",
          unionKey: "sectionName",
        },
        {
          type: "input",
          maxlen: 20,
          label: "路段编码",
          placeholder: "请输入（非必填）",
          unionKey: "sectionCode",
        },
      ],
    },
    {
      title: "路段特征",
      items: [
        {
          type: "select",
          label: "所属路线",
          placeholder: "请选择",
          unionKey: "routeIdLabel",
          unionKeyDetail: formData.value.routeIdLabel
            ? "routeIdLabel"
            : "routeName",
          optionsPath:
            "/pages/dataAcquisition/relationInfoChoose/roadLineChoose",
        },
        {
          type: "select",
          label: "起点桩号",
          placeholder: "请选择（非必填）",
          unionKey: "startStakeIdLabel",
          unionKeyDetail: formData.value.startStakeIdLabel
            ? "startStakeIdLabel"
            : "startStake",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose`,
          pathKey: "sectionId",
        },
        {
          type: "input",
          maxlen: 20,
          label: "起点桩号名称",
          placeholder: "请输入",
          unionKey: "startStakeName",
        },
        {
          type: "select",
          label: "终点桩号",
          placeholder: "请选择（非必填）",
          otherOptionKey: "sectionId",
          unionKey: "endStakeIdLabel",
          unionKeyDetail: formData.value.endStakeIdLabel
            ? "endStakeIdLabel"
            : "endStake",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose`,
          pathKey: "sectionId",
        },
        {
          type: "input",
          maxlen: 20,
          label: "终点桩号名称",
          placeholder: "请输入",
          unionKey: "endStakeName",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "路段长度（km）",
          placeholder: "请输入",
          unionKey: "sectionLength",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "养护里程（km）",
          placeholder: "请输入（非必填）",
          unionKey: "maintainLength",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "可绿化里程（km）",
          placeholder: "请输入（非必填）",
          unionKey: "canAfforestLength",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "已绿化里程（km）",
          placeholder: "请输入（非必填）",
          unionKey: "afforestLength",
        },
      ],
    },
    {
      title: "行车道",
      items: [
        {
          type: "select",
          label: "设计行车道数",
          placeholder: "请选择（非必填）",
          unionKey: "designLaneNumLabel",
          // unionKeyDetail: formData.value.designLaneNumLabel
          //   ? "designLaneNumLabel"
          //   : "designLaneNumName",
          unionKeyDetail: formData.value.designLaneNumLabel
            ? "designLaneNumLabel"
            : "designLaneNum",
          options: dicts.value.laneNum||[],
        },
        {
          type: "select",
          label: "使用行车道数",
          placeholder: "请选择",
          unionKey: "useLaneNumLabel",
          // unionKeyDetail: formData.value.useLaneNumLabel
          //   ? "useLaneNumLabel"
          //   : "useLaneNumName",
          unionKeyDetail: formData.value.useLaneNumLabel
            ? "useLaneNumLabel"
            : "useLaneNum",
          options: dicts.value.laneNum||[],
        },
        {
          type: "select",
          label: "行车道方向属性",
          placeholder: "请选择",
          unionKey: "laneDirectionLabel",
          unionKeyDetail: formData.value.laneDirectionLabel
            ? "laneDirectionLabel"
            : "laneDirectionName",
          options: dicts.value.laneDirection||[],
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "实际行车道宽度（m）",
          placeholder: "请输入",
          unionKey: "laneWidth",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "设计行车道宽度（m）",
          placeholder: "请输入（非必填）",
          unionKey: "designLaneWidth",
        },
      ],
    },
    {
      title: "路肩",
      items: [
        {
          type: "select",
          label: "路肩类型",
          placeholder: "请选择",
          unionKey: "sectionGeometryData.shoulderTypeLabel",
          unionKeyDetail: formData.value.sectionGeometryData.shoulderTypeLabel
            ? "sectionGeometryData.shoulderTypeLabel"
            : "sectionGeometryData.shoulderTypeName",
          options: dicts.value.shoulderType||[],
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "左路肩宽度（m）",
          placeholder: "请输入",
          unionKey: "sectionGeometryData.leftShoulderWidth",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "右路肩宽度（m）",
          placeholder: "请输入",
          unionKey: "sectionGeometryData.rightShoulderWidth",
        },
      ],
    },
  ];
});

// 查看详情
const getDetail = async (id) => {
  const res = await DataAcquisitionService.roadLineDetail(id);
  formData.value = res.data;
  formData.value.sectionId = res.data.id;
  console.log("路段详情", formData.value);
};

// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  console.log(formData.value);
  console.log("formItem数据修改回调", unionKey, val);
  setNestedValue(formData.value, unionKey.split("."), val);
};
// formItem 从选择页面回调的 select 数据修改
let autoFormRef = ref(null);
const onFormSelChange = () => {
  // 处理select选择的label
  setNestedValue(
    formData.value,
    dataAcquisitionInfo.stateData.key.split("."),
    dataAcquisitionInfo.stateData.label
  );
  // 处理select选择的value
  let str = dataAcquisitionInfo.stateData.key.replace(/Label$/, "");
  setNestedValue(
    formData.value,
    str.split("."),
    dataAcquisitionInfo.stateData.value
  );
  console.log("选择更改", formData.value);
  if(autoFormRef.value?.formRef){
    autoFormRef.value.formRef.validateField(dataAcquisitionInfo.stateData.key);
  }
};
const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
};

// 暂存时校验部分表单字段
const validateFormFields = async () => {  
  autoFormRef.value.formRef.clearValidate();  
  let mainUnionKey = 'sectionName';  
  let roadUnionKey = 'routeIdLabel';  
  try {  
    // 使用 async/await 等待 validateField 方法的 Promise 解析  
    let err = await new Promise((resolve) => {  
      autoFormRef.value.formRef.validateField(mainUnionKey, (validateErr) => {  
        resolve(validateErr);  
      });  
    });  
    let err2 = await new Promise((resolve) => {  
      autoFormRef.value.formRef.validateField(roadUnionKey, (validateErr) => {  
        resolve(validateErr);  
      });  
    });  
    console.log('暂存校验结果1', err, err2);  
    if (!err.length && !err2.length) {  
      console.log('暂存校验结果2', flag.value); // 注意：这里的 flag 需要在外部定义并在需要时传递  
      flag.value = true; // 如果 flag 是组件内部状态，考虑使用 Vue 的响应式系统（如 ref 或 reactive）  
    }  
  } catch (error) {  
    console.error('验证过程中发生错误:', error);  
  }  
}

// form表单提交回调
let flag = ref(false);
let toast = ref(null);
let res = ref({});
let btnLoading = ref(""); // '0'-暂存按钮；'1'-提交按钮
const onFormSubmit = async ({ data, status }) => {
  console.log("提交路段采集", data, status);
  if(status == 0){
    await validateFormFields();
  }else{
    flag.value = true;
  }
  if(!flag.value){
    console.log('校验不通过，return',flag.value);
    backModal.value.close();
    return;
  }
  // 正式-1；草稿-0
  data.status = status;
  data.sectionCode = data.sectionCode || null;
  btnLoading.value = String(status);

  // 将临时新增到本地保存的桩号，一同传递给后端
  if(pileNumberInfo.pileNumbers?.length){
    data.newStakes = pileNumberInfo.pileNumbers;
    data.newStakes.forEach(item=>{
      item.status = status;
      // // 经纬度转换
      // let trans = transformLngLat(item.longitude,item.latitude);
      // item.longitude = String(trans[0]);
      // item.latitude = String(trans[1]);
    });
  }

  try {
    if (data.id) {
      // 编辑
      res.value = await DataAcquisitionService.roadLineEdit(data);
    } else {
      // 新增
      res.value = await DataAcquisitionService.roadLineAdd(data);
    }
    btnLoading.value = "";
    console.log("res！！！！", res.value.data);
    if (res.value.code == 200) {
      // 清空本地保存的新增桩号
      pileNumberInfo.clearPileNumber();
      toast.value.show({
        type: "success",
        message: `数据${status ? "提交" : "暂存"}成功`,
        complete() {
          uni.navigateBack({
            delta: 1,
          });
        },
      });
    }
  } catch (error) {
    btnLoading.value = "";
  }
};

// 切换表单状态：详情-》编辑
const onFormEdit = () => {
  isViewDetail.value = false;
  isFromDetail.value = true;
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding-top: v-bind(systemBarHeight);
  background: #f4f8ff;
}
.uv-nav-slot {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}
.content {
  height: calc(100% - 208rpx);
  margin-top: 0rpx;
  .info_card {
    background: #fff;
    margin-bottom: 48rpx;
    .info_title {
      height: 88rpx;
      padding: 22rpx 40rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #8e8e8e;
      line-height: 44rpx;
      box-sizing: border-box;
      border-bottom: 2rpx solid #f0f0f0;
    }
  }
}
.bottom_btns {
  position: fixed;
  bottom: 0rpx;
  width: 100vw;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  background: #f4f8ff;
  .btn {
    width: 256rpx;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    box-sizing: border-box;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .save_btn {
    color: #4378ff;
    background: #fff;
    border: 2rpx solid #4378ff;
  }
  .sub_btn {
    color: #fff;
    background: #4378ff;
  }
}
</style>
