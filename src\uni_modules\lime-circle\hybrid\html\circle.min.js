!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).lime={})}(this,(function(t){"use strict";function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){for(var i=0;e.length>i;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function n(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function r(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==i)return;var n,r,a=[],o=!0,s=!1;try{for(i=i.call(t);!(o=(n=i.next()).done)&&(a.push(n.value),!e||a.length!==e);o=!0);}catch(t){s=!0,r=t}finally{try{o||null==i.return||i.return()}finally{if(s)throw r}}return a}(t,e)||a(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t,e){if(t){if("string"==typeof t)return o(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?o(t,e):void 0}}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}var s=function(t){return/^#.{3,6}$/.test(t)?4===t.length?t.substring(1).split("").map((function(t){return 17*parseInt(t,16)})):[t.substring(1,3),t.substring(3,5),t.substring(5,7)].map((function(t){return parseInt(t,16)})):(console.error("lime-circle: 渐变仅支持hex值"),[0,0,0])},u=function(t){return 1===t.length?"0"+t:t},h=function(t,e,i){var n,r,a,o,h=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,l=[],c=[],f=function(t){return Math.pow(t/255,h)};for(t=s(t).map(f),e=s(e).map(f),n=0;i>n;n++){for(o=1-(a=n/(i-1)),r=0;3>r;r++)c[r]=u(Math.round(255*Math.pow(t[r]*o+e[r]*a,1/h)).toString(16));l.push("#"+c.join(""))}return l};var l=function(t,e,i,n){var r=1e-6,a=3*t-3*i+1,o=3*i-6*t,s=3*t,u=3*e-3*n+1,h=3*n-6*e,l=3*e;function c(t){return((a*t+o)*t+s)*t}return function(t){return e=function(t){for(var e,i,n,u=t,h=0;8>h;h++){if(i=c(u)-t,r>Math.abs(i))return u;if(r>Math.abs(e=(3*a*(n=u)+2*o)*n+s))break;u-=i/e}var l=1,f=0;for(u=t;l>f;){if(i=c(u)-t,r>Math.abs(i))return u;i>0?l=u:f=u,u=(l+f)/2}return u}(t),((u*e+h)*e+l)*e;var e}}(.25,.1,.25,1),c=Symbol("tick"),f=Symbol("tick-handler"),d=Symbol("animations"),v=Symbol("start-times"),m=Symbol("pause-start"),y=Symbol("pause-time"),p="undefined"!=typeof requestAnimationFrame?requestAnimationFrame:function(t){return setTimeout(t,1e3/60)},g="undefined"!=typeof cancelAnimationFrame?cancelAnimationFrame:function(t){clearTimeout(t)},b=function(){function t(){e(this,t),this.state=void 0,this.state="Initiated",this[d]=new Set,this[v]=new Map}return n(t,[{key:"start",value:function(){var t=this;if("Initiated"===this.state){this.state="Started";var e=Date.now();this[y]=0,this[c]=function(){var i,n=Date.now(),r=function(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=a(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return s=t.done,t},e:function(t){u=!0,o=t},f:function(){try{s||null==i.return||i.return()}finally{if(u)throw o}}}}(t[d]);try{for(r.s();!(i=r.n()).done;){var o=i.value,s=void 0;(s=t[v].get(o)<e?n-e-o.delay-t[y]:n-t[v].get(o)-o.delay-t[y])>o.duration&&(t[d].delete(o),s=o.duration),s>0&&o.run(s)}}catch(t){r.e(t)}finally{r.f()}t[f]=p(t[c])},this[c]()}}},{key:"pause",value:function(){"Started"===this.state&&(this.state="Paused",this[m]=Date.now(),g(this[f]))}},{key:"resume",value:function(){"Paused"===this.state&&(this.state="Started",this[y]+=Date.now()-this[m],this[c]())}},{key:"reset",value:function(){this.pause(),this.state="Initiated",this[y]=0,this[m]=0,this[d]=new Set,this[v]=new Map,this[f]=null}},{key:"add",value:function(t,e){2>arguments.length&&(e=Date.now()),this[d].add(t),this[v].set(t,e)}}]),t}(),w=function(){function t(i,n,r,a,o,s){e(this,t),this.startValue=void 0,this.endValue=void 0,this.duration=void 0,this.timingFunction=void 0,this.delay=void 0,this.template=void 0,o=o||function(t){return t},s=s||function(t){return t},this.startValue=i,this.endValue=n,this.duration=r,this.timingFunction=o,this.delay=a,this.template=s}return n(t,[{key:"run",value:function(t){var e=this.endValue-this.startValue,i=this.timingFunction(t/this.duration);this.template(this.startValue+e*i)}}]),t}(),A=Math.PI/180,k=function(){function t(i,n){e(this,t),this.canvas=void 0,this.context=void 0,this.current=0,this.size=0,this.pixelRatio=1,this._isConicGradient=!1,this._attrs={percent:0,size:120,lineCap:"round",strokeWidth:6,strokeColor:"#2db7f5",trailWidth:6,trailColor:"#ddd",dashboard:!1,clockwise:!0,duration:300,max:100,beforeAnimate:!0,animate:!0,formatter:"{d}{d}.{d}{d}%",fontSize:"16px",showText:!1,gapDegree:90,gapPosition:"bottom"},this._timer=void 0,this.startTime=0,this.target=0,this._colors=[],this._gradientColors=[],this._rAF=function(t){},this._cAf=function(t){},this.timeline=void 0,this.run=void 0,this.canvas=i,this.run=n.run,this.size=n.size||120,this.pixelRatio=n.pixelRatio||1,this.init(),this.initRaf(),this.timeline=new b}return n(t,[{key:"init",value:function(){var t=this.size,e=this.pixelRatio;if(this.canvas){this.canvas.width=t*e,this.canvas.height=t*e;var i=this.canvas.getContext("2d");this._isConicGradient=!!i.createConicGradient,this.context=i}}},{key:"initRaf",value:function(){var t=this.canvas;"undefined"!=typeof window?(this._rAF=window.requestAnimationFrame||function(t){return window.setTimeout(t,1e3/60)},this._cAf=window.cancelAnimationFrame||function(t){window.clearTimeout(t)}):t&&t.requestAnimationFrame?(this._rAF=t.requestAnimationFrame,this._cAf=t.cancelAnimationFrame):(this._rAF=function(t){return setTimeout(t,16.7)},this._cAf=function(t){clearTimeout(t)})}},{key:"setOption",value:function(t){Object.assign(this._attrs,t)}},{key:"set",value:function(t,e){this._attrs[t]=e}},{key:"get",value:function(t){return this._attrs[t]||this.canvas[t]}},{key:"play",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.get("percent");this.target=Math.max(Math.min(e,this.get("max")||100),0),this.createConicGradient(),this.timeline.start(),this.timeline.add(new w(this.current,e,this.get("duration"),0,l,(function(e){t.current=1e-4>e?0:e,t.render(t.current),t.run(t.current)})))}},{key:"createConicGradient",value:function(){if(!this._isConicGradient){var t=this._attrs.strokeColor;if("string"!=typeof t&&this._colors!=t&&Array.isArray(t)){var e=r(this.getDeg(),2),i=e[0],n=e[1];this._colors=t,this._gradientColors=[];for(var a=n-i,o=t.length-1,s=Math.floor(a/o),u=0;o>u;u++){a-=s,this._gradientColors=this._gradientColors.concat(h(t[u],t[u+1],u+1===o?s+a:s,1))}}}}},{key:"render",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=this.context,i=this.size,n=this.pixelRatio,r=this.getSalce(),a=i/2;e.setTransform(1,0,0,1,0,0),e.clearRect(2*-a,2*-a,4*i,4*i),e.setTransform(r*n,0,0,n,a*n,a*n),this.drawTrail(a),this.drawStroke(a,t),e.draw&&e.draw()}},{key:"drawArc",value:function(t,e,i,n,r){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this._attrs.lineCap,o=this.context;o.beginPath(),o.lineCap=a,o.strokeStyle=e,o.lineWidth=i,o.arc(0,0,t,n,r),o.stroke()}},{key:"getSalce",value:function(){return this.get("clockwise")?1:-1}},{key:"getDeg",value:function(){var t=this._attrs,e=t.gapDegree;t.dashboard||(e=0);var i=(0===e?0:{bottom:0,top:180,left:90,right:-90}[t.gapPosition])+(e>0?90+e/2:-90)+0;return[i,i+360*((360-e)/360)]}},{key:"drawTrail",value:function(t){var e=this._attrs,i=e.trailWidth,n=e.trailColor,a=t-i/2,o=r(this.getDeg(),2);this.drawArc(a,n,i,o[0]*A,o[1]*A)}},{key:"drawStroke",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(e){var i=this._attrs,n=i.strokeWidth,a=i.strokeColor,o=i.max,s=i.dashboard,u=i.lineCap,h=this.getDeg(),l=r(h,2),c=l[0],f=l[1],d=t-n/2,v=Math.round((f-c)/o*e);if("string"==typeof a||this._isConicGradient)if(Array.isArray(a)&&this._isConicGradient){var m=this.context,y=m.createConicGradient(c,0,0);a.forEach((function(t,e){y.addColorStop(e/(a.length-1),t)})),this.drawArc(d,y,n,c*A,(v+c)*A)}else this.drawArc(d,a,n,c*A,(v+c)*A);else for(var p=0;v>p;p++)this.drawArc(d,this._gradientColors[p],n,(p+c)*A,(p+1+c)*A,s||f!=v+c?u:"butt")}}}]),t}();t.Circle=k,Object.defineProperty(t,"__esModule",{value:!0})}));
