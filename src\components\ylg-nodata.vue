<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-03-27 16:50:40
 * @Description: 
-->
<template>
  <view class="content">
    <image class="img" :src="imgUrl" :style="imgStyle" mode="widthFix" />
    <view class="noice" :style="{fontSize: textSize, color: textColor, marginTop: textMarginT}">{{ noticeTip }}</view>
  </view>
</template>
<script setup>
import { ref } from 'vue';
const props = defineProps({
  imgUrl:{
    type: String,
    default: '/static/image/list_nodata.png'
  },
  imgStyle: {
    type: Object,
    default: () => ({
        width: '544rpx',
        // height: '544rpx'
    })
  },
  noticeTip:{
    type: String,
    default:'暂无相关数据～'
  },
  textSize: {
    type: String,
    default: '36rpx'
  },
  textColor: {
    type: String,
    default: '#A09F9F'
  },
  textMarginT: {
    type: String,
    default: '48rpx'
  }
})

</script>
<style lang="scss" scoped>
.content{
  display: flex;
  flex-direction: column;
  align-items: center;
}
.img{
  display: block;
  width: 544rpx;
  // height: 544rpx;
}
.noice{
  text-align: center;
  margin-top: 48rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 36rpx;
  color: #A09F9F;
  line-height: 50rpx;
}
</style>