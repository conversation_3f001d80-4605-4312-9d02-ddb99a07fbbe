<template>
  <view class="blank-box">
    <image
      class="black-icon"
      :src="eventHandling_nodata_placeholder"
      mode="scaleToFill"
    />
    <view class="black-tip">暂无数据，请点击右上角按钮添加</view>
  </view>
</template>

<script setup>
import eventHandling_nodata_placeholder from "@/static/icon/eventHandling_nodata_placeholder.png";
</script>

<style lang="scss" scoped>
.blank-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 28rpx;
  height: 220rpx;
  background: #f1f1f1;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  .black-icon {
    display: block;
    width: 120rpx;
    height: 120rpx;
  }
  .black-tip {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #a09f9f;
    line-height: 32rpx;
  }
}
</style>
