<template>
  <view class="container">
    <template v-if="!pageLoading">
      <view class="check-version">
        <view class="check-row">
          <view class="check-left">
            <view class="label">当前版本</view>
            <view class="version">{{ getSearchVersion().sysVersion }}</view>
          </view>
          <view class="check-right flex-center">
            <uv-loading-icon v-show="checkLoading" mode="spinner" size="19"></uv-loading-icon>
            <view class="check-right_btn" @click="checkUpdate">检查更新</view>
          </view>
        </view>
      </view>
      <view class="version-details">
        <view v-if="baseInfo.updateAvailable" class="details">
          <view class="tip">
            <image class="icon" :src="version_tip" mode="widthFix" />
            <text class="label"> 你有一个可用的版本更新，建议立即更新 </text>
          </view>
          <view class="detail-row">
            <view class="label"> 版本号：</view>
            <view class="content">{{ baseInfo.version }} </view>
          </view>
          <view class="detail-row">
            <view class="label"> 发布时间：</view>
            <view class="content"> {{ baseInfo.publishTime }} </view>
          </view>
          <view v-if="baseInfo.apkSize" class="detail-row">
            <view class="label">安装包大小： </view>
            <view class="content">{{ setSize(baseInfo.apkSize) }} </view>
          </view>
          <view class="detail-content">
            <view class="label">更新内容： </view>
            <view class="paragraphs">
              <view
                v-for="(item, index) in paragraphs"
                :key="index"
                class="content paragraph"
              >
                {{ item }}
              </view>
            </view>
          </view>
        </view>
        <no-version v-else class="nodata"></no-version>
      </view>
    </template>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="检查中..."
      font-size="24rpx"
    ></uv-loading-page>
    <view
      class="bottom_box"
      v-if="baseInfo.updateAvailable && baseInfo.apkPath"
    >
      <view class="btn" @click="updateNow">立即更新</view>
    </view>
    <view
      v-if="baseInfo.updateAvailable && !baseInfo.apkPath"
      class="bottom_box"
    >
      <view class="btn" @click="IKnew">我知道了</view>
    </view>
    <UpdateVersionModal
      ref="UpdateVersionModalRef"
      v-if="UpdateVersionModalVisible"
      :url="url"
      :baseInfo="baseInfo"
      @openModal="openModal"
    />
  </view>
</template>
<script setup>
import { ref, computed } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import NoVersion from "@/components/ylg-noversion.vue";
import version_tip from "/static/icon/version_tip.png";
import { VersionService } from "@/service";
import UpdateVersionModal from "./components/UpdateVersionModal.vue";

const pageLoading = ref(false);
const url = ref("");
const downloadTask = ref(null);
const UpdateVersionModalVisible = ref(false); // 全量更新modal
// 版本详情信息
const baseInfo = ref({
  // version: "V 0.5.0",
  // time: "2021-10-28 10:00:00",
  // size: "2MB",
  // content:
  //   "1.【事件处置记录】模块新增“事件重新处置”功能，支持对病害/其他事件进行重新处置，更便于用户操作，减少错误数据的产生 \n2.【事件处置】模块新增“病害事件上报”与“其他事件上报”入口，方便用户在网页端进行事件补录 \n3.【道路资产管理】模块详情页新增“操作日志”展示，支持对道路资产及其文件进行全生命周期操作留痕 \n4.【项目管理】模块详情页新增“操作日志”展示，支持对项目及其文件进行全生命周期操作留痕 \n5.【事件处置记录】模块详情页新增“操作日志”展示，支持对病害处置记录进行操作留痕\n1.【事件处置记录】模块新增“事件重新处置”功能，支持对病害/其他事件进行重新处置，更便于用户操作，减少错误数据的产生 \n2.【事件处置】模块新增“病害事件上报”与“其他事件上报”入口，方便用户在网页端进行事件补录 \n3.【道路资产管理】模块详情页新增“操作日志”展示，支持对道路资产及其文件进行全生命周期操作留痕 \n4.【项目管理】模块详情页新增“操作日志”展示，支持对项目及其文件进行全生命周期操作留痕 \n5.【事件处置记录】模块详情页新增“操作日志”展示，支持对病害处置记录进行操作留痕\n",
});
// 获取段落
const paragraphs = computed(() => {
  const ps = baseInfo.value.releaseNote.split("\n") || [];
  return ps;
});
const UpdateVersionModalRef = ref(); // 更新弹框
const setSize = (size) => {
  if (!size) return 0 + "B";
  let data = "";
  const _size = Number.parseFloat(size);
  if (_size < 1 * 1024) {
    //如果小于0.1KB转化成B
    data = _size.toFixed(2) + "B";
  } else if (_size < 1 * 1024 * 1024) {
    //如果小于0.1MB转化成KB
    data = (_size / 1024).toFixed(2) + "KB";
  } else if (_size < 1 * 1024 * 1024 * 1024) {
    //如果小于0.1GB转化成MB
    data = (_size / (1024 * 1024)).toFixed(2) + "MB";
  } else {
    //其他转化成GB
    data = (_size / (1024 * 1024 * 1024)).toFixed(2) + "GB";
  }
  const size_str = data + "";
  const len = size_str.indexOf(".");
  const dec = size_str.substr(len + 1, 2);
  if (dec == "00") {
    //当小数点后为00时 去掉小数部分
    return size_str.substring(0, len) + size_str.substr(len + 3, 2);
  }
  return size_str;
};
// 获取版本号
const getSearchVersion = () => {
  const { sysVersion, appVersion, sysPlatform } = uni.getStorageSync("version");
  return {
    sysVersion: sysVersion || appVersion,
    sysPlatform: sysPlatform || process.env.APP_DESCRIPTION
  };
};
// 展示版本相关
// 获取最新版本
let checkLoading = ref(false)
const getNewVersion = async () => {
  console.log("获取最新版本", getSearchVersion());
  try {
    checkLoading.value = true;
    const { code, data } = await VersionService.isAppUpdateAvailable({
      business: "yhPublishPlatform",
      version: getSearchVersion().sysVersion,
      publishPlatform: getSearchVersion().sysPlatform,
      publishPlatforms: uni.getStorageSync("platforms").join(','),
    });
    if (code === 200 && data) {
      baseInfo.value = data;
      console.log("个人中心版本信息", baseInfo.value);
    }
    checkLoading.value = false;
  } catch (error) {
    checkLoading.value = false;
    console.log("版本更新检查出错", error);
  }
};
// 检查更新
const checkUpdate = () => {
  // pageLoading.value = true;
  getNewVersion();
};
// 立即更新
const updateNow = async () => {
  console.log("立即更新", baseInfo.value.apkPath);
  if (!baseInfo.value.apkPath) return;
  const filePath = baseInfo.value.apkPath;
  const firstIndex = filePath.indexOf("/");
  const firstPart = filePath.substring(0, firstIndex);
  const secondPart = filePath.substring(firstIndex);
  url.value =
    "https://" +
    firstPart +
    ".obs.cn-southwest-2.myhuaweicloud.com" +
    secondPart;
  console.log(firstPart);
  console.log(secondPart);
  console.log(url.value);
  console.log("版本更新方式", baseInfo.value.updateMethod);
  // 热更
  if (baseInfo.value.updateMethod === "hotUpdate") {
    console.log("热更");
    comfirmUpdate(url.value);
  } else {
    // 全量更新
    UpdateVersionModalVisible.value = true;
  }
};
// 我知道了
const IKnew = () => {
  // 更新sys
  const sysVersion = baseInfo.value.version.toLowerCase();
  const cacheVersion = uni.getStorageSync("version");
  setTimeout(() => {
    uni.setStorageSync("version", {
      ...cacheVersion,
      sysVersion,
    });
  }, 1);
  console.log("个人中心我知道了-接口缓存", sysVersion);
  console.log("个人中心我知道了-获取的缓存", cacheVersion);
  console.log("个人中心我知道了-更新缓存", {
    ...cacheVersion,
    sysVersion,
  });

  setTimeout(() => {
    checkUpdate(); // 重新去检测更新
  }, 100);
};
// 热更
const comfirmUpdate = (url) => {
  pageLoading.value = true;
  downloadTask.value = uni.downloadFile({
    url,
    success: (res) => {
      if (res.statusCode === 200) {
        // 更新dl
        const dlVersion = baseInfo.value.version.toLowerCase();
        const cacheVersion = uni.getStorageSync("version");
        setTimeout(() => {
          uni.setStorageSync("version", {
            ...cacheVersion,
            dlVersion,
          });
        }, 100);
        console.log("热更下载完成");
        console.log("个人中心热更读取的缓存", uni.getStorageSync("version"));
        // 写入缓存后安装
        setTimeout(() => {
          installWgt(res.tempFilePath);
        }, 200);
        pageLoading.value = false;
      } else {
        uni.showToast({
          title: "下载失败",
          icon: "none",
        });
        pageLoading.value = false;
      }
    },
    fail: (err) => {
      uni.showToast({
        title: "下载失败",
        icon: "none",
      });
      pageLoading.value = false;
    },
  });
  // 监听下载进度
  downloadTask.value.onProgressUpdate((progress) => {
    console.log("热更下载进度", progress.progress);
  });
};
const installWgt = (filePath) => {
  plus.runtime.install(
    filePath,
    {
      force: true,
    },
    () => {
      console.log("安装成功！！！！");
      plus.runtime.restart();
    },
    (err) => {
      console.log("安装失败！！！！", err);
      uni.showToast({
        icon: "none",
        title: "安装失败~" + err,
      });
    }
  );
};
// 全量更新 to 当前页面 打开弹窗
const openModal = () => {
  UpdateVersionModalVisible.value = false;
};
onShow(() => {
  console.log("系统版本onShow");
  if (!UpdateVersionModalRef.value) {
    getNewVersion(); // 初始化版本信息
  }
});
</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 40rpx;
  background: #f4f8ff;
  box-sizing: border-box;
  .tips {
    color: #404040;
    font-family: PingFang SC-Regular;
    font-size: 32rpx;
    margin-bottom: 48rpx;
  }
  .check-version {
    margin-bottom: 28rpx;
    .check-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 92rpx;
      background: #ffffff;
      padding: 22rpx 28rpx;
      box-sizing: border-box;
      font-family: PingFang SC-Medium;
      border: 2rpx solid #f0f0f0;
      border-radius: 16rpx;
      box-shadow: 4rpx 4rpx 20rpx 0rpx rgba(255, 255, 255, 0.04);
      margin-bottom: 32rpx;
      .check-left {
        display: flex;
        .label {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #373737;
          line-height: 33rpx;
          margin-right: 28rpx;
        }
        .version {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 28rpx;
          color: #373737;
          line-height: 33rpx;
        }
      }
      .check-right {
        &_btn {
          margin-left: 6rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 28rpx;
          color: #4378ff;
          line-height: 33rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    .active_card {
      border-color: #4378ff;
      background: #ecf1ff;
      .item_title {
        color: #4378ff;
      }
    }
  }
  .version-details {
    width: 670rpx;
    height: 1080rpx;
    // height: calc(100vh - 460rpx);
    background: #ffffff;
    box-shadow: 4rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.08);
    border-radius: 16rpx;
    .details {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      line-height: 33rpx;
      padding: 12rpx 32rpx 0 32rpx;
      .label {
        color: #a09f9f;
      }
      .content {
        color: #373737;
      }
      .tip {
        display: flex;
        align-items: center;
        height: 80rpx;
        border-bottom: 2px solid #f2f2f2;
        .icon {
          display: block;
          width: 36rpx;
          height: 36rpx;
          margin-right: 12rpx;
        }
      }
      .detail-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 80rpx;
        border-bottom: 2px solid #f2f2f2;
      }
      .detail-content {
        .label {
          display: flex;
          align-items: center;
          height: 80rpx;
        }
        .content {
          white-space: pre-wrap;
        }
        .paragraphs {
          height: 620rpx;
          overflow-y: auto;
          .paragraph {
            margin-bottom: 32rpx;
          }
        }
      }
    }
    .nodata {
      padding-top: 106rpx;
    }
  }
}

.bottom_box {
  width: calc(100% - 80rpx);
  position: fixed;
  bottom: 92rpx;
  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100rpx;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
  }
  .btn-forbid {
    background: #a09f9f;
  }
}
</style>
