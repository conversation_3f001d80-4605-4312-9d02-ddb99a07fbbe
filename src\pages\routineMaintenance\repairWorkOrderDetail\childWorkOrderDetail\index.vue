<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-09-27 11:49:15
 * @Description: 
-->
<template>
  <view class="container">
    <template v-if="!pageLoading">
      <view class="item_box">
        <view class="label">工单编码</view>
        <view class="content">{{ detailData?.childWorkCode || "-" }}</view>
        <view class="right_btn" @click="copy(detailData?.childWorkCode)"
          >复制</view
        >
      </view>
      <view class="item_box">
        <view class="label">上报人</view>
        <view class="content">{{ detailData?.updateUserName || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">上报时间</view>
        <view class="content">{{ detailData?.updateTime || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">地址信息</view>
        <view class="content">{{ detailData?.address || "-" }}</view>
        <view class="right_btn" @click="viewLocation">查看</view>
      </view>
      <view class="item_box">
        <view class="label">施工情况说明</view>
        <view class="content">{{ detailData?.conditionRemark || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">施工前照片</view>
        <view class="imgs">
          <ylg-load-image class="img" :scroll-top="0" loading-mode="spin-circle" :image-src="showImg(detailData?.buildBeforePath)" @click="previewImg(0)"></ylg-load-image>
        </view>
      </view>
      <view class="item_box">
        <view class="label">施工中照片</view>
        <view class="imgs">
          <ylg-load-image class="img" :scroll-top="0" loading-mode="spin-circle" :image-src="showImg(detailData?.buildPath)" @click="previewImg(1)"></ylg-load-image>
        </view>
      </view>
      <view class="item_box">
        <view class="label">施工后照片</view>
        <view class="imgs">
          <ylg-load-image class="img" :scroll-top="0" loading-mode="spin-circle" :image-src="showImg(detailData?.buildAfterPath)" @click="previewImg(2)"></ylg-load-image>
        </view>
      </view>
    </template>
    <uv-loading-page
    :loading="pageLoading"
    loading-text="加载中..."
    font-size="24rpx"
  ></uv-loading-page>
  </view>
</template>
<script setup>
import { ref, reactive } from "vue";
import { RoutineMaintenanceService } from "@/service";
import { onLoad } from "@dcloudio/uni-app";
import { reTransformLngLat } from "@/utils/location";
import { showImg } from "@/utils";

let pageLoading = ref(true);
onLoad((options) => {
  getDetail(options.workId, options.childWorkId);
});
let detailData = ref({});
let previewImgs = reactive([]);
const getDetail = async (workId, childWorkId) => {
  try {
    let { code, data } = await RoutineMaintenanceService.childOrderDetail({
      workId,
      childWorkId,
    });
    console.log("作业详情", code, data);

    if (code == 200) {
      detailData.value = data;
      if(data.buildBeforePath){
        previewImgs.push(showImg(data.buildBeforePath))
      }
      if(data.buildPath){
        previewImgs.push(showImg(data.buildPath))
      }
      if(data.buildAfterPath){
        previewImgs.push(showImg(data.buildAfterPath))
      }
    }
    pageLoading.value = false;
  } catch (error) {
    pageLoading.value = false;
    console.log("获取详情失败", error);
  }
};

const copy = (e) => {
  uni.setClipboardData({
    data: e, // e是你要保存的内容
    success: function () {
      uni.showToast({
        title: "复制成功！",
        icon: "none",
      });
    },
  });
};
// 查看定位
const viewLocation = () => {
  // 将后端返回的标准坐标转换为高德使用的gcj02坐标
  let reTrans = reTransformLngLat(
    detailData.value.longitude,
    detailData.value.latitude
  );
  uni.navigateTo({
    url: `/pages/roadConditionInspection/coordinatePicking?isDetail=${true}&curLongitude=${String(
      detailData.value.longitude
    )}&curLatitude=${String(detailData.value.latitude)}&curAddress=${
      detailData.value.address
    }`,
  });
};

// 预览图片
const previewImg = (cur) => {
  uni.previewImage({
    urls: previewImgs, // 需要预览的图片HTTP链接列表
    current: cur, // 当前显示图片的链接索引
  });
};
</script>
<style lang="scss" scoped>
// .container {
// }
.item_box {
  display: flex;
  align-items: center;
  padding: 24rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #404040;
  line-height: 40rpx;
  .label {
    width: 168rpx;
  }
  .content {
    margin-left: 66rpx;
    flex: 1;
    // width: 300rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .right_btn {
    width: 96rpx;
    text-align: right;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 24rpx;
    color: #4378ff;
    line-height: 34rpx;
  }
  .imgs {
    display: flex;
    align-items: center;
    margin: 0 0 0 66rpx;
    .img {
      display: block;
      width: 176rpx;
      height: 176rpx;
      border-radius: 8rpx;
      margin-right: 34rpx;
    }
  }
}
</style>
