<template>
  <view class="implementation-details">
    <view class="shm-same-add-title">通知执行</view>
    <scroll-view scroll-y="true" class="scroll-Y">
      <view class="title">通知内容</view>
      <view class="notice-content">{{ noticeContent || "--" }}</view>
    </scroll-view>
    <template v-for="(item, index) of sendDetailList" :key="index">
        <view
          class="send-status"
          v-if="item.msgSendType !== 'APP'"
        >
          <view class="title">发送情况</view>
          <view class="send-userc flex-start-top">
            <view
              class="user-name"
              v-for="(temp, index) of item.sendConditionList"
              :key="index"
              :class="'user-fail' + temp.sendStatus"
              >{{ temp.userName }}</view
            >
          </view>
          <view class="flex-start-center">
            <view class="title send-type">短信</view>
            <uv-line-progress :percentage="(item.sendSuccess / item.sendTotal) * 100" height="20rpx" activeColor="#71D47B" :showText="false"></uv-line-progress>
          </view>
        </view>
    </template>
    <view class="device-box">
      <view class="shm-same-add-title"
        >设备执行 ({{ deviceInProgress }}/{{ deviceTotal }})</view
      >
      <template v-if="executeDetailVOList.length">
        <view
          v-for="(item, index) of executeDetailVOList"
          :key="index"
          class="device-item"
        >
          <view class="device-corner-mark">
            <template v-if="item.stopExecuteStatus !== null">
              <view
                class="device-corner"
                :class="'device-corner' + item.stopExecuteStatus"
              >
                <!-- 操作中 -->
                <text v-if="item.stopExecuteStatus === '0'" style="color: #fff"
                  >操作中</text
                >
                <!-- 中断成功 -->
                <text v-if="item.stopExecuteStatus === '1'">中断成功</text>
                <!-- 中断失败 -->
                <text v-if="item.stopExecuteStatus === '2'">中断失败</text>
              </view>
            </template>
            <template v-else>
              <view
                class="device-corner"
                :class="'device-corner' + item.executeStatus"
              >
                <!-- 执行中 -->
                <text v-if="item.executeStatus === '0'" style="color: #fff"
                  >执行中</text
                >
                <!-- 执行完毕 -->
                <text v-else-if="item.executeStatus === '1'">执行完毕</text>
                <!-- 执行失败 -->
                <text v-if="item.executeStatus === '2'">执行失败</text>
              </view>
            </template>
          </view>

          <view class="device-name">{{ item.deviceName }}</view>
          <view class="flex-start-top same-item">
            <view class="label">设备ID：</view>
            <view class="content">{{ item.deviceId }}</view>
          </view>
          <view class="flex-start-top same-item">
            <view class="label">设备类型：</view>
            <view class="content">{{ item.deviceTypeName }}</view>
          </view>
          <view class="flex-start-top same-item">
            <view class="label">监测参数：</view>
            <view class="content">
              <uv-read-more
                show-height="100rpx"
                :toggle="true"
                closeText="显示更多"
              >
                <view class="content"> {{ item.monitoringParam }}</view>
              </uv-read-more>
            </view>
          </view>
          <view class="flex-start-top same-item">
            <view class="label">执行命令：</view>
            <view class="content">{{ item.executeOrder }}</view>
          </view>
          <view class="flex-start-top same-item">
            <template v-if="item.stopExecuteStatus !== null">
              <uv-button
                type="primary"
                loading
                v-if="
                  item.stopExecuteStatus === '0' && hasAuth('alarmEventsEdit')
                "
                :custom-style="startBtnStyle"
                :customTextStyle="btnTextStyle"
                plain
                text="操作中"
              ></uv-button>
              <uv-button
                type="primary"
                icon="/static/shmStatic/icon/zhixingmingling_20250414.png"
                iconColor="#fff"
                v-else-if="
                  item.stopExecuteStatus === '1' && hasAuth('alarmEventsEdit')
                "
                :custom-style="startBtnStyle"
                :customTextStyle="btnTextStyle"
                @click="handleExecute(item)"
                text="执行命令"
                :loading="item.btnLoading"
                :disabled="item.btnLoading"
              ></uv-button>
              <uv-button
                type="primary"
                icon="/static/shmStatic/icon/zhongduanzhixing1_20250414.png"
                iconColor="#fff"
                v-else-if="
                  item.stopExecuteStatus === '2' && hasAuth('alarmEventsEdit')
                "
                :custom-style="startBtnStyle"
                :customTextStyle="btnTextStyle"
                @click="handleExecute(item)"
                text="中断执行"
                :loading="item.btnLoading"
                :disabled="item.btnLoading"
              ></uv-button>
            </template>
            <template v-else>
              <uv-button
                type="primary"
                icon="/static/shmStatic/icon/zhongduanzhixing2_20250414.png"
                iconColor="#fff"
                v-if="item.executeStatus === '0' && hasAuth('alarmEventsEdit')"
                :custom-style="startBtnStyle"
                :customTextStyle="btnTextStyle"
                @click="handleExecute(item)"
                text="中断执行"
                :loading="item.btnLoading"
                :disabled="item.btnLoading"
              ></uv-button>
              <uv-button
                type="primary"
                icon="/static/shmStatic/icon/zhixingmingling_20250414.png"
                iconColor="#fff"
                @click="handleExecute(item)"
                v-if="item.executeStatus !== '0' && hasAuth('alarmEventsEdit')"
                :custom-style="startBtnStyle"
                :customTextStyle="btnTextStyle"
                text="执行命令"
                :loading="item.btnLoading"
                :disabled="item.btnLoading"
              ></uv-button>
            </template>
          </view>
        </view>
      </template>
      <ylg-nodata
        v-else
        textSize="30rpx"
        :imgStyle="{ width: '380rpx' }"
      ></ylg-nodata>
    </view>
    <uv-toast ref="toast"></uv-toast>
  </view>
</template>
<script setup>
import { ref } from "vue";
import {
  onHide,
  onLoad,
  onShow,
  onUnload,
  onBackPress,
} from "@dcloudio/uni-app";
import { AssetService } from "@/service";
import { hasAuth } from "@/config/permissions.js";
const props = defineProps({
  eventItemId: {
    type: String,
    required: true,
    default: "",
  },
});

const startBtnStyle = {
  width: "100%",
  height: "60rpx",
  lineHeight: "60rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378FF",
  color: " #ffffff",
};
const btnTextStyle = {
  fontSize: "28rpx",
};
let toast = ref(null);
// 按钮状态（按钮状态存在：中断执行、操作中、执行命令）
// 1、中断执行：中断当前报警规则执行动作已下发的命令（当执行状态为中断失败时，此按钮前方的图标状态需变更）
// 2、操作中：loding状态，此时按钮不可点击，同时执行状态置灰
// 3、执行命令：执行报警规则执行动作中配置的命令
// 直接操作命令
const handleExecute = (row) => {
  console.log(row);
  row.btnLoading = true;
  let flag = false;
  let isSuccess = false;
  // 1、中断执行：中断当前报警规则执行动作已下发的命令
  if (row.stopExecuteStatus !== null && row.stopExecuteStatus === "2") {
    flag = true;
  } else {
    if (row.executeStatus === "0") {
      flag = true;
    }
  }
  AssetService.alarmEventExecute({
    alarmEventExecuteId: row.id,
    executeType: flag,
  })
    .then((res) => {
      console.log("执行命令", res);
      isSuccess = true;
      setTimeout(() => {
        getAppexecuteAndNotice(props.eventItemId);
      }, 5000);
    })
    .catch((error) => {
      isSuccess = false;
      console.log("执行命令失败", error);
    })
    .finally(() => {
      if (isSuccess) {
        setTimeout(() => {
          toast.value.show({
            type: "success",
            message: `操作成功`,
          });
          row.btnLoading = false;
        }, 5000);
      } else {
        row.btnLoading = false;
      }
    });
};

const btnLoading = ref(false);
const noticeContent = ref(null);
const sendDetailList = ref([]);
const executeDetailVOList = ref([]);
const deviceInProgress = ref(0);
const deviceTotal = ref(0);
const timer = ref(null);
const parentId = ref(null);
const getAppexecuteAndNotice = (id) => {
  parentId.value = id;
  AssetService.appexecuteAndNotice({ id })
    .then((res) => {
      let alarmNoticeDetailVO = res.data.alarmNoticeDetailVO;
      noticeContent.value = alarmNoticeDetailVO.noticeContent;
      sendDetailList.value = alarmNoticeDetailVO?.sendDetailList || [];
      executeDetailVOList.value = res?.data?.executeDetailVOList || [];
      deviceInProgress.value = res?.data?.deviceInProgress || 0;
      deviceTotal.value = res?.data?.deviceTotal || 0;
      // 如果数据中有执行中的，就需要启动定时器
      let filterItem = executeDetailVOList.value.filter(
        (item) => item.executeStatus === "0" || item.stopExecuteStatus === "0"
      );
      if (filterItem.length > 0) {
        // 启动定时器
        if (!timer.value) {
          timer.value = setInterval(() => {
            getAppexecuteAndNotice(parentId.value);
          }, 5000);
        }
      } else {
        clearInterval(timer.value);
        timer.value = null;
      }
    })
    .catch((err) => {
      console.log("请求失败", err);
    });
};
onBackPress((backOptions) => {
  console.log("backOptions", backOptions);
  clearInterval(timer.value);
});
defineExpose({
  getAppexecuteAndNotice,
});
</script>
<style lang="scss" scoped>
.implementation-details {
  padding: 0 28rpx;
  .scroll-Y,
  .send-status {
    border: 2rpx solid #d9d9d9;
    padding: 16rpx 20rpx;
    border-radius: 16rpx;
    margin-top: 32rpx;
    box-sizing: border-box;
    max-height: 196rpx;
    overflow-y: auto;
    .title {
      color: #373737;
      font-size: 28rpx;
      margin-bottom: 20rpx;
    }
    .send-type {
        font-size: 24rpx;
        margin-right: 10rpx;
        margin-bottom: 0;
    }
    .send-userc {
      flex-wrap: wrap;
    }
    .notice-content {
      font-size: 24rpx;
      color: #989898;
      line-height: 32rpx;
    }
  }
  .send-status {
    margin-top: 28rpx;
    height: auto;
    flex-wrap: wrap;
    overflow-y: auto;
    .user-name {
      white-space: nowrap;
      height: 48rpx;
      line-height: 48rpx;
      box-sizing: border-box;
      padding: 0 14rpx;
      font-size: 24rpx;
      color: #4378ff;
      border: 2rpx solid #4378ff;
      background: #fff;
      border-radius: 8rpx;
      margin-bottom: 28rpx;
      margin-right: 28rpx;
      &.user-fail0 {
        color: #a09f9f;
        border: 2rpx solid #f2f2f2;
        background: #f2f2f2;
      }
    }
  }
  .device-box {
    margin-top: 40rpx;
    .device-item {
      border-radius: 24rpx;
      box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
      margin-top: 28rpx;
      padding: 28rpx;
      position: relative;
      .device-name {
        font-weight: 500;
        font-size: 32rpx;
        color: #373737;
        height: 44rpx;
        line-height: 44rpx;
        margin-bottom: 28rpx;
      }
      .device-corner-mark {
        .device-corner {
          height: 52rpx;
          line-height: 52rpx;
          font-size: 26rpx;
          position: absolute;
          right: 0;
          top: 0;
          box-sizing: border-box;
          color: #fff;
          text-align: center;
          &.device-corner0 {
            width: 124rpx;
            color: #fff;
            background: url("../../../../static/shmStatic/icon/loading-icon-device.png")
              center center no-repeat;
            background-size: 100%; /* 高度100%，宽度自动 */
          }
          &.device-corner1 {
            width: 140rpx;
            background: url("../../../../static/shmStatic/icon/success-icon-device.png")
              center center no-repeat;
            background-size: 100%; /* 高度100%，宽度自动 */
          }
          &.device-corner2 {
            width: 140rpx;
            color: #fff;
            background: url("../../../../static/shmStatic/icon/error-icon-device.png")
              center center no-repeat;
            background-size: 100%; /* 高度100%，宽度自动 */
          }
        }
      }
    }
    .same-item {
      margin-bottom: 28rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        font-size: 28rpx;
        color: #b0b0b0;
        height: 40rpx;
        line-height: 40rpx;
      }
      .content {
        flex: 1;
        text-align: left;
        line-height: 36rpx;
        font-size: 28rpx;
        color: #404040;
        text-indent: 0;
      }
    }
  }
}
</style>
