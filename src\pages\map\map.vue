<template>
  <view class="container">
    <!-- <div class="aaa" @click="postMessage">sadasdsa</div> -->
		<input class="uni-input search_input" @input="inpChange" v-model="inpVal" placeholder="请输入地址" />
    <web-view
			class="webview_box"
      src="./static/tianditu/map.html"
      @onPostMessage="handlePostMessage"
      @message="handlePostMessage"
      ref="webview"
      :webview-styles="webviewStyles"
    >
    </web-view>
		<!-- v-if="!isViewDetail" resetPosition -->

		<view class="content">
			<view class="pile_list">
				<view class="pile" v-for="(item,index) in pileList" :key="index" @click="choosePile(item)">{{ item.title }}</view>
			</view>
		</view>
  </view>
</template>

<script setup>
import { ref, inject, onMounted, watch } from "vue";
import { systemInfo } from "./system.js";
import { onReady, onShow } from "@dcloudio/uni-app";
const { uniPlatform, platform } = systemInfo();

// webview实例
const pages = getCurrentPages();
let vw = ref(null);

let inpVal = ref("");
const inpChange = (e)=>{
	console.log('输入改变',e);
}


let pileList = ref([]);
onShow(()=>{
	vw.value = pages[pages.length - 1].$getAppWebview().children()[0];
		vw.value.evalJS(`receiveData({type:'add'})`);
	// 模拟接口请求桩号列表
	setTimeout(() => {
		
		getPoints();
	}, 3000);
})


// 监听桩号列表更新
watch(()=>pileList.value,(newval)=>{
	if(pileList.value.length){
		vw.value = pages[pages.length - 1].$getAppWebview().children()[0];
		vw.value.evalJS(`receiveData({type:'poilist',data:${JSON.stringify(pileList.value)}})`);
	}
})

const postMessage = () => {
  console.log("rrr");
  const userData = { TOKEN: "AAAAAA" };
  // uniapp 向 webview 发消息
	vw.value = pages[pages.length - 1].$getAppWebview().children()[0];
  vw.value.evalJS(`receiveData(${JSON.stringify(userData)},'enh')`);
};




const handlePostMessage = (data) => {
  console.log("webview天地图发来的数据",data.detail.data[0]);
	switch (data.detail.data[0].action) {
		case 'getPile':
			getPoints();
			break;
	
		default:
			break;
	}
};

const getPoints = () => {
  let arr = [
    {
      id: "0",
      lat: "30.68584",
      lng: "103.98459",
      title: "桩号1",
    },
    {
      id: "1",
      lat: "30.708584",
      lng: "103.68459",
      title: "桩号2",
    },
    {
      id: "2",
      lat: "30.98984",
      lng: "103.96459",
      title: "桩号3",
    },
    {
      id: "3",
      lat: "30.768584",
      lng: "103.18459",
      title: "桩号4",
    },
    {
      id: "4",
      lat: "30.59984",
      lng: "103.99459",
      title: "桩号5",
    },
  ];
  const shuffled = arr.sort(() => 0.5 - Math.random()); // 打乱数组
  pileList.value = shuffled.slice(0, 4); // 返回前 4 项
};

const choosePile = (item) => {
	console.log('item',item);
	vw.value = pages[pages.length - 1].$getAppWebview().children()[0];
  vw.value.evalJS(`receiveData({type:'choosePile',data:{choosedId:${item.id},dataList:${JSON.stringify(pileList.value)}}})`);
}

const getLocation = () => {
  console.log("ff");
  // 定位开启状态 true=开启，false=未开启
  let bool = false;

  // android平台
  if (uni.getSystemInfoSync().platform == "android") {
    var context = plus.android.importClass("android.content.Context");
    var locationManager = plus.android.importClass(
      "android.location.LocationManager"
    );
    var main = plus.android.runtimeMainActivity();
    var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
    bool = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER);

    uni.getLocation({
      type: "gcj02", //返回可以用于uni.openLocation的经纬度
      success: function (res) {
        console.log(res);
        const latitude = res.latitude;
        const longitude = res.longitude;
        // uni.openLocation({
        // 	latitude: latitude,
        // 	longitude: longitude,
        // 	success: function () {
        // 		console.log('success');
        // 	}
        // });
      },
    });
  }

  // 未开启定位功能
  if (bool === false) {
    uni.showModal({
      title: "提示",
      content: "请打开定位服务",
      success: ({ confirm, cancel }) => {
        if (confirm) {
          // android平台
          if (uni.getSystemInfoSync().platform == "android") {
            var Intent = plus.android.importClass("android.content.Intent");
            var Settings = plus.android.importClass(
              "android.provider.Settings"
            );
            var intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            var main = plus.android.runtimeMainActivity();
            main.startActivity(intent); // 打开系统设置GPS服务页面
          }

          // ios平台
          if (uni.getSystemInfoSync().platform == "ios") {
            var UIApplication = plus.ios.import("UIApplication");
            var application2 = UIApplication.sharedApplication();
            var NSURL2 = plus.ios.import("NSURL");
            var setting2 = NSURL2.URLWithString(
              "App-Prefs:root=Privacy&path=LOCATION"
            );
            application2.openURL(setting2);
            plus.ios.deleteObject(setting2);
            plus.ios.deleteObject(NSURL2);
            plus.ios.deleteObject(application2);
          }
        }

        // 用户取消前往开启定位服务
        if (cancel) {
          // do sth...
        }
      },
    });
  }
};

const webviewStyles = {
  width: "100%",
  height: "500px",
  top: 50,
  left: 0,
};
</script>

<style lang="scss" scoped>
.container {
  overflow: hidden;
  position: relative;
  min-height: 100vh;
}
.search_input {
  width: 80%;
  height: 100rpx;
  // background: red;
	// z-index: 1;
}
// .webview_box{
// 	z-index: 1;
// }
.reset_btn{
	position: absolute;
	top: 1000rpx;
	right: 20rpx;
	display: block;
	width: 80rpx;
	height: 80rpx;
	z-index: 1000;
}
.content{
	position: absolute;
	top: 1150rpx;
	.pile_list{
		.pile{
			padding: 20rpx;
		}
	}
}
</style>
