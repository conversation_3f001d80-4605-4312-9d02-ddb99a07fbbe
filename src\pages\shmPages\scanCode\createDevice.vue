<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-09 16:55:19
 * @Description: 
-->
<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-03-28 16:46:40
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 步骤条 -->
    <Steps
      :stepItems="stepItems"
      :current="currentStep"
      @onClickStep="onClickStep"
    />
    <!-- 创建设备表单 -->
    <DeviceForm
      ref="deviceFormRef"
      v-show="currentStep == 0"
      :formData="deviceFormData"
      :isDeviceEdit="isDeviceEdit"
      @onChangeProperty="onChangeProperty"
      @toNext="toNext"
      @onCancel="onCancelCreate"
    />
    <!-- 创建测点表单 卡片 -->
    <view class="swiper_box" v-show="currentStep == 1">
      <swiper
        class="swiper"
        :circular="true"
        previous-margin="52rpx"
        next-margin="44rpx"
        :indicator-dots="false"
        :duration="500"
        :current="currentPointIndex"
        @change="handleChange"
      >
        <swiper-item
          class=""
          v-for="(item, index) in pointFormDataList"
          :key="index"
        >
          <view class="swiper_item">
            <view class="swiper_ind">{{ padZero(index + 1) }}</view>
            <view class="black" style="height: 48rpx"></view>
            <!-- 测点表单组件 -->
            <PointForm
              ref="pointFormRefs"
              :pointIndex="index"
              :stateData="stateDataPoint"
              :choosedMonitorItems="choosedMonitorItems"
              :propertyBridgeType="propertyBridgeType"
              :propertyBridgeAlias="propertyBridgeAlias"
              :formData="item"
              :isLast="pointFormDataList.length === 1"
              @onChooseMonitorItem="handleMonitorItemOptionsDis"
              @onDelPoint="() => onDelPoint(index)"
              @onAddPoint="(e) => onAddPoint(index, e)"
            />
          </view>
        </swiper-item>
      </swiper>
    </view>
    <!-- 底部提交按钮 -->
    <view class="bottom_btn" v-if="currentStep == 1">
      <uv-button
        type="primary"
        color="#4378FF"
        :loading="submitLoading"
        text="全部提交"
        :customTextStyle="btnTextStyle"
        @click="onSubmitAll"
      ></uv-button>
    </view>
    <!-- 取消创建设备 二次确认弹窗 -->
    <uv-modal
      ref="cancelDeviceModalRef"
      title="数据未提交"
      :showCancelButton="true"
      content="当前填写数据，尚未提交完成！请确认是否放弃创建？"
      :textStyle="{ color: '#404040' }"
    >
      <template v-slot:confirmButton>
        <view class="cancel_btn no_cancel_btn" @click="cancelBack">取消</view>
        <view class="cancel_btn confirm_del_btn" @click="confirmBack"
          >确认放弃</view
        >
      </template>
    </uv-modal>
    <!-- 取消创建测点 二次确认弹窗 -->
    <uv-modal
      ref="cancelPointModalRef"
      title="取消创建测点"
      :showCancelButton="true"
      content="当前页面测点尚未保存！请确认是否取消创建？"
      :textStyle="{ color: '#404040' }"
    >
      <template v-slot:confirmButton>
        <view class="cancel_btn no_cancel_btn" @click="cancelDel">返回</view>
        <view class="cancel_btn confirm_del_btn" @click="confirmDel"
          >确认取消</view
        >
      </template>
    </uv-modal>
    <uv-toast ref="toast" position="top"></uv-toast>
  </view>
</template>
<script setup>
import { ref, reactive, computed, nextTick } from "vue";
import { cloneDeep as _cloneDeep } from "lodash";
import dayjs from "dayjs";
import { showImg, findItemByComponentTypeId } from "@/utils";
import Steps from "./components/Steps.vue";
import DeviceForm from "./components/DeviceForm.vue";
import PointForm from "./components/PointForm.vue";
import stepOneActiveIcon from "../../../static/shmStatic/icon/step_one_active_icon_20250310.png";
import stepTwoActiveIcon from "../../../static/shmStatic/icon/step_two_active_icon_20250310.png";
import stepTwoInActiveIcon from "../../../static/shmStatic/icon/step_two_inactive_icon_20250310.png";
import stepCompleteIcon from "../../../static/shmStatic/icon/step_complete_icon_20250310.png";
import { onBackPress, onLoad } from "@dcloudio/uni-app";
import { ShmService } from "@/service";
import { DataAcquisitionService } from "@/service";

let toast = ref(null);
// 设备id
let deviceId = ref("");
// 设备状态
let deviceStatus = ref("");
let deviceFormRef = ref(null);

// 步骤条相关
const currentStep = ref(0);
let createdPointNum = computed(() => {
  return `${
    pointFormDataList.value.filter((point) => point.isFinished).length
  }`;
});
const stepItems = computed(() => {
  return [
    {
      text: isDeviceEdit.value == "1" ? "编辑设备" : "创建设备",
      desc: "",
      activeIcon: stepOneActiveIcon,
      completeIcon: stepCompleteIcon,
    },
    {
      text: "创建测点",
      desc: createdPointNum.value,
      isGray: createdPointNum.value == 0,
      activeIcon: stepTwoActiveIcon,
      InActiveIcon: stepTwoInActiveIcon,
      completeIcon: stepCompleteIcon,
    },
  ];
});
const onClickStep = (e) => {
  if (e == 1 && createdPointNum.value == 0) {
    return;
  }
  currentStep.value = e;
};

// 桥梁类型
let propertyBridgeType = ref("");

// 设备信息
let deviceData = ref({});
let isDeviceEdit = ref(false);

onLoad((options) => {
  console.log(
    "查看设备id",
    deviceId.value,
    options,
    JSON.parse(options.deviceInfo).deviceName
  );
  deviceId.value = options.deviceId || "";
  deviceStatus.value = options.deviceStatus || "";
  // 是否存在设备，是，则为编辑状态；否，则为新增状态
  isDeviceEdit.value = options.isDeviceEdit == "1" ? true : false;
  // 回显设备信息
  deviceData.value = JSON.parse(options.deviceInfo);
  showBackDeviceInfo();
  // 获取监测项列表
  stateDataPoint.value.monitorOptions = JSON.parse(options.monitorItemList);
  console.log("查看监测项列表", stateDataPoint.value.monitorOptions);

  // 回显步骤条
  currentStep.value = Number(options.step);
  // 若存在设备，那么资产是已经有了的，就直接查询 关联构件、监测类别和内容、关联监测位置
  if (isDeviceEdit.value) {
    // 只有资产为桥梁时，才展示关联构件、截面、监测类别和内容字段
    if (deviceData.value?.mainBridgeType) {
      getComponentDataList(deviceData.value.assetInfo[1]);
      propertyBridgeType.value = deviceData.value?.mainBridgeType || "";
      getYhBaseDictData();
    }
    getLocationList(deviceData.value.assetInfo[1]);
  }
});

// 设备信息回显
const showBackDeviceInfo = () => {
  let deviceObj = deviceData.value;
  deviceObj.picture = deviceObj.picture
    ? [{ url: showImg(deviceObj.picture) }]
    : [];
    deviceObj.assetId = deviceObj?.assetInfo;
    deviceObj.assetIdLabel = deviceObj?.assetInfo;
    deviceObj.assetType = deviceObj?.assetType || '';
  // 回显设备名称和设备照片
  deviceFormData.value.deviceName = deviceObj?.deviceName || "";
  deviceFormData.value.picture = deviceObj?.picture;
  // 如果存在设备，则需要回显设备所有信息
  if (isDeviceEdit.value) {
    for (const key in deviceObj) {
      if (Object.prototype.hasOwnProperty.call(deviceObj, key)) {
        const element = deviceObj[key];
        deviceFormData.value[key] = element;
      }
    }
  }
  console.log("查看处理的设备详情", deviceFormData.value);
};

// 测点相关的数据项
const stateDataPoint = ref({
  componentOptions: [], // 关联构件
  locationImgOptions: [], // 监测位置
  monitorOptions: [], // 监测项
});

// 创建设备页面选择资产
let propertyId = ref("");
let propertyBridgeAlias = ref("");
let propertyType = ref("");
const onChangeProperty = (e) => {
  propertyId.value = e;
  let curChooseProperty = findItemByComponentTypeId(
    deviceFormRef.value.stateData.shmAsset,
    e,
    "objectId"
  );
  deviceFormData.value.assetAlias = curChooseProperty?.bridgeAlias || "";
  propertyBridgeAlias.value = curChooseProperty?.bridgeAlias || "";
  propertyType.value = curChooseProperty?.type || "";
  console.log("选择资产", e, curChooseProperty, deviceFormData.value);

  // 清空所有测点的关联构件、截面序号、关联监测位置、测点编码
  pointFormRefs.value.forEach((form, formInd) => {
    form.setNestedValue(
      form.localFormData,
      "assetContentDTO.componentId".split("."),
      ""
    );
    form.setNestedValue(
      form.localFormData,
      "sectionNumberIdLabel".split("."),
      ""
    );
    form.setNestedValue(form.localFormData, "locationIdLabel".split("."), "");
    form.setNestedValue(form.localFormData, "pointCode".split("."), "");
  });
  // 只有资产为桥梁时，才展示关联构件、截面、监测类别和内容字段
  if (curChooseProperty.type == "5") {
    // 请求关联构件
    getComponentDataList(e);
    // 查询字典数据-根据桥梁类型查询 监测类别和内容
    propertyBridgeType.value = curChooseProperty.mainBridgeType || "";
    getYhBaseDictData();
  }
  // 请求监测位置
  getLocationList(e);
};

// 查询监测位置列表
const getLocationList = async (propertyId) => {
  let params = {
    assetId: propertyId,
    kelocationNameywords: "",
    associatedComponent: "",
  };
  let { code, data } = await ShmService.getMonitoringLocation(params);
  console.log("查看监测位置", code, data);
  data = data.map((item) => {
    item.dictValue = item.locationName;
    item.dictKey = item.id;
    return item;
  });
  stateDataPoint.value.locationImgOptions = data;
};
// 查询关联构件列表
const getComponentDataList = async (propertyId) => {
  let params = {
    bridgeId: propertyId,
  };
  let { code, data } = await ShmService.selectComponentDataList(params);
  console.log("查询关联构件列表", code, data);
  stateDataPoint.value.componentOptions = data;
};
// 查询字典数据：资产的监测类别和内容
const getYhBaseDictData = () => {
  let str = `${propertyBridgeType.value}`;
  DataAcquisitionService.getYhBaseDict({
    dictCodes: str,
  }).then((res) => {
    try {
      let data = res.data;
      for (var m in data) {
        if (propertyBridgeType.value === m) {
          stateDataPoint.value[m] = JSON.parse(data[m][0].remark);
          console.log("stateDataPoint[m]", stateDataPoint[m]);
        } else {
          stateDataPoint.value[m] = data[m];
        }
      }
    } catch (error) {
      console.log("请求字典错误", error);
    }
  });
};

// 创建设备
let deviceFormData = ref({
  // 设备名称
  deviceName: "",
  // 设备编码
  deviceCode: "",
  // 所属资产
  assetId: [],
  assetIdLabel: [],
  assetAlias: "",
  // 设备类型
  deviceType: "",
  deviceTypeLabel: "",
  // 安装位置
  installLocation: "",
  // 安装日期
  installDate: dayjs().format("YYYY-MM-DD"),
  // 设备照片
  picture: [],
});
// 创建设备-下一步
const toNext = (e) => {
  console.log("创建设备完成，下一步", e);
  deviceFormData.value = e.data;
  currentStep.value++;
};

const cancelDeviceModalRef = ref(null);
// 取消创建设备
onBackPress((backOptions) => {
  if (backOptions.from === "backbutton") {
    cancelDeviceModalRef.value.open();
    return true;
  } else if (backOptions.from === "navigateBack") {
    return false;
  }
});
const onCancelCreate = () => {
  cancelDeviceModalRef.value.open();
};
const cancelBack = () => {
  cancelDeviceModalRef.value.close();
};
const confirmBack = () => {
  uni.navigateBack({
    delta: 1,
  });
};

// 测点相关
let pointFormDataList = ref([
  {
    isFinished: false,
    // 测点名称
    pointName: "",
    assetContentDTO: {
      // 关联构件
      componentId: "",
    },
    // 截面序号
    sectionNumberId: "",
    sectionNumber: "",
    sectionNumberIdLabel: "",
    // 测点编码
    pointCode: "",
    // 监测项
    monitorItem: "",
    monitorItemLabel: "",
    // 监测类别和内容
    monitorInfo: "",
    // 关联监测位置
    locationId: "",
    locationIdLabel: "",
    locationImg: "",
  },
]);
let currentPointIndex = ref(0);
// 取消某个测点
const cancelPointModalRef = ref(null);
let curDelInd = ref(0);
const onDelPoint = (ind) => {
  cancelPointModalRef.value.open();
  curDelInd.value = ind;
};
const confirmDel = () => {
  pointFormDataList.value.splice(curDelInd.value, 1); // 处理测点数据列表
  choosedMonitorItems.value.splice(curDelInd.value, 1); // 处理已选监测项集合
  handleMonitorItemOptionsDis({ curPointInd: "", val: "" });
  console.log("取消创建测点", curDelInd.value, pointFormDataList.value);
  nextTick(() => {
    currentPointIndex.value = Math.min(
      curDelInd.value,
      pointFormDataList.value.length - 1
    );
  });
  cancelPointModalRef.value.close();
};
const cancelDel = () => {
  cancelPointModalRef.value.close();
};

// 当前已经选中的监测项集合
let choosedMonitorItems = ref([]);
// 处理监测项列表已选项禁用
const handleMonitorItemOptionsDis = ({ curPointInd, val: curVal }) => {
  if (curVal) {
    choosedMonitorItems.value[curPointInd] = curVal;
  }
  stateDataPoint.value.monitorOptions.forEach((item) => {
    if (choosedMonitorItems.value.includes(item.dictKey)) {
      item.disabled = true;
    } else {
      item.disabled = false;
    }
  });
  console.log(
    "stateDataPoint.value.monitorOptions",
    stateDataPoint.value.monitorOptions
  );
};

// 保存当前测点并继续创建新测点
const onAddPoint = (ind, e) => {
  console.log("保存并继续创建新的2", ind, e, pointFormDataList.value);
  // choosedMonitorItems.value.push(e.data.monitorItem);
  // 剩余可选的监测项列表
  let filterMonitorOptions = stateDataPoint.value.monitorOptions.filter(
    (item) => !choosedMonitorItems.value.includes(item.dictValue)
  );
  console.log(
    "查看剩余可选监测项22",
    filterMonitorOptions,
    choosedMonitorItems.value
  );
  // 如果监测项被选完了，就不能再新增测点了
  if (!filterMonitorOptions.length) {
    toast.value.show({
      type: "warning",
      message: "当前设备所有测项均已创建测点，无法继续创建!",
    });
    console.log(
      "查看当前测点个数",
      pointFormDataList.value.length,
      createdPointNum.value
    );
    return;
  }
  // 监测项还有剩余未选择的，才可以继续创建测点
  let formData = {
    ..._cloneDeep(e.data),
    isFinished: false, // 标识该测点还未完成填写与创建
    // 测点名称、测点编码不自动回填；监测项自动选中剩余列表的第一个
    pointName: "",
    pointCode: "",
    monitorItem: filterMonitorOptions[0].dictKey,
    monitorItemLabel: filterMonitorOptions[0].dictValue,
  };
  pointFormDataList.value.push(formData);
  choosedMonitorItems.value.push(filterMonitorOptions[0].dictKey);
  nextTick(() => {
    currentPointIndex.value = pointFormDataList.value.length - 1;
    handleMonitorItemOptionsDis({ curPointInd: ind, val: e.data.monitorItem });
    // 获取当前截面序号列表
    pointFormRefs.value[ind + 1].onFormDictChange({
      val: e.data.assetContentDTO.componentId,
      unionKey: "assetContentDTO.componentId",
    });
  });
};
// 测点表单集合
let pointFormRefs = ref([]);
let submitLoading = ref(false);
const onSubmitAll = () => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  console.log("测点表单集合", pointFormRefs.value);
  let isAllFinished = false; // 是否全部填写完毕
  Promise.all(
    pointFormRefs.value.map((form, formInd) => {
      console.log(formInd);
      // 检验所有测点 必填项
      // 注意：此处pointFormRefs是在页面渲染后才能获取到的，收集了每个测点的页面组件集合
      return form.autoFormRef.formRef.validate();
    })
  )
    .then((res) => {
      // 校验设备 必填项
      deviceFormRef.value.autoFormRef.formRef
        .validate()
        .then(async (res) => {
          isAllFinished = true;
          console.log(
            "全部校验成功",
            res,
            deviceFormData.value,
            pointFormDataList.value
          );
          // 处理设备照片参数
          let pictureStr = "";
          if (deviceFormData.value.picture?.length) {
            let startIndex =
              deviceFormData.value.picture[0]?.url.indexOf("/file");
            if (startIndex !== -1) {
              pictureStr =
                deviceFormData.value.picture[0]?.url.substring(startIndex);
            } else {
              console.log("未找到 '/file' 子字符串");
            }
          } else {
            pictureStr = "";
          }
          // 设置已创建测点的标志状态
          pointFormDataList.value.forEach((point) => {
            point.isFinished = true;
          });
          let pointList = [..._cloneDeep(pointFormDataList.value)];
          let params = {
            deviceId: deviceId.value,
            deviceStatus: deviceStatus.value,
            assetType:
              propertyType.value || deviceFormData.value?.assetType || "",
            deviceInfo: {
              ...deviceFormData.value,
              alias: deviceFormData.value.deviceName,
              deviceCode: deviceFormData.value.deviceCode || null,
              assetId: deviceFormData.value.assetId[1],
              picture: pictureStr,
            },
            pointInfo: pointList.map((point) => {
              point.assetContentDTO.componentId = point.assetContentDTO
                .componentId
                ? point.assetContentDTO.componentId.join(",")
                : null;
              point.assetContentDTO.sectionNumberId =
                point.sectionNumberId || null;
              point.assetContentDTO.sectionNumber = point.sectionNumber || null;
              point.assetContentDTO.sectionNumberIdLabel =
                point.sectionNumberIdLabel || null;
              delete point.sectionNumberId;
              delete point.sectionNumber;
              delete point.sectionNumberIdLabel;
              delete point.locationImg;
              return point;
            }),
          };
          console.log("查看提交参数", params);
          let { code, data } = await ShmService.addDevicePoint(params);
          submitLoading.value = false;
          if (code == 200) {
            toast.value.show({
              type: "success",
              message: "创建成功",
            });
            uni.navigateTo({
              url: `/pages/shmPages/scanCode/detail?deviceId=${deviceId.value}&assetId=${deviceFormData.value.assetId[1]}&pageFrom=createForm`,
            });
          }
        })
        .catch((errors) => {
          console.log("校验失败设备", errors);
          submitLoading.value = false;
          if (!isAllFinished) {
            toast.value.show({
              type: "warning",
              message: "请完整填写设备及测点数据",
            });
          }
        });
    })
    .catch((error) => {
      // 至少有一个表单验证失败
      console.error("有测点表单验证失败", error);
      submitLoading.value = false;
      if (!isAllFinished) {
        toast.value.show({
          type: "warning",
          message: "请完整填写设备及测点数据",
        });
      }
    });
};
// 切换测点卡片
const handleChange = (e) => {
  console.log("切换卡片", e);
  const nowIndex = e.detail.current;
  currentPointIndex.value = nowIndex;
};

const padZero = (num) => {
  return num.toString().padStart(2, "0");
};

const btnTextStyle = reactive({
  fontSize: "40rpx",
  fontWeight: 600,
});
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  background: #f4f8ff;
}
:deep(.setps) {
  margin-bottom: 20rpx;
}

.swiper_box {
  // height: 1204rpx;
  height: calc(100% - 386rpx);
  width: 100%;
  background: #f4f8ff;
  .swiper {
    height: 100%;
    width: 100%;
    background: #f4f8ff;
    .swiper_item {
      position: relative;
      padding: 22rpx;
      width: 640rpx;
      height: 100%;
      // height: 924rpx;
      box-sizing: border-box;
      border-radius: 24rpx;
      background: #fff;

      .swiper_ind {
        z-index: 99;
        position: absolute;
        top: 0;
        left: 0;
        width: 80rpx;
        height: 48rpx;
        border-radius: 24rpx 0rpx 24rpx 0rpx;
        background: rgba(150, 180, 255, 1);
        text-align: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 26rpx;
        color: #ffffff;
        line-height: 48rpx;
      }
    }
  }
}

.bottom_btn {
  position: fixed;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
  padding: 40rpx;
}

.cancel_btn {
  margin: 0 auto;
  width: 520rpx;
  height: 72rpx;
  border-radius: 8rpx;
  text-align: center;
  box-sizing: border-box;
  line-height: 72rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #373737;
}
.no_cancel_btn {
  background: rgba(228, 228, 228, 1);
  color: #373737;
}
.confirm_del_btn {
  margin: 28rpx auto;
  background: rgba(67, 120, 255, 1);
  color: #ffffff;
}
:deep(.uv-modal__title) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 36rpx;
  color: #373737;
  line-height: 50rpx;
}
</style>
