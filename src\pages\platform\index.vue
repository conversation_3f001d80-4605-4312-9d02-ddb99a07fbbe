<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-05-23 14:44:42
 * @Description: 
-->
<template>
  <view class="container">
    <uv-navbar
      title="智慧养护"
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      leftIcon=""
      :bgColor="bgColor"
    >
    </uv-navbar>
    <!-- 登陆用户信息 -->
    <view class="user_msg_box">
      <image
        class="userFace"
        v-if="userInfo?.userFace"
        :src="showImg(userInfo?.userFace)"
        mode="widthFix"
      />
      <view class="default-avater" v-else>{{ shortUserName }}</view>
      <view class="user-account">
        <view class="name">
          <view class="userFullName">
            {{ userInfo.userFullName }}
          </view>
          <view class="postName" v-if="userInfo && userInfo.postName">
            {{ userInfo.postName }}
          </view>
        </view>
        <view class="time"> {{ getGreeting() }} </view>
      </view>
    </view>
    <!-- 天气 -->
    <view class="weather_box" v-if="weatherData.temp && weatherAlarm">
      <view class="top_box flex-between-start">
        <view class="left_temperature">
          <view class="area">{{ myAddress?.addressComponent?.county }}</view>
          <view class="cur_temperature">{{ weatherData.temp }}°</view>
        </view>
        <view class="right_weather">
          <view class="cur_weather flex-between-center">
            <image
              class="cur_weather_icon"
              :src="getWeatherIcon(weatherData.icon, weatherData)"
              @error="(e) => handleWeatherIconError(e, weatherData)"
            />
            <view class="cur_weather_content">{{ weatherData.text }}</view>
          </view>
          <view class="day_temperature flex-ac">
            <view class="highest_title">最高</view>
            <view class="highest_num">{{ weatherData.tempMax }}°</view>
            <view class="minimum_title">最低</view>
            <view class="minimum_num">{{ weatherData.tempMin }}°</view>
          </view>
        </view>
      </view>
      <view class="future_temperature flex-between-start">
        <view
          class="hour_temperature_item"
          v-for="(futureWeatherItem, futureWeatherInd) in weatherData.predict"
          :key="futureWeatherInd"
        >
          <view class="hour">{{ futureWeatherItem.time }}时</view>
          <image
            class="future_weather_icon"
            :src="getWeatherIcon(futureWeatherItem.icon, futureWeatherItem)"
            @error="(e) => handleWeatherIconError(e, futureWeatherItem)"
          />
          <view class="future_temperature_num"
            >{{ futureWeatherItem.temp }}°</view
          >
        </view>
      </view>
    </view>
    <!-- 定位、天气失败 占位图 -->
    <view class="no_weather_box" v-if="!weatherData.temp || !weatherAlarm">
      <uv-button
        class="weather_btn"
        type="primary"
        :loading="weatherLoading"
        text="重新加载"
        @click="reLoadWeather"
      ></uv-button>
      <view class="weather_error_text">天气数据获取失败，点击按钮重新获取</view>
    </view>
    <view
      v-if="weatherAlarm && weatherAlarm != '-'"
      class="weather_alarm flex-start"
    >
      <image
        class="weather_alarm_icon"
        src="/static/platformStatic/icon/alarm_icon_20250417.png"
      />
      <view class="weather_alarm_notice">{{ weatherAlarm }}!</view>
    </view>
    <!-- 子系统入口 -->
    <view v-if="sCode?.length" class="entrance_box flex-start-center">
      <!-- 日常养护 入口 -->
      <view
        v-if="sCode.includes('4')"
        class="entrance_item yh_index"
        @click="toYh"
      >
        <image
          class="entrance_item_img"
          src="/static/shmStatic/image/entrance_yh_icon_20250402.png"
        ></image>
        <view class="entrance_item_title">日常养护</view>
      </view>
      <!-- 轻量化监测 入口 -->
      <view
        v-if="sCode.includes('5')"
        class="entrance_item shm_property"
        @click="toShmProperty"
      >
        <image
          class="entrance_item_img"
          src="/static/shmStatic/image/entrance_shm_icon_20250402.png"
        ></image>
        <view class="entrance_item_title">轻量化监测</view>
      </view>
      <!-- 设备扫码 入口 -->
      <view
        v-if="sCode.includes('5')"
        class="entrance_item shm_scancode"
        @click="toShmScancode"
      >
        <image
          class="entrance_item_img"
          src="/static/shmStatic/image/entrance_scancode_icon_20250402.png"
        ></image>
        <view class="entrance_item_title">设备扫码</view>
      </view>
    </view>
    <!-- 无权限 占位图 -->
    <view class="no_right_box" v-if="!sCode?.length">
      <ylg-nodata
        :imgStyle="{
          width: '176rpx',
        }"
        noticeTip="暂无权限"
        textSize="28rpx"
        textMarginT="4rpx"
        imgUrl="/static/image/no_platform_right_20250410.png"
      ></ylg-nodata>
    </view>
    <ylg-tabBar />
    <CheckVersionModal />
  </view>
</template>
<script setup>
import { ref } from 'vue'
import {
  onPageScroll,
  onBackPress,
  onShow,
  onLoad,
  onPullDownRefresh,
  onLaunch
} from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { useSystemStore } from '@/store/system'
const systemInfo = useSystemStore()
import { useUserStore } from '@/store/user'
const userInfo = useUserStore()
import { useTabBarStore } from '@/store/tabBar'
const tabBarStoreInfo = useTabBarStore()
import { useLocationStore } from '@/store/location'
const useLocationInfo = useLocationStore()
import { getSysteminfo, showImg } from '@/utils'
import { getCurLocation, reverseGeocode } from '@/utils/location'
import { PlatformService, ShmService } from '@/service'
import CheckVersionModal from '@/pages/userCenter/components/CheckVersionModal.vue' //更新版本

// 获取手机系统栏高度
const systemBarHeight = `${Number(getSysteminfo().systemBarHeight) * 2 + 18}rpx`

let bgColor = ref('transparent')
onPageScroll((e) => {
  if (e.scrollTop > 35) {
    bgColor.value = '#4383F8'
  } else {
    bgColor.value = 'transparent'
  }
})

const shortUserName = ref('')
shortUserName.value = userInfo.userFullName.slice(-2)

const getCid = () => {
    console.log('systemInfo.systemCode')
    console.log(systemInfo.systemCode)
    if (systemInfo.systemCode.includes('5')) {
        uni.getPushClientId({
          success: (res) => {
            console.log('客户端推送标识:', res.cid)
            // 这里可以将 cid 发送到服务器保存
            ShmService.setMessagePush({
              cid: res.cid,
            })
          },
          fail: (err) => {
            console.error('获取推送标识失败:', err)
            // 失败处理逻辑
          },
        })
    }
}

let sCode = systemInfo.systemCode || []
// let isHandled = false;
// const handlePushClick = () => {
//   if (isHandled) return;
//   isHandled = true;
//   console.log("处理推送点击");
//   uni.onPushMessage((res) => {
//     console.log('客户端收到推送消息', res)
//     if (res.type === 'click') {
//       const { page, params } = res.data.payload || {}
//       console.log('点击推送', page, params)
//       if (page && userInfo.token) {
//         uni.navigateTo({
//           url: `/pages/shmPages/alarmEvents/detail?id=${params.id}`,
//         })
//         // uni.navigateTo({
//         //   url: `/${page}?${Object.entries(params || {})
//         //     .map(([k, v]) => `${k}=${v}`)
//         //     .join('&')}`,
//         // })
//       } else {
//         uni.navigateTo({
//             url: "/pages/login/index",
//         });
//       }
//     }
//   })
//   setTimeout(() => { isHandled = false; }, 2000); // 1秒内不再重复执行
// }
// let _pushListenerRegistered = false
onLoad(() => {
  getLocation()
  // handlePushClick()
  setTimeout(() => {
    getCid()
  }, 2000)
  
  // // 确保只监听一次
  //   if (!_pushListenerRegistered) {
  //     uni.onPushMessage((res) => {
  //       console.log('客户端收到推送消息', res)
  //       if (res.type === 'click') {
  //         const { page, params } = res.data.payload || {}
  //         console.log('点击推送', page, params)
          // if (page && userInfo.token) {
          //   uni.navigateTo({
          //     url: `/pages/shmPages/alarmEvents/detail?id=${params.id}`,
          //   })
          //   // uni.navigateTo({
          //   //   url: `/${page}?${Object.entries(params || {})
          //   //     .map(([k, v]) => `${k}=${v}`)
          //   //     .join('&')}`,
          //   // })
          // } else {
          //   uni.navigateTo({
          //       url: "/pages/login/index",
          //   });
  //         }
  //       }
		// })
  //     _pushListenerRegistered = true; // 标记已注册
  //   }
  
  
  
})

onShow(() => {
  // 返回平台首页后，
  // 重置底部tabbar选中第一项
  tabBarStoreInfo.updateActiveTab(0)
  getLocation()
  getGreeting()
})

// 首页禁止左滑返回上一页
onBackPress(() => {
  return true
})

onPullDownRefresh(async () => {
  await getLocation()
  uni.stopPullDownRefresh()
})

const getGreeting = () => {
  const hour = dayjs().hour() // 获取当前小时 (0-23)
  if (hour >= 6 && hour < 12) {
    return '上午好～'
  } else if (hour >= 12 && hour < 18) {
    return '下午好～'
  } else {
    return '晚上好～'
  }
}

// 天气相关
let myLocation = ref({
  longitude: useLocationInfo.longitude,
  latitude: useLocationInfo.latitude,
  // longitude: "103.954391",
  // latitude: "30.681541",
})
let myAddress = ref(useLocationInfo.initailAddress)
// 获取用户当前所处位置
const getLocation = async () => {
  console.log('平台首页请求定位0');
  try {
    let locationRes = await getCurLocation();
    if (locationRes.errMsg == "getLocation:ok") {
      myLocation.value.longitude = locationRes.longitude;
      myLocation.value.latitude = locationRes.latitude;
      useLocationInfo.updateInitialLocation(myLocation.value);
      console.log('平台首页请求定位1');
      await getAddress();
      await getCurWeather();
      await getAlarm();
      weatherLoading.value = false;
    }
  } catch (error) {
    weatherLoading.value = false
    console.log('查看定位结果 error', error)
    myLocation.value.longitude = ''
    myLocation.value.latitude = ''
  }
}
// 通过经纬度转换获取详细地址
const getAddress = async () => {
  let address = await reverseGeocode(
    myLocation.value.longitude,
    myLocation.value.latitude,
    true
  )
  if (address && address.addressComponent) {
    myAddress.value = address
    useLocationInfo.updateInitailAddress(address)
  } else {
    myAddress.value = ''
  }
  console.log('查看地址', address)
}
// 获取当时天气、未来6小时天气
let weatherData = ref(useLocationInfo.initailWeather)
const getCurWeather = async () => {
  try {
    let { code, data } = await PlatformService.getWeather(myLocation.value)
    if (code == 200 && data) {
      weatherData.value = data
      console.log('查看天气', weatherData.value)
      useLocationInfo.updateInitailWeather(data)
    } else {
      weatherData.value = {}
    }
  } catch (error) {
    weatherData.value = {}
    console.log('查看天气 error', error)
  }
}
// 处理返回天气图标
const getWeatherIcon = (iconCode, item) => {
  if (item.isImgLoadError) {
    return '/static/platformStatic/weatherIcons/wi_unknown.png'
  } else {
    return `/static/platformStatic/weatherIcons/wi_${iconCode}.png`
  }
}
// 图片渲染失败时
const handleWeatherIconError = (e, item) => {
  console.log('图片失败', e)
  item.isImgLoadError = true
}
// 获取天气预警
let weatherAlarm = ref(useLocationInfo.initailWeatherAlarm)
const getAlarm = async () => {
  try {
    let { code, data } = await PlatformService.getWeatherAlarm(myLocation.value)
    if (code == 200) {
      weatherAlarm.value = data?.prediction || '-'
      useLocationInfo.updateInitailWeatherAlarm(weatherAlarm.value)
    } else {
      weatherAlarm.value = ''
    }
  } catch (error) {
    weatherAlarm.value = ''
    console.log('查看天气预警 error', error)
  }
}
// 获取定位、天气失败时，点击按钮重新获取
let weatherLoading = ref(false)
const reLoadWeather = () => {
  weatherLoading.value = true
  getLocation()
}

// 进入日常养护子系统
const toYh = () => {
  uni.navigateTo({
    url: `/pages/home/<USER>
  })
}
// 进入结构健康监测子系统
const toShmScancode = () => {
  uni.navigateTo({
    url: `/pages/shmPages/scanCode/index`,
  })
}
// 进入设备扫码子系统
const toShmProperty = () => {
  uni.navigateTo({
    url: `/pages/shmPages/multiSssetWorkbench/index`,
  })
}
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  padding: 0 40rpx;
  padding-top: v-bind(systemBarHeight);
  background-image: url('../../static/image/home_bg.png');
  background-color: #f0f5ff;
  background-size: cover;
  background-repeat: no-repeat;
}
.user_msg_box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 84rpx;
  padding: 36rpx 40rpx;
  border-radius: 24rpx;
  box-sizing: border-box;
  // width: 100%;
  height: 192rpx;
  background: #fff;
  box-shadow:
    4rpx 4rpx 20rpx 0px rgba(0, 0, 0, 0.08),
    -4rpx -4rpx 20rpx 0px rgba(0, 0, 0, 0.08);

  .userFace {
    width: 120rpx;
    height: 120rpx !important;
    border-radius: 50%;
    overflow: hidden;
  }
  .default-avater {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    text-align: center;
    line-height: 120rpx;
    background: rgba(77, 138, 255, 1);
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }

  .user-account {
    margin-left: 40rpx;
    flex: 1;
    .name {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 28rpx;

      .userFullName {
        max-width: 218rpx;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        color: #373737;
        font-size: 36rpx;
        margin-right: 28rpx;
      }

      .postName {
        background: #f2f2f2;
        border-radius: 4rpx;
        padding: 4rpx 10rpx;
        color: #a09f9f;
        font-size: 24rpx;
      }
    }

    .time {
      color: #8e8e8e;
      font-size: 28rpx;
    }
  }
}
.entrance_box {
  .entrance_item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-right: 54rpx;
    width: 188rpx;
    height: 200rpx;
    border-radius: 24rpx;
    background: linear-gradient(
      to bottom,
      rgba(251, 251, 251, 1),
      rgba(244, 246, 255, 0.8)
    );
    &_img {
      width: 96rpx;
      height: 96rpx;
    }
    &_title {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #606060;
      line-height: 40rpx;
    }
  }
  .entrance_item:last-child {
    margin-right: 0 !important;
  }
}

.yh_index {
  height: 32px;
  margin-top: 24px;
}
.shm_scancode {
  height: 32px;
  margin-top: 24px;
}
.shm_property {
  height: 32px;
  margin-top: 24px;
}

.no_right_box {
  margin-top: 28rpx;
  padding: 28rpx 0;
  box-sizing: border-box;
  width: 100%;
  height: 280rpx;
  background: #ffffff;
  box-shadow: -4rpx -4rpx 24rpx 0rpx rgba($color: #588bff, $alpha: 0.1);
  border-radius: 24rpx;
}

.weather_box {
  margin-top: 28rpx;
  padding: 32rpx;
  background: #fff;
  box-sizing: border-box;
  border-radius: 24rpx;
  box-shadow: -4rpx -4rpx 24rpx 0rpx rgba($color: #588bff, $alpha: 0.1);
  .top_box {
    .left_temperature {
      .area {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 600;
        font-size: 36rpx;
        color: #404040;
        line-height: 50rpx;
      }
      .cur_temperature {
        margin-top: 26rpx;
        font-family: Aldrich, Aldrich;
        font-weight: 400;
        font-size: 64rpx;
        color: #404040;
        line-height: 48rpx;
      }
    }
    .right_weather {
      display: flex;
      flex-direction: column;
      align-items: end;
      .cur_weather {
        // width: 176rpx !important;
        height: 76rpx;
        padding: 0 20rpx 0 16rpx;
        box-sizing: border-box;
        border-radius: 16rpx;
        box-shadow: 0rpx 0rpx 20rpx 0rpx rgba($color: #000000, $alpha: 0.1);
        .cur_weather_icon {
          margin-right: 12rpx;
          display: inline-block;
          width: 64rpx;
          height: 64rpx;
        }
        .cur_weather_content {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 32rpx;
          color: #404040;
          line-height: 44rpx;
        }
      }
      .day_temperature {
        margin-top: 24rpx;
        .highest_title,
        .minimum_title {
          writing-mode: vertical-lr;
          text-orientation: upright;
          margin-right: 8rpx;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #606060;
        }
        .highest_num,
        .minimum_num {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #404040;
          line-height: 40rpx;
        }
        .highest_num {
          margin-right: 18rpx;
        }
      }
    }
  }
  .future_temperature {
    margin-top: 40rpx;
    .hour_temperature_item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .hour {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #a09f9f;
        line-height: 32rpx;
      }
      .future_weather_icon {
        width: 52rpx;
        height: 52rpx;
      }
      .future_temperature_num {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #404040;
        line-height: 32rpx;
      }
    }
  }
}
.no_weather_box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 28rpx;
  padding: 28rpx 0;
  box-sizing: border-box;
  width: 100%;
  height: 412rpx;
  background: #ffffff;
  box-shadow: 4rpx 4rpx 24rpx 0rpx rgba($color: #588bff, $alpha: 0.1);
  border-radius: 24rpx;
  .weather_btn {
    width: 212rpx;
    margin-bottom: 20rpx;
  }
  .weather_error_text {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #a09f9f;
    line-height: 40rpx;
  }
}
.weather_alarm {
  margin-top: 28rpx;
  border-radius: 24rpx;
  box-sizing: border-box;
  background: rgba(255, 243, 243, 1);
  padding: 14rpx 34rpx;
  &_icon {
    width: 28rpx;
    height: 28rpx;
  }
  &_notice {
    flex: 1;
    margin-left: 22rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #f35555;
    line-height: 32rpx;
  }
}
</style>
