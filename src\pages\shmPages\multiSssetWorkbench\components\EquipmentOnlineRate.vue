<template>
  <view class="charts-box">
    <view class="charts-parent flex-between-center">
      <view class="charts-item"><l-echart ref="chartRef"></l-echart></view>
      <view class="online-rate">
        <view class="online-item flex-start-center">
          <view class="online-icon"></view>
          <text class="online-name">95%以上</text>
        </view>
        <view class="online-item flex-start-center">
          <view class="online-icon"></view>
          <text class="online-name">85%-95%</text>
        </view>
        <view class="online-item flex-start-center">
          <view class="online-icon"></view>
          <text class="online-name">85%以下</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import * as echarts from "echarts";
import { ref } from "vue";
import { getSysteminfo } from "@/utils";
import { AssetService } from "@/service";
import { onLoad } from "@dcloudio/uni-app";
let deviceOnlineStatistics = [];
const screenWidth = getSysteminfo().phoneWindowWidth; // 屏幕宽度（px）
const rpxToPx = (rpx) => (screenWidth / 750) * rpx; // 将 rpx 转换为 px
const chartRef = ref(null);
const option = ref({
  backgroundColor: "#fff",
  title: {
    show: false,
  },
  tooltip: {
    trigger: "item",
    position: [rpxToPx(-10), rpxToPx(-14)], // 固定在左上方 (10px, 10px)
    padding: [rpxToPx(0), rpxToPx(10)],
    backgroundColor: "rgba(255, 255, 255, 0.9)", // 背景颜色
    borderColor: "#ddd", // 边框颜色
    borderWidth: 1, // 边框宽度
    height: rpxToPx(20),
    lineHeight: rpxToPx(20),
    textStyle: {
      color: "#404040", // 文字颜色
      fontSize: rpxToPx(24), // 文字大小
    },
    formatter: (params) => {
      const { name, value, percent } = params;
      return `资产个数：${value}个\n设备在线率：${name}`;
    },
  },
  color: ["#34D5E1", "#4BC6FF", "#FFC653"],
  legend: {
    show: false,
    data: ["#34D5E1", "#4BC6FF", "#FFC653"],
  },
  grid: {
    left: 0,
    right: 0,
    bottom: 0,
    top: "5%",
    containLabel: true,
  },
  series: [
    {
      name: "设备在线率",
      type: "pie",
      radius: ["50%", "90%"],
      itemStyle: {
        borderRadius: rpxToPx(6),
      },
      // padAngle: rpxToPx(5),
      data: [],
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
    },
  ],
});

let myChart = null;
// 记录当前选中的扇区
let currentSelectedIndex = null;
// 获取接口数据
const getDeviceOnlineStatistics = () => {
  AssetService.deviceOnlineStatistics().then((res) => {
    deviceOnlineStatistics = res.data ? res.data : [];
    let arr = deviceOnlineStatistics.map((item) => {
      let obj = {
        name:
          item.code === "0"
            ? "95% 以上"
            : item.code === "1"
            ? "85%-95%"
            : "85% 以下",
        value: item.assetNum * 1,
      };
      return obj;
    });
    console.log(arr);
    option.value.series[0].data = arr
    // option.value.series[0].data = [
    //   {
    //     name: "95% 以上",
    //     value: 10,
    //   },
    //   {
    //     name: "85%-95%",
    //     value: 20,
    //   },
    //   {
    //     name: "85% 以下",
    //     value: 70,
    //   },
    // ];
    // 组件能被调用必须是组件的节点已经被渲染到页面上
    setTimeout(async () => {
      if (!chartRef.value) return;
      myChart = await chartRef.value.init(echarts);
      console.log(option.value);
      myChart.setOption(option.value);
      myChart.on("click", (params) => {
        console.log("查看echarts点击", params);

        // 如果点击到图表外部（比如空白区域），params 为 undefined或 componentType 不存在
        if (!params || !params.componentType) {
          // 清除高亮和隐藏 tooltip
          myChart.dispatchAction({
            type: "hideTip",
          });
          myChart.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
          });
          currentSelectedIndex = null;
          return;
        }
        // 如果点击的是同一个扇区，认为是取消选择
        if (currentSelectedIndex === params.dataIndex) {
          myChart.dispatchAction({
            type: "hideTip",
          });
          myChart.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
          });
          currentSelectedIndex = null;
        } else {
          // 先将所有扇区还原状态
          myChart.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
          });
          // 对当前点击项高亮
          myChart.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            dataIndex: params.dataIndex,
          });
          // 显示 tooltip
          myChart.dispatchAction({
            type: "showTip",
            seriesIndex: 0,
            dataIndex: params.dataIndex,
          });
          currentSelectedIndex = params.dataIndex;
        }
      });
    }, 200);
  });
};

onLoad(() => {
  getDeviceOnlineStatistics();
});
</script>

<style lang="scss" scoped>
.charts-box {
  width: 100%;
  .charts-parent {
    margin-top: 20rpx;
    width: 100%;
    height: 228rpx;
  }
  .charts-item {
    width: 300rpx;
    height: 200rpx;
  }
  .level-box {
    padding: 0 26rpx;
    .level-item {
      box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(0, 0, 0, 0.08);
      box-sizing: border-box;
      border: 2rpx solid #fff;
      border-radius: 8rpx;
      color: #373737;
      font-size: 24rpx;
      padding: 0 18rpx;
      height: 52rpx;
      line-height: 52rpx;
      .level-icon {
        width: 24rpx;
        height: 24rpx;
        border-radius: 12rpx;
        margin-right: 8rpx;
      }
      &:nth-child(1) {
        .level-icon {
          background: #2f86ff;
        }
      }
      &:nth-child(2) {
        .level-icon {
          background: #ed862d;
        }
      }
      &:nth-child(3) {
        .level-icon {
          background: #f55152;
        }
      }
      &.active1 {
        border: 2rpx solid #2f86ff;
      }
      &.active2 {
        border: 2rpx solid #f55152;
      }
      &.active3 {
        border: 2rpx solid #f55152;
      }
    }
  }
  .online-rate {
    flex: 1;
    .online-item {
      width: 100%;
      box-sizing: border-box;
      padding-left: 50rpx;
      margin-bottom: 20rpx;
      .online-icon {
        width: 24rpx;
        height: 24rpx;
        background: #34d5e1;
        border-radius: 12rpx;
        margin-right: 14rpx;
      }
      .online-name {
        color: #404040;
        font-size: 28rpx;
      }
      &:nth-child(2) {
        .online-icon {
          background: #4bc6ff;
        }
      }
      &:nth-child(3) {
        .online-icon {
          background: #ffc653;
        }
      }
    }
  }
}
</style>
