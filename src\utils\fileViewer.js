/**
 * 文件查看工具
 * 支持不同平台的文件查看功能
 */

import { ref } from 'vue';
import { showImg } from '@/utils';

// 下载状态管理
const downloadLoading = ref(false);

/**
 * 查看文件
 * @param {Object} item - 文件对象，包含 filePath 属性
 * @param {Object} options - 配置选项
 */
export const viewFile = (item, options = {}) => {
  console.log("点击查看文件", item);
  
  if (!item?.filePath) {
    uni.showToast({
      title: "文件路径不存在",
      icon: "none",
    });
    return;
  }

  // 默认使用 showImg 处理 URL
  const url = showImg(item.filePath);
  
  // 检查运行环境
  // #ifdef APP-PLUS
  handleAppFile(item, url);
  // #endif
  
  // #ifdef H5
  handleH5File(url);
  // #endif
};

// App环境下的文件处理
const handleAppFile = (item, url) => {
  const id = item.filePath;
  let files = {};
  
  try {
    const storedFiles = uni.getStorageSync("files");
    files = storedFiles ? JSON.parse(storedFiles) : {};
  } catch (error) {
    console.error("读取本地文件记录失败:", error);
  }

  if (files[id]) {
    // 已下载的文件直接打开
    console.log("打开已下载文件:", files[id]);
    plus.runtime.openFile(files[id], {}, (error) => {
      console.log("文件可能已被删除，重新下载");
      downloadFileInApp(id, url, files);
    });
  } else {
    // 文件未下载，开始下载
    downloadFileInApp(id, url, files);
  }
};

// App环境下载文件
const downloadFileInApp = (id, url, files) => {
  uni.showToast({
    title: "开始下载文件...",
    icon: "loading",
  });
  
  downloadLoading.value = true;
  
  const task = plus.downloader.createDownload(url, {}, (download, status) => {
    downloadLoading.value = false;
    
    if (status === 200) {
      console.log("文件下载成功:", download.filename);
      
      // 保存文件记录
      files[id] = download.filename;
      try {
        uni.setStorageSync("files", JSON.stringify(files));
      } catch (error) {
        console.error("保存文件记录失败:", error);
      }
      
      // 打开文件
      plus.runtime.openFile(download.filename);
      
      uni.showToast({
        title: "下载完成",
        icon: "success",
      });
    } else {
      console.error("文件下载失败, status:", status);
      uni.showToast({
        title: "下载失败，请稍后重试",
        icon: "error",
      });
    }
  });
  
  task.start();
};

// H5环境下的文件处理
const handleH5File = (url) => {
  // H5环境直接在新窗口打开
  window.open(url, '_blank');
};

/**
 * 获取下载状态
 * @returns {import('vue').Ref<boolean>} 下载状态的响应式引用
 */
export const getDownloadLoading = () => downloadLoading;

/**
 * 获取文件名（从路径中提取）
 * @param {string} filePath - 文件路径
 * @returns {string} 文件名
 */
export const getFileName = (filePath) => {
  if (!filePath) return '';
  return filePath.split('/').pop() || '';
};

/**
 * 检查文件是否已下载（仅App环境）
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否已下载
 */
export const isFileDownloaded = (filePath) => {
  // #ifdef APP-PLUS
  try {
    const storedFiles = uni.getStorageSync("files");
    const files = storedFiles ? JSON.parse(storedFiles) : {};
    return !!files[filePath];
  } catch (error) {
    console.error("检查文件下载状态失败:", error);
    return false;
  }
  // #endif
  
  // #ifndef APP-PLUS
  return false;
  // #endif
};
