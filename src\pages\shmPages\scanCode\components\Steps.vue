<template>
  <view class="setps flex-center">
    <template v-for="(stepItem, index) in stepItems" :key="index">
      <view class="step_item flex-start-center" @click="onClickStepItem(index)">
        <image
          class="step_item_icon"
          v-if="index === current || !stepItem.isGray"
          :src="stepItem.activeIcon"
        ></image>
        <image
          class="step_item_icon"
          v-else-if="index > current"
          :src="stepItem.InActiveIcon"
        ></image>
        <image
          class="step_item_icon"
          v-else-if="index < current"
          :src="stepItem.completeIcon"
        ></image>
        <!-- <view :class="['step_item_title', index > current ? 'gray_title' : '']" -->
        <view
          :class="[
            'step_item_title',
            stepItem.isGray && index > current ? 'gray_title' : '',
          ]"
          >{{ stepItem.text
          }}{{ stepItem.desc ? "(" + stepItem.desc + ")" : "" }}</view
        >
      </view>
      <view
        :class="['line', index < current ? 'active_line' : '']"
        v-if="index < stepItems.length - 1"
      ></view>
    </template>
  </view>
</template>
<script setup>
const props = defineProps({
  stepItems: {
    type: Array,
    required: true,
    default: () => [],
  },
  current: {
    type: Number,
    required: true,
    default: 0,
  },
});
console.log(props);
const $emits = defineEmits(["onClickStep"]);
const onClickStepItem = (ind) => {
  $emits("onClickStep", ind);
};
</script>
<style lang="scss" scoped>
.setps {
  width: 100%;
  box-sizing: border-box;
  padding: 26rpx 40rpx;
  background: #fff;
  .step_item {
    padding: 0 20px;
    &_icon {
      display: block;
      margin-right: 12rpx;
      width: 36rpx;
      height: 36rpx;
    }
    &_title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #4378ff;
      line-height: 40rpx;
    }
    .gray_title {
      color: #9c9c9c;
    }
  }
  .step_item:first-child {
    padding-left: 0;
  }
  .step_item:last-child {
    padding-right: 0;
  }
  .line {
    flex: 1;
    height: 2rpx;
    background: #d9d9d9;
  }
  .active_line {
    background: #4378ff;
  }
}
</style>
