/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-08-08 15:57:07
 * @Description:
 */
import { defineStore } from "pinia";

export const usePileNumberStore = defineStore({
  id: "pileNumber",
  state: () => ({
    pileNumbers: [],
    curChooedMap: [
      // {unionKey: 'id'}
    ],
  }),
  // 存储数据（同步|异步）
  actions: {
    clearPileNumber(){
      this.$state.pileNumbers = [];
    },
    addPileNumber(data) {
      this.$state.pileNumbers.push(data)
    },
    delPileNumber(id){
      this.$state.pileNumbers = this.$state.pileNumbers.filter(item=>item.id!==id);
    },
    // 更新当前新增桩号中，已经被占用的桩号列表
    updateCurChoosedMap(unionItem){
      for (const key in unionItem) {
        if (Object.hasOwnProperty.call(unionItem, key)) {
          let found = false; // 标记是否找到匹配的键  
          for (let i = 0; i < this.$state.curChooedMap.length; i++) {
            const element = this.$state.curChooedMap[i];
            if(key in element){
              element[key] = unionItem[key];  
              found = true;  
              break; // 找到后跳出循环  
            }
          }
          if(!found){
            this.$state.curChooedMap.push({[key]: unionItem[key]});
          }
        }
      }
    }
  },
});
