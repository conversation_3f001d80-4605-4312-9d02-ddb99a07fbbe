<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-07 09:32:22
 * @Description: 
-->
<template>
  <view>
    <uv-popup
      ref="popup"
      mode="bottom"
      round="24"
      @change="change"
      @touchmove.native.prevent="
        (e) => {
          e.preventDefault();
        }
      "
    >
      <view class="content">
        <uv-tabs
          :list="disposalTabs"
          lineWidth="64"
          lineColor="#4378FF"
          :activeStyle="{
            color: '#4378FF',
            fontWeight: 'bold',
            fontSize: '16px',
            paddingBottom: '10px',
          }"
          :inactiveStyle="{
            color: '#A09F9F',
            fontSize: '16px',
            paddingBottom: '10px',
          }"
          itemStyle="padding:0 15px  ; height: 34px;"
          :customStyle="{
            marginLeft: '-15px',
            borderBottom: '1px solid #F0F0F0',
            marginBottom: '20px',
          }"
          @click="disposalClick"
        >
          <template v-slot:right>
            <view style="margin-right: 40rpx" @click="close()">
              <uv-icon name="close" size="16" bold></uv-icon>
            </view>
          </template>
        </uv-tabs>
        <view v-if="disposalActiveTab === '1'">
          <view class="title-box">
            <view class="title">处置方式 </view>
            <view class="required">必填 </view>
          </view>
          <view class="tabs_box">
            <view
              :class="[
                'tab_item',
                item.type == state.handleType ? 'tab_active' : '',
              ]"
              v-for="(item, index) in types"
              :key="index"
              @click="changeType(item)"
              >{{ item.name }}</view
            >
          </view>
          <view class="title">备注 </view>
          <view class="tabs_box" style="padding-right: 28rpx">
            <uv-textarea
              v-model="state.handleRemark"
              count
              maxlength="150"
              placeholder="请输入备注"
            ></uv-textarea>
          </view>
        </view>
        <view class="placeholder-box" v-if="disposalActiveTab === '2'">
          <image
            class="placeholder-img"
            :src="placeholder_trans_other"
            mode="scaleToFill"
          />
          <view class="placeholder-tips">是否确认转移该事件？ </view>
        </view>
      </view>
      <view class="btn_box">
        <view class="comfirm_btn btn" @click="comfirm">确 定</view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { DataAcquisitionService, eventHandlingApi } from "@/service";
import placeholder_trans_other from "@/static/icon/placeholder_trans_other.png";

const props = defineProps({
  taskId: {
    type: [String, Number],
    required: true,
    default: "",
  },
});

const disposalTabs = reactive([
  {
    type: "1",
    name: "处置事件",
  },
  {
    type: "2",
    name: "转为其他事件",
  },
]);
const types = ref([]);

const disposalActiveTab = ref("1");

const state = reactive({
  handleType: "1",
  handleRemark: "",
});

const resetState = () => {
  disposalActiveTab.value = "1";
  state.handleType = "1";
  state.handleRemark = "";
};
const disposalClick = (record) => {
  disposalActiveTab.value = record.type;
};
// 查询字典枚举
const getDicts = async () => {
  const { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes: "eventHandleType",
  });
  if (code == 200) {
    types.value = data.eventHandleType
      .filter((item) => !["6", "8"].includes(item.dictKey))
      .map((i) => {
        return { name: i.dictValue, type: i.dictKey };
      });
  }
};

// 切换分类
const changeType = (curItem) => {
  state.handleType = curItem.type;
};

// 确定
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
const comfirm = () => {
  let params = {};
  switch (disposalActiveTab.value) {
    case "1":
      params = {
        projectId: projectInfo.projectId,
        handleType: state.handleType,
        eventIds: [props.taskId],
        handleRemark: state.handleRemark,
      };
      eventHandlingApi.inspectEventCheckWorkOrder(props.taskId).then((res) => {
        if (res.code == 200) {
          if (res.data === null) {
            params.eventId = props.taskId;
            eventHandlingApi.inspectEventReHandle(params).then(() => {
              uni.showToast({
                icon: "none",
                title: "处置事件成功",
              });
              backToHomePage();
            });
          } else {
            uni.showToast({
              icon: "none",
              title: "该事件关联工单已被合并，请解除合并后再进行重新处置",
            });
          }
        }
      });
      break;
    case "2":
      params = {
        projectId: projectInfo.projectId,
        eventIds: [props.taskId],
        handleRemark: state.handleRemark,
        eventType: "2",
      };
      eventHandlingApi.inspectEventCheckWorkOrder(props.taskId).then((res) => {
        if (res.code == 200) {
          if (res.data === null) {
            params.id = props.taskId;
            eventHandlingApi.inspectEventReHandleTransfer(params).then(() => {
              uni.showToast({
                icon: "none",
                title: "转为其他事件成功",
              });
              backToHomePage();
            });
          } else {
            uni.showToast({
              icon: "none",
              title: "该事件关联工单已被合并，请解除合并后再进行重新处置",
            });
          }
        }
      });
      break;

    default:
      break;
  }
};

const backToHomePage = () => {
  uni.navigateBack({
    delta: 1,
  });
};
// 打开弹窗
const popup = ref(null);
const detailData = ref({});
const open = (form) => {
  popup.value.open();
  detailData.value = form;
  state.handleType = detailData.value.handleType;
  state.handleRemark = detailData.value.handleRemark;
};
const close = () => {
  popup.value.close();
  resetState();
};
const change = (e) => {
  if (!e.show) {
    resetState();
  }
};
onLoad(() => {
  getDicts();
});
defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.content {
  padding: 40rpx;
  padding-right: 0;
  margin-bottom: 50rpx;
  .title-box {
    display: flex;
    align-items: center;
    gap: 0 12rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    .title {
      font-size: 32rpx;
      color: #373737;
    }
    .required {
      display: inherit;
      align-items: center;
      justify-content: center;
      width: 84rpx;
      height: 40rpx;
      font-size: 24rpx;
      color: #f06f6f;
      background: #fff0f0;
      border-radius: 8rpx;
    }
  }

  .tabs_box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 32rpx;
    margin-bottom: 24rpx;
    .tab_item {
      margin: 0 24rpx 24rpx 0;
      border-radius: 16rpx;
      padding: 6rpx 28rpx;
      box-sizing: border-box;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      background: #f2f2f2;
      color: #8e8e8e;
      line-height: 40rpx;
    }
    .tab_active {
      background: #4378ff;
      color: #fff;
    }
  }
  .placeholder-box {
    height: 400rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 48rpx;
    padding-right: 28rpx;
    .placeholder-img {
      width: 160rpx;
      height: 160rpx;
    }
    .placeholder-tips {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: #a09f9f;
    }
  }
}
.btn_box {
  padding: 0 40rpx 40rpx 40rpx;
  display: flex;
  justify-content: space-between;
  .btn {
    // width: 256rpx;
    height: 96rpx;
    padding: 20rpx 0;
    box-sizing: border-box;
    text-align: center;
    border-radius: 4rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .comfirm_btn {
    width: 100%;
    background: #4378ff;
    color: #fff;
  }
}
</style>
