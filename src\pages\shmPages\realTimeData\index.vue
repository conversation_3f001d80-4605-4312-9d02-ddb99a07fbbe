<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-15 10:27:37
 * @Description: 
-->
<template>
  <view :class="['container', containerNoScroll ? 'noContainerScroll' : '']">
    <view class="top_sticky flex-between-center">
      <uv-search
        height="40"
        shape="square"
        searchIcon="../../../static/shmStatic/icon/search_icon_20250318.png"
        searchIconSize="18"
        placeholder="搜索测点编码"
        placeholderColor="#A09F9F"
        bgColor="#fff"
        :showAction="false"
        :boxStyle="searchStyle"
        v-model="inpVal"
        @blur="inpBlur"
        @clear="inpClear"
        @search="inpSearch"
      ></uv-search>
      <view class="filter_btn flex-start-center" @click="toPick">
        <view class="btn">筛选</view>
        <image
          class="right_arrow_icon"
          src="/static/shmStatic/icon/right_arrow_20250310.png"
        ></image>
      </view>
    </view>
    <!-- 测点选择 -->
    <view class="point_picker flex-start-center" @click="toChoosePoint">
      <view class="cur_point">{{ curChoosedPoint?.pointCode || "-" }}</view>
      <image
        class="picker_arrow_icon"
        src="/static/shmStatic/icon/picker_arrow_down_20250318.png"
      ></image>
    </view>
    <!-- 实时折线图 -->
    <view class="echarts_box">
      <view class="echart_title same-add-title">实时数据</view>
      <LineEchart
        ref="lineEchartRef"
        :assetId="assetId"
        :pointId="curChoosedPoint?.id || ''"
        :curPointMonitorUnit="curPointMonitorUnit"
      />
    </view>
    <!-- 昨日数据概览 -->
    <view class="yesterday_data_view">
      <view class="title same-add-title">昨日数据概览</view>
      <view class="flex-between-center">
        <view class="data_item">
          <view class="data_item_title flex-between-center">
            <view class="text">日均值</view>
            <image
              class="icon"
              src="/static/shmStatic/icon/daily_average_icon_20250318.png"
            ></image>
          </view>
          <view class="data_item_val">
            <text class="num">{{ yesterdayData.avg || "-" }}</text>
            <text class="unit" v-if="curPointMonitorUnit">{{
              curPointMonitorUnit
            }}</text>
          </view>
        </view>
        <view class="data_item">
          <view class="data_item_title flex-between-center">
            <view class="text">最大值</view>
            <image
              class="icon"
              src="/static/shmStatic/icon/max_val_icon_20250318.png"
            ></image>
          </view>
          <view class="data_item_val">
            <text
              :class="[
                'num',
                yesterdayData.max > yesterdayData.avg ? 'red' : '',
              ]"
              >{{ yesterdayData.max || "-" }}</text
            >
            <text class="unit" v-if="curPointMonitorUnit">{{
              curPointMonitorUnit
            }}</text>
          </view>
        </view>
        <view class="data_item">
          <view class="data_item_title flex-between-center">
            <view class="text">最小值</view>
            <image
              class="icon"
              src="/static/shmStatic/icon/min_val_icon_20250318.png"
            ></image>
          </view>
          <view class="data_item_val">
            <text
              :class="[
                'num',
                yesterdayData.min < yesterdayData.avg ? 'red' : '',
              ]"
              >{{ yesterdayData.min || "-" }}</text
            >
            <text class="unit" v-if="curPointMonitorUnit">{{
              curPointMonitorUnit
            }}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 测点基础信息 -->
    <PointBaseInfo :detailData="pointDetailData" />
    <!-- 筛选弹窗 -->
    <SearchPicker
      ref="pickerRef"
      :assetId="assetId"
      @tabPickerCallback="onTabPickerCallback"
      @onMaskClick="onMaskClick"
    />
    <!-- 测点选择弹窗 -->
    <ylg-dicts-picker
      ref="pointPickerRef"
      :curChoosedKey="curChoosedPointKey"
      title="测点选择"
      :options="pointList"
      @onSelect="onSelectPoint"
      @onMaskClick="onMaskClick"
    ></ylg-dicts-picker>
  </view>
</template>
<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { ShmService } from "@/service";
import SearchPicker from "./components/SearchPicker.vue";
import LineEchart from "../components/LineEchart.vue";
import PointBaseInfo from "../components/PointBaseInfo.vue";

// const assetId = userStore.propertyInfo ? userStore.propertyInfo.objectId : null
let assetId = ref("");
onLoad((options) => {
  assetId.value = options.assetId || "";
  getPointList();
});

let inpVal = ref("");
const inpClear = () => {
  inpVal.value = "";
  pickerRef.value.reset();
  console.log("输入清空", inpVal.value);
  getPointList();
};
const inpBlur = (val) => {
  inpVal.value = val;
  pickerRef.value.reset();
  console.log("输入切换", inpVal.value);
  getPointList();
};
const inpSearch = (val) => {
  inpVal.value = val;
  pickerRef.value.reset();
  console.log("点击搜索", inpVal.value);

  getPointList();
};

// 打开筛选弹窗
let containerNoScroll = ref(false);
const pickerRef = ref(null);
const toPick = () => {
  pickerRef.value.open();
  containerNoScroll.value = true;
};
let curMonitorParams = ref("");
const onTabPickerCallback = (e) => {
  switch (e.type) {
    // 选择监测类别/监测位置
    case "confirmTabs":
      curMonitorParams.value = e.data;
      inpVal.value = "";
      if (Array.isArray(e.data)) {
        // 根据监测类别查询测点
        getPointList(e.data, "");
      } else {
        // 根据监测位置查询测点
        getPointList("", e.data);
      }
      pickerRef.value.close();
      containerNoScroll.value = false;
      break;
    case "reset":
      getPointList();
      pickerRef.value.close();
      containerNoScroll.value = false;
      break;
    default:
      break;
  }
};
const onMaskClick = () => {
  containerNoScroll.value = false;
};

// 测点选择相关
let pointPickerRef = ref(null);
let curChoosedPoint = ref({});
let curPointMonitorUnit = ref("");
const pointList = ref([]);
const getPointList = async (monitorType, locationId) => {
  console.log("查询测点参数,", monitorType, locationId);
  try {
    let params = {};
    let resData = {};
    // 1.查询资产下所有的测点
    if (!monitorType && !locationId) {
      params = {
        searchName: inpVal.value,
        assetId: assetId.value,
        fieldTypes: "int,double,long,float",
      };
      resData = await ShmService.getMonitoringPointList(params);
    } else if (monitorType) {
      // 2.根据所选监测类别查询测点
      params = {
        assetId: assetId.value,
        monitorType: monitorType[1],
        fieldTypes: "int,double,long,float",
      };
      resData = await ShmService.getMonitoringPointList(params);
    } else if (locationId) {
      // 3.根据所选监测位置查询测点
      params = {
        assetId: assetId.value,
        locationIds: locationId,
      };
      resData = await ShmService.getPointListByLocationIds(params);
    }
    // 处理测点数据
    if (resData.code == 200) {
      let myDaya =
        locationId && resData.data
          ? resData.data[locationId]
          : resData.data?.length
          ? resData.data
          : [];
      pointList.value = myDaya?.map((point) => {
        point.dictValue = point.pointCode;
        point.dictKey = point.id;
        return point;
      });
      curChoosedPoint.value = pointList.value[0];
      curPointMonitorUnit.value = pointList.value[0]?.monitorItemUnit;
      curChoosedPointKey.value = pointList.value[0]
        ? `${pointList.value[0]?.dictValue}${pointList.value[0]?.dictKey}`
        : "";
      if (pointList.value.length) {
        // 获取测点详情
        getPointDetail();
        // 获取昨日概览数据
        getYesterdayData();
      } else {
        pointDetailData.value = {};
      }
    }
  } catch (error) {
    console.log("请求测点 error", error);
  }
};
const toChoosePoint = () => {
  pointPickerRef.value.open();
  containerNoScroll.value = true;
};
let curChoosedPointKey = ref("");

let lineEchartRef = ref(null);
const onSelectPoint = (e) => {
  curChoosedPoint.value = e;
  curChoosedPointKey.value = `${e.dictValue}${e.dictKey}`;
  curPointMonitorUnit.value = e.monitorItemUnit || "";
  pointPickerRef.value.close();
  containerNoScroll.value = false;
  getPointDetail();
  getYesterdayData();
  lineEchartRef.value.clearTooltip();
  lineEchartRef.value.initChart();
};

// 昨日数据概览
let yesterdayData = ref({});
const getYesterdayData = async () => {
  try {
    let { code, data } = await ShmService.getPoiontYesterdayData({
      pointId: curChoosedPoint.value.id,
    });
    if (code == 200) {
      yesterdayData.value = {
        avg: Number(data.avg),
        max: Number(data.max),
        min: Number(data.min),
      };
    } else {
      yesterdayData.value = {};
    }
  } catch (error) {
    yesterdayData.value = {};
  }
};

// 测点基础信息
const pointDetailData = ref({});
const getPointDetail = async () => {
  try {
    let { code, data } = await ShmService.getMonitoringPointDetail(
      curChoosedPoint.value.id
    );
    if (code == 200 && data) {
      data.deviceDesc = data.deviceName
        ? `${data.deviceName}(${data.iotId})`
        : "";
      pointDetailData.value = data;
    } else {
      pointDetailData.value = data;
    }
  } catch (error) {
    pointDetailData.value = {};
    console.log("测点详情 error", error);
  }
};

// 搜索
const searchStyle = reactive({
  width: "548rpx",
  borderRadius: "8px",
});
</script>
<style lang="scss" scoped>
.container {
  background: #f4f8ff;
  height: 100vh;
  overflow: scroll;
  padding: 24rpx 40rpx;
  box-sizing: border-box;
}
.noContainerScroll {
  overflow: hidden;
}
.top_sticky {
  .uv-search {
    width: 548rpx;
  }
  .filter_btn {
    margin-left: 28rpx;
    .btn {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      line-height: 40rpx;
    }
    .right_arrow_icon {
      display: block;
      margin-left: 8rpx;
      width: 28rpx;
      height: 28rpx;
    }
  }
}
.point_picker {
  margin-top: 28rpx;
  // background: rgba($color: #fff, $alpha: 1);
  .cur_point {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 32rpx;
    color: #373737;
    line-height: 44rpx;
  }
  .picker_arrow_icon {
    display: block;
    width: 28rpx;
    height: 28rpx;
    margin-left: 16rpx;
  }
}
.echarts_box {
  margin-top: 28rpx;
  padding: 28rpx 46rpx;
  box-sizing: border-box;
  width: 100%;
  height: 428rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 0 16rpx 0rpx rgba(0, 0, 0, 0.1);
  .echart_title {
    padding: "";
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #404040;
    line-height: 40rpx;
  }
}
.yesterday_data_view {
  margin-top: 28rpx;
  width: 100%;
  padding: 28rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 0 16rpx 0rpx rgba(0, 0, 0, 0.1);
  .title {
    margin-bottom: 28rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 28rpx;
    color: #404040;
    line-height: 40rpx;
  }
  .data_item {
    width: 182rpx;
    height: 140rpx;
    padding: 20rpx 16rpx;
    box-sizing: border-box;
    border-radius: 16rpx;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.1);
    .data_item_title {
      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #404040;
        line-height: 32rpx;
      }
      .icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
    .data_item_val {
      margin-top: 32rpx;
      .num {
        font-family: Aldrich, Aldrich;
        font-weight: 400;
        font-size: 36rpx;
        color: #4378ff;
        line-height: 36rpx;
      }
      .red {
        color: #ff3838;
      }
      .unit {
        margin-left: 4rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #a09f9f;
        line-height: 32rpx;
      }
    }
  }
}
</style>
