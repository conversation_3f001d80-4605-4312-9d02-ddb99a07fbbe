<template>
  <view class="container">
    <template v-if="!pageLoading">
      <view class="item_box">
        <view class="label">上报人</view>
        <view class="content">{{ detailData.reportUserName || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">上报时间</view>
        <view class="content">{{ detailData.reportTime || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">作业类型</view>
        <view class="content">{{ detailData.maintainTypeName || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">作业数量</view>
        <view class="content">{{ formatWorkNumber(detailData) }}</view>
      </view>
      <view class="item_box">
        <view class="label">位置方向</view>
        <view class="content">{{ detailData.updownMarkName || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">附近桩号</view>
        <view class="content">{{ detailData.stakeName || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">地址信息</view>
        <view class="content">{{ detailData.reportAddress || "-" }}</view>
        <view class="right_btn" @click="viewLocation">查看</view>
      </view>
      <view class="item_box">
        <view class="label">作业备注</view>
        <view class="content">{{ detailData.remark || "-" }}</view>
      </view>
      <view class="item_box">
        <view class="label">作业前照片</view>
        <view class="imgs">
          <ylg-load-image
            class="img"
            :scroll-top="0"
            loading-mode="spin-circle"
            :image-src="showImg(detailData?.beforePicture)"
            @click="previewImg(0)"
          ></ylg-load-image>
        </view>
      </view>
      <view class="item_box">
        <view class="label">作业中照片</view>
        <view class="imgs">
          <ylg-load-image
            class="img"
            :scroll-top="0"
            loading-mode="spin-circle"
            :image-src="showImg(detailData?.doingPicture)"
            @click="previewImg(1)"
          ></ylg-load-image>
        </view>
      </view>
      <view class="item_box">
        <view class="label">作业后照片</view>
        <view class="imgs">
          <ylg-load-image
            class="img"
            :scroll-top="0"
            loading-mode="spin-circle"
            :image-src="showImg(detailData?.afterPicture)"
            @click="previewImg(2)"
          ></ylg-load-image>
        </view>
      </view>
    </template>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      font-size="24rpx"
    ></uv-loading-page>
  </view>
</template>
<script setup>
import { ref, reactive } from "vue";
import { RoutinePreserveService } from "@/service";
import { onLoad } from "@dcloudio/uni-app";
import { reTransformLngLat } from "@/utils/location";
import { showImg } from "@/utils";

let pageLoading = ref(true);
onLoad((options) => {
  if (options?.id) {
    getDetail(options.id);
  }
});
const detailData = ref({});
let previewImgs = reactive([]);
const getDetail = async (id) => {
  try {
    let { code, data } = await RoutinePreserveService.getHomeworkDetail(id);
    console.log("事件详情", code, data);
    if (code == 200) {
      detailData.value = data;
      if (data.beforePicture) {
        previewImgs.push(showImg(data.beforePicture));
      }
      if (data.doingPicture) {
        previewImgs.push(showImg(data.doingPicture));
      }
      if (data.afterPicture) {
        previewImgs.push(showImg(data.afterPicture));
      }
    }
    pageLoading.value = false;
  } catch (error) {
    console.log("获取详情失败", error);
    pageLoading.value = false;
  }
};

// 查看定位
const viewLocation = () => {
  // 将后端返回的标准坐标转换为高德使用的gcj02坐标
  let reTrans = reTransformLngLat(
    detailData.value.longitude,
    detailData.value.latitude
  );
  uni.navigateTo({
    url: `/pages/roadConditionInspection/coordinatePicking?isDetail=${true}&curLongitude=${String(
      detailData.value.longitude
    )}&curLatitude=${String(detailData.value.latitude)}&curAddress=${
      detailData.value.reportAddress
    }`,
  });
};

// 预览图片
const previewImg = (cur) => {
  console.log("预览", previewImgs);

  uni.previewImage({
    urls: previewImgs, // 需要预览的图片HTTP链接列表
    current: cur, // 当前显示图片的链接索引
  });
};
const formatWorkNumber = (obj = {}) => {
  const { number, units } = obj;
  if (number) {
    if (units) return number + units;
    return number;
  } else {
    return "-";
  }
};
</script>
<style lang="scss" scoped>
// .container {
// }
.item_box {
  display: flex;
  align-items: center;
  padding: 24rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #404040;
  line-height: 40rpx;
  .label {
    width: 168rpx;
  }
  .content {
    margin-left: 66rpx;
    flex: 1;
    // width: 300rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .right_btn {
    width: 96rpx;
    text-align: right;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 24rpx;
    color: #4378ff;
    line-height: 34rpx;
  }
  .imgs {
    display: flex;
    align-items: center;
    margin: 0 0 0 66rpx;
    .img {
      display: block;
      width: 176rpx;
      height: 176rpx;
      border-radius: 8rpx;
      margin-right: 34rpx;
    }
  }
}
</style>
