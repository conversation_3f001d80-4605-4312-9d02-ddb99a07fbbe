<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-10-14 11:02:59
 * @Description: 
-->
<!DOCTYPE html>
<html style="">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <title></title>
    <style>
      body,
      html {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
        /* transform: translateX(-1.5px); */
      }
      #mapDiv{
        position: relative;
      }
      img {
        object-fit: contain !important;
      }
    </style>
    <!-- 引入uniapp -->
    <!-- <script
      src="./hybrid_html_uni.webview.1.5.5.js"
      type="text/javascript"
    ></script> -->
    <script
      type="text/javascript"
      src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js"
    ></script>
    <!-- 引入天地图 -->
    <script
      src="https://api.tianditu.gov.cn/api?v=4.0&tk=307b00f916df23b531cf212d1e73216b"
      type="text/javascript"
    ></script>

    <body style="width: 100%; height: 100%;" onLoad="onLoad()">
      <div id="mapDiv" style="width: 100%; height: 100%"></div>
    </body>
    <script>
      // 获取应用发来的 URL 参数
      const url = location.href;
      const urlParams = new URLSearchParams(url.split('?')[1]);
      let isDetail = JSON.parse(urlParams.get('isDetail'));
      let detailLng = "";
      let detailLat = "";
      let ControlTop = urlParams.get('ControlTop') || '530px'; // 右下角控件按钮的位置
      console.log('dweeeeed',ControlTop);
      
      if(isDetail){
        detailLng = urlParams.get('longitude');
        detailLat = urlParams.get('latitude');
      }

      // 接收应用发送过来的数据
      function receiveData(data) {
        console.log("下面是我收到的信息内容：", data.type);
        // localStorage.setItem() // 在这里可以拿到传递过来的数据，写入localStorage，然后其他逻辑
        switch (data.type) {
          // 初始化时获取当前所处位置
          case "initLocation":
            toCurPosition();
            break;
          // 选择某个地址
          case "changeLocation":   
            map.panTo(new T.LngLat(data.data.lng, data.data.lat));
            upnewCenterMark(new T.LngLat(data.data.lng, data.data.lat));
            break;
          // 通过经纬度逆解析中文地址
          case "getReverseAddress": 
            reverseGeocode(new T.LngLat(data.data.lng, data.data.lat))
            break;
          // 桩号采集页面，点击编辑按钮，切换页面状态
          case "changeDetailState": 
            isDetail = false;
            break;
          case "upnewPilelist":
            console.log('桩号列表',data.data.list, typeof data.data.list);
            upnewPileMarkers(data.data.list);
            break;
          case "choosePile":
            addPileLabel(data.data.choosedId, data.data.dataList);
            break;

          default:
            break;
        }
      }

      let map; //地图实例
      let zoom = 16; //缩放等级
      let resetMarker; // 右下角重置标记
      let centerMarker; // 中心点标记
      let pileMarkers = []; // 桩号点标记
      let markLabel; // 桩号点label

      //等待dom加载
      function onLoad() {
        let lnglat = {};
        const devicePixelRatio = window.devicePixelRatio || 1;
        console.log("devicePixelRatio", devicePixelRatio);
        // map = new T.Map('mapDiv',{  renderer: 'webgl' // 使用WebGL进行 });//实例化地图
        map = new T.Map("mapDiv", { dpi: devicePixelRatio }); //实例化地图
        map.centerAndZoom(new T.LngLat(114.09974, 22.549), zoom); //默认数值
        map.isDrag(true);
        
        // 添加控件，右下角重置按钮
        addResetControl();

        // 设置事件监听
        map.addEventListener("moveend", function () {
          if(isDetail){
            console.log('详情页，拖动地图'); 
          }else{
            upnewCenterMark();
          }
        });

        // 等待uniapp加载完成后发送信息
        document.addEventListener("UniAppJSBridgeReady", () => {
          uni.postMessage({
            data: {
              title: "天地图",
              msg: "我滴任务完成啦",
            },
          });
        });
      }

      function toCurPosition(){
        let geolocation = new T.Geolocation();
        //根据经纬度重新设置地图
        getCurrentPositionCallback = function (e) {
          console.log('根据经纬度重新设置地图',e.lnglat);
          
          // reverseGeocode(e.lnglat)
          if (this.getStatus() == 0) {
            map.centerAndZoom(e.lnglat, 12);
            lnglat = e.lnglat;
            centerMarker = new T.Marker(e.lnglat);
            map.addOverLay(centerMarker);
          }
          if (this.getStatus() == 1) {
            map.centerAndZoom(e.lnglat, e.level);
            lnglat = e.lnglat;
            centerMarker = new T.Marker(e.lnglat);
            map.addOverLay(centerMarker);
          }
          map.setZoom(zoom);
        };
        //获取当前用户经纬度
        geolocation.getCurrentPosition(getCurrentPositionCallback);
      }

      // 添加右下角控件，重置中心点的位置
      let LocationControl = T.Control.extend({
        initialize: function () {
          // 创建控件元素
          this.button = document.createElement("div");
          this.button.innerHTML =
            '<img src="../icon/current_position.png" style="width:40px;height:40px;">'; // 自定义图标
          this.button.style.cursor = "pointer";
          this.button.style.poaition = "absolute";
          this.button.style.top = ControlTop;
          this.button.style.right = "10px";
          this.button.title = "定位当前位置";
          let targetElement = document.getElementById('mapDiv');
          targetElement.appendChild(this.button);
          // 绑定点击事件
          this.button.onclick = () => {
            if(isDetail){
              this.resetToDetailLocate();
            }else{
              this.locateUser();
            }
          };
        },
        onAdd: function (map) {
          return this.button;
        },
        onRemove: function (map) {
          // 清理工作
        },
        // 设置地图中心为用户当前位置
        locateUser: function () {
          if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
              (position) => {
                const lng = position.coords.longitude;
                const lat = position.coords.latitude;
                map.panTo(new T.LngLat(lng, lat));
                upnewCenterMark(new T.LngLat(lng, lat));
                console.log("当前位置:", lng, lat);
              },
              (error) => {
                console.error("获取位置失败:", error);
                alert("无法获取当前位置");
              }
            );
          } else {
            alert("浏览器不支持地理定位");
          }
        },
        // 设置地图中心为详情位置
        resetToDetailLocate: function(){
          map.panTo(new T.LngLat(detailLng, detailLat));
          upnewCenterMark(new T.LngLat(detailLng, detailLat));    
        }
      });
      function addResetControl() {
        resetMarker = new LocationControl();
        map.addControl(resetMarker);
      }

      // 更新中心点标记
      function upnewCenterMark(centerP='') {
        let center = centerP || map.getCenter();
        // 1清除所有标记
        map.clearOverLays();
        // 2更新中心标记
        centerMarker = new T.Marker(center);
        map.addOverLay(centerMarker);
        // 3告诉应用，重新获取新位置的附近桩号列表
        // uni.postMessage({ data: { msg: "获取桩号", action: "getPile" } });
        
        if(!isDetail){
          // 3告诉应用，移动了地图，中心点发生变化
          uni.postMessage({ data: { msg: "地图中心点变化", action: "moveMap",center } });
          reverseGeocode(center)
        }
      }

      // 更新附近桩号标注点
      function upnewPileMarkers(piles) {
        pileMarkers.forEach(pm=>{
          map.removeOverLay(pm);
        })
        pileMarkers = [];

        piles.forEach((pile) => {
          addPileMarker(pile);
        });
        console.log("渲染桩号完毕", pileMarkers);
      }
      function addPileMarker(pile) {
        let markerObj = {
          icon: new T.Icon({
            iconUrl: "../icon/pile_number_mark.png",
            iconSize: new T.Point(30, 40),
            iconAnchor: new T.Point(15, 30),
          }),
        };
        let pointMarker = new T.Marker(
          new T.LngLat(pile.longitude, pile.latitude),
          markerObj
        );
        map.addOverLay(pointMarker);
        pileMarkers.push(pointMarker);
      }

      // 点击某个桩号，添加label
      function addPileLabel(pileId, list) {
        console.log("addPileLabel", pileId, list);
        if (markLabel) {
          map.removeOverLay(markLabel); // 仅在 markLabel 有效时移除，移除所有桩号点的label
        }
        list.forEach((item) => {
          console.log("addPileLabeladdPileLabel", item);
          if (item.id == pileId) {
            markLabel = new T.Label({
              text: item.stakeName,
              position: new T.LngLat(item.longitude, item.latitude),
              offset: new T.Point(0, -20),
            });
            map.addOverLay(markLabel); // 给当前选中的桩号设置label
          }
        });
      }



      // 创建地理编码，逆地理编码对象
      let geocode = new T.Geocoder();
      // 通过经纬度，获取地址
      function reverseGeocode(LngLat) {
        console.log('3逆解析中文地址');
        geocode.getLocation(LngLat,(res) => {
          let result = res.result
          if(result && result.msg === 'ok') {
            uni.postMessage({ data: { msg: "通过经纬度逆解析中文地址", action: "reverseGeocode",address:result.result.formatted_address } });
          }
        })
      }

    </script>
  </head>
</html>
