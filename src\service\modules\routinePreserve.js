/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-01-15 16:36:16
 * @Description:
 */
import BaseService from "../request";

class RoutinePreserveService extends BaseService {
  constructor() {
    super();
  }

  // 首页今日待办任务
  getHomeTodayList(projectId) {
    return this.get(`/yh/app/home/<USER>/maintain/list?projectId=${projectId}`);
  }
  // 日常保养，历史列表
  getHistoryData(params) {
    return this.get(`/yh/dailyMaintainTask/history/list`,params);
  }
  // 日常保养，首页列表，今日（代办、超时）
  getTodayList(projectId) {
    return this.get(`/yh/dailyMaintainTask/today/todo?projectId=${projectId}`);
  }
  // 日常维修，首页列表，往日，未来
  getNewOrOldList(params) {
    return this.get(
      `/yh/dailyMaintainTask/newOrOld/list?projectId=${params.projectId}&date=${params.date}`
    );
  }

  // 任务详情
  getTaskDetail(id) {
    return this.get(`/yh/dailyMaintainTask/detail/${id}`);
  }
  // 任务完成情况
  getTaskCompleteInfo(id) {
    return this.get(`/yh/dailyMaintainTask/complete/situation/${id}`);
  }
  // 开始保养任务
  startPreserveTask(params) {
    return this.put(`/yh/dailyMaintainTask/start/${params.maintainTaskId}`, params);
  }
  // 新增轨迹-日常养护
  updateLocationPreserve(data) {
    return this.post(`/yh/dailyMaintainTaskTrajectory/save`, data);
  }
  // 结束保养任务
  endPreserveTask(params) {
    return this.put(`/yh/dailyMaintainTask/end/${params.maintainTaskId}`, params);
  }
  
  // 保养作业上报/暂存
  submitPreserve(data) {
    return this.post(`/yh/dailyMaintainReport/save`, data);
  }
  // 上报作业详情
  getHomeworkDetail(id) {
    return this.get(`/yh/dailyMaintainReport/detail/${id}`);
  }
  // 作业上报/暂存 时分页查询作业类型
  maintainTypePage(params) {
    return this.get(`/yh/maintainConfig/selected/?maintainId=${params.maintainId}&nameOrCode=${params.nameOrCode}`);
  }
}
export default new RoutinePreserveService();
