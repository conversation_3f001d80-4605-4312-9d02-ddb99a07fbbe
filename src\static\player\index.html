<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-05-09 15:50:22
 * @Description: 
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Jessibuca Player</title>
    <script src="../jessibuca/jessibuca.js"></script>
    <style>
      * {
        margin: 0;
      }
      html,
      body,
      #player {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        background: black;
      }
      #player.fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
      }
    </style>
  </head>
  <body onLoad="onLoad()">
    <div id="player"></div>
    <script>
      function onLoad() {
        const player = new window.Jessibuca({
          container: document.getElementById("player"),
          decoder: "../jessibuca/decoder.js",
          autoplay: true,
          isResize: false,
          url: 'wss://************:443/rtp/34020000001320000001_34020000001320000001.live.flv'
        });
        console.log("查看player", player);

        // 测试的 demo 地址。
        // 感谢nodemedia提供的测试地址。
        // if (player.hasLoaded()) {
        //   console.log('0');
        //   player.play("http://flv.bdplay.nodemedia.cn/live/bbb.flv");
        // } else {
        //   console.log('1');
        //   player.on("load", function () {
        //     console.log('2');
        //     player.play("http://flv.bdplay.nodemedia.cn/live/bbb.flv");
        //   });
        // }
      }

      // —— 双击全屏 ——
      // let lastTouch = 0;
      // player.container.addEventListener("touchend", (e) => {
      //   const now = Date.now();
      //   if (now - lastTouch < 300) {
      //     // 切换全屏样式
      //     player.container.classList.toggle("fullscreen");
      //     // 如果需要，还可以调用网页端全屏 API
      //     if (player.container.classList.contains("fullscreen")) {
      //       player.container.requestFullscreen?.();
      //     } else {
      //       document.exitFullscreen?.();
      //     }
      //   }
      //   lastTouch = now;
      // });
    </script>
  </body>
</html>
