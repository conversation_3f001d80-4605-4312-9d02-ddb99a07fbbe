<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-08-06 09:48:29
 * @Description: 
-->
<template>
  <view>
    <uv-popup ref="popup" mode="bottom" round="24" @change="change">
      <view class="content">
        <view class="title">分类</view>
        <view class="tabs_box">
          <view
            :class="['tab_item', item.value === curType ? 'tab_active' : '']"
            v-for="(item, index) in tabsList1"
            :key="index"
            @click="changeType(item)"
            >{{ item.label }}</view
          >
        </view>
        <view class="title">数据状态</view>
        <view class="tabs_box">
          <view
            :class="['tab_item', item.value === curState ? 'tab_active' : '']"
            v-for="(item, index) in tabsList2"
            :key="index"
            @click="changeState(item)"
            >{{ item.label }}</view
          >
        </view>
      </view>
      <view class="btn_box">
        <view class="reset_btn btn" @click="reset">重置</view>
        <view class="comfirm_btn btn" @click="comfirm">确定</view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";

// 分类
let curType = ref('');
// 分类tab帅选 相关
const tabsList1 = reactive([
  {
    label: "全部",
    value: ''
  },
  {
    label: "路段",
    value: 1
  },
  {
    label: "桩号",
    value: 2
  },
  {
    label: "路基",
    value: 3
  },
  {
    label: "路面",
    value: 4
  },
  {
    label: "桥梁",
    value: 5
  },
  {
    label: "隧道",
    value: 6
  },
  {
    label: "涵洞",
    value: 7
  },
  {
    label: "沿线设施",
    value: 8
  },
]);

const changeType = (curItem) =>{
  curType.value = curItem.value;
}
// 数据状态
let curState = ref('');
const tabsList2 = reactive([
  {
    label: "全部",
    value: '',
  },
  {
    label: "正常",
    value: 1,
  },
  {
    label: "草稿",
    value: 0,
  },
]);
const changeState = (curItem) =>{
  curState.value = curItem.value;
}
// 重置
const reset = () => {
  curType.value = '';
  curState.value = '';
}
// 确定
const emit = defineEmits(['confirmTabs','cancelTabs']);
const comfirm = () =>{
  emit('confirmTabs',{type:curType.value,state:curState.value});
}
// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const change = (e) => {
  console.log("弹窗状态改变：", e);
  if(!e.show){
    emit('cancelTabs');
  }
};

defineExpose({
  curType,
  curState,
  open,
  close
});
</script>
<style lang="scss" scoped>
.content {
  padding: 40rpx 0 40rpx 40rpx;
  margin-bottom: 100rpx;
  .title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #373737;
    line-height: 44rpx;
  }
  .tabs_box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 32rpx;
    .tab_item {
      margin: 0 24rpx 24rpx 0;
      border-radius: 16rpx;
      padding: 6rpx 28rpx;
      box-sizing: border-box;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      background: #f2f2f2;
      color: #8e8e8e;
      line-height: 40rpx;
    }
    .tab_active{
      background: #4378FF;
      color: #FFF;
    }
  }
}
.btn_box {
  padding: 0 40rpx 40rpx 40rpx;
  display: flex;
  justify-content: space-between;
  .btn {
    width: 256rpx;
    height: 96rpx;
    padding: 20rpx 0;
    box-sizing: border-box;
    text-align: center;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .reset_btn {
    background: #ffffff;
    border: 2rpx solid #4378ff;
    color: #4378ff;
    line-height: 56rpx;
  }
  .comfirm_btn {
    background: #4378ff;
    color: #fff;
    line-height: 56rpx;
  }
}
</style>
