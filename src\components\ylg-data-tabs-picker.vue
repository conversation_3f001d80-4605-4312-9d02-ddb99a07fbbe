<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-09-11 15:20:21
 * @Description: 
-->
<template>
  <view>
    <uv-popup ref="popup" mode="bottom" round="24" @change="change">
      <view class="content">
        <view class="title">分类</view>
        <view class="tabs_box">
          <view
            :class="['tab_item', item.type == curType ? 'tab_active' : '']"
            v-for="(item, index) in types"
            :key="index"
            @click="changeType(item)"
            >{{ item.name }}</view
          >
        </view>
        <view class="title">数据状态</view>
        <view class="tabs_box">
          <view
            :class="['tab_item', item.value === curState ? 'tab_active' : '']"
            v-for="(item, index) in dataStatus"
            :key="index"
            @click="changeState(item)"
            >{{ item.label }}</view
          >
        </view>
      </view>
      <view class="btn_box">
        <view class="reset_btn btn" @click="reset">重置</view>
        <view class="comfirm_btn btn" @click="comfirm">确定</view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";

const props = defineProps({
  types:{
    type: Object,
    required: true,
    default:()=>{}
  },
  dataStatus:{
    type: Object,
    required: true,
    default:()=>{}
  },
  curType:{
    type: [String, Number],
    required: true,
    default:""
  },
  curState:{
    type: [String, Number],
    required: true,
    default:""
  }
})
// 切换分类
const changeType = (curItem) =>{
  emit('tabPickerCallback',{type:'changeType',val:curItem.type})
}
// 切换数据状态
const changeState = (curItem) =>{
  emit('tabPickerCallback',{type:'changeState',val:curItem.value})
}
// 重置
const reset = () => {
  emit('tabPickerCallback',{type:'reset'})
}
// 确定
const emit = defineEmits(['confirmTabs','tabPickerCallback']);
const comfirm = () =>{
  emit('tabPickerCallback',{type:'confirmTabs'});
}
// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const change = (e) => {
  console.log("弹窗状态改变：", e);
  if(!e.show){
    emit('tabPickerCallback',{type:'cancelTabs'})
  }
};

defineExpose({
  open,
  close
});
</script>
<style lang="scss" scoped>
.content {
  padding: 40rpx 0 40rpx 40rpx;
  margin-bottom: 100rpx;
  .title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #373737;
    line-height: 44rpx;
  }
  .tabs_box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 32rpx;
    .tab_item {
      margin: 0 24rpx 24rpx 0;
      border-radius: 16rpx;
      padding: 6rpx 28rpx;
      box-sizing: border-box;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      background: #f2f2f2;
      color: #8e8e8e;
      line-height: 40rpx;
    }
    .tab_active{
      background: #4378FF;
      color: #FFF;
    }
  }
}
.btn_box {
  padding: 0 40rpx 40rpx 40rpx;
  display: flex;
  justify-content: space-between;
  .btn {
    width: 256rpx;
    height: 96rpx;
    padding: 20rpx 0;
    box-sizing: border-box;
    text-align: center;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .reset_btn {
    background: #ffffff;
    border: 2rpx solid #4378ff;
    color: #4378ff;
    line-height: 56rpx;
  }
  .comfirm_btn {
    background: #4378ff;
    color: #fff;
    line-height: 56rpx;
  }
}
</style>
