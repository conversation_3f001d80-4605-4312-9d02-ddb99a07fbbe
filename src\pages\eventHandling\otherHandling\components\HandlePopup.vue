<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-14 15:10:41
 * @Description: 
-->
<template>
  <view>
    <uv-popup
      ref="popup"
      mode="bottom"
      round="24"
      @change="change"
      @touchmove.native.prevent="
        (e) => {
          e.preventDefault();
        }
      "
    >
      <view class="content">
        <uv-tabs
          :list="disposalTabs"
          lineWidth="64"
          lineColor="#4378FF"
          :activeStyle="{
            color: '#4378FF',
            fontWeight: 'bold',
            fontSize: '16px',
            paddingBottom: '10px',
          }"
          :inactiveStyle="{
            color: '#A09F9F',
            fontSize: '16px',
            paddingBottom: '10px',
          }"
          itemStyle="padding:0 15px  ; height: 34px;"
          :customStyle="{
            marginLeft: '-15px',
            borderBottom: '1px solid #F0F0F0',
            marginBottom: '20px',
          }"
          @click="disposalClick"
        >
          <template v-slot:right>
            <view style="margin-right: 40rpx" @click="close()">
              <uv-icon name="close" size="16" bold></uv-icon>
            </view>
          </template>
        </uv-tabs>
        <view v-if="disposalActiveTab === '1'">
          <view class="title-box">
            <view class="title">处置方式 </view>
            <view class="required">必填 </view>
          </view>
          <view class="tabs_box">
            <view
              :class="[
                'tab_item',
                item.type == state1.curType ? 'tab_active' : '',
              ]"
              v-for="(item, index) in state1.types"
              :key="index"
              @click="changeType1(item)"
              >{{ item.name }}</view
            >
          </view>
          <view class="title">备注 </view>
          <view class="tabs_box" style="padding-right: 28rpx">
            <uv-textarea
              v-model="state1.remark"
              count
              height="120px"
              maxlength="150"
              placeholder="请输入备注"
            ></uv-textarea>
          </view>
        </view>
        <view v-if="disposalActiveTab === '2'">
          <view class="title-box">
            <view class="title">病害类型 </view>
            <view class="required">必填 </view>
          </view>
          <view class="tabs_box">
            <view
              :class="[
                'tab_item',
                item.type == state2.curType ? 'tab_active' : '',
              ]"
              v-for="(item, index) in state2.types"
              :key="index"
              @click="changeType2(item)"
              >{{ item.name }}</view
            >
          </view>

          <view class="title-box">
            <view class="title">病害计量 </view>
            <view class="required">必填 </view>
          </view>
          <view class="tabs_box road_box">
            <view class="inp">
              <uv-input
                v-model="state2.diseasesCount"
                border="surround"
                maxlength="8"
                :precision="2"
                type="number"
                placeholder="请输入病害计量 "
                :disabled="!state2.curType"
                @blur="inputBlur"
              ></uv-input>
            </view>
            <view class="right_box">
              {{
                getUnitsMeasure(
                  props.eventObjectType,
                  dictAllList,
                  state2.curType,
                  "dictKey"
                )
              }}
            </view>
          </view>
          <view class="title-box">
            <view class="title">病害说明 </view>
            <view class="required">必填 </view>
          </view>
          <view class="tabs_box" style="padding-right: 28rpx">
            <uv-textarea
              v-model="state2.remark"
              count
              maxlength="150"
              placeholder="请输入病害说明"
            ></uv-textarea>
          </view>
        </view>
      </view>
      <view class="btn_box">
        <view
          v-if="
            (disposalActiveTab === '1' && state1.curType) ||
            (disposalActiveTab === '2' &&
              state2.curType &&
              state2.diseasesCount &&
              state2.remark)
          "
          class="comfirm_btn btn"
          @click="comfirm"
          >确 定</view
        >
        <view v-else class="comfirm_btn btn" style="background: #b0b0b0">
          确 定
        </view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { ref, reactive } from "vue";
import { DataAcquisitionService, eventHandlingApi } from "@/service";

const props = defineProps({
  taskId: {
    type: [String, Number],
    required: true,
    default: "",
  },
  eventObjectType: {
    type: [String, Number],
    required: true,
    default: "",
  },
});
const disposalTabs = reactive([
  {
    type: "1",
    name: "处置事件",
  },
  {
    type: "2",
    name: "转为病害事件",
  },
]);
const state1 = reactive({
  types: [],
  curType: "8",
  remark: "",
});
const state2 = reactive({
  types: [],
  curType: "",
  diseasesCount: "",
  remark: "",
});
const disposalActiveTab = ref("1");
const resetState = () => {
  disposalActiveTab.value = "1";
  state1.curType = "8";
  state1.remark = "";
  state2.curType = "";
  state2.diseasesCount = "";
  state2.remark = "";
};
const disposalClick = (record) => {
  disposalActiveTab.value = record.type;
};
const inputBlur = () => {
  if (state2.diseasesCount === "") return;
  if (Number(state2.diseasesCount) < 0) {
    state2.diseasesCount = 0;
    return;
  }
  if (Number(state2.diseasesCount) > 99999.99) {
    state2.diseasesCount = 99999.99;
    return;
  }
  const num = parseFloat(state2.diseasesCount);
  if (!isNaN(num)) {
    state2.diseasesCount = num.toFixed(2);
  }
};
// 查询字典枚举
const stateData = {
  eventHandleType: ref([]),
  rfDiseasesType: ref([]), // 病害类型（沿线设施）
  culvertDiseasesType: ref([]), //病害类型（涵洞）
  tunnelDiseasesType: ref([]), // 病害类型（隧道）
  bridgeDiseasesType: ref([]), // 病害类型（桥梁）
  pavementDiseasesType: ref([]), //病害类型（路面）
  embankmentDiseasesType: ref([]), // 病害类型（路基
};
const dictAllList = ref([]);
const getDicts = async () => {
  const { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes:
      "eventHandleType,rfDiseasesType,culvertDiseasesType,tunnelDiseasesType,bridgeDiseasesType,pavementDiseasesType,embankmentDiseasesType",
  });
  if (code == 200) {
    dictAllList.value = data;
    state1.types = data.eventHandleType
      .filter((item) => ["6", "7", "8"].includes(item.dictKey))
      .map((i) => {
        return { name: i.dictValue, type: i.dictKey };
      });

    for (let m in data) {
      stateData[m].value = data[m];
    }
    getDiseasesType(props.eventObjectType);
  }
};
const getDiseasesType = (key) => {
  switch (key) {
    case "3":
      state2.types = stateData.embankmentDiseasesType.value;
      break;
    case "4":
      state2.types = stateData.pavementDiseasesType.value;
      break;
    case "5":
      state2.types = stateData.bridgeDiseasesType.value;
      break;
    case "6":
      state2.types = stateData.tunnelDiseasesType.value;
      break;
    case "7":
      state2.types = stateData.culvertDiseasesType.value;
      break;
    case "8":
      state2.types = stateData.rfDiseasesType.value;
      break;
    default:
      state2.types = [];
      break;
  }
  state2.types = state2.types.map((i) => {
    return { name: i.dictValue, type: i.dictKey };
  });
};
const getUnitsMeasure = (type, response, name, key = "dictValue") => {
  console.log("type", type);
  let arr = [];
  if (type === "3") {
    arr = response.embankmentDiseasesType;
  } else if (type === "4") {
    arr = response.pavementDiseasesType;
  } else if (type === "5") {
    arr = response.bridgeDiseasesType;
  } else if (type === "6") {
    arr = response.tunnelDiseasesType;
  } else if (type === "7") {
    arr = response.culvertDiseasesType;
  } else if (type === "8") {
    arr = response.rfDiseasesType;
  }
  let filterItem = arr.filter((item) => item[key] === name);
  if (filterItem.length > 0) {
    return filterItem[0].remark;
  }
  return "";
};
// 切换分类
const changeType1 = (curItem) => {
  state1.curType = curItem.type;
};
const changeType2 = (curItem) => {
  state2.curType = curItem.type;
};
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
// 确定
const comfirm = () => {
  let params = {};
  switch (disposalActiveTab.value) {
    case "1":
      params = {
        projectId: projectInfo.projectId,
        handleType: state1.curType,
        eventIds: [props.taskId],
        handleRemark: state1.remark,
      };
      if (state1.curType === "8") {
        uni.navigateTo({
          url: `/pages/eventHandling/addMaintainPlan/index?taskId=${props.taskId}&eventHandleRemark=${state1.remark}`,
        });
      } else {
        eventHandlingApi.inspectEventBatchHandle(params).then((res) => {
          if (res.code === 200) {
            uni.showToast({
              icon: "none",
              title: "处置事件成功",
            });
            backToHomePage();
          }
        });
      }

      break;
    case "2":
      params = {
        projectId: projectInfo.projectId,
        eventType: "1",
        eventIds: [props.taskId],
        diseasesType: state2.curType,
        diseasesCount: state2.diseasesCount,
        remark: state2.remark,
      };
      eventHandlingApi.inspectEventTransfer(params).then((res) => {
        if (res.code === 200) {
          uni.showToast({
            icon: "none",
            title: "转为其他事件成功",
          });
          backToHomePage();
        }
      });
      break;

    default:
      break;
  }
};

const backToHomePage = () => {
  uni.navigateBack({
    delta: 1,
  });
};
// 打开弹窗
const popup = ref(null);
const open = () => {
  popup.value.open();
  getDicts();
};
const close = () => {
  popup.value.close();
  resetState();
};
const change = (e) => {
  if (!e.show) {
    resetState();
  }
};
defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.content {
  padding: 40rpx;
  padding-right: 0;
  margin-bottom: 50rpx;
  .title-box {
    display: flex;
    align-items: center;
    gap: 0 12rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    .title {
      font-size: 32rpx;
      color: #373737;
    }
    .required {
      display: inherit;
      align-items: center;
      justify-content: center;
      width: 84rpx;
      height: 40rpx;
      font-size: 24rpx;
      color: #f06f6f;
      background: #fff0f0;
      border-radius: 8rpx;
    }
  }

  .tabs_box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 32rpx;
    margin-bottom: 24rpx;
    .tab_item {
      margin: 0 24rpx 24rpx 0;
      border-radius: 16rpx;
      padding: 6rpx 28rpx;
      box-sizing: border-box;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      background: #f2f2f2;
      color: #8e8e8e;
      line-height: 40rpx;
    }
    .tab_active {
      background: #4378ff;
      color: #fff;
    }
  }
  .road_box {
    padding-right: 40rpx;
    gap: 0 18rpx;
    margin-bottom: 48rpx;
    .inp {
      flex: 1;
    }
    .right_box {
      display: inherit;
      align-items: center;
      .btn {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #4378ff;
        line-height: 32rpx;
      }
      .icon {
      }
    }
  }
  .placeholder-box {
    height: 400rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 48rpx;
    padding-right: 28rpx;
    .placeholder-img {
      width: 160rpx;
      height: 160rpx;
    }
    .placeholder-tips {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: #a09f9f;
    }
  }
}
.btn_box {
  padding: 0 40rpx 40rpx 40rpx;
  display: flex;
  justify-content: space-between;
  .btn {
    // width: 256rpx;
    height: 96rpx;
    padding: 20rpx 0;
    box-sizing: border-box;
    text-align: center;
    border-radius: 4rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .comfirm_btn {
    width: 100%;
    background: #4378ff;
    color: #fff;
  }
}
</style>
