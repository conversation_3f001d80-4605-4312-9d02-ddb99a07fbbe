<template>
  <view class="point_basic_info">
    <view class="info_title same-add-title">测点基础信息</view>
    <view class="info_content">
      <view class="info_item flex-between-center">
        <view class="info_item_left">监测项</view>
        <view class="info_item_right">{{
          detailData?.monitorItem || "-"
        }}</view>
      </view>
      <view class="info_item flex-between-center" v-if="showDeviceInfo">
        <view class="info_item_left">关联设备</view>
        <view class="info_item_right">{{ detailData?.deviceDesc || "-" }}</view>
      </view>
      <view class="info_item flex-between-center" v-if="showDeviceInfo">
        <view class="info_item_left">设备状态</view>
        <image
          v-if="detailData?.deviceStatus == '1'"
          class="info_item_icon"
          src="/static/shmStatic/icon/device_online_icon_20250318.png"
        ></image>
        <image
          v-else-if="detailData?.deviceStatus == '0'"
          class="info_item_icon"
          src="/static/shmStatic/icon/device_outline_icon_20250326.png"
        ></image>
        <view v-else class="info_item_right">-</view>
      </view>
      <view class="info_item flex-between-center">
        <view class="info_item_left">关联构件</view>
        <view class="info_item_right">{{
          detailData?.assetContentDTO?.componentName || "-"
        }}</view>
      </view>
      <view class="info_item flex-between-center">
        <view class="info_item_left">构件所属截面</view>
        <scroll-view
          v-if="detailData?.assetContentDTO?.sectionalInfoList?.length"
          scroll-x="true"
          class="info_item_imgs"
        >
          <template
            v-for="(sectionalInfoItem, index) in detailData?.assetContentDTO
              ?.sectionalInfoList"
            :key="index"
          >
            <image
              v-if="sectionalInfoItem.drawSectionalPath"
              class="img"
              :src="showImg(sectionalInfoItem.drawSectionalPath)"
              @click="previewImg(index, sectionImgUrls)"
            ></image>
            <view v-else-if="!sectionImgUrls.length" class="info_item_right">-</view>
          </template>
        </scroll-view>
        <view v-else class="info_item_right">-</view>
      </view>
      <view class="info_item flex-between-center">
        <view class="info_item_left">关联测点位置</view>
        <scroll-view
          v-if="detailData?.layoutPath"
          scroll-x="true"
          class="info_item_imgs"
        >
          <image
            class="img"
            :src="showImg(detailData?.layoutPath)"
            @click="previewImg(0, [showImg(detailData.layoutPath)])"
          ></image>
        </scroll-view>
        <view v-else class="info_item_right">-</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { showImg } from "@/utils";
const props = defineProps({
  detailData: {
    type: Object,
    required: true,
    default: () => {},
  },
  showDeviceInfo: {
    type: Boolean,
    required: false,
    default: true,
  },
});

let sectionImgUrls = ref([]);

watch(
  () => props.detailData,
  (newval) => {
    console.log("查看详情数据", newval);
    sectionImgUrls.value = [];
    newval.assetContentDTO?.sectionalInfoList?.forEach((item) => {
      if(item.drawSectionalPath){
        sectionImgUrls.value.push(showImg(item.drawSectionalPath));
      }
    });
  },
  { immediate: true, deep: true }
);

const previewImg = (cur, urls) => {
  uni.previewImage({
    urls: urls, // 需要预览的图片HTTP链接列表
    current: cur, // 当前显示图片的链接索引
  });
};
</script>
<style lang="scss" scoped>
.point_basic_info {
  margin: 28rpx 0;
  width: 100%;
  padding: 28rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 0 16rpx 0rpx rgba(0, 0, 0, 0.1);
  .info_title {
    // margin-left: 40rpx;
    // padding: 28rpx 40rpx 20rpx 40rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 28rpx;
    color: #404040;
    line-height: 40rpx;
  }
  .info_content {
    margin-top: 44rpx;
    .info_item {
      padding: 24rpx 40rpx;
      line-height: 40rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      border-bottom: 2rpx solid #f0f0f0;
      &_left {
        width: 168rpx;
      }
      &_right {
        width: calc(100% - 224rpx);
      }
      &_icon {
        width: 100rpx;
        height: 48rpx;
        position: relative;
        right: 214rpx;
      }
      &_imgs {
        white-space: nowrap;
        width: calc(100% - 224rpx);
        // overflow-x: scroll;
        .img {
          display: inline-block;
          width: 200rpx;
          height: 200rpx;
          margin-right: 20rpx;
        }
      }
    }
    .info_item:last-child {
      border-bottom: none;
    }
  }
}
</style>
