/*
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2024-10-18 15:30:55
 * @Description:
 */
import { defineStore } from "pinia";
// import { UserService } from "@/service";
// import { usePointStore } from './point';
// import { AuthService, HomeService } from '@/service';
// import { track, register } from '@/common/tLog';
// import { datafluxRum } from '@cloudcare/rum-uniapp';
import { UserService } from "@/service";
export const useUserStore = defineStore({
  id: "user",
  state: () => ({
    token: "",
    id: "",
    mobile: "",
    username: "",
    userId: "",
    postName: "", //职位
    userFullName: "", //名称
    userFace: "", //头像
    tenantId: "", // 租户id
    tenantName: "", // 租户名
    assetInfo: {} //当前选中资产信息
  }),
  // 读取数据
  getters: {
    /**
     * 手机号脱敏
     * @param state
     * @returns
     */
    phoneD: ({ mobile }) =>
      mobile ? `${mobile.slice(0, 3)}****${mobile.slice(-4)}` : "",
  },
  // 存储数据（同步|异步）
  actions: {
    updateUser(user) {
      Object.assign(this, { ...user });
    },
    // 资产信息报错
    async updateAssetInfo(assetInfo) {
      this.assetInfo = assetInfo;
    },
    async updateToken(token) {
      this.token = token;
    },
    async clearToken() {
      this.token = "";
    },
    async logout() {
      UserService.logoutLogin().then((res) => {
        this.token = "";
        uni.reLaunch({
          url: "/pages/login/index",
        });
      });
    },
  },
  persist: {
    // 开启持久化
    enabled: true,
    H5Storage: window?.localStorage || "",
    // strategies: [
    //   {
    //     key: 'userInfo',
    //     paths: [
    //       'token',
    //       'id',
    //       'username',
    //       'mobile',
    //       'postName',
    //       'userFullName',
    //       'userFace',
    //     ],
    //   },
    // ],
  },
});
