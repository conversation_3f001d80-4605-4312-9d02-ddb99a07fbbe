/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-05-23 14:43:21
 * @Description:
 */
import JSEncrypt from "jsencrypt";
import useLoadingStore from "@/store/loading";
import Compressor from "compressorjs";
import uniImageCompress from "uniapp-image-compress";
// import { ref } from 'vue';

export const debounce = (fn, wait) => {
  let timer;
  return function (...args) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, wait);
  };
};

// 获取某月有几天
export const getDaysInMonth = (year, month) => {
  var date = new Date(year, month, 0);
  // 读取date对象的日期部分，它现在表示下一个月的第一天，但由于日期被设置为0，
  // 它实际上返回了上一个月的最后一天，也就是我们想要的天数
  return date.getDate();
};

// 获取图片拼接路径
export const showImg = (path) => {
  if (!path) {
    return;
  }
  if (path.indexOf("http") !== -1) {
    return path;
  }
  // console.log('打印环境',import.meta.env.MODE,process.env.NODE_ENV);
  if (import.meta.env.MODE === "production") {
    // 提测打包时，用本地&测试环境域名，发版时要改回正式的域名
    // return 'https://platform.ylgsz.com/minio' + path
    return "http://139.9.240.160:9090" + path; //本地
  } else if (import.meta.env.MODE === "development") {
    return "http://139.9.240.160:9090" + path; //本地
  } else {
    return "http://139.9.240.160:9090" + path; //
  }
};

// 上传图片
export const uploadFilePromise = (url) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      // url: '/api/file/upload?bucketName=file',
      // url: 'http://192.168.2.145:8080/file/upload?bucketName=file',
      url: `${import.meta.env.VITE_BASE_URL}/file/upload?bucketName=file`,
      filePath: url,
      name: "file",
      success: (res) => {
        console.log("上传成功", res);
        if (res.statusCode === 200) {
          resolve(JSON.parse(res.data)); // 解析为上传文件的 URL
        } else {
          reject(new Error("服务器响应错误: " + res.statusCode));
        }
      },
      fail: (err) => {
        console.error("上传失败", err);
        reject(err);
      },
      complete: (res1) => {
        // console.log("上传完成", res1);
      },
    });
  });
};

export const imgCompress = (imgSrc) => {
  return new Promise((resolve, reject) => {
    plus.zip.compressImage(
      {
        src: imgSrc,
        dst: imgSrc,
        overwrite: true, //是否生成新图片
        quality: 80, //1-100,1图片最小，100图片最大
        width: "50%",
        height: "50%",
      },
      (res) => {
        console.log("压缩成功", res);

        let imgUrl = res.target;
        let imgSize = res.size;
        resolve({ imgUrl, imgSize });
      },
      (err) => {
        reject({ msg: "Compress error!", error: err });
      }
    );
  });
};

export const imgCompress2 = (imgSrc) => {
  function getLastPartAfterSlash(str) {
    let lastIndex = str.lastIndexOf("/");
    if (lastIndex !== -1) {
      const newStr = str.substring(lastIndex + 1);
      const timestamp = Date.now();
      const subStr = newStr.slice(-60);
      const fileName = timestamp + "_" + subStr;
      return fileName;
    }
    return str;
  }
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: imgSrc,
      quality: 50,
      success: (res) => {
        // resolve({ code: "1", imgUrl: res.tempFilePath });  // ps:原始逻辑
        const { tempFilePath } = res;
        const newFileName = getLastPartAfterSlash(tempFilePath);
        plus.io.resolveLocalFileSystemURL(tempFilePath, function (entry) {
          plus.io.resolveLocalFileSystemURL("_doc", function (root) {
            entry.copyTo(
              root,
              newFileName,
              function (success) {
                resolve({ code: "1", imgUrl: success.fullPath });
              },
              function (error) {
                resolve({ code: "0", error });
              }
            );
          });
        });
      },
      fail: (error) => {
        resolve({ code: "0", error });
      },
    });
  });
};

// 获取加密key
export const getRsaCode = (password, key) => {
  const encrypt = new JSEncrypt();
  const PUBLIC_KEY = key;
  encrypt.setPublicKey(PUBLIC_KEY);
  const data = encrypt.encrypt(password);
  return data;
};
/**
 * 显示laoding加载
 * @param title
 */
export const showLoading = (options = {}) => {
  const store = useLoadingStore();
  if (typeof options === "string") {
    options = { title: options };
  }
  store.showLoading(options);
};
/**
 * 关闭loading
 */
export const hideLoading = () => {
  const store = useLoadingStore();
  store.hideLoading();
};
/**
 * 显示toast
 * @param title toast内容
 * @param icon toast图标 默认none
 */
export const showToast = (title, icon = "none") => {
  uni.showToast({
    title: title || "服务异常，请稍后再试",
    icon,
    mask: true,
    duration: 2000,
  });
};

/**
 * 定义系统导航栏高度
 */
export const getSysteminfo = () => {
  let systemBarHeight = "",
    systemsafeAreaHeight = "",
    phoneScreenHeight = "",
    phoneWindowWidth = "",
    bottomSafeArea = "";
  uni.getSystemInfo({
    success: (res) => {
      const { statusBarHeight, safeAreaInsets, screenHeight, windowHeight, windowWidth } =
        res;
      systemBarHeight = `${statusBarHeight + 0}`;
      systemsafeAreaHeight = `-${safeAreaInsets}`;
      phoneScreenHeight = `${screenHeight}`;
      phoneWindowWidth = windowWidth
      bottomSafeArea = screenHeight + 0 - (windowHeight + 0);
    },
  });
  return {
    systemBarHeight,
    systemsafeAreaHeight,
    phoneScreenHeight,
    bottomSafeArea,
    phoneWindowWidth
  };
};


// 级联数据查找
export const findItemByComponentTypeId = (list, targetId, searchParams) => {
  // console.log(list, targetId, searchParams);
  // debugger
  for (let item of list) {
    // 如果当前项的 id 与目标 id 相同，返回该对象
    if (item[searchParams] === targetId) {
      return item;
    }
    // 如果有 children 数组，则递归调用
    if (item.children && item.children.length > 0) {
      const result = findItemByComponentTypeId(item.children, targetId, searchParams);
      if (result) {
        return result;
      }
    }
  }
  return null; // 如果没有找到，返回 null
};

// 防抖
export const throttle = (fn, delay = 300) => {
    let last = 0;
    return function (...args) {
      const now = Date.now();
      if (now - last > delay) {
        fn.apply(this, args);
        last = now;
      }
    };
  };
  
  function safeNavigateTo(url) {
    const pages = getCurrentPages();
    const lastPage = pages[pages.length - 1];
  console.log(url)
  console.log(lastPage)
    // 避免重复跳转同一个页面
    if (lastPage && ('/' + lastPage.route) === url.split('?')[0]) {
      console.warn('⚠️ 已经在该页面，不执行跳转:', url);
      return;
    }
  
    uni.navigateTo({ url });
  }
  // App.vue 或全局初始化文件中（如 store 或 util/init.js）
  var hasBindPush = false
  export const bindPushListener = () => {
    if (hasBindPush) return;
    hasBindPush = true;
  
  
    uni.onPushMessage((res) => {
      console.log('📥 收到推送消息:', res);
  
      if (res.type === 'click') {
  
        const { page, params } = res.data.payload || {};
        console.log('🔘 点击推送:', page, params);
		let userInfo = uni.getStorageSync('user');
        if (page && userInfo && JSON.parse(userInfo).token) {
			safeNavigateTo(`/pages/shmPages/alarmEvents/detail?id=${params.id}`);
          // uni.navigateTo({
          //   url: `/pages/shmPages/alarmEvents/detail?id=${params.id}`,
          // })
          // uni.navigateTo({
          //   url: `/${page}?${Object.entries(params || {})
          //     .map(([k, v]) => `${k}=${v}`)
          //     .join('&')}`,
          // })
        } else {
          uni.navigateTo({
              url: "/pages/login/index",
          });
        // 重置点击标识，防止短时间重复跳转
        setTimeout(() => {  hasBindPush = false; }, 2000);
      }
    }
    })
  }

/**
 * 构建带参数的 URL。
 * @param {string} baseUrl - 基础 URL。
 * @param {Object} params - 要附加到 URL 的参数对象。
 * @returns {string} - 带查询参数的完整 URL。
 */
export const buildUrlWithParams = (baseUrl, params) => {
  const query = Object.entries(params)
    .filter(([key, val]) => val !== undefined && val !== null)
    .map(([key, val]) => `${encodeURIComponent(key)}=${encodeURIComponent(val)}`)
    .join("&");
  return query ? `${baseUrl}?${query}` : baseUrl;
}