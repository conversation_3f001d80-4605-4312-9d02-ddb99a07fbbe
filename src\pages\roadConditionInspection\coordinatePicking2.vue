<template>
  <view class="container">
    <!-- 地图组件 -->
    <!-- <view class="map_box"> -->
      <!-- :style="{width: '100%', height: `calc(100vh - ${isDetail?'104px':'164px'})`}" -->
      <!-- :style="{width: '100%', height: `580px`}" -->
      <web-view
        class="webview_box"
        src="./static/tianditu/map.html"
        @onPostMessage="handlePostMessage"
        @message="handlePostMessage"
        ref="webview"
        :webview-styles="webviewStyles"
      >
      </web-view>
    <!-- </view> -->
  </view>
</template>
<script setup>

const webviewStyles = {
  width: "100%",
  height: "500px",
  top: 50,
  left: 0,
};
</script>
<style lang="scss" scoped>
.container {
  // min-height: 100vh;
  // background: #f4f8ff;
  overflow: hidden;
  position: relative;
  min-height: 100vh;
}
.map_box {
  position: relative;
  // margin-top: -40rpx;
  .reset_icon {
    width: 80rpx;
    height: 80rpx;
    position: absolute;
    right: 40rpx;
    bottom: 40rpx;
  }
}

.bottom_btn {
  width: 100%;
  position: fixed;
  bottom: 0rpx;
  box-sizing: border-box;
  padding: 32rpx 40rpx 20rpx 40rpx;
  background: #f4f8ff;
  .cur_address_desc {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .mb0 {
    margin-bottom: 0rpx;
  }
  .comfrim_btn {
    width: 670rpx;
    height: 84rpx;
    box-sizing: border-box;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
  .plain_btn {
    margin-bottom: 28rpx;
    border: 2rpx solid #4378ff;
    background: #f4f8ff;
    color: #4378ff;
  }
  .detail_address {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 60rpx;
    .cur_address_desc {
      width: 520rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #4378ff;
      line-height: 40rpx;
    }
    .img {
      display: block;
      width: 72rpx;
      height: 72rpx;
    }
  }
}
.address_list {
  position: absolute;
  top: 80rpx;
  left: 0;
  padding: 0 24rpx;
  width: 100%;
  height: calc(100vh - 80rpx);
  box-sizing: border-box;
  background: #fff;
  .top_line {
    height: 2rpx;
    background: #d8d6d6;
  }
  .address_item {
    padding: 24rpx 0rpx;
    display: flex;
    justify-content: space-between;
    align-content: center;
    .left {
      .address_name {
        color: #373737;
        font-size: 32rpx;
      }
      .address_detail {
        color: #a09f9f;
        font-size: 22rpx;
        margin-top: 8rpx;
      }
    }
    .right {
      color: #666;
      font-size: 14rpx;
    }
  }
}
</style>
