<template>
  <view @touchmove.stop.prevent>
    <uv-modal
      ref="updateModalRef"
      title=""
      content=""
      :closeOnClickOverlay="false"
      @touchmove.stop.prevent
    >
      <view class="update-container">
        <view class="update-header"> 正在更新 </view>
        <view class="update-progress">
          <l-circle
            size="174"
            :max="100"
            :percent="rate"
            strokeColor="#3C62FA"
            :strokeWidth="12"
            trailColor="#F2F2F2"
            :trailWidth="12"
          >
            <text class="process-text">
              {{ rate + "%" }}
            </text>
          </l-circle>
        </view>
        <view class="update-tip"> 更新中，请勿关闭App </view>
      </view>
      <template v-slot:confirmButton>
        <view class="btns-box">
          <view class="btn ghost" @click="cancelUpdate">取消更新</view>
        </view>
      </template>
    </uv-modal>
  </view>
</template>
<script setup>
import {
  onLoad,
  onShow,
  onReady,
  onBackPress,
  onPullDownRefresh,
} from "@dcloudio/uni-app";
import { ref, onMounted } from "vue";
// import { showImg } from "@/utils";

const props = defineProps({
  url: {
    type: String,
    default: "",
  },
  baseInfo: {
    type: Object,
    default: () => {},
  },
});
const emits = defineEmits(["openModal"]);
const rate = ref(0);
const updateModalRef = ref();
const downloadTask = ref(null);
const checkUpdate = () => {
  if (updateModalRef.value) {
    updateModalRef.value.open();
    comfirmUpdate("apk");
  }
};
// 全量更新
const comfirmUpdate = () => {
  console.log("全量更新url", props.url);
  downloadTask.value = uni.downloadFile({
    // url: showImg('/file/2024-11-15/__UNI__8A196AA_20241115154611A027.wgt'),
    // url: showImg('/file/2024-11-18/__UNI__8A196AA_20241118103705A030.wgt'),
    // url: showImg(
    //   "/file/2024-11-13/__UNI__8A196AA__20241113114430_20241113151942A880.apk"
    // ),
    url: props.url,
    success: (res) => {
      console.log("下载安装包", res);
      if (res.statusCode === 200) {
        // 写入dlversion - 更新
        const dlVersion = props.baseInfo.version.toLowerCase();
        const cacheVersion = uni.getStorageSync("version");
        setTimeout(() => {
          uni.setStorageSync("version", { ...cacheVersion, dlVersion });
        }, 100);
        // 写入缓存后安装
        setTimeout(() => {
          console.log("下载完成apk");
          plus.runtime.openFile(res.tempFilePath);
        }, 200);
      } else {
        uni.showToast({
          title: "下载失败",
          icon: "none",
        });
      }
    },
    fail: (err) => {
      uni.showToast({
        title: "下载失败",
        icon: "none",
      });
      console.error(err);
    },
  });
  // 监听下载进度
  downloadTask.value.onProgressUpdate((progress) => {
    console.log("下载进度" + progress.progress);
    rate.value = progress.progress;
    console.log("已下载数据长度" + progress.totalBytesWritten);
    console.log(
      "预期需要下载的数据总长度" + progress.totalBytesExpectedToWrite
    );
    // 这里可以更新进度条或其他 UI 元素来显示下载进度
  });
  // updateModalRef.value.close(); // 关闭弹窗
};
// 取消更新
const cancelUpdate = () => {
  updateModalRef.value.close();
  downloadTask.value.abort();
  // 调起检查更新
  emits("openModal");
};
onReady(() => {});
onLoad(async () => {});
onShow(() => {});

onPullDownRefresh(async () => {
  console.log("onPullDownRefresh");
  uni.stopPullDownRefresh();
});
// 首页禁止左滑返回上一页
onBackPress((e) => {
  return true;
});
onMounted(() => {
  // 检查更新-热更新
  checkUpdate();
});
</script>
<style lang="scss" scoped>
// 升级弹窗
:deep(.uv-modal__content) {
  display: block !important;
  padding: 28rpx !important;
}

.update-container {
  .update-header {
    width: 112rpx;
    height: 40rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #373737;
    line-height: 33rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 3rpx;
  }

  .update-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 28rpx 0 20rpx;

    .process-text {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 48rpx;
      color: #404040;
      line-height: 56rpx;
    }
  }

  .update-tip {
    width: 100%;
    height: 40rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #a09f9f;
    line-height: 33rpx;
    text-align: center;
  }
}

.btns-box {
  margin: 48rpx 0 64rpx;
  padding: 0 28rpx;

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 72rpx;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    line-height: 38rpx;
    border: 1px solid #3c62fa;
  }

  .ghost {
    background: #ffffff;
    color: #3c62fa;
  }
}
</style>
