!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(require("path"),require("fs"),require("crypto")):"function"==typeof define&&define.amd?define(["path","fs","crypto"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).path,e.fs,e.crypto$1)}(this,(function(e,r,t){"use strict";function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=n(e),i=n(r),a=n(t);var s=function(e,r){return e(r={exports:{}},r.exports),r.exports}((function(e){var r,t=void 0!==t?t:{},n={};for(r in t)t.hasOwnProperty(r)&&(n[r]=t[r]);var s,u,c,f,l,d="./this.program",p="object"==typeof window,h="function"==typeof importScripts,m="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,v="";m?(v=h?o.default.dirname(v)+"/":__dirname+"/",s=function(e,r){return f||(f=i.default),l||(l=o.default),e=l.normalize(e),f.readFileSync(e,r?null:"utf8")},c=function(e){var r=s(e,!0);return r.buffer||(r=new Uint8Array(r)),k(r.buffer),r},u=function(e,r,t){f||(f=i.default),l||(l=o.default),e=l.normalize(e),f.readFile(e,(function(e,n){e?t(e):r(n.buffer)}))},process.argv.length>1&&(d=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),e.exports=t,process.on("uncaughtException",(function(e){if(!(e instanceof Zr))throw e})),process.on("unhandledRejection",ee),t.inspect=function(){return"[Emscripten Module object]"}):(p||h)&&(h?v=self.location.href:"undefined"!=typeof document&&document.currentScript&&(v=document.currentScript.src),v=0!==v.indexOf("blob:")?v.substr(0,v.lastIndexOf("/")+1):"",s=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},h&&(c=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),u=function(e,r,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?r(n.response):t()},n.onerror=t,n.send(null)});var y,g,w=t.print||console.log.bind(console),E=t.printErr||console.warn.bind(console);for(r in n)n.hasOwnProperty(r)&&(t[r]=n[r]);function b(e){b.shown||(b.shown={}),b.shown[e]||(b.shown[e]=1,E(e))}n=null,t.arguments,t.thisProgram&&(d=t.thisProgram),t.quit,t.wasmBinary&&(y=t.wasmBinary),t.noExitRuntime,"object"!=typeof WebAssembly&&ee("no native wasm support detected");var _=!1;function k(e,r){e||ee("Assertion failed: "+r)}var T="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function P(e,r,t){for(var n=r+t,o=r;e[o]&&!(o>=n);)++o;if(o-r>16&&e.subarray&&T)return T.decode(e.subarray(r,o));for(var i="";r<o;){var a=e[r++];if(128&a){var s=63&e[r++];if(192!=(224&a)){var u=63&e[r++];if((a=224==(240&a)?(15&a)<<12|s<<6|u:(7&a)<<18|s<<12|u<<6|63&e[r++])<65536)i+=String.fromCharCode(a);else{var c=a-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&a)<<6|s)}else i+=String.fromCharCode(a)}return i}function C(e,r){return e?P($,e,r):""}function A(e,r,t,n){if(!(n>0))return 0;for(var o=t,i=t+n-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a);if(s<=127){if(t>=i)break;r[t++]=s}else if(s<=2047){if(t+1>=i)break;r[t++]=192|s>>6,r[t++]=128|63&s}else if(s<=65535){if(t+2>=i)break;r[t++]=224|s>>12,r[t++]=128|s>>6&63,r[t++]=128|63&s}else{if(t+3>=i)break;r[t++]=240|s>>18,r[t++]=128|s>>12&63,r[t++]=128|s>>6&63,r[t++]=128|63&s}}return r[t]=0,t-o}function D(e,r,t){return A(e,$,r,t)}function S(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++t)),n<=127?++r:r+=n<=2047?2:n<=65535?3:4}return r}var F,x,$,R,M,O,I,j,U,N,B="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function L(e,r){for(var t=e,n=t>>1,o=n+r/2;!(n>=o)&&M[n];)++n;if((t=n<<1)-e>32&&B)return B.decode($.subarray(e,t));for(var i="",a=0;!(a>=r/2);++a){var s=R[e+2*a>>1];if(0==s)break;i+=String.fromCharCode(s)}return i}function W(e,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var n=r,o=(t-=2)<2*e.length?t/2:e.length,i=0;i<o;++i){var a=e.charCodeAt(i);R[r>>1]=a,r+=2}return R[r>>1]=0,r-n}function z(e){return 2*e.length}function H(e,r){for(var t=0,n="";!(t>=r/4);){var o=O[e+4*t>>2];if(0==o)break;if(++t,o>=65536){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n}function V(e,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var n=r,o=n+t-4,i=0;i<e.length;++i){var a=e.charCodeAt(i);if(a>=55296&&a<=57343)a=65536+((1023&a)<<10)|1023&e.charCodeAt(++i);if(O[r>>2]=a,(r+=4)+4>o)break}return O[r>>2]=0,r-n}function X(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&++t,r+=4}return r}t.INITIAL_MEMORY;var G=[],q=[],Y=[];var K=0,J=null;function Q(e){K++,t.monitorRunDependencies&&t.monitorRunDependencies(K)}function Z(e){if(K--,t.monitorRunDependencies&&t.monitorRunDependencies(K),0==K&&J){var r=J;J=null,r()}}function ee(e){throw t.onAbort&&t.onAbort(e),E(e+=""),_=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(e)}t.preloadedImages={},t.preloadedAudios={};var re,te,ne;function oe(e){return e.startsWith("data:application/octet-stream;base64,")}function ie(e){return e.startsWith("file://")}function ae(e){try{if(e==re&&y)return new Uint8Array(y);if(c)return c(e);throw"both async and sync fetching of the wasm failed"}catch(e){ee(e)}}function se(e){for(;e.length>0;){var r=e.shift();if("function"!=typeof r){var n=r.func;"number"==typeof n?void 0===r.arg?N.get(n)():N.get(n)(r.arg):n(void 0===r.arg?null:r.arg)}else r(t)}}function ue(){var e=new Error;if(!e.stack){try{throw new Error}catch(r){e=r}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}oe(re="decoder.wasm")||(re=function(e){return t.locateFile?t.locateFile(e,v):v+e}(re));var ce={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,r){for(var t=0,n=e.length-1;n>=0;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),t++):t&&(e.splice(n,1),t--)}if(r)for(;t;t--)e.unshift("..");return e},normalize:function(e){var r="/"===e.charAt(0),t="/"===e.substr(-1);return(e=ce.normalizeArray(e.split("/").filter((function(e){return!!e})),!r).join("/"))||r||(e="."),e&&t&&(e+="/"),(r?"/":"")+e},dirname:function(e){var r=ce.splitPath(e),t=r[0],n=r[1];return t||n?(n&&(n=n.substr(0,n.length-1)),t+n):"."},basename:function(e){if("/"===e)return"/";var r=(e=(e=ce.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===r?e:e.substr(r+1)},extname:function(e){return ce.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return ce.normalize(e.join("/"))},join2:function(e,r){return ce.normalize(e+"/"+r)}};var fe={resolve:function(){for(var e="",r=!1,t=arguments.length-1;t>=-1&&!r;t--){var n=t>=0?arguments[t]:he.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,r="/"===n.charAt(0)}return(r?"/":"")+(e=ce.normalizeArray(e.split("/").filter((function(e){return!!e})),!r).join("/"))||"."},relative:function(e,r){function t(e){for(var r=0;r<e.length&&""===e[r];r++);for(var t=e.length-1;t>=0&&""===e[t];t--);return r>t?[]:e.slice(r,t-r+1)}e=fe.resolve(e).substr(1),r=fe.resolve(r).substr(1);for(var n=t(e.split("/")),o=t(r.split("/")),i=Math.min(n.length,o.length),a=i,s=0;s<i;s++)if(n[s]!==o[s]){a=s;break}var u=[];for(s=a;s<n.length;s++)u.push("..");return(u=u.concat(o.slice(a))).join("/")}},le={ttys:[],init:function(){},shutdown:function(){},register:function(e,r){le.ttys[e]={input:[],output:[],ops:r},he.registerDevice(e,le.stream_ops)},stream_ops:{open:function(e){var r=le.ttys[e.node.rdev];if(!r)throw new he.ErrnoError(43);e.tty=r,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,r,t,n,o){if(!e.tty||!e.tty.ops.get_char)throw new he.ErrnoError(60);for(var i=0,a=0;a<n;a++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new he.ErrnoError(29)}if(void 0===s&&0===i)throw new he.ErrnoError(6);if(null==s)break;i++,r[t+a]=s}return i&&(e.node.timestamp=Date.now()),i},write:function(e,r,t,n,o){if(!e.tty||!e.tty.ops.put_char)throw new he.ErrnoError(60);try{for(var i=0;i<n;i++)e.tty.ops.put_char(e.tty,r[t+i])}catch(e){throw new he.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(e){if(!e.input.length){var r=null;if(m){var t=Buffer.alloc(256),n=0;try{n=f.readSync(process.stdin.fd,t,0,256,null)}catch(e){if(!e.toString().includes("EOF"))throw e;n=0}r=n>0?t.slice(0,n).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(r=window.prompt("Input: "))&&(r+="\n"):"function"==typeof readline&&null!==(r=readline())&&(r+="\n");if(!r)return null;e.input=Hr(r,!0)}return e.input.shift()},put_char:function(e,r){null===r||10===r?(w(P(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&e.output.length>0&&(w(P(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,r){null===r||10===r?(E(P(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&e.output.length>0&&(E(P(e.output,0)),e.output=[])}}};function de(e){e=function(e,r){return Math.ceil(e/r)*r}(e,65536);var r=Qr(65536,e);return r?(function(e,r){$.fill(0,e,e+r)}(r,e),r):0}var pe={ops_table:null,mount:function(e){return pe.createNode(null,"/",16895,0)},createNode:function(e,r,t,n){if(he.isBlkdev(t)||he.isFIFO(t))throw new he.ErrnoError(63);pe.ops_table||(pe.ops_table={dir:{node:{getattr:pe.node_ops.getattr,setattr:pe.node_ops.setattr,lookup:pe.node_ops.lookup,mknod:pe.node_ops.mknod,rename:pe.node_ops.rename,unlink:pe.node_ops.unlink,rmdir:pe.node_ops.rmdir,readdir:pe.node_ops.readdir,symlink:pe.node_ops.symlink},stream:{llseek:pe.stream_ops.llseek}},file:{node:{getattr:pe.node_ops.getattr,setattr:pe.node_ops.setattr},stream:{llseek:pe.stream_ops.llseek,read:pe.stream_ops.read,write:pe.stream_ops.write,allocate:pe.stream_ops.allocate,mmap:pe.stream_ops.mmap,msync:pe.stream_ops.msync}},link:{node:{getattr:pe.node_ops.getattr,setattr:pe.node_ops.setattr,readlink:pe.node_ops.readlink},stream:{}},chrdev:{node:{getattr:pe.node_ops.getattr,setattr:pe.node_ops.setattr},stream:he.chrdev_stream_ops}});var o=he.createNode(e,r,t,n);return he.isDir(o.mode)?(o.node_ops=pe.ops_table.dir.node,o.stream_ops=pe.ops_table.dir.stream,o.contents={}):he.isFile(o.mode)?(o.node_ops=pe.ops_table.file.node,o.stream_ops=pe.ops_table.file.stream,o.usedBytes=0,o.contents=null):he.isLink(o.mode)?(o.node_ops=pe.ops_table.link.node,o.stream_ops=pe.ops_table.link.stream):he.isChrdev(o.mode)&&(o.node_ops=pe.ops_table.chrdev.node,o.stream_ops=pe.ops_table.chrdev.stream),o.timestamp=Date.now(),e&&(e.contents[r]=o,e.timestamp=o.timestamp),o},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,r){var t=e.contents?e.contents.length:0;if(!(t>=r)){r=Math.max(r,t*(t<1048576?2:1.125)>>>0),0!=t&&(r=Math.max(r,256));var n=e.contents;e.contents=new Uint8Array(r),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,r){if(e.usedBytes!=r)if(0==r)e.contents=null,e.usedBytes=0;else{var t=e.contents;e.contents=new Uint8Array(r),t&&e.contents.set(t.subarray(0,Math.min(r,e.usedBytes))),e.usedBytes=r}},node_ops:{getattr:function(e){var r={};return r.dev=he.isChrdev(e.mode)?e.id:1,r.ino=e.id,r.mode=e.mode,r.nlink=1,r.uid=0,r.gid=0,r.rdev=e.rdev,he.isDir(e.mode)?r.size=4096:he.isFile(e.mode)?r.size=e.usedBytes:he.isLink(e.mode)?r.size=e.link.length:r.size=0,r.atime=new Date(e.timestamp),r.mtime=new Date(e.timestamp),r.ctime=new Date(e.timestamp),r.blksize=4096,r.blocks=Math.ceil(r.size/r.blksize),r},setattr:function(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp),void 0!==r.size&&pe.resizeFileStorage(e,r.size)},lookup:function(e,r){throw he.genericErrors[44]},mknod:function(e,r,t,n){return pe.createNode(e,r,t,n)},rename:function(e,r,t){if(he.isDir(e.mode)){var n;try{n=he.lookupNode(r,t)}catch(e){}if(n)for(var o in n.contents)throw new he.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=t,r.contents[t]=e,r.timestamp=e.parent.timestamp,e.parent=r},unlink:function(e,r){delete e.contents[r],e.timestamp=Date.now()},rmdir:function(e,r){var t=he.lookupNode(e,r);for(var n in t.contents)throw new he.ErrnoError(55);delete e.contents[r],e.timestamp=Date.now()},readdir:function(e){var r=[".",".."];for(var t in e.contents)e.contents.hasOwnProperty(t)&&r.push(t);return r},symlink:function(e,r,t){var n=pe.createNode(e,r,41471,0);return n.link=t,n},readlink:function(e){if(!he.isLink(e.mode))throw new he.ErrnoError(28);return e.link}},stream_ops:{read:function(e,r,t,n,o){var i=e.node.contents;if(o>=e.node.usedBytes)return 0;var a=Math.min(e.node.usedBytes-o,n);if(a>8&&i.subarray)r.set(i.subarray(o,o+a),t);else for(var s=0;s<a;s++)r[t+s]=i[o+s];return a},write:function(e,r,t,n,o,i){if(!n)return 0;var a=e.node;if(a.timestamp=Date.now(),r.subarray&&(!a.contents||a.contents.subarray)){if(i)return a.contents=r.subarray(t,t+n),a.usedBytes=n,n;if(0===a.usedBytes&&0===o)return a.contents=r.slice(t,t+n),a.usedBytes=n,n;if(o+n<=a.usedBytes)return a.contents.set(r.subarray(t,t+n),o),n}if(pe.expandFileStorage(a,o+n),a.contents.subarray&&r.subarray)a.contents.set(r.subarray(t,t+n),o);else for(var s=0;s<n;s++)a.contents[o+s]=r[t+s];return a.usedBytes=Math.max(a.usedBytes,o+n),n},llseek:function(e,r,t){var n=r;if(1===t?n+=e.position:2===t&&he.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new he.ErrnoError(28);return n},allocate:function(e,r,t){pe.expandFileStorage(e.node,r+t),e.node.usedBytes=Math.max(e.node.usedBytes,r+t)},mmap:function(e,r,t,n,o,i){if(0!==r)throw new he.ErrnoError(28);if(!he.isFile(e.node.mode))throw new he.ErrnoError(43);var a,s,u=e.node.contents;if(2&i||u.buffer!==F){if((n>0||n+t<u.length)&&(u=u.subarray?u.subarray(n,n+t):Array.prototype.slice.call(u,n,n+t)),s=!0,!(a=de(t)))throw new he.ErrnoError(48);x.set(u,a)}else s=!1,a=u.byteOffset;return{ptr:a,allocated:s}},msync:function(e,r,t,n,o){if(!he.isFile(e.node.mode))throw new he.ErrnoError(43);return 2&o||pe.stream_ops.write(e,r,0,n,t,!1),0}}};var he={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e,r){if(r=r||{},!(e=fe.resolve(he.cwd(),e)))return{path:"",node:null};var t={follow_mount:!0,recurse_count:0};for(var n in t)void 0===r[n]&&(r[n]=t[n]);if(r.recurse_count>8)throw new he.ErrnoError(32);for(var o=ce.normalizeArray(e.split("/").filter((function(e){return!!e})),!1),i=he.root,a="/",s=0;s<o.length;s++){var u=s===o.length-1;if(u&&r.parent)break;if(i=he.lookupNode(i,o[s]),a=ce.join2(a,o[s]),he.isMountpoint(i)&&(!u||u&&r.follow_mount)&&(i=i.mounted.root),!u||r.follow)for(var c=0;he.isLink(i.mode);){var f=he.readlink(a);if(a=fe.resolve(ce.dirname(a),f),i=he.lookupPath(a,{recurse_count:r.recurse_count}).node,c++>40)throw new he.ErrnoError(32)}}return{path:a,node:i}},getPath:function(e){for(var r;;){if(he.isRoot(e)){var t=e.mount.mountpoint;return r?"/"!==t[t.length-1]?t+"/"+r:t+r:t}r=r?e.name+"/"+r:e.name,e=e.parent}},hashName:function(e,r){for(var t=0,n=0;n<r.length;n++)t=(t<<5)-t+r.charCodeAt(n)|0;return(e+t>>>0)%he.nameTable.length},hashAddNode:function(e){var r=he.hashName(e.parent.id,e.name);e.name_next=he.nameTable[r],he.nameTable[r]=e},hashRemoveNode:function(e){var r=he.hashName(e.parent.id,e.name);if(he.nameTable[r]===e)he.nameTable[r]=e.name_next;else for(var t=he.nameTable[r];t;){if(t.name_next===e){t.name_next=e.name_next;break}t=t.name_next}},lookupNode:function(e,r){var t=he.mayLookup(e);if(t)throw new he.ErrnoError(t,e);for(var n=he.hashName(e.id,r),o=he.nameTable[n];o;o=o.name_next){var i=o.name;if(o.parent.id===e.id&&i===r)return o}return he.lookup(e,r)},createNode:function(e,r,t,n){var o=new he.FSNode(e,r,t,n);return he.hashAddNode(o),o},destroyNode:function(e){he.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return 49152==(49152&e)},flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:function(e){var r=he.flagModes[e];if(void 0===r)throw new Error("Unknown file open mode: "+e);return r},flagsToPermissionString:function(e){var r=["r","w","rw"][3&e];return 512&e&&(r+="w"),r},nodePermissions:function(e,r){return he.ignorePermissions||(!r.includes("r")||292&e.mode)&&(!r.includes("w")||146&e.mode)&&(!r.includes("x")||73&e.mode)?0:2},mayLookup:function(e){var r=he.nodePermissions(e,"x");return r||(e.node_ops.lookup?0:2)},mayCreate:function(e,r){try{he.lookupNode(e,r);return 20}catch(e){}return he.nodePermissions(e,"wx")},mayDelete:function(e,r,t){var n;try{n=he.lookupNode(e,r)}catch(e){return e.errno}var o=he.nodePermissions(e,"wx");if(o)return o;if(t){if(!he.isDir(n.mode))return 54;if(he.isRoot(n)||he.getPath(n)===he.cwd())return 10}else if(he.isDir(n.mode))return 31;return 0},mayOpen:function(e,r){return e?he.isLink(e.mode)?32:he.isDir(e.mode)&&("r"!==he.flagsToPermissionString(r)||512&r)?31:he.nodePermissions(e,he.flagsToPermissionString(r)):44},MAX_OPEN_FDS:4096,nextfd:function(e,r){e=e||0,r=r||he.MAX_OPEN_FDS;for(var t=e;t<=r;t++)if(!he.streams[t])return t;throw new he.ErrnoError(33)},getStream:function(e){return he.streams[e]},createStream:function(e,r,t){he.FSStream||(he.FSStream=function(){},he.FSStream.prototype={object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}});var n=new he.FSStream;for(var o in e)n[o]=e[o];e=n;var i=he.nextfd(r,t);return e.fd=i,he.streams[i]=e,e},closeStream:function(e){he.streams[e]=null},chrdev_stream_ops:{open:function(e){var r=he.getDevice(e.node.rdev);e.stream_ops=r.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new he.ErrnoError(70)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,r){return e<<8|r},registerDevice:function(e,r){he.devices[e]={stream_ops:r}},getDevice:function(e){return he.devices[e]},getMounts:function(e){for(var r=[],t=[e];t.length;){var n=t.pop();r.push(n),t.push.apply(t,n.mounts)}return r},syncfs:function(e,r){"function"==typeof e&&(r=e,e=!1),he.syncFSRequests++,he.syncFSRequests>1&&E("warning: "+he.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var t=he.getMounts(he.root.mount),n=0;function o(e){return he.syncFSRequests--,r(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,o(e));++n>=t.length&&o(null)}t.forEach((function(r){if(!r.type.syncfs)return i(null);r.type.syncfs(r,e,i)}))},mount:function(e,r,t){var n,o="/"===t,i=!t;if(o&&he.root)throw new he.ErrnoError(10);if(!o&&!i){var a=he.lookupPath(t,{follow_mount:!1});if(t=a.path,n=a.node,he.isMountpoint(n))throw new he.ErrnoError(10);if(!he.isDir(n.mode))throw new he.ErrnoError(54)}var s={type:e,opts:r,mountpoint:t,mounts:[]},u=e.mount(s);return u.mount=s,s.root=u,o?he.root=u:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),u},unmount:function(e){var r=he.lookupPath(e,{follow_mount:!1});if(!he.isMountpoint(r.node))throw new he.ErrnoError(28);var t=r.node,n=t.mounted,o=he.getMounts(n);Object.keys(he.nameTable).forEach((function(e){for(var r=he.nameTable[e];r;){var t=r.name_next;o.includes(r.mount)&&he.destroyNode(r),r=t}})),t.mounted=null;var i=t.mount.mounts.indexOf(n);t.mount.mounts.splice(i,1)},lookup:function(e,r){return e.node_ops.lookup(e,r)},mknod:function(e,r,t){var n=he.lookupPath(e,{parent:!0}).node,o=ce.basename(e);if(!o||"."===o||".."===o)throw new he.ErrnoError(28);var i=he.mayCreate(n,o);if(i)throw new he.ErrnoError(i);if(!n.node_ops.mknod)throw new he.ErrnoError(63);return n.node_ops.mknod(n,o,r,t)},create:function(e,r){return r=void 0!==r?r:438,r&=4095,r|=32768,he.mknod(e,r,0)},mkdir:function(e,r){return r=void 0!==r?r:511,r&=1023,r|=16384,he.mknod(e,r,0)},mkdirTree:function(e,r){for(var t=e.split("/"),n="",o=0;o<t.length;++o)if(t[o]){n+="/"+t[o];try{he.mkdir(n,r)}catch(e){if(20!=e.errno)throw e}}},mkdev:function(e,r,t){return void 0===t&&(t=r,r=438),r|=8192,he.mknod(e,r,t)},symlink:function(e,r){if(!fe.resolve(e))throw new he.ErrnoError(44);var t=he.lookupPath(r,{parent:!0}).node;if(!t)throw new he.ErrnoError(44);var n=ce.basename(r),o=he.mayCreate(t,n);if(o)throw new he.ErrnoError(o);if(!t.node_ops.symlink)throw new he.ErrnoError(63);return t.node_ops.symlink(t,n,e)},rename:function(e,r){var t,n,o=ce.dirname(e),i=ce.dirname(r),a=ce.basename(e),s=ce.basename(r);if(t=he.lookupPath(e,{parent:!0}).node,n=he.lookupPath(r,{parent:!0}).node,!t||!n)throw new he.ErrnoError(44);if(t.mount!==n.mount)throw new he.ErrnoError(75);var u,c=he.lookupNode(t,a),f=fe.relative(e,i);if("."!==f.charAt(0))throw new he.ErrnoError(28);if("."!==(f=fe.relative(r,o)).charAt(0))throw new he.ErrnoError(55);try{u=he.lookupNode(n,s)}catch(e){}if(c!==u){var l=he.isDir(c.mode),d=he.mayDelete(t,a,l);if(d)throw new he.ErrnoError(d);if(d=u?he.mayDelete(n,s,l):he.mayCreate(n,s))throw new he.ErrnoError(d);if(!t.node_ops.rename)throw new he.ErrnoError(63);if(he.isMountpoint(c)||u&&he.isMountpoint(u))throw new he.ErrnoError(10);if(n!==t&&(d=he.nodePermissions(t,"w")))throw new he.ErrnoError(d);he.hashRemoveNode(c);try{t.node_ops.rename(c,n,s)}catch(e){throw e}finally{he.hashAddNode(c)}}},rmdir:function(e){var r=he.lookupPath(e,{parent:!0}).node,t=ce.basename(e),n=he.lookupNode(r,t),o=he.mayDelete(r,t,!0);if(o)throw new he.ErrnoError(o);if(!r.node_ops.rmdir)throw new he.ErrnoError(63);if(he.isMountpoint(n))throw new he.ErrnoError(10);r.node_ops.rmdir(r,t),he.destroyNode(n)},readdir:function(e){var r=he.lookupPath(e,{follow:!0}).node;if(!r.node_ops.readdir)throw new he.ErrnoError(54);return r.node_ops.readdir(r)},unlink:function(e){var r=he.lookupPath(e,{parent:!0}).node,t=ce.basename(e),n=he.lookupNode(r,t),o=he.mayDelete(r,t,!1);if(o)throw new he.ErrnoError(o);if(!r.node_ops.unlink)throw new he.ErrnoError(63);if(he.isMountpoint(n))throw new he.ErrnoError(10);r.node_ops.unlink(r,t),he.destroyNode(n)},readlink:function(e){var r=he.lookupPath(e).node;if(!r)throw new he.ErrnoError(44);if(!r.node_ops.readlink)throw new he.ErrnoError(28);return fe.resolve(he.getPath(r.parent),r.node_ops.readlink(r))},stat:function(e,r){var t=he.lookupPath(e,{follow:!r}).node;if(!t)throw new he.ErrnoError(44);if(!t.node_ops.getattr)throw new he.ErrnoError(63);return t.node_ops.getattr(t)},lstat:function(e){return he.stat(e,!0)},chmod:function(e,r,t){var n;"string"==typeof e?n=he.lookupPath(e,{follow:!t}).node:n=e;if(!n.node_ops.setattr)throw new he.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&r|-4096&n.mode,timestamp:Date.now()})},lchmod:function(e,r){he.chmod(e,r,!0)},fchmod:function(e,r){var t=he.getStream(e);if(!t)throw new he.ErrnoError(8);he.chmod(t.node,r)},chown:function(e,r,t,n){var o;"string"==typeof e?o=he.lookupPath(e,{follow:!n}).node:o=e;if(!o.node_ops.setattr)throw new he.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown:function(e,r,t){he.chown(e,r,t,!0)},fchown:function(e,r,t){var n=he.getStream(e);if(!n)throw new he.ErrnoError(8);he.chown(n.node,r,t)},truncate:function(e,r){if(r<0)throw new he.ErrnoError(28);var t;"string"==typeof e?t=he.lookupPath(e,{follow:!0}).node:t=e;if(!t.node_ops.setattr)throw new he.ErrnoError(63);if(he.isDir(t.mode))throw new he.ErrnoError(31);if(!he.isFile(t.mode))throw new he.ErrnoError(28);var n=he.nodePermissions(t,"w");if(n)throw new he.ErrnoError(n);t.node_ops.setattr(t,{size:r,timestamp:Date.now()})},ftruncate:function(e,r){var t=he.getStream(e);if(!t)throw new he.ErrnoError(8);if(0==(2097155&t.flags))throw new he.ErrnoError(28);he.truncate(t.node,r)},utime:function(e,r,t){var n=he.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(r,t)})},open:function(e,r,n,o,i){if(""===e)throw new he.ErrnoError(44);var a;if(n=void 0===n?438:n,n=64&(r="string"==typeof r?he.modeStringToFlags(r):r)?4095&n|32768:0,"object"==typeof e)a=e;else{e=ce.normalize(e);try{a=he.lookupPath(e,{follow:!(131072&r)}).node}catch(e){}}var s=!1;if(64&r)if(a){if(128&r)throw new he.ErrnoError(20)}else a=he.mknod(e,n,0),s=!0;if(!a)throw new he.ErrnoError(44);if(he.isChrdev(a.mode)&&(r&=-513),65536&r&&!he.isDir(a.mode))throw new he.ErrnoError(54);if(!s){var u=he.mayOpen(a,r);if(u)throw new he.ErrnoError(u)}512&r&&he.truncate(a,0),r&=-131713;var c=he.createStream({node:a,path:he.getPath(a),flags:r,seekable:!0,position:0,stream_ops:a.stream_ops,ungotten:[],error:!1},o,i);return c.stream_ops.open&&c.stream_ops.open(c),!t.logReadFiles||1&r||(he.readFiles||(he.readFiles={}),e in he.readFiles||(he.readFiles[e]=1)),c},close:function(e){if(he.isClosed(e))throw new he.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{he.closeStream(e.fd)}e.fd=null},isClosed:function(e){return null===e.fd},llseek:function(e,r,t){if(he.isClosed(e))throw new he.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new he.ErrnoError(70);if(0!=t&&1!=t&&2!=t)throw new he.ErrnoError(28);return e.position=e.stream_ops.llseek(e,r,t),e.ungotten=[],e.position},read:function(e,r,t,n,o){if(n<0||o<0)throw new he.ErrnoError(28);if(he.isClosed(e))throw new he.ErrnoError(8);if(1==(2097155&e.flags))throw new he.ErrnoError(8);if(he.isDir(e.node.mode))throw new he.ErrnoError(31);if(!e.stream_ops.read)throw new he.ErrnoError(28);var i=void 0!==o;if(i){if(!e.seekable)throw new he.ErrnoError(70)}else o=e.position;var a=e.stream_ops.read(e,r,t,n,o);return i||(e.position+=a),a},write:function(e,r,t,n,o,i){if(n<0||o<0)throw new he.ErrnoError(28);if(he.isClosed(e))throw new he.ErrnoError(8);if(0==(2097155&e.flags))throw new he.ErrnoError(8);if(he.isDir(e.node.mode))throw new he.ErrnoError(31);if(!e.stream_ops.write)throw new he.ErrnoError(28);e.seekable&&1024&e.flags&&he.llseek(e,0,2);var a=void 0!==o;if(a){if(!e.seekable)throw new he.ErrnoError(70)}else o=e.position;var s=e.stream_ops.write(e,r,t,n,o,i);return a||(e.position+=s),s},allocate:function(e,r,t){if(he.isClosed(e))throw new he.ErrnoError(8);if(r<0||t<=0)throw new he.ErrnoError(28);if(0==(2097155&e.flags))throw new he.ErrnoError(8);if(!he.isFile(e.node.mode)&&!he.isDir(e.node.mode))throw new he.ErrnoError(43);if(!e.stream_ops.allocate)throw new he.ErrnoError(138);e.stream_ops.allocate(e,r,t)},mmap:function(e,r,t,n,o,i){if(0!=(2&o)&&0==(2&i)&&2!=(2097155&e.flags))throw new he.ErrnoError(2);if(1==(2097155&e.flags))throw new he.ErrnoError(2);if(!e.stream_ops.mmap)throw new he.ErrnoError(43);return e.stream_ops.mmap(e,r,t,n,o,i)},msync:function(e,r,t,n,o){return e&&e.stream_ops.msync?e.stream_ops.msync(e,r,t,n,o):0},munmap:function(e){return 0},ioctl:function(e,r,t){if(!e.stream_ops.ioctl)throw new he.ErrnoError(59);return e.stream_ops.ioctl(e,r,t)},readFile:function(e,r){if((r=r||{}).flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'+r.encoding+'"');var t,n=he.open(e,r.flags),o=he.stat(e).size,i=new Uint8Array(o);return he.read(n,i,0,o,0),"utf8"===r.encoding?t=P(i,0):"binary"===r.encoding&&(t=i),he.close(n),t},writeFile:function(e,r,t){(t=t||{}).flags=t.flags||577;var n=he.open(e,t.flags,t.mode);if("string"==typeof r){var o=new Uint8Array(S(r)+1),i=A(r,o,0,o.length);he.write(n,o,0,i,void 0,t.canOwn)}else{if(!ArrayBuffer.isView(r))throw new Error("Unsupported data type");he.write(n,r,0,r.byteLength,void 0,t.canOwn)}he.close(n)},cwd:function(){return he.currentPath},chdir:function(e){var r=he.lookupPath(e,{follow:!0});if(null===r.node)throw new he.ErrnoError(44);if(!he.isDir(r.node.mode))throw new he.ErrnoError(54);var t=he.nodePermissions(r.node,"x");if(t)throw new he.ErrnoError(t);he.currentPath=r.path},createDefaultDirectories:function(){he.mkdir("/tmp"),he.mkdir("/home"),he.mkdir("/home/<USER>")},createDefaultDevices:function(){he.mkdir("/dev"),he.registerDevice(he.makedev(1,3),{read:function(){return 0},write:function(e,r,t,n,o){return n}}),he.mkdev("/dev/null",he.makedev(1,3)),le.register(he.makedev(5,0),le.default_tty_ops),le.register(he.makedev(6,0),le.default_tty1_ops),he.mkdev("/dev/tty",he.makedev(5,0)),he.mkdev("/dev/tty1",he.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return function(){return crypto.getRandomValues(e),e[0]}}if(m)try{var r=a.default;return function(){return r.randomBytes(1)[0]}}catch(e){}return function(){ee("randomDevice")}}();he.createDevice("/dev","random",e),he.createDevice("/dev","urandom",e),he.mkdir("/dev/shm"),he.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){he.mkdir("/proc");var e=he.mkdir("/proc/self");he.mkdir("/proc/self/fd"),he.mount({mount:function(){var r=he.createNode(e,"fd",16895,73);return r.node_ops={lookup:function(e,r){var t=+r,n=he.getStream(t);if(!n)throw new he.ErrnoError(8);var o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return o.parent=o,o}},r}},{},"/proc/self/fd")},createStandardStreams:function(){t.stdin?he.createDevice("/dev","stdin",t.stdin):he.symlink("/dev/tty","/dev/stdin"),t.stdout?he.createDevice("/dev","stdout",null,t.stdout):he.symlink("/dev/tty","/dev/stdout"),t.stderr?he.createDevice("/dev","stderr",null,t.stderr):he.symlink("/dev/tty1","/dev/stderr"),he.open("/dev/stdin",0),he.open("/dev/stdout",1),he.open("/dev/stderr",1)},ensureErrnoError:function(){he.ErrnoError||(he.ErrnoError=function(e,r){this.node=r,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},he.ErrnoError.prototype=new Error,he.ErrnoError.prototype.constructor=he.ErrnoError,[44].forEach((function(e){he.genericErrors[e]=new he.ErrnoError(e),he.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:function(){he.ensureErrnoError(),he.nameTable=new Array(4096),he.mount(pe,{},"/"),he.createDefaultDirectories(),he.createDefaultDevices(),he.createSpecialDirectories(),he.filesystems={MEMFS:pe}},init:function(e,r,n){he.init.initialized=!0,he.ensureErrnoError(),t.stdin=e||t.stdin,t.stdout=r||t.stdout,t.stderr=n||t.stderr,he.createStandardStreams()},quit:function(){he.init.initialized=!1;var e=t._fflush;e&&e(0);for(var r=0;r<he.streams.length;r++){var n=he.streams[r];n&&he.close(n)}},getMode:function(e,r){var t=0;return e&&(t|=365),r&&(t|=146),t},findObject:function(e,r){var t=he.analyzePath(e,r);return t.exists?t.object:null},analyzePath:function(e,r){try{e=(n=he.lookupPath(e,{follow:!r})).path}catch(e){}var t={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=he.lookupPath(e,{parent:!0});t.parentExists=!0,t.parentPath=n.path,t.parentObject=n.node,t.name=ce.basename(e),n=he.lookupPath(e,{follow:!r}),t.exists=!0,t.path=n.path,t.object=n.node,t.name=n.node.name,t.isRoot="/"===n.path}catch(e){t.error=e.errno}return t},createPath:function(e,r,t,n){e="string"==typeof e?e:he.getPath(e);for(var o=r.split("/").reverse();o.length;){var i=o.pop();if(i){var a=ce.join2(e,i);try{he.mkdir(a)}catch(e){}e=a}}return a},createFile:function(e,r,t,n,o){var i=ce.join2("string"==typeof e?e:he.getPath(e),r),a=he.getMode(n,o);return he.create(i,a)},createDataFile:function(e,r,t,n,o,i){var a=r?ce.join2("string"==typeof e?e:he.getPath(e),r):e,s=he.getMode(n,o),u=he.create(a,s);if(t){if("string"==typeof t){for(var c=new Array(t.length),f=0,l=t.length;f<l;++f)c[f]=t.charCodeAt(f);t=c}he.chmod(u,146|s);var d=he.open(u,577);he.write(d,t,0,t.length,0,i),he.close(d),he.chmod(u,s)}return u},createDevice:function(e,r,t,n){var o=ce.join2("string"==typeof e?e:he.getPath(e),r),i=he.getMode(!!t,!!n);he.createDevice.major||(he.createDevice.major=64);var a=he.makedev(he.createDevice.major++,0);return he.registerDevice(a,{open:function(e){e.seekable=!1},close:function(e){n&&n.buffer&&n.buffer.length&&n(10)},read:function(e,r,n,o,i){for(var a=0,s=0;s<o;s++){var u;try{u=t()}catch(e){throw new he.ErrnoError(29)}if(void 0===u&&0===a)throw new he.ErrnoError(6);if(null==u)break;a++,r[n+s]=u}return a&&(e.node.timestamp=Date.now()),a},write:function(e,r,t,o,i){for(var a=0;a<o;a++)try{n(r[t+a])}catch(e){throw new he.ErrnoError(29)}return o&&(e.node.timestamp=Date.now()),a}}),he.mkdev(o,i,a)},forceLoadFile:function(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!s)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=Hr(s(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new he.ErrnoError(29)}},createLazyFile:function(e,r,t,n,o){function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(e){if(!(e>this.length-1||e<0)){var r=e%this.chunkSize,t=e/this.chunkSize|0;return this.getter(t)[r]}},i.prototype.setDataGetter=function(e){this.getter=e},i.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",t,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+t+". Status: "+e.status);var r,n=Number(e.getResponseHeader("Content-length")),o=(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r,i=(r=e.getResponseHeader("Content-Encoding"))&&"gzip"===r,a=1048576;o||(a=n);var s=this;s.setDataGetter((function(e){var r=e*a,o=(e+1)*a-1;if(o=Math.min(o,n-1),void 0===s.chunks[e]&&(s.chunks[e]=function(e,r){if(e>r)throw new Error("invalid range ("+e+", "+r+") or no bytes requested!");if(r>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",t,!1),n!==a&&o.setRequestHeader("Range","bytes="+e+"-"+r),"undefined"!=typeof Uint8Array&&(o.responseType="arraybuffer"),o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+t+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):Hr(o.responseText||"",!0)}(r,o)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!i&&n||(a=n=1,n=this.getter(0).length,a=n,w("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=a,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!h)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new i;Object.defineProperties(a,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var s={isDevice:!1,contents:a}}else s={isDevice:!1,url:t};var u=he.createFile(e,r,s,n,o);s.contents?u.contents=s.contents:s.url&&(u.contents=null,u.url=s.url),Object.defineProperties(u,{usedBytes:{get:function(){return this.contents.length}}});var c={};return Object.keys(u.stream_ops).forEach((function(e){var r=u.stream_ops[e];c[e]=function(){return he.forceLoadFile(u),r.apply(null,arguments)}})),c.read=function(e,r,t,n,o){he.forceLoadFile(u);var i=e.node.contents;if(o>=i.length)return 0;var a=Math.min(i.length-o,n);if(i.slice)for(var s=0;s<a;s++)r[t+s]=i[o+s];else for(s=0;s<a;s++)r[t+s]=i.get(o+s);return a},u.stream_ops=c,u},createPreloadedFile:function(e,r,n,o,i,a,s,c,f,l){Browser.init();var d=r?fe.resolve(ce.join2(e,r)):e;function p(n){function u(t){l&&l(),c||he.createDataFile(e,r,t,o,i,f),a&&a(),Z()}var p=!1;t.preloadPlugins.forEach((function(e){p||e.canHandle(d)&&(e.handle(n,d,u,(function(){s&&s(),Z()})),p=!0)})),p||u(n)}Q(),"string"==typeof n?function(e,r,t,n){var o=n?"":"al "+e;u(e,(function(t){k(t,'Loading data file "'+e+'" failed (no arrayBuffer).'),r(new Uint8Array(t)),o&&Z()}),(function(r){if(!t)throw'Loading data file "'+e+'" failed.';t()})),o&&Q()}(n,(function(e){p(e)}),s):p(n)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(e,r,t){r=r||function(){},t=t||function(){};var n=he.indexedDB();try{var o=n.open(he.DB_NAME(),he.DB_VERSION)}catch(e){return t(e)}o.onupgradeneeded=function(){w("creating db"),o.result.createObjectStore(he.DB_STORE_NAME)},o.onsuccess=function(){var n=o.result.transaction([he.DB_STORE_NAME],"readwrite"),i=n.objectStore(he.DB_STORE_NAME),a=0,s=0,u=e.length;function c(){0==s?r():t()}e.forEach((function(e){var r=i.put(he.analyzePath(e).object.contents,e);r.onsuccess=function(){++a+s==u&&c()},r.onerror=function(){s++,a+s==u&&c()}})),n.onerror=t},o.onerror=t},loadFilesFromDB:function(e,r,t){r=r||function(){},t=t||function(){};var n=he.indexedDB();try{var o=n.open(he.DB_NAME(),he.DB_VERSION)}catch(e){return t(e)}o.onupgradeneeded=t,o.onsuccess=function(){var n=o.result;try{var i=n.transaction([he.DB_STORE_NAME],"readonly")}catch(e){return void t(e)}var a=i.objectStore(he.DB_STORE_NAME),s=0,u=0,c=e.length;function f(){0==u?r():t()}e.forEach((function(e){var r=a.get(e);r.onsuccess=function(){he.analyzePath(e).exists&&he.unlink(e),he.createDataFile(ce.dirname(e),ce.basename(e),r.result,!0,!0,!0),++s+u==c&&f()},r.onerror=function(){u++,s+u==c&&f()}})),i.onerror=t},o.onerror=t}},me={mappings:{},DEFAULT_POLLMASK:5,umask:511,calculateAt:function(e,r,t){if("/"===r[0])return r;var n;if(-100===e)n=he.cwd();else{var o=he.getStream(e);if(!o)throw new he.ErrnoError(8);n=o.path}if(0==r.length){if(!t)throw new he.ErrnoError(44);return n}return ce.join2(n,r)},doStat:function(e,r,t){try{var n=e(r)}catch(e){if(e&&e.node&&ce.normalize(r)!==ce.normalize(he.getPath(e.node)))return-54;throw e}return O[t>>2]=n.dev,O[t+4>>2]=0,O[t+8>>2]=n.ino,O[t+12>>2]=n.mode,O[t+16>>2]=n.nlink,O[t+20>>2]=n.uid,O[t+24>>2]=n.gid,O[t+28>>2]=n.rdev,O[t+32>>2]=0,ne=[n.size>>>0,(te=n.size,+Math.abs(te)>=1?te>0?(0|Math.min(+Math.floor(te/4294967296),4294967295))>>>0:~~+Math.ceil((te-+(~~te>>>0))/4294967296)>>>0:0)],O[t+40>>2]=ne[0],O[t+44>>2]=ne[1],O[t+48>>2]=4096,O[t+52>>2]=n.blocks,O[t+56>>2]=n.atime.getTime()/1e3|0,O[t+60>>2]=0,O[t+64>>2]=n.mtime.getTime()/1e3|0,O[t+68>>2]=0,O[t+72>>2]=n.ctime.getTime()/1e3|0,O[t+76>>2]=0,ne=[n.ino>>>0,(te=n.ino,+Math.abs(te)>=1?te>0?(0|Math.min(+Math.floor(te/4294967296),4294967295))>>>0:~~+Math.ceil((te-+(~~te>>>0))/4294967296)>>>0:0)],O[t+80>>2]=ne[0],O[t+84>>2]=ne[1],0},doMsync:function(e,r,t,n,o){var i=$.slice(e,e+t);he.msync(r,i,o,t,n)},doMkdir:function(e,r){return"/"===(e=ce.normalize(e))[e.length-1]&&(e=e.substr(0,e.length-1)),he.mkdir(e,r,0),0},doMknod:function(e,r,t){switch(61440&r){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-28}return he.mknod(e,r,t),0},doReadlink:function(e,r,t){if(t<=0)return-28;var n=he.readlink(e),o=Math.min(t,S(n)),i=x[r+o];return D(n,r,t+1),x[r+o]=i,o},doAccess:function(e,r){if(-8&r)return-28;var t;if(!(t=he.lookupPath(e,{follow:!0}).node))return-44;var n="";return 4&r&&(n+="r"),2&r&&(n+="w"),1&r&&(n+="x"),n&&he.nodePermissions(t,n)?-2:0},doDup:function(e,r,t){var n=he.getStream(t);return n&&he.close(n),he.open(e,r,0,t,t).fd},doReadv:function(e,r,t,n){for(var o=0,i=0;i<t;i++){var a=O[r+8*i>>2],s=O[r+(8*i+4)>>2],u=he.read(e,x,a,s,n);if(u<0)return-1;if(o+=u,u<s)break}return o},doWritev:function(e,r,t,n){for(var o=0,i=0;i<t;i++){var a=O[r+8*i>>2],s=O[r+(8*i+4)>>2],u=he.write(e,x,a,s,n);if(u<0)return-1;o+=u}return o},varargs:void 0,get:function(){return me.varargs+=4,O[me.varargs-4>>2]},getStr:function(e){return C(e)},getStreamFromFD:function(e){var r=he.getStream(e);if(!r)throw new he.ErrnoError(8);return r},get64:function(e,r){return e}};function ve(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var ye=void 0;function ge(e){for(var r="",t=e;$[t];)r+=ye[$[t++]];return r}var we={},Ee={},be={};function _e(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=48&&r<=57?"_"+e:e}function ke(e,r){return e=_e(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function Te(e,r){var t=ke(r,(function(e){this.name=r,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var Pe=void 0;function Ce(e){throw new Pe(e)}var Ae=void 0;function De(e){throw new Ae(e)}function Se(e,r,t){function n(r){var n=t(r);n.length!==e.length&&De("Mismatched type converter count");for(var o=0;o<e.length;++o)Fe(e[o],n[o])}e.forEach((function(e){be[e]=r}));var o=new Array(r.length),i=[],a=0;r.forEach((function(e,r){Ee.hasOwnProperty(e)?o[r]=Ee[e]:(i.push(e),we.hasOwnProperty(e)||(we[e]=[]),we[e].push((function(){o[r]=Ee[e],++a===i.length&&n(o)})))})),0===i.length&&n(o)}function Fe(e,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=r.name;if(e||Ce('type "'+n+'" must have a positive integer typeid pointer'),Ee.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;Ce("Cannot register type '"+n+"' twice")}if(Ee[e]=r,delete be[e],we.hasOwnProperty(e)){var o=we[e];delete we[e],o.forEach((function(e){e()}))}}function xe(e){if(!(this instanceof He))return!1;if(!(e instanceof He))return!1;for(var r=this.$$.ptrType.registeredClass,t=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return r===n&&t===o}function $e(e){Ce(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Re=!1;function Me(e){}function Oe(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function Ie(e){return"undefined"==typeof FinalizationGroup?(Ie=function(e){return e},e):(Re=new FinalizationGroup((function(e){for(var r=e.next();!r.done;r=e.next()){var t=r.value;t.ptr?Oe(t):console.warn("object already deleted: "+t.ptr)}})),Ie=function(e){return Re.register(e,e.$$,e.$$),e},Me=function(e){Re.unregister(e.$$)},Ie(e))}function je(){if(this.$$.ptr||$e(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,r=Ie(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return r.$$.count.value+=1,r.$$.deleteScheduled=!1,r}function Ue(){this.$$.ptr||$e(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Ce("Object already scheduled for deletion"),Me(this),Oe(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Ne(){return!this.$$.ptr}var Be=void 0,Le=[];function We(){for(;Le.length;){var e=Le.pop();e.$$.deleteScheduled=!1,e.delete()}}function ze(){return this.$$.ptr||$e(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Ce("Object already scheduled for deletion"),Le.push(this),1===Le.length&&Be&&Be(We),this.$$.deleteScheduled=!0,this}function He(){}var Ve={};function Xe(e,r,t){if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||Ce("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}}function Ge(e,r,t,n,o,i,a,s){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}function qe(e,r,t){for(;r!==t;)r.upcast||Ce("Expected null or instance of "+t.name+", got an instance of "+r.name),e=r.upcast(e),r=r.baseClass;return e}function Ye(e,r){if(null===r)return this.isReference&&Ce("null is not a valid "+this.name),0;r.$$||Ce('Cannot pass "'+Ar(r)+'" as a '+this.name),r.$$.ptr||Ce("Cannot pass deleted object as a pointer of type "+this.name);var t=r.$$.ptrType.registeredClass;return qe(r.$$.ptr,t,this.registeredClass)}function Ke(e,r){var t;if(null===r)return this.isReference&&Ce("null is not a valid "+this.name),this.isSmartPointer?(t=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,t),t):0;r.$$||Ce('Cannot pass "'+Ar(r)+'" as a '+this.name),r.$$.ptr||Ce("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&r.$$.ptrType.isConst&&Ce("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);var n=r.$$.ptrType.registeredClass;if(t=qe(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&Ce("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?t=r.$$.smartPtr:Ce("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:t=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)t=r.$$.smartPtr;else{var o=r.clone();t=this.rawShare(t,Cr((function(){o.delete()}))),null!==e&&e.push(this.rawDestructor,t)}break;default:Ce("Unsupporting sharing policy")}return t}function Je(e,r){if(null===r)return this.isReference&&Ce("null is not a valid "+this.name),0;r.$$||Ce('Cannot pass "'+Ar(r)+'" as a '+this.name),r.$$.ptr||Ce("Cannot pass deleted object as a pointer of type "+this.name),r.$$.ptrType.isConst&&Ce("Cannot convert argument of type "+r.$$.ptrType.name+" to parameter type "+this.name);var t=r.$$.ptrType.registeredClass;return qe(r.$$.ptr,t,this.registeredClass)}function Qe(e){return this.fromWireType(I[e>>2])}function Ze(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function er(e){this.rawDestructor&&this.rawDestructor(e)}function rr(e){null!==e&&e.delete()}function tr(e,r,t){if(r===t)return e;if(void 0===t.baseClass)return null;var n=tr(e,r,t.baseClass);return null===n?null:t.downcast(n)}function nr(){return Object.keys(ar).length}function or(){var e=[];for(var r in ar)ar.hasOwnProperty(r)&&e.push(ar[r]);return e}function ir(e){Be=e,Le.length&&Be&&Be(We)}var ar={};function sr(e,r){return r=function(e,r){for(void 0===r&&Ce("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r}(e,r),ar[r]}function ur(e,r){return r.ptrType&&r.ptr||De("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!==!!r.smartPtr&&De("Both smartPtrType and smartPtr must be specified"),r.count={value:1},Ie(Object.create(e,{$$:{value:r}}))}function cr(e){var r=this.getPointee(e);if(!r)return this.destructor(e),null;var t=sr(this.registeredClass,r);if(void 0!==t){if(0===t.$$.count.value)return t.$$.ptr=r,t.$$.smartPtr=e,t.clone();var n=t.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?ur(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):ur(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var i,a=this.registeredClass.getActualType(r),s=Ve[a];if(!s)return o.call(this);i=this.isConst?s.constPointerType:s.pointerType;var u=tr(r,this.registeredClass,i.registeredClass);return null===u?o.call(this):this.isSmartPointer?ur(i.registeredClass.instancePrototype,{ptrType:i,ptr:u,smartPtrType:this,smartPtr:e}):ur(i.registeredClass.instancePrototype,{ptrType:i,ptr:u})}function fr(e,r,t,n,o,i,a,s,u,c,f){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=f,o||void 0!==r.baseClass?this.toWireType=Ke:n?(this.toWireType=Ye,this.destructorFunction=null):(this.toWireType=Je,this.destructorFunction=null)}function lr(e,r,n){return e.includes("j")?function(e,r,n){var o=t["dynCall_"+e];return n&&n.length?o.apply(null,[r].concat(n)):o.call(null,r)}(e,r,n):N.get(r).apply(null,n)}function dr(e,r){var t,n,o,i=(e=ge(e)).includes("j")?(t=e,n=r,o=[],function(){o.length=arguments.length;for(var e=0;e<arguments.length;e++)o[e]=arguments[e];return lr(t,n,o)}):N.get(r);return"function"!=typeof i&&Ce("unknown function pointer with signature "+e+": "+r),i}var pr=void 0;function hr(e){var r=Kr(e),t=ge(r);return Xr(r),t}function mr(e,r){var t=[],n={};throw r.forEach((function e(r){n[r]||Ee[r]||(be[r]?be[r].forEach(e):(t.push(r),n[r]=!0))})),new pr(e+": "+t.map(hr).join([", "]))}function vr(e,r){for(var t=[],n=0;n<e;n++)t.push(O[(r>>2)+n]);return t}function yr(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function gr(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var t=ke(e.name||"unknownFunctionName",(function(){}));t.prototype=e.prototype;var n=new t,o=e.apply(n,r);return o instanceof Object?o:n}function wr(e,r,t,n,o){var i=r.length;i<2&&Ce("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==r[1]&&null!==t,s=!1,u=1;u<r.length;++u)if(null!==r[u]&&void 0===r[u].destructorFunction){s=!0;break}var c="void"!==r[0].name,f="",l="";for(u=0;u<i-2;++u)f+=(0!==u?", ":"")+"arg"+u,l+=(0!==u?", ":"")+"arg"+u+"Wired";var d="return function "+_e(e)+"("+f+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";s&&(d+="var destructors = [];\n");var p=s?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[Ce,n,o,yr,r[0],r[1]];a&&(d+="var thisWired = classParam.toWireType("+p+", this);\n");for(u=0;u<i-2;++u)d+="var arg"+u+"Wired = argType"+u+".toWireType("+p+", arg"+u+"); // "+r[u+2].name+"\n",h.push("argType"+u),m.push(r[u+2]);if(a&&(l="thisWired"+(l.length>0?", ":"")+l),d+=(c?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",s)d+="runDestructors(destructors);\n";else for(u=a?1:2;u<r.length;++u){var v=1===u?"thisWired":"arg"+(u-2)+"Wired";null!==r[u].destructorFunction&&(d+=v+"_dtor("+v+"); // "+r[u].name+"\n",h.push(v+"_dtor"),m.push(r[u].destructorFunction))}return c&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),d+="}\n",h.push(d),gr(Function,h).apply(null,m)}function Er(e,r,t){return e instanceof Object||Ce(t+' with invalid "this": '+e),e instanceof r.registeredClass.constructor||Ce(t+' incompatible with "this" of type '+e.constructor.name),e.$$.ptr||Ce("cannot call emscripten binding method "+t+" on deleted object"),qe(e.$$.ptr,e.$$.ptrType.registeredClass,r.registeredClass)}var br=[],_r=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function kr(e){e>4&&0==--_r[e].refcount&&(_r[e]=void 0,br.push(e))}function Tr(){for(var e=0,r=5;r<_r.length;++r)void 0!==_r[r]&&++e;return e}function Pr(){for(var e=5;e<_r.length;++e)if(void 0!==_r[e])return _r[e];return null}function Cr(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=br.length?br.pop():_r.length;return _r[r]={refcount:1,value:e},r}}function Ar(e){if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e}function Dr(e,r){switch(r){case 2:return function(e){return this.fromWireType(j[e>>2])};case 3:return function(e){return this.fromWireType(U[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Sr(e,r,t){switch(r){case 0:return t?function(e){return x[e]}:function(e){return $[e]};case 1:return t?function(e){return R[e>>1]}:function(e){return M[e>>1]};case 2:return t?function(e){return O[e>>2]}:function(e){return I[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function Fr(e){return e||Ce("Cannot use deleted val. handle = "+e),_r[e].value}function xr(e,r){var t=Ee[e];return void 0===t&&Ce(r+" has unknown type "+hr(e)),t}var $r={};var Rr=[];function Mr(e,r){return(e>>>0)+4294967296*r}function Or(e,r){if(e<=0)return e;var t=r<=32?Math.abs(1<<r-1):Math.pow(2,r-1);return e>=t&&(r<=32||e>t)&&(e=-2*t+e),e}function Ir(e,r){return e>=0?e:r<=32?2*Math.abs(1<<r-1)+e:Math.pow(2,r)+e}function jr(e){if(!e||!e.callee||!e.callee.name)return[null,"",""];e.callee.toString();var r=e.callee.name,t="(",n=!0;for(var o in e){var i=e[o];n||(t+=", "),n=!1,t+="number"==typeof i||"string"==typeof i?i:"("+typeof i+")"}t+=")";var a=e.callee.caller;return n&&(t=""),[e=a?a.arguments:[],r,t]}function Ur(e,r){24&e&&(r=r.replace(/\s+$/,""),r+=(r.length>0?"\n":"")+function(e){var r=ue(),t=r.lastIndexOf("_emscripten_log"),n=r.lastIndexOf("_emscripten_get_callstack"),o=r.indexOf("\n",Math.max(t,n))+1;r=r.slice(o),32&e&&b("EM_LOG_DEMANGLE is deprecated; ignoring"),8&e&&"undefined"==typeof emscripten_source_map&&(b('Source map information is not available, emscripten_log with EM_LOG_C_STACK will be ignored. Build with "--pre-js $EMSCRIPTEN/src/emscripten-source-map.min.js" linker flag to add source map loading to code.'),e^=8,e|=16);var i=null;if(128&e)for(i=jr(arguments);i[1].includes("_emscripten_");)i=jr(i[0]);var a=r.split("\n");r="";var s=new RegExp("\\s*(.*?)@(.*?):([0-9]+):([0-9]+)"),u=new RegExp("\\s*(.*?)@(.*):(.*)(:(.*))?"),c=new RegExp("\\s*at (.*?) \\((.*):(.*):(.*)\\)");for(var f in a){var l=a[f],d="",p="",h=0,m=0,v=c.exec(l);if(v&&5==v.length)d=v[1],p=v[2],h=v[3],m=v[4];else{if((v=s.exec(l))||(v=u.exec(l)),!(v&&v.length>=4)){r+=l+"\n";continue}d=v[1],p=v[2],h=v[3],m=0|v[4]}var y=!1;if(8&e){var g=emscripten_source_map.originalPositionFor({line:h,column:m});(y=g&&g.source)&&(64&e&&(g.source=g.source.substring(g.source.replace(/\\/g,"/").lastIndexOf("/")+1)),r+="    at "+d+" ("+g.source+":"+g.line+":"+g.column+")\n")}(16&e||!y)&&(64&e&&(p=p.substring(p.replace(/\\/g,"/").lastIndexOf("/")+1)),r+=(y?"     = "+d:"    at "+d)+" ("+p+":"+h+":"+m+")\n"),128&e&&i[0]&&(i[1]==d&&i[2].length>0&&(r=r.replace(/\s+$/,""),r+=" with values: "+i[1]+i[2]+"\n"),i=jr(i[0]))}return r.replace(/\s+$/,"")}(e)),1&e?4&e?E(r):2&e?console.warn(r):512&e?console.info(r):256&e?console.debug(r):w(r):6&e?E(r):w(r)}var Nr={};function Br(){if(!Br.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:d||"./this.program"};for(var r in Nr)void 0===Nr[r]?delete e[r]:e[r]=Nr[r];var t=[];for(var r in e)t.push(r+"="+e[r]);Br.strings=t}return Br.strings}var Lr=function(e,r,t,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=he.nextInode++,this.name=r,this.mode=t,this.node_ops={},this.stream_ops={},this.rdev=n},Wr=365,zr=146;function Hr(e,r,t){var n=t>0?t:S(e)+1,o=new Array(n),i=A(e,o,0,o.length);return r&&(o.length=i),o}Object.defineProperties(Lr.prototype,{read:{get:function(){return(this.mode&Wr)===Wr},set:function(e){e?this.mode|=Wr:this.mode&=-366}},write:{get:function(){return(this.mode&zr)===zr},set:function(e){e?this.mode|=zr:this.mode&=-147}},isFolder:{get:function(){return he.isDir(this.mode)}},isDevice:{get:function(){return he.isChrdev(this.mode)}}}),he.FSNode=Lr,he.staticInit(),function(){for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);ye=e}(),Pe=t.BindingError=Te(Error,"BindingError"),Ae=t.InternalError=Te(Error,"InternalError"),He.prototype.isAliasOf=xe,He.prototype.clone=je,He.prototype.delete=Ue,He.prototype.isDeleted=Ne,He.prototype.deleteLater=ze,fr.prototype.getPointee=Ze,fr.prototype.destructor=er,fr.prototype.argPackAdvance=8,fr.prototype.readValueFromPointer=Qe,fr.prototype.deleteObject=rr,fr.prototype.fromWireType=cr,t.getInheritedInstanceCount=nr,t.getLiveInheritedInstances=or,t.flushPendingDeletes=We,t.setDelayFunction=ir,pr=t.UnboundTypeError=Te(Error,"UnboundTypeError"),t.count_emval_handles=Tr,t.get_first_emval=Pr;var Vr={y:function(e,r,t){me.varargs=t;try{var n=me.getStreamFromFD(e);switch(r){case 0:return(o=me.get())<0?-28:he.open(n.path,n.flags,0,o).fd;case 1:case 2:case 13:case 14:return 0;case 3:return n.flags;case 4:var o=me.get();return n.flags|=o,0;case 12:o=me.get();return R[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return i=28,O[qr()>>2]=i,-1}}catch(e){return void 0!==he&&e instanceof he.ErrnoError||ee(e),-e.errno}var i},x:function(e,r,t){me.varargs=t;try{var n=me.getStr(e),o=t?me.get():0;return he.open(n,r,o).fd}catch(e){return void 0!==he&&e instanceof he.ErrnoError||ee(e),-e.errno}},s:function(e,r,t,n,o){},D:function(e,r,t,n,o){var i=ve(t);Fe(e,{name:r=ge(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?n:o},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===t)n=x;else if(2===t)n=R;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);n=O}return this.fromWireType(n[e>>i])},destructorFunction:null})},n:function(e,r,n,o,i,a,s,u,c,f,l,d,p){l=ge(l),a=dr(i,a),u&&(u=dr(s,u)),f&&(f=dr(c,f)),p=dr(d,p);var h=_e(l);!function(e,r,n){t.hasOwnProperty(e)?((void 0===n||void 0!==t[e].overloadTable&&void 0!==t[e].overloadTable[n])&&Ce("Cannot register public name '"+e+"' twice"),Xe(t,e,e),t.hasOwnProperty(n)&&Ce("Cannot register multiple overloads of a function with the same number of arguments ("+n+")!"),t[e].overloadTable[n]=r):(t[e]=r,void 0!==n&&(t[e].numArguments=n))}(h,(function(){mr("Cannot construct "+l+" due to unbound types",[o])})),Se([e,r,n],o?[o]:[],(function(r){var n,i;r=r[0],i=o?(n=r.registeredClass).instancePrototype:He.prototype;var s=ke(h,(function(){if(Object.getPrototypeOf(this)!==c)throw new Pe("Use 'new' to construct "+l);if(void 0===d.constructor_body)throw new Pe(l+" has no accessible constructor");var e=d.constructor_body[arguments.length];if(void 0===e)throw new Pe("Tried to invoke ctor of "+l+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(d.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),c=Object.create(i,{constructor:{value:s}});s.prototype=c;var d=new Ge(l,s,c,p,n,a,u,f),m=new fr(l,d,!0,!1,!1),v=new fr(l+"*",d,!1,!1,!1),y=new fr(l+" const*",d,!1,!0,!1);return Ve[e]={pointerType:v,constPointerType:y},function(e,r,n){t.hasOwnProperty(e)||De("Replacing nonexistant public symbol"),void 0!==t[e].overloadTable&&void 0!==n?t[e].overloadTable[n]=r:(t[e]=r,t[e].argCount=n)}(h,s),[m,v,y]}))},i:function(e,r,t,n,o,i){k(r>0);var a=vr(r,t);o=dr(n,o),Se([],[e],(function(e){var t="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[r-1])throw new Pe("Cannot register multiple constructors with identical number of parameters ("+(r-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[r-1]=function(){mr("Cannot construct "+e.name+" due to unbound types",a)},Se([],a,(function(n){return n.splice(1,0,null),e.registeredClass.constructor_body[r-1]=wr(t,n,null,o,i),[]})),[]}))},f:function(e,r,t,n,o,i,a,s){var u=vr(t,n);r=ge(r),i=dr(o,i),Se([],[e],(function(e){var n=(e=e[0]).name+"."+r;function o(){mr("Cannot call "+n+" due to unbound types",u)}r.startsWith("@@")&&(r=Symbol[r.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(r);var c=e.registeredClass.instancePrototype,f=c[r];return void 0===f||void 0===f.overloadTable&&f.className!==e.name&&f.argCount===t-2?(o.argCount=t-2,o.className=e.name,c[r]=o):(Xe(c,r,n),c[r].overloadTable[t-2]=o),Se([],u,(function(o){var s=wr(n,o,e,i,a);return void 0===c[r].overloadTable?(s.argCount=t-2,c[r]=s):c[r].overloadTable[t-2]=s,[]})),[]}))},I:function(e,r,t,n,o,i,a,s,u,c){r=ge(r),o=dr(n,o),Se([],[e],(function(e){var n=(e=e[0]).name+"."+r,f={get:function(){mr("Cannot access "+n+" due to unbound types",[t,a])},enumerable:!0,configurable:!0};return f.set=u?function(){mr("Cannot access "+n+" due to unbound types",[t,a])}:function(e){Ce(n+" is a read-only property")},Object.defineProperty(e.registeredClass.instancePrototype,r,f),Se([],u?[t,a]:[t],(function(t){var a=t[0],f={get:function(){var r=Er(this,e,n+" getter");return a.fromWireType(o(i,r))},enumerable:!0};if(u){u=dr(s,u);var l=t[1];f.set=function(r){var t=Er(this,e,n+" setter"),o=[];u(c,t,l.toWireType(o,r)),yr(o)}}return Object.defineProperty(e.registeredClass.instancePrototype,r,f),[]})),[]}))},C:function(e,r){Fe(e,{name:r=ge(r),fromWireType:function(e){var r=_r[e].value;return kr(e),r},toWireType:function(e,r){return Cr(r)},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:null})},l:function(e,r,t){var n=ve(t);Fe(e,{name:r=ge(r),fromWireType:function(e){return e},toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Ar(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Dr(r,n),destructorFunction:null})},c:function(e,r,t,n,o){r=ge(r),-1===o&&(o=4294967295);var i=ve(t),a=function(e){return e};if(0===n){var s=32-8*t;a=function(e){return e<<s>>>s}}var u=r.includes("unsigned");Fe(e,{name:r,fromWireType:a,toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+Ar(t)+'" to '+this.name);if(t<n||t>o)throw new TypeError('Passing a number "'+Ar(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+n+", "+o+"]!");return u?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:Sr(r,i,0!==n),destructorFunction:null})},b:function(e,r,t){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(e){var r=I,t=r[e>>=2],o=r[e+1];return new n(F,o,t)}Fe(e,{name:t=ge(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},m:function(e,r){var t="std::string"===(r=ge(r));Fe(e,{name:r,fromWireType:function(e){var r,n=I[e>>2];if(t)for(var o=e+4,i=0;i<=n;++i){var a=e+4+i;if(i==n||0==$[a]){var s=C(o,a-o);void 0===r?r=s:(r+=String.fromCharCode(0),r+=s),o=a+1}}else{var u=new Array(n);for(i=0;i<n;++i)u[i]=String.fromCharCode($[e+4+i]);r=u.join("")}return Xr(e),r},toWireType:function(e,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var n="string"==typeof r;n||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||Ce("Cannot pass non-string to std::string");var o=(t&&n?function(){return S(r)}:function(){return r.length})(),i=Gr(4+o+1);if(I[i>>2]=o,t&&n)D(r,i+4,o+1);else if(n)for(var a=0;a<o;++a){var s=r.charCodeAt(a);s>255&&(Xr(i),Ce("String has UTF-16 code units that do not fit in 8 bits")),$[i+4+a]=s}else for(a=0;a<o;++a)$[i+4+a]=r[a];return null!==e&&e.push(Xr,i),i},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:function(e){Xr(e)}})},h:function(e,r,t){var n,o,i,a,s;t=ge(t),2===r?(n=L,o=W,a=z,i=function(){return M},s=1):4===r&&(n=H,o=V,a=X,i=function(){return I},s=2),Fe(e,{name:t,fromWireType:function(e){for(var t,o=I[e>>2],a=i(),u=e+4,c=0;c<=o;++c){var f=e+4+c*r;if(c==o||0==a[f>>s]){var l=n(u,f-u);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),u=f+r}}return Xr(e),t},toWireType:function(e,n){"string"!=typeof n&&Ce("Cannot pass non-string to C++ string type "+t);var i=a(n),u=Gr(4+i+r);return I[u>>2]=i>>s,o(n,u+4,i+r),null!==e&&e.push(Xr,u),u},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:function(e){Xr(e)}})},E:function(e,r){Fe(e,{isVoid:!0,name:r=ge(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})},v:function(e,r,t){e=Fr(e),r=xr(r,"emval::as");var n=[],o=Cr(n);return O[t>>2]=o,r.toWireType(n,e)},e:function(e,r,t,n){var o,i;(e=Rr[e])(r=Fr(r),t=void 0===(i=$r[o=t])?ge(o):i,null,n)},p:kr,d:function(e,r){for(var t=function(e,r){for(var t=new Array(e),n=0;n<e;++n)t[n]=xr(O[(r>>2)+n],"parameter "+n);return t}(e,r),n=t[0],o=n.name+"_$"+t.slice(1).map((function(e){return e.name})).join("_")+"$",i=["retType"],a=[n],s="",u=0;u<e-1;++u)s+=(0!==u?", ":"")+"arg"+u,i.push("argType"+u),a.push(t[1+u]);var c="return function "+_e("methodCaller_"+o)+"(handle, name, destructors, args) {\n",f=0;for(u=0;u<e-1;++u)c+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(f?"+"+f:"")+");\n",f+=t[u+1].argPackAdvance;for(c+="    var rv = handle[name]("+s+");\n",u=0;u<e-1;++u)t[u+1].deleteObject&&(c+="    argType"+u+".deleteObject(arg"+u+");\n");n.isVoid||(c+="    return retType.toWireType(destructors, rv);\n"),c+="};\n",i.push(c);var l,d,p=gr(Function,i).apply(null,a);return l=p,d=Rr.length,Rr.push(l),d},o:function(e){e>4&&(_r[e].refcount+=1)},q:function(e){yr(_r[e].value),kr(e)},B:function(e,r){return Cr((e=xr(e,"_emval_take_value")).readValueFromPointer(r))},a:function(){ee()},G:function e(){return void 0===e.start&&(e.start=Date.now()),1e3*(Date.now()-e.start)|0},H:function(e,r,t){var n=function(e,r){var t=e,n=r;function o(e){var r;return n=function(e,r){return"double"!==r&&"i64"!==r||7&e&&(e+=4),e}(n,e),"double"===e?(r=U[n>>3],n+=8):"i64"==e?(r=[O[n>>2],O[n+4>>2]],n+=8):(e="i32",r=O[n>>2],n+=4),r}for(var i,a,s,u,c=[];;){var f=t;if(0===(i=x[t>>0]))break;if(a=x[t+1>>0],37==i){var l=!1,d=!1,p=!1,h=!1,m=!1;e:for(;;){switch(a){case 43:l=!0;break;case 45:d=!0;break;case 35:p=!0;break;case 48:if(h)break e;h=!0;break;case 32:m=!0;break;default:break e}t++,a=x[t+1>>0]}var v=0;if(42==a)v=o("i32"),t++,a=x[t+1>>0];else for(;a>=48&&a<=57;)v=10*v+(a-48),t++,a=x[t+1>>0];var y,g=!1,w=-1;if(46==a){if(w=0,g=!0,t++,42==(a=x[t+1>>0]))w=o("i32"),t++;else for(;;){var E=x[t+1>>0];if(E<48||E>57)break;w=10*w+(E-48),t++}a=x[t+1>>0]}switch(w<0&&(w=6,g=!1),String.fromCharCode(a)){case"h":104==x[t+2>>0]?(t++,y=1):y=2;break;case"l":108==x[t+2>>0]?(t++,y=8):y=4;break;case"L":case"q":case"j":y=8;break;case"z":case"t":case"I":y=4;break;default:y=null}switch(y&&t++,a=x[t+1>>0],String.fromCharCode(a)){case"d":case"i":case"u":case"o":case"x":case"X":case"p":var b=100==a||105==a;s=o("i"+8*(y=y||4)),8==y&&(s=117==a?(s[0]>>>0)+4294967296*(s[1]>>>0):Mr(s[0],s[1])),y<=4&&(s=(b?Or:Ir)(s&Math.pow(256,y)-1,8*y));var _=Math.abs(s),k="";if(100==a||105==a)C=Or(s,8*y).toString(10);else if(117==a)C=Ir(s,8*y).toString(10),s=Math.abs(s);else if(111==a)C=(p?"0":"")+_.toString(8);else if(120==a||88==a){if(k=p&&0!=s?"0x":"",s<0){s=-s,C=(_-1).toString(16);for(var T=[],P=0;P<C.length;P++)T.push((15-parseInt(C[P],16)).toString(16));for(C=T.join("");C.length<2*y;)C="f"+C}else C=_.toString(16);88==a&&(k=k.toUpperCase(),C=C.toUpperCase())}else 112==a&&(0===_?C="(nil)":(k="0x",C=_.toString(16)));if(g)for(;C.length<w;)C="0"+C;for(s>=0&&(l?k="+"+k:m&&(k=" "+k)),"-"==C.charAt(0)&&(k="-"+k,C=C.substr(1));k.length+C.length<v;)d?C+=" ":h?C="0"+C:k=" "+k;(C=k+C).split("").forEach((function(e){c.push(e.charCodeAt(0))}));break;case"f":case"F":case"e":case"E":case"g":case"G":var C;if(s=o("double"),isNaN(s))C="nan",h=!1;else if(isFinite(s)){var A=!1,D=Math.min(w,20);if(103==a||71==a){A=!0,w=w||1;var S=parseInt(s.toExponential(D).split("e")[1],10);w>S&&S>=-4?(a=(103==a?"f":"F").charCodeAt(0),w-=S+1):(a=(103==a?"e":"E").charCodeAt(0),w--),D=Math.min(w,20)}101==a||69==a?(C=s.toExponential(D),/[eE][-+]\d$/.test(C)&&(C=C.slice(0,-1)+"0"+C.slice(-1))):102!=a&&70!=a||(C=s.toFixed(D),0===s&&((u=s)<0||0===u&&1/u==-1/0)&&(C="-"+C));var F=C.split("e");if(A&&!p)for(;F[0].length>1&&F[0].includes(".")&&("0"==F[0].slice(-1)||"."==F[0].slice(-1));)F[0]=F[0].slice(0,-1);else for(p&&-1==C.indexOf(".")&&(F[0]+=".");w>D++;)F[0]+="0";C=F[0]+(F.length>1?"e"+F[1]:""),69==a&&(C=C.toUpperCase()),s>=0&&(l?C="+"+C:m&&(C=" "+C))}else C=(s<0?"-":"")+"inf",h=!1;for(;C.length<v;)d?C+=" ":C=!h||"-"!=C[0]&&"+"!=C[0]?(h?"0":" ")+C:C[0]+"0"+C.slice(1);a<97&&(C=C.toUpperCase()),C.split("").forEach((function(e){c.push(e.charCodeAt(0))}));break;case"s":var R=o("i8*"),M=R?Yr(R):"(null)".length;if(g&&(M=Math.min(M,w)),!d)for(;M<v--;)c.push(32);if(R)for(P=0;P<M;P++)c.push($[R++>>0]);else c=c.concat(Hr("(null)".substr(0,M),!0));if(d)for(;M<v--;)c.push(32);break;case"c":for(d&&c.push(o("i8"));--v>0;)c.push(32);d||c.push(o("i8"));break;case"n":var I=o("i32*");O[I>>2]=c.length;break;case"%":c.push(i);break;default:for(P=f;P<t+2;P++)c.push(x[P>>0])}t+=2}else c.push(i),t+=1}return c}(r,t);Ur(e,P(n,0))},t:function(e){$.length,ee("OOM")},u:function(e,r){var t=0;return Br().forEach((function(n,o){var i=r+t;O[e+4*o>>2]=i,function(e,r,t){for(var n=0;n<e.length;++n)x[r++>>0]=e.charCodeAt(n);t||(x[r>>0]=0)}(n,i),t+=n.length+1})),0},w:function(e,r){var t=Br();O[e>>2]=t.length;var n=0;return t.forEach((function(e){n+=e.length+1})),O[r>>2]=n,0},k:function(e){try{var r=me.getStreamFromFD(e);return he.close(r),0}catch(e){return void 0!==he&&e instanceof he.ErrnoError||ee(e),e.errno}},z:function(e,r){try{var t=me.getStreamFromFD(e),n=t.tty?2:he.isDir(t.mode)?3:he.isLink(t.mode)?7:4;return x[r>>0]=n,0}catch(e){return void 0!==he&&e instanceof he.ErrnoError||ee(e),e.errno}},A:function(e,r,t,n){try{var o=me.getStreamFromFD(e),i=me.doReadv(o,r,t);return O[n>>2]=i,0}catch(e){return void 0!==he&&e instanceof he.ErrnoError||ee(e),e.errno}},r:function(e,r,t,n,o){try{var i=me.getStreamFromFD(e),a=4294967296*t+(r>>>0),s=9007199254740992;return a<=-s||a>=s?-61:(he.llseek(i,a,n),ne=[i.position>>>0,(te=i.position,+Math.abs(te)>=1?te>0?(0|Math.min(+Math.floor(te/4294967296),4294967295))>>>0:~~+Math.ceil((te-+(~~te>>>0))/4294967296)>>>0:0)],O[o>>2]=ne[0],O[o+4>>2]=ne[1],i.getdents&&0===a&&0===n&&(i.getdents=null),0)}catch(e){return void 0!==he&&e instanceof he.ErrnoError||ee(e),e.errno}},j:function(e,r,t,n){try{var o=me.getStreamFromFD(e),i=me.doWritev(o,r,t);return O[n>>2]=i,0}catch(e){return void 0!==he&&e instanceof he.ErrnoError||ee(e),e.errno}},F:function(e){var r=Date.now();return O[e>>2]=r/1e3|0,O[e+4>>2]=r%1e3*1e3|0,0},g:function(e){}};!function(){var e={a:Vr};function r(e,r){var n,o,i=e.exports;t.asm=i,g=t.asm.J,n=g.buffer,F=n,t.HEAP8=x=new Int8Array(n),t.HEAP16=R=new Int16Array(n),t.HEAP32=O=new Int32Array(n),t.HEAPU8=$=new Uint8Array(n),t.HEAPU16=M=new Uint16Array(n),t.HEAPU32=I=new Uint32Array(n),t.HEAPF32=j=new Float32Array(n),t.HEAPF64=U=new Float64Array(n),N=t.asm.N,o=t.asm.K,q.unshift(o),Z()}function n(e){r(e.instance)}function o(r){return function(){if(!y&&(p||h)){if("function"==typeof fetch&&!ie(re))return fetch(re,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+re+"'";return e.arrayBuffer()})).catch((function(){return ae(re)}));if(u)return new Promise((function(e,r){u(re,(function(r){e(new Uint8Array(r))}),r)}))}return Promise.resolve().then((function(){return ae(re)}))}().then((function(r){return WebAssembly.instantiate(r,e)})).then((function(e){return e})).then(r,(function(e){E("failed to asynchronously prepare wasm: "+e),ee(e)}))}if(Q(),t.instantiateWasm)try{return t.instantiateWasm(e,r)}catch(e){return E("Module.instantiateWasm callback failed with error: "+e),!1}y||"function"!=typeof WebAssembly.instantiateStreaming||oe(re)||ie(re)||"function"!=typeof fetch?o(n):fetch(re,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,e).then(n,(function(e){return E("wasm streaming compile failed: "+e),E("falling back to ArrayBuffer instantiation"),o(n)}))}))}(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.K).apply(null,arguments)};var Xr=t._free=function(){return(Xr=t._free=t.asm.L).apply(null,arguments)},Gr=t._malloc=function(){return(Gr=t._malloc=t.asm.M).apply(null,arguments)},qr=t.___errno_location=function(){return(qr=t.___errno_location=t.asm.O).apply(null,arguments)},Yr=t._strlen=function(){return(Yr=t._strlen=t.asm.P).apply(null,arguments)},Kr=t.___getTypeName=function(){return(Kr=t.___getTypeName=t.asm.Q).apply(null,arguments)};t.___embind_register_native_and_builtin_types=function(){return(t.___embind_register_native_and_builtin_types=t.asm.R).apply(null,arguments)};var Jr,Qr=t._memalign=function(){return(Qr=t._memalign=t.asm.S).apply(null,arguments)};function Zr(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function et(e){function r(){Jr||(Jr=!0,t.calledRun=!0,_||(t.noFSInit||he.init.initialized||he.init(),he.ignorePermissions=!1,se(q),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)e=t.postRun.shift(),Y.unshift(e);var e;se(Y)}()))}K>0||(!function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)e=t.preRun.shift(),G.unshift(e);var e;se(G)}(),K>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),r()}),1)):r()))}if(t.dynCall_ijiii=function(){return(t.dynCall_ijiii=t.asm.T).apply(null,arguments)},t.dynCall_viiijj=function(){return(t.dynCall_viiijj=t.asm.U).apply(null,arguments)},t.dynCall_jij=function(){return(t.dynCall_jij=t.asm.V).apply(null,arguments)},t.dynCall_jii=function(){return(t.dynCall_jii=t.asm.W).apply(null,arguments)},t.dynCall_jiji=function(){return(t.dynCall_jiji=t.asm.X).apply(null,arguments)},t._ff_h264_cabac_tables=83749,J=function e(){Jr||et(),Jr||(J=e)},t.run=et,t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();et(),e.exports=t}));const u="initVideo",c="render",f="playAudio",l="print",d="printErr",p="initAudio",h="audioCode",m="videoCode",v=1,y=2,g="init",w="decode",E="audioDecode",b="videoDecode",_="close",k="key",T="delta";(()=>{try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(e instanceof WebAssembly.Module)return new WebAssembly.Instance(e)instanceof WebAssembly.Instance}}catch(e){}})(),Date.now||(Date.now=function(){return(new Date).getTime()}),s.print=function(e){postMessage({cmd:l,text:e})},s.printErr=function(e){postMessage({cmd:d,text:e})},s.postRun=function(){var e=[],r={};"VideoEncoder"in self&&(r={hasInit:!1,isEmitInfo:!1,offscreenCanvas:null,offscreenCanvasCtx:null,decoder:new VideoDecoder({output:function(e){r.isEmitInfo||(t.opt.debug&&console.log("Jessibuca: [worker] Webcodecs Video Decoder initSize"),postMessage({cmd:u,w:e.codedWidth,h:e.codedHeight}),r.isEmitInfo=!0,r.offscreenCanvas=new OffscreenCanvas(e.codedWidth,e.codedHeight),r.offscreenCanvasCtx=r.offscreenCanvas.getContext("2d")),r.offscreenCanvasCtx.drawImage(e,0,0,e.codedWidth,e.codedHeight);let n=r.offscreenCanvas.transferToImageBitmap();postMessage({cmd:c,buffer:n,delay:t.delay,ts:0},[n]),setTimeout((function(){e.close?e.close():e.destroy()}),100)},error:function(e){console.error(e)}}),decode:function(e,n){const o=e[0]>>4==1;if(r.hasInit){const t=new EncodedVideoChunk({data:e.slice(5),timestamp:n,type:o?k:T});r.decoder.decode(t)}else if(o&&0===e[1]){const n=15&e[0];t.setVideoCodec(n);const o=function(e){let r=e.subarray(1,4),t="avc1.";for(let e=0;e<3;e++){let n=r[e].toString(16);n.length<2&&(n="0"+n),t+=n}return{codec:t,description:e}}(e.slice(5));r.decoder.configure(o),r.hasInit=!0}},reset(){r.hasInit=!1,r.isEmitInfo=!1,r.offscreenCanvas=null,r.offscreenCanvasCtx=null}});var t={opt:{},useOffscreen:function(){return!this.opt.forceNoOffscreen&&"undefined"!=typeof OffscreenCanvas},initAudioPlanar:function(e,r){postMessage({cmd:p,sampleRate:r,channels:e});var t=[],n=[],o=0;this.playAudioPlanar=function(r,i,a){for(var u=i,c=[],l=0,d=0;d<2;d++){var p=s.HEAPU32[(r>>2)+d]>>2;c[d]=s.HEAPF32.subarray(p,p+u)}if(o){if(!(u>=(i=1024-o)))return o+=u,t[0]=Float32Array.of(...t[0],...c[0]),void(2==e&&(t[1]=Float32Array.of(...t[1],...c[1])));n[0]=Float32Array.of(...t[0],...c[0].subarray(0,i)),2==e&&(n[1]=Float32Array.of(...t[1],...c[1].subarray(0,i))),postMessage({cmd:f,buffer:n,ts:a},n.map((e=>e.buffer))),l=i,u-=i}for(o=u;o>=1024;o-=1024)n[0]=c[0].slice(l,l+=1024),2==e&&(n[1]=c[1].slice(l-1024,l)),postMessage({cmd:f,buffer:n,ts:a},n.map((e=>e.buffer)));o&&(t[0]=c[0].slice(l),2==e&&(t[1]=c[1].slice(l)))}},setVideoCodec:function(e){postMessage({cmd:m,code:e})},setAudioCodec:function(e){postMessage({cmd:h,code:e})},setVideoSize:function(e,r){postMessage({cmd:u,w:e,h:r});var n=e*r,o=n>>2;t.useOffscreen()?(this.offscreenCanvas=new OffscreenCanvas(e,r),this.offscreenCanvasGL=this.offscreenCanvas.getContext("webgl"),this.webglObj=(e=>{var r=["attribute vec4 vertexPos;","attribute vec4 texturePos;","varying vec2 textureCoord;","void main()","{","gl_Position = vertexPos;","textureCoord = texturePos.xy;","}"].join("\n"),t=["precision highp float;","varying highp vec2 textureCoord;","uniform sampler2D ySampler;","uniform sampler2D uSampler;","uniform sampler2D vSampler;","const mat4 YUV2RGB = mat4","(","1.1643828125, 0, 1.59602734375, -.87078515625,","1.1643828125, -.39176171875, -.81296875, .52959375,","1.1643828125, 2.017234375, 0, -1.081390625,","0, 0, 0, 1",");","void main(void) {","highp float y = texture2D(ySampler,  textureCoord).r;","highp float u = texture2D(uSampler,  textureCoord).r;","highp float v = texture2D(vSampler,  textureCoord).r;","gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;","}"].join("\n"),n=e.createShader(e.VERTEX_SHADER);e.shaderSource(n,r),e.compileShader(n),e.getShaderParameter(n,e.COMPILE_STATUS)||console.log("Vertex shader failed to compile: "+e.getShaderInfoLog(n));var o=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(o,t),e.compileShader(o),e.getShaderParameter(o,e.COMPILE_STATUS)||console.log("Fragment shader failed to compile: "+e.getShaderInfoLog(o));var i=e.createProgram();e.attachShader(i,n),e.attachShader(i,o),e.linkProgram(i),e.getProgramParameter(i,e.LINK_STATUS)||console.log("Program failed to compile: "+e.getProgramInfoLog(i)),e.useProgram(i);var a=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,a),e.bufferData(e.ARRAY_BUFFER,new Float32Array([1,1,-1,1,1,-1,-1,-1]),e.STATIC_DRAW);var s=e.getAttribLocation(i,"vertexPos");e.enableVertexAttribArray(s),e.vertexAttribPointer(s,2,e.FLOAT,!1,0,0);var u=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,u),e.bufferData(e.ARRAY_BUFFER,new Float32Array([1,0,0,0,1,1,0,1]),e.STATIC_DRAW);var c=e.getAttribLocation(i,"texturePos");function f(r,t){var n=e.createTexture();return e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.bindTexture(e.TEXTURE_2D,null),e.uniform1i(e.getUniformLocation(i,r),t),n}e.enableVertexAttribArray(c),e.vertexAttribPointer(c,2,e.FLOAT,!1,0,0);var l=f("ySampler",0),d=f("uSampler",1),p=f("vSampler",2);return{render:function(r,t,n,o,i){e.viewport(0,0,r,t),e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,l),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,r,t,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,d),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,r/2,t/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,o),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,p),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,r/2,t/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,i),e.drawArrays(e.TRIANGLE_STRIP,0,4)},destroy:function(){try{e.deleteProgram(i),e.deleteBuffer(a),e.deleteBuffer(u),e.deleteTexture(l),e.deleteTexture(d),e.deleteBuffer(p)}catch(e){}}}})(this.offscreenCanvasGL),this.draw=function(t,i,a,u){this.webglObj.render(e,r,s.HEAPU8.subarray(i,i+n),s.HEAPU8.subarray(a,a+o),s.HEAPU8.subarray(u,u+o));let f=this.offscreenCanvas.transferToImageBitmap();postMessage({cmd:c,buffer:f,delay:this.delay,ts:t},[f])}):this.draw=function(e,r,t,i){var a=[s.HEAPU8.subarray(r,r+n),s.HEAPU8.subarray(t,t+o),s.HEAPU8.subarray(i,i+o)].map((e=>Uint8Array.from(e)));postMessage({cmd:c,output:a,delay:this.delay,ts:e},a.map((e=>e.buffer)))}},getDelay:function(e){return e?(this.firstTimestamp=e,this.startTimestamp=Date.now(),this.delay=-1,this.getDelay=function(e){return e&&(this.delay=Date.now()-this.startTimestamp-(e-this.firstTimestamp)),this.delay},-1):-1},init:function(){t.opt.debug&&console.log("Jessibuca: [worker] init");const n=e=>{t.opt.useWCS&&t.useOffscreen()&&e.type===y&&r.decode?r.decode(e.payload,e.ts):e.decoder.decode(e.payload,e.ts)};this.stopId=setInterval((()=>{if(e.length)if(this.dropping){for(r=e.shift();!r.isIFrame&&e.length;)r=e.shift();r.isIFrame&&(this.dropping=!1,n(r))}else{var r=e[0];if(-1===this.getDelay(r.ts))e.shift(),n(r);else if(this.delay>this.opt.videoBuffer+1e3)this.dropping=!0;else for(;e.length&&(r=e[0],this.getDelay(r.ts)>this.opt.videoBuffer);)e.shift(),n(r)}}),10)},close:function(){this.opt.debug&&console.log("Jessibuca: [worker]: close"),clearInterval(this.stopId),this.stopId=null,n.clear(),o.clear(),r.reset&&r.reset(),this.firstTimestamp=0,this.startTimestamp=0,this.delay=-1,this.webglObj&&(this.webglObj.destroy(),this.offscreenCanvas=null,this.offscreenCanvasGL=null,this.offscreenCanvasCtx=null),e=[],delete this.playAudioPlanar,delete this.draw},pushBuffer:function(r,t){t.type===v?e.push({ts:t.ts,payload:r,decoder:n,type:v}):t.type===y&&e.push({ts:t.ts,payload:r,decoder:o,type:y,isIFrame:t.isIFrame})}},n=new s.AudioDecoder(t),o=new s.VideoDecoder(t);postMessage({cmd:g}),self.onmessage=function(e){var r=e.data;switch(r.cmd){case g:t.opt=JSON.parse(r.opt),n.sample_rate=r.sampleRate,t.init();break;case w:t.pushBuffer(r.buffer,r.options);break;case E:n.decode(r.buffer,r.ts);break;case b:o.decode(r.buffer,r.ts);break;case _:t.close()}}}}));
