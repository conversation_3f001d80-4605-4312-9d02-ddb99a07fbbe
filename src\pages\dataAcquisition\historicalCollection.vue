<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-10-10 11:41:46
 * @Description: 
-->
<template>
  <view class="container">
    <view class="top_sticky">
      <uv-search
        height="40"
        shape="square"
        searchIcon="../../static/icon/search_icon.png"
        searchIconSize="18"
        placeholder="搜索数据采集记录"
        placeholderColor="#A09F9F"
        bgColor="#fff"
        :showAction="false"
        :boxStyle="searchStyle"
        v-model="inpVal"
        @change="inpChange"
        @search="inpSearch"
      ></uv-search>
      <uv-tabs
        :list="tabTypes"
        :current="tabType"
        @click="clickTab"
        lineColor="transparent"
        :activeStyle="{
          marginLeft: tabType === '' ? '2px' : '0px',
          height: '24px',
          lineHeight: '24px',
          padding: '0px 14px',
          borderRadius: '8px',
          backgroundColor: '#4378FF',
          color: '#FFF',
          transform: 'scale(1.05)',
        }"
        :inactiveStyle="{
          padding: '0px 14px',
          borderRadius: '8px',
          backgroundColor: '#FFFFFF',
          color: '#8E8E8E',
          transform: 'scale(1)',
        }"
        :itemStyle="searchTabsItemStyle"
      >
        <template v-slot:right>
          <view style="padding-left: 4px" @tap="showAllTabs">
            <image
              class="tabbar_more_icon"
              src="../../static/icon/tabbar_more_icon.png"
            />
          </view>
        </template>
      </uv-tabs>
    </view>
    <view v-if="dataList.length">
      <view
        class="list_box"
        v-for="(monthGroup, index) in dataList"
        :key="index"
      >
        <view ref="stickyRef" class="month_sticky_bar">
          <view class="month">
            <text>{{ monthGroup.month }}</text>
            <text class="red_text"
              >（{{ monthGroup.draftTotal }}/{{ monthGroup.total }}）</text
            >
          </view>
          <image
            class="date_select_icon"
            src="../../static/icon/dropdown_icon.png"
            @click="showTime"
          />
        </view>
        <view class="month_group_list">
          <view class="card" v-for="item in monthGroup.data" :key="item.id">
            <view class="draft_mark" v-if="item.status == 0">草稿</view>
            <view class="card_top">
              <view class="data_title_box">
                <image
                  class="data_logo"
                  :style="{ width: item.type == 8 ? '156rpx' : '108rpx' }"
                  :src="logoUrls[item.type]"
                />
                <view class="data_title">{{ item.name }}</view>
              </view>
            </view>
            <!-- 桩号 -->
            <view class="card_content" v-if="item.type == 2">
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{ item.sectionName || '-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">上下行：</view>
                <view class="content_val">{{ item.updownMarkName || '-' }}</view>
              </view>
            </view>
            <!-- 路段 -->
            <view class="card_content flex_content" v-if="item.type == 1">
              <view class="content_item">
                <view class="content_name">所属路线：</view>
                <view class="content_val">{{ item.routeName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">起点：</view>
                <view class="content_val">{{ item.startStakeName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">上下行：</view>
                <view class="content_val">{{ item.updownMarkName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">终点：</view>
                <view class="content_val">{{ item.endStakeName ||'-' }}</view>
              </view>
            </view>
            <!-- 路基、路面、桥梁、隧道 -->
            <view
              class="card_content flex_content"
              v-if="['3', '4', '5', '6'].includes(item.type)"
            >
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{ item.sectionName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">起点：</view>
                <view class="content_val">{{ item.startStakeName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">上下行：</view>
                <view class="content_val">{{ item.updownMarkName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">终点：</view>
                <view class="content_val">{{ item.endStakeName ||'-' }}</view>
              </view>
            </view>
            <!-- 涵洞 -->
            <view class="card_content flex_content" v-if="item.type == 7">
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{ item.sectionName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">位置：</view>
                <view class="content_val">{{ item.locationStakeName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">上下行：</view>
                <view class="content_val">{{ item.updownMarkName ||'-' }}</view>
              </view>
            </view>
            <!-- 沿线设施 -->
            <view class="card_content" v-if="item.type == 8">
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{ item.sectionName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">类型：</view>
                <view class="content_val">{{ item.facilityTypeName ||'-' }}</view>
              </view>
              <view class="flex_content">
                <view class="content_item">
                  <view class="content_name">位置：</view>
                  <view class="content_val">{{ item.locationStakeName ||'-' }}</view>
                </view>
                <view class="content_item">
                  <view class="content_name">上下行：</view>
                  <view class="content_val">{{ item.updownMarkName ||'-' }}</view>
                </view>
              </view>
            </view>
            <view class="card_bottom">
              <view class="log_date">
                <image
                  class="date_icon"
                  src="../../static/icon/date_icon.png"
                />
                <view class="date">录入时间：{{ item.createTime }}</view>
              </view>
              <view
                class="btn"
                @click="toDetail(item.type, item.status, item.id)"
                >{{ item.status == 1 ? "查看" : "继续录入" }}</view
              >
            </view>
          </view>
        </view>
      </view>
    </view>
    <template v-else>
      <view v-if="startD" ref="stickyRef" class="month_sticky_bar">
          <view class="month">
            <text>{{ dayjs(startD).format('YYYY-M') }}</text>
            <text class="red_text"
              >（0/0）</text
            >
          </view>
          <image
            class="date_select_icon"
            src="../../static/icon/dropdown_icon.png"
            @click="showTime"
          />
        </view>
      <ylg-nodata class="nodata"></ylg-nodata>
    </template>

    <!-- <dataTypesPicker
      ref="tabsPicker"
      @confirmTabs="confirmTabs"
      @cancelTabs="cancelTabs"
    ></dataTypesPicker> -->
    <ylg-data-tabs-picker
      ref="tabsPicker"
      :types="tabTypes"
      :dataStatus="tabDataStatus"
      :curType="pickerType"
      :curState="pickerState"
      @tabPickerCallback="tabPickerCallback"
    ></ylg-data-tabs-picker>
    <ylg-date-range-picker
      ref="datePicker"
      @confirmDate="confirmDate"
      @cancelDate="cancelDate"
    ></ylg-date-range-picker>
  </view>
</template>
<script setup>
import { reactive, ref, computed, watch } from "vue";
import dataTypesPicker from "./components/dataTypesPicker.vue";
import { onShow, onReachBottom } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { DataAcquisitionService } from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { getDaysInMonth } from "@/utils"

// 列表相关数据
let dataList = ref([]);
// 分页查询参数
let pageInfo = reactive({
  page: 1,
  limit: 10,
});
let noData = ref(false);
let loading = ref(false);
// 输入框搜索内容
let inpVal = ref("");
// 数据类型type
let tabType = ref(0);
// 数据状态
let tabStatus = ref("");

onShow(() => {
  pageInfo.page = 1;
  inpVal.value = "";
  getList();
});

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  marginBottom: "28rpx",
});
// 分类tab筛选 相关
let pickerType = ref("");
let pickerState = ref("");
const tabTypes = reactive([
  {
    name: "全部",
    type: ''
  },
  {
    name: "路段",
    type: 1
  },
  {
    name: "桩号",
    type: 2
  },
  {
    name: "路基",
    type: 3
  },
  {
    name: "路面",
    type: 4
  },
  {
    name: "桥梁",
    type: 5
  },
  {
    name: "隧道",
    type: 6
  },
  {
    name: "涵洞",
    type: 7
  },
  {
    name: "沿线设施",
    type: 8
  },
]);
const tabDataStatus = reactive([
  {
    label: "全部",
    value: '',
  },
  {
    label: "正常",
    value: 1,
  },
  {
    label: "草稿",
    value: 0,
  },
]);
const searchTabsItemStyle = reactive({
  height: "26px",
  boxSizing: "border-box",
  padding: "0px 0px",
  backgroundColor: "#fff",
  borderRadius: "8px",
  marginRight: "12px",
  fontSize: "14px",
});

const inpChange = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  tabType.value = "";
  tabStatus.value = "";
  pickerType.value = "";
  pickerState.value = "";
  startD.value = "";
  endD.value = "";
  getList();
};
const inpSearch = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  tabType.value = "";
  tabStatus.value = "";
  pickerType.value = "";
  pickerState.value = "";
  startD.value = "";
  endD.value = "";
  getList();
};

const clickTab = (env) => {
  tabType.value = env.type;
  pickerType.value = env.type;
  pageInfo.page = 1;
  getList();
};
const tabsPicker = ref(null);
let stickyZIndex = ref(999);
const showAllTabs = () => {
  stickyZIndex.value = 99;
  tabsPicker.value.open();
  pickerType.value = tabType.value;
  pickerState.value = tabStatus.value;
};

// 分类&状态选择组件，回调
const tabPickerCallback = (envRes) => {
  switch (envRes.type) {
    case "changeType":
      pickerType.value = envRes.val;
      break;
    case "changeState":
      pickerState.value = envRes.val;
      break;
    case "confirmTabs":
      console.log("选择回调", envRes, pickerType.value,pickerState.value);
      tabType.value = pickerType.value;
      tabStatus.value = pickerState.value;
      tabsPicker.value.close();
      stickyZIndex.value = 999;
      pageInfo.page = 1;
      getList();
      break;
    case "reset":
      pickerType.value = "";
      pickerState.value = "";
      break;
    case "cancelTabs":
      stickyZIndex.value = 999;
      break;
  
    default:
      break;
  }
}

// 时间选择组件
const datePicker = ref(null);
const showTime = () => {
  stickyZIndex.value = 99;
  datePicker.value.open();
};
let startD = ref("");
let endD = ref("");
const confirmDate = (envRes) => {
  let { dateType, year,month, startDate, endDate } = envRes;
  if (dateType === "date") {
    let days = getDaysInMonth(year,Number(month));
    startD.value = `${year}-${month<10?'0'+month:month}-01 00:00:00`;
    endD.value = `${year}-${month<10?'0'+month:month}-${days} 23:59:59`;
  } else if (dateType === "daterange") {
    startD.value = startDate;
    endD.value = endDate;
  }
  stickyZIndex.value = 999;
  datePicker.value.close();
  pageInfo.page = 1;
  getList();
};
const cancelDate = () => {
  console.log("取消选择时间回调");
  stickyZIndex.value = 999;
};

// logo图映射
const logoUrls = reactive({
  1: `../../static/icon/road_part_logo20241010.png`,
  2: `../../static/icon/pile_number_logo20241010.png`,
  3: `../../static/icon/roadbed_logo20241010.png`,
  4: `../../static/icon/road_face_logo20241010.png`,
  5: `../../static/icon/bridge_logo20241010.png`,
  6: `../../static/icon/tunnel_logo20241010.png`,
  7: `../../static/icon/culvert_logo20241010.png`,
  8: `../../static/icon/roadside_facilities_logo20241010.png`,
});

// 查询列表
const getList = async () => {
  let params = {
    projectId: projectInfo.projectId,
    searchName: inpVal.value,
    type: tabType.value || "",
    status: tabStatus.value,
    createStartTime: startD.value,
    createEndTime: endD.value,
    page: pageInfo.page,
    limit: pageInfo.limit,
  };
  loading.value = true;
  let {
    code,
    data: resData,
    total,
  } = await DataAcquisitionService.getHistoryData(params);
  // 如果接口异常，page-1，不执行后续操作
  if (code != 200) {
    pageInfo.page--;
    return;
  }
  if (pageInfo.page === 1) {
    dataList.value = [];
    noData.value = false;
  }
  // 处理数据，将月份相同的数据拼接到一个分组里
  console.log("resData", resData);
  let monthArr = dataList.value.map((grounp) => grounp?.month || "");
  console.log("monthArr", monthArr);
  resData.forEach((resGrounp) => {
    let sameMonthIndex = monthArr.findIndex((item) => item === resGrounp.month);
    console.log("sameMonthIndex", sameMonthIndex);
    if (sameMonthIndex !== -1) {
      dataList.value[sameMonthIndex].data = dataList.value[
        sameMonthIndex
      ].data.concat(resGrounp.data);
    } else {
      dataList.value.push(resGrounp);
    }
  });

  // 计算每个分组里的，草稿数量
  // dataList.value.forEach((group) => {
  //   group.draftNum = group.data.filter((item) => item.status == 0).length;
  // });

  loading.value = false;
  if (!resData.length) {
    noData.value = true;
  }
  console.log("数据采集列表", dataList.value);
};

/** 页面上拉 **/
onReachBottom(() => {
  if (noData.value) {
    uni.showToast({
      icon: "none",
      title: "没有更多了~",
    });
    return;
  }
  pageInfo.page++;
  getList();
});

let detailPageUrls = {
  1: "/pages/dataAcquisition/roadPartCollection",
  2: "/pages/dataAcquisition/pileNumberCollection", // 桩号
  3: "/pages/dataAcquisition/roadbedCollection", // 路基
  4: "/pages/dataAcquisition/roadFaceCollection",
  5: "/pages/dataAcquisition/bridgeCollection", // 桥梁
  6: "/pages/dataAcquisition/tunnelCollection", // 隧道
  7: "/pages/dataAcquisition/culvertCollection", // 涵洞
  8: "/pages/dataAcquisition/roadsideFacilities",
};
const toDetail = (type, status, id) => {
  uni.navigateTo({
    url: `${detailPageUrls[type]}?status=${status}&id=${id}`,
  });
};
</script>
<style lang="scss" scoped>
.container {
  background: #f4f8ff;
  min-height: 100vh;
  padding: 24rpx 40rpx;
  // overflow: hidden;
}
.top_sticky {
  // background: #f4f8ff;
  // position: sticky;
  // top: 0rpx;
  // z-index: 999;
}
.tabbar_more_icon {
  display: inline-block;
  width: 116rpx;
  height: 64rpx;
  vertical-align: middle;
}
.list_box {
  // margin-top: 48rpx;
  .card {
    position: relative;
    text-align: center;
    box-sizing: border-box;
    margin-top: 28rpx;
    border-radius: 28rpx;
    background: #fff;
    box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
    .draft_mark {
      position: absolute;
      right: 0;
      top: 0;
      background-color: #eaeaea;
      width: 92rpx;
      height: 44rpx;
      border-radius: 0 24rpx 0 24rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #404040;
      line-height: 44rpx;
    }
    .card_top {
      padding: 28rpx 28rpx 0rpx 28rpx;
      .data_title_box {
        display: flex;
        align-items: center;
        .data_logo {
          display: inline-block;
          margin-right: 20rpx;
          width: 108rpx;
          height: 48rpx;
        }
        .data_title {
          flex: 1;
          text-align: left;
        }
      }
    }
    .card_content {
      margin-top: 28rpx;
      padding: 0rpx 28rpx;
      font-family: PingFang SC, PingFang SC;
      border-bottom: 2rpx solid #f0f0f0;
      .content_item {
        margin-bottom: 20rpx;
        display: flex;
        align-items: baseline;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 40rpx;
        .content_name {
          width: 140rpx;
          color: #b0b0b0;
          text-align: right;
        }
        .content_val {
          flex: 1;
          text-align: left;
          color: #404040;
        }
      }
    }
    .flex_content {
      display: flex;
      flex-wrap: wrap;
      .content_item:nth-child(2n) {
        width: 40%;
      }
      .content_item:nth-child(2n + 1) {
        // margin-right: 112rpx;
        width: 60%;
      }
    }
    .card_bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 28rpx;
      .log_date {
        display: flex;
        align-items: center;
        .date_icon {
          display: inline-block;
          margin-right: 8rpx;
          width: 28rpx;
          height: 28rpx;
        }
        .date {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #a09f9f;
          line-height: 34rpx;
        }
      }
      .btn {
        padding: 6rpx 20rpx;
        box-sizing: border-box;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 28rpx;
        color: #4378ff;
        line-height: 40rpx;
        border: 2rpx solid #4378ff;
        border-radius: 8rpx;
      }
    }
  }
  .card:first-child {
    margin-top: 0 !important;
  }
  .black {
    height: 200rpx;
  }
}
.month_sticky_bar {
    padding: 48rpx 0 28rpx 0;
    display: flex;
    align-items: center;
    position: sticky;
    top: 206rpx;
    top: 0rpx;
    z-index: v-bind(stickyZIndex);
    background: #f4f8ff;
    .month {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #373737;
      line-height: 44rpx;
      .red_text {
        color: #ff3132;
      }
    }
    .date_select_icon {
      display: block;
      margin-left: 16rpx;
      width: 28rpx;
      height: 28rpx;
    }
  }
.nodata{
  margin-top: 80rpx;
}
</style>
