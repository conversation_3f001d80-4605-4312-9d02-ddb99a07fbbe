<template>
  <view class="number-switcher">
    <view
      class="switcher-btn prev-btn"
      :class="{ disabled: currentPage <= 1 }"
      @click="handlePrev"
    >
      <uv-icon name="arrow-left" size="16" color="#666"></uv-icon>
    </view>

    <view class="page-info">
      <text class="current-page">{{ currentPage }}</text>
      <text class="separator">/</text>
      <text class="total-pages">{{ totalPages }}</text>
    </view>

    <view
      class="switcher-btn next-btn"
      :class="{ disabled: currentPage >= totalPages }"
      @click="handleNext"
    >
      <uv-icon name="arrow-right" size="16" color="#666"></uv-icon>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  // 当前页码
  currentPage: {
    type: Number,
    default: 1,
  },
  // 总页数
  totalPages: {
    type: Number,
    default: 1,
  },
});

const emit = defineEmits(["prev", "next", "change"]);

// 上一页
const handlePrev = () => {
  if (props.currentPage > 1) {
    const newPage = props.currentPage - 1;
    emit("prev", newPage);
    emit("change", newPage);
  }
};

// 下一页
const handleNext = () => {
  if (props.currentPage < props.totalPages) {
    const newPage = props.currentPage + 1;
    emit("next", newPage);
    emit("change", newPage);
  }
};
</script>

<style lang="scss" scoped>
.number-switcher {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;

  .switcher-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:hover:not(.disabled) {
      background-color: #e8f4ff;
      border-color: #4378ff;
    }

    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;

      :deep(.uv-icon) {
        color: #ccc !important;
      }
    }
  }

  .page-info {
    display: flex;
    align-items: center;
    gap: 8rpx;
    min-width: 80rpx;
    height: 80rpx;
    justify-content: center;
    flex-shrink: 0;

    .current-page {
      font-size: 32rpx;
      font-weight: bold;
      color: #4378ff;
      line-height: 1;
    }

    .separator {
      font-size: 32rpx;
      font-weight: bold;
      color: #999;
      line-height: 1;
    }

    .total-pages {
      font-size: 32rpx;
      font-weight: bold;
      color: #666;
      line-height: 1;
    }
  }
}
</style>
