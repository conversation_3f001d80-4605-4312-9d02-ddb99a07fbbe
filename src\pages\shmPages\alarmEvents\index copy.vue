<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-10 09:42:13
 * @Description: 
-->
<template>
  <view class="container">
    <view class="top_sticky">
      <!-- <view class="ss" style="height: 80rpx;"></view> -->
      <uv-search
        height="40"
        shape="square"
        searchIcon="../../../static/shmStatic/icon/search_icon_20250318.png"
        searchIconSize="18"
        placeholder="搜索测点编码/报警事件ID"
        placeholderColor="#A09F9F"
        bgColor="#fff"
        :showAction="false"
        v-model="searchForm.uuidOrPointCode"
        @search="handleSearch"
        @clear="handleClear"
      ></uv-search>
      <view class="tabs-box flex-start-center">
        <view
          v-for="(item, index) of tabs"
          class="tab-item"
          :key="index"
          :class="{ active: item.value === searchForm.alarmLevel }"
          @click="changeTab(item.value)"
          >{{ item.name }}</view
        >
      </view>
      <view class="time" @click="showTime">
        <text v-if="searchForm.customTime">{{ searchForm.customTime }}</text>
        <text
          v-if="
            !searchForm.customTime &&
            searchForm.alarmTimeStart &&
            searchForm.alarmTimeEnd
          "
          >{{ searchForm.alarmTimeStart.split(" ")[0] }} 至
          {{ searchForm.alarmTimeEnd.split(" ")[0] }}</text
        >

        <image
          src="../../../static/shmStatic/icon/picker_arrow_down_20250318.png"
          mode="scaleToFill"
          class="arrow"
        />
      </view>
    </view>
    <view class="list-box">
      <swiper
        class="alarm-swiper"
        circular
        @change="(e) => swiperChange(e, 'auto')"
        :current="tabsIndex"
      >
        <swiper-item v-for="(temp, index) of tabs" :key="index">
          <template v-if="temp.value === searchForm.alarmLevel">
            <scroll-view
              :scroll-top="scrollTop"
              :scroll-y="true"
              class="scroll-Y"
              @scrolltolower="upper"
            >
              <AlarmRulesItem
                v-for="(item, index) of alarmEventsList"
                @click="toDetail(item.id)"
                :key="index"
                :data="item"
              />
              <uv-loading-icon
                :show="loading"
                text="加载中"
                textSize="30rpx"
              ></uv-loading-icon>
              <ylgNodata
                v-if="empty"
                noticeTip="未查询到报警事件"
                :imgStyle="{
                  width: '244rpx',
                  height: '168rpx',
                }"
                textSize="32rpx"
                textColor="#A09F9F"
                imgUrl="../../../static/shmStatic/image/asset-nodata-img.png"
              />
              <view class="no-data" v-if="noData && !empty"
                >没有更多数据了~</view
              >
            </scroll-view>
          </template>
        </swiper-item>
      </swiper>
    </view>
    <ylg-date-range-picker
      ref="datePicker"
      @confirmDate="confirmDate"
      @cancelDate="cancelDate"
    ></ylg-date-range-picker>
  </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getSysteminfo } from "@/utils";
import { AssetService } from "@/service";
import dayjs from "dayjs";
import AlarmRulesItem from "../singleAssetWorkbench/components/AlarmRulesItem.vue";
import { cloneDeep as _cloneDeep } from "lodash";
import { useUserStore } from "@/store/user";
import ylgNodata from "@/components/ylg-nodata.vue";
const userStore = useUserStore();
// 获取手机系统栏高度
const systemBarHeight = `${Number(getSysteminfo().systemBarHeight)}rpx`;
const tabs = [
  {
    name: "全部",
    value: 0,
  },
  {
    name: "超限三级",
    value: 3,
  },
  {
    name: "超限二级",
    value: 2,
  },
  {
    name: "超限一级",
    value: 1,
  },
];
const loading = ref(false);
const scrollTop = ref(0);
// 搜索表单
const searchForm = ref({
  page: 1,
  limit: 10,
  assetId: userStore?.assetInfo?.objectId, //资产id
  customTime: null,
  alarmTimeStart: null,
  alarmTimeEnd: null,
  alarmLevel: 3,
  uuidOrPointCode: null, // 搜索框输入值
});

// 时间选择事件
const confirmDate = (e) => {
  console.log(e);
  const { endDate, startDate, month, year } = e;
  if (e.month) {
    searchForm.value.customTime =
      year + "-" + `${month > 9 ? month : "0" + month}`;
    searchForm.value.alarmTimeStart = null;
    searchForm.value.alarmTimeEnd = null;
  } else {
    searchForm.value.customTime = null;
    searchForm.value.alarmTimeStart = startDate;
    searchForm.value.alarmTimeEnd = endDate;
  }
  datePicker.value.close();
  resetList();
};
const cancelDate = () => {};

const parseYearMonth = (str) => {
  // 使用 split 分割字符串，得到形如 ["2025", "04"] 的数组
  const [yearStr, monthStr] = str.split("-");
  // 转换成数字后返回
  const year = Number(yearStr);
  const month = Number(monthStr);
  return { year, month };
};

// 时间选择组件
const datePicker = ref(null);
const showTime = () => {
  //   stickyZIndex.value = 99
  datePicker.value.open();
  // 在日期范围选择组件中回显已选日期
  datePicker.value.changeT(searchForm.value.customTime ? 0 : 1);
  let dateObj = searchForm.value.customTime
    ? {
        dateType: "date",
        year: parseYearMonth(searchForm.value.customTime).year,
        month: parseYearMonth(searchForm.value.customTime).month,
      }
    : {
        dateType: "daterange",
        startDate: searchForm.value.alarmTimeStart,
        endDate: searchForm.value.alarmTimeEnd,
      };
  datePicker.value.setDefaultDate(dateObj);
};

// 将 pointVOList 转换为二维数组，每个子数组最多两个元素
const chunkArray = (arr, size) => {
  const result = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};

const alarmEventsList = ref([]);
const empty = ref(false);
const getList = async () => {
  let params = _cloneDeep(searchForm.value);
  params.alarmLevel = params.alarmLevel === 0 ? null : params.alarmLevel;
  if (loading.value) return;
  loading.value = true;
  // 获取事件列表
  const res = await AssetService.alarmEventPage(params);
  loading.value = false;
  empty.value = false;
  //   无数据的时候提示
  if ((!res.data || res.data.length === 0) && searchForm.value.page === 1) {
    empty.value = true;
  }
  if (res.data && res.data.length > 0) {
    if (
      searchForm.value.page === 1 &&
      res.data.length < searchForm.value.limit
    ) {
      noData.value = true;
    } else {
      noData.value = false;
    }
    // 遍历最外层数组，对每个对象的 pointVOList 进行分块处理
    let arr = res.data.map((item) => {
      return {
        ...item, // 保留其他属性
        pointVOList: chunkArray(item.pointVOList, 2), // 分块处理 pointVOList
      };
    });
    if (searchForm.value.page === 1) {
      alarmEventsList.value = arr;
    } else {
      alarmEventsList.value = [...alarmEventsList.value, ...arr];
    }
  } else {
    noData.value = true;
  }
};

// 跳转事件详情
const toDetail = (id) => {
  uni.navigateTo({
    url: `/pages/shmPages/alarmEvents/detail?id=${id}`,
  });
};

// 计算 tabsIndex 和 alarmLevel 的映射
const getAlarmLevelByIndex = (index) => {
  const map = [0, 3, 2, 1];
  return map[index];
};
const getIndexByAlarmLevel = (level) => {
  const map = { 0: 0, 3: 1, 2: 2, 1: 3 };
  return map[level];
};

// 重置列表
const resetList = () => {
  alarmEventsList.value = [];
  searchForm.value.page = 1;
  noData.value = false;
  getList();
};

// 输入框事件
const handleSearch = (e) => {
  searchForm.value.uuidOrPointCode = e;
  console.log(searchForm.value);
  resetList();
};

// 清空输入框事件
const handleClear = () => {
  searchForm.value.uuidOrPointCode = null;
  resetList();
};

const tabsIndex = ref(1);
// 点击预警等级
const changeTab = (val) => {
  if (val === 0) {
    tabsIndex.value = 0;
  } else if (val === 1) {
    tabsIndex.value = 3;
  } else if (val === 2) {
    tabsIndex.value = 2;
  } else if (val === 3) {
    tabsIndex.value = 1;
  }
  // searchForm.value.alarmLevel = _cloneDeep(tabsIndex.value);
  searchForm.value.alarmLevel = _cloneDeep(val);
  scrollTop.value = 0;
  searchForm.value.uuidOrPointCode = null;
  console.log(
    "点击tab item",
    val,
    tabsIndex.value,
    searchForm.value.alarmLevel
  );
  // resetList();
  swiperChange(
    { detail: { current: tabsIndex.value, source: "touch" } },
    "auto"
  );
};

// 轮播滑动
const swiperChange = (e, type) => {
  let idx = 0;
  if (e.detail.current === 0) {
    idx = 0;
  } else if (e.detail.current === 1) {
    idx = 3;
  } else if (e.detail.current === 2) {
    idx = 2;
  } else if (e.detail.current === 3) {
    idx = 1;
  }
  searchForm.value.alarmLevel = idx;
  console.log(
    "swiperChange",
    type,
    e.detail.current,
    e.detail.source,
    idx,
    searchForm.value.alarmLevel
  );
  searchForm.value.uuidOrPointCode = null;
  scrollTop.value = 0;
  resetList();
};
const noData = ref(false);
/** 页面上拉 **/
const upper = () => {
  console.log("ggg");
  if (loading.value) return false;
  if (noData.value) {
    uni.showToast({
      icon: "none",
      title: "没有更多了~",
    });
    return;
  }
  searchForm.value.page++;
  getList();
};

// 初始化
const init = (isToday = false) => {
  // 获取当前日期和时间
  if (isToday) {
    let arr = [
      dayjs().format("YYYY-MM-DD 00:00:00"),
      dayjs().format("YYYY-MM-DD HH:mm:ss"),
    ];
    searchForm.value.alarmTimeStart = arr[0];
    searchForm.value.alarmTimeEnd = arr[1];
    searchForm.value.customTime = "";
  } else {
    const now = dayjs();
    const formattedDate = now.format("YYYY-MM");
    searchForm.value.customTime = formattedDate;
    searchForm.value.alarmTimeStart = "";
    searchForm.value.alarmTimeEnd = "";
    console.log(`当前年月: ${formattedDate}`);
  }
  // changeTab(3);
  getList();
};
searchForm.value.assetId;
onLoad(async (options) => {
  if (options.today) {
    await init(true);
  } else {
    await init();
  }
});
</script>
<style lang="scss" scoped>
.container {
  background: #f4f8ff;
  height: 100vh;
  //   overflow: hidden;
  //   padding: 24rpx 0;
  box-sizing: border-box;
}
.top_sticky {
  position: fixed;
  left: 0;
  top: 0;
  //   top: v-bind(systemBarHeight);
  background: #f4f8ff;
  width: 100%;
  z-index: 11;
  box-sizing: border-box;
  padding: 0 40rpx 20rpx 40rpx;
  .tabs-box {
    padding: 28rpx 0 48rpx 0;
    .tab-item {
      border-radius: 16rpx;
      height: 52rpx;
      line-height: 52rpx;
      width: 140rpx;
      text-align: center;
      margin-right: 24rpx;
      background: #fff;
      color: #8e8e8e;
      font-size: 28rpx;
      &:nth-child(1) {
        width: 112rpx;
      }
      &.active {
        background: #4378ff;
        color: #fff;
      }
    }
  }
  .time {
    color: #373737;
    font-size: 32rpx;
    font-weight: 500;
    .arrow {
      width: 28rpx;
      height: 28rpx;
      margin-left: 16rpx;
      vertical-align: middle;
    }
  }
}
.alarm-swiper {
  width: 100%;
  height: 100%;
  .no-data {
    text-align: center;
    font-size: 28rpx;
    color: #8e8e8e;
    margin-top: 40rpx;
  }
}
.list-box {
  padding-top: 268rpx;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}
.scroll-Y {
  height: calc(100vh - 268rpx);
}
// 设置nodata组件向下移一点
:deep(.content) {
  margin-top: 100rpx;
}
</style>
