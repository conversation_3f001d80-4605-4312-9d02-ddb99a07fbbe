<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-03 14:40:56
 * @Description: 
-->
<template>
  <!-- TabBar 内容，例如按钮、图标等 -->
  <view class="custom-tabbar">
    <view class="tab_item_box" @click="toTabbr('/pages/platform/index', 0)">
      <image
        v-if="tabBarStoreInfo.curActiveTab == 1"
        src="../static/shmStatic/icon/home_icon_20250403.png"
        class="icon"
      />
      <image
        v-else
        src="../static/shmStatic/icon/home_active_icon_20250403.png"
        class="icon"
      />
      <view
        :class="[
          'text',
          tabBarStoreInfo.curActiveTab == 0 ? 'active_text' : '',
        ]"
        >首页</view
      >
    </view>
    <view class="tab_item_box" @click="toTabbr('/pages/userCenter/index', 1)">
      <image
        v-if="tabBarStoreInfo.curActiveTab == 0"
        src="../static/shmStatic/icon/mine_icon_20250403.png"
        class="icon"
      />
      <image
        v-else
        src="../static/shmStatic/icon/mine_active_icon_20250403.png"
        class="icon"
      />
      <view
        :class="[
          'text',
          tabBarStoreInfo.curActiveTab == 1 ? 'active_text' : '',
        ]"
        >我的</view
      >
    </view>
  </view>
</template>

<script setup>
import { useTabBarStore } from "@/store/tabBar"; // 引入状态管理
const tabBarStoreInfo = useTabBarStore();

const toTabbr = (url, tabInd) => {
  if (tabBarStoreInfo.curActiveTab == tabInd) return;
  tabBarStoreInfo.updateActiveTab(tabInd);
  uni.redirectTo({
    url: url,
  });
};
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 98rpx;
  padding: 8rpx 0 10rpx 0;
  box-sizing: border-box;
  background: white;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0rpx -2rpx 20rpx 0rpx rgba(0, 0, 0, 0.06);
}
.tab_item_box {
  text-align: center;
  .icon {
    width: 44rpx;
    height: 44rpx;
  }
  .text {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #c7c7c7;
    line-height: 32rpx;
  }
  .active_text {
    color: #4378ff;
  }
}
</style>
