<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-11 09:25:54
 * @Description: 
-->
<template>
  <view>
    <uv-popup
      ref="popup"
      mode="bottom"
      round="24"
      @change="change"
      @touchmove.native.prevent="
        (e) => {
          e.preventDefault();
        }
      "
    >
      <view class="content">
        <view class="content-header" style="height: 112rpx">
          <view class="header-title"> 保养内容 </view>
          <view class="header-icon" @click="close()">
            <uv-icon name="close" size="16" bold></uv-icon>
          </view>
        </view>
        <view class="content-body">
          <view class="main-title-box">
            <view class="left">
              <view class="bar"> </view>
              <view class="title">{{ entity.name }}</view>
            </view>
          </view>

          <view class="title-box">
            <view class="title">单位 </view>
          </view>
          <view class="view-box">{{ entity.unitName }} </view>

          <view class="title-box">
            <view class="title">计划数量 </view>
            <view class="required">必填 </view>
          </view>
          <view class="input-box">
            <uv-input
              placeholder="请输入"
              type="number"
              border="surround"
              v-model="state1.planNumber"
              :customStyle="{
                height: '64rpx',
                background: '#FFFFFF',
                borderRadius: '8rpx',
                border: ' 2rpx solid #D9D9D9',
              }"
              @blur="inputBlur"
            ></uv-input>
          </view>

          <view class="title-box">
            <view class="title">单价 (元) </view>
          </view>
          <view class="view-box">{{ entity.unitPrice }} </view>

          <view class="plan-cost">
            <view class="title">预计花费： </view>
            <view class="num">¥{{ planCost }} </view>
            <view class="unit">元</view>
          </view>
        </view>
      </view>
      <view class="btn_box">
        <view class="delete-btn" @click="handleDelete">删 除</view>
        <view
          v-if="state1.planNumber === null || state1.planNumber === ''"
          class="comfirm_btn btn"
          style="background: #b0b0b0"
        >
          确 定
        </view>
        <view v-else class="comfirm_btn btn" @click="comfirm">确 定</view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { ref, reactive, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";

const emits = defineEmits(["contentChange"]);
const props = defineProps({
  taskId: {
    type: [String, Number],
    required: true,
    default: "",
  },
});
const state1 = reactive({
  planNumber: "",
});
const planCost = computed(() => {
  if (
    state1.planNumber === null ||
    state1.planNumber === "" ||
    entity.value.unitPrice === null ||
    entity.value.unitPrice === ""
  ) {
    return "-";
  } else {
    return (Number(state1.planNumber) * Number(entity.value.unitPrice)).toFixed(
      2
    );
  }
});
const resetState = () => {
  state1.remark = "";
  entity.value = {};
};
const inputBlur = () => {
  if (state1.planNumber === "") return;
  if (Number(state1.planNumber) < 0) {
    state1.planNumber = 0;
    return;
  }
  if (Number(state1.planNumber) > 99999.99) {
    state1.planNumber = 99999.99;
    return;
  }
  const num = parseFloat(state1.planNumber);
  if (!isNaN(num)) {
    state1.planNumber = num.toFixed(2);
  }
};
const comfirm = () => {
  emits(
    "contentChange",
    { ...entity.value, planNumber: state1.planNumber },
    "comfirm"
  );
  close();
};
const handleDelete = () => {
  emits("contentChange", entity.value, "delete");
  close();
};

// 打开弹窗
const popup = ref(null);
const entity = ref({});
const open = (record) => {
  entity.value = record;
  state1.planNumber = record.planNumber;
  popup.value.open();
};
const close = () => {
  popup.value.close();
  resetState();
};
const change = (e) => {
  if (!e.show) {
    resetState();
  }
};
onLoad(() => {});
defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.content {
  margin-bottom: 50rpx;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 112rpx;
    border-bottom: 2rpx solid #f2f2f2;
    padding: 0 40rpx;
    .header-title {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      font-size: 36rpx;
      color: #373737;
      line-height: 52rpx;
    }
    .header-icon {
    }
  }
  .content-body {
    padding: 0 40rpx;
    .main-title-box {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      margin: 32rpx 0;
      .left {
        display: flex;
        align-items: center;
        .bar {
          margin-right: 8rpx;
          width: 6rpx;
          height: 28rpx;
          background: #4378ff;
          border-radius: 4rpx;
        }
        .title {
          margin-right: 12rpx;
          font-size: 32rpx;
          font-weight: bold;
          color: #373737;
        }
      }
    }
    .title-box {
      display: flex;
      align-items: center;
      gap: 0 12rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      margin-bottom: 16rpx;
      .title {
        font-size: 32rpx;
        color: #373737;
      }
      .required {
        display: inherit;
        align-items: center;
        justify-content: center;
        width: 84rpx;
        height: 40rpx;
        font-size: 24rpx;
        color: #f06f6f;
        background: #fff0f0;
        border-radius: 8rpx;
      }
    }
    .view-box {
      display: flex;
      align-items: center;
      height: 64rpx;
      background: #f2f2f2;
      border-radius: 8rpx;
      margin-bottom: 40rpx;
      padding: 10rpx 24rpx;
    }
    .input-box {
      margin-bottom: 40rpx;
    }
    .plan-cost {
      display: flex;
      align-items: center;
      margin-top: 40rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      .num {
        font-family: Aldrich, Aldrich;
        font-size: 40rpx;
        color: #4378ff;
        margin: 0 10rpx;
      }
      .unit {
        align-self: flex-end;
      }
    }
  }
}
.btn_box {
  // padding: 0 40rpx 40rpx 40rpx;
  padding: 32rpx 40rpx;
  display: flex;
  justify-content: space-between;
  gap: 0 40rpx;
  .delete-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200rpx;
    height: 96rpx;
    background: #dddddd;
    border-radius: 8rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #62697b;
  }
  .btn {
    height: 96rpx;
    padding: 20rpx 0;
    box-sizing: border-box;
    text-align: center;
    border-radius: 4rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .comfirm_btn {
    width: 100%;
    background: #4378ff;
    color: #fff;
  }
}
</style>
