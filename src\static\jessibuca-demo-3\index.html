<!DOCTYPE html>
<html style="">
  <head>
    <meta charset="UTF-8" />
    <title>实时监控</title>
    <script src="./jessibuca.js"></script>
    <style>
      * {
        margin: 0;
      }
      html,
      body {
        background-color: antiquewhite;
        margin: 0;
        padding: 0;
        /* height: 37.5vh !important; */
      }
      .root {
        position: relative;
        height: 100vh;
      }

      #container {
        background: rgba(13, 14, 27, 0.7);
        width: 100vw;
        /* height: 37.5vh; */
        height: 100vh;
        /* .jessibuca-controls{
            height: 68px;
            background: rgba(10,10,10,0.4);
            .jessibuca-icon{
                width: 46px;
                height: 46px;
            }
        } */
      }
      #bottomInfo {
        position: absolute;
        bottom: 0;
        width: 100vw;
        height: 4rem;
        z-index: 1000;
        background: rgba(10, 10, 10, 0.4);
        display: flex;
        align-items: center;
        justify-content: start;
      }
      .monitor_status_icon {
        width: 2rem;
        height: 2rem;
        margin: 0 1.5rem 0 2rem;
      }
      .monitor_device_name {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 32rpx;
        color: #ffffff;
        line-height: 48rpx;
      }
      #pauseMask {
        position: absolute;
        top: 0;
        width: 100%;
        /* height: 37.5vh; */
        height: 100%;
        z-index: 999;
        /* background: rgba(10, 10, 10, 0.4); */
        display: flex;
        align-items: center;
        justify-content: center;
      }
      #pauseIcon {
        width: 8vw;
        height: 8vw;
      }
      #outlineNotice{
        color: #ffffff;
        font-size: 1.8rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
      }
      @media (max-width: 720px) {
        #container {
          width: 90vw;
          height: 52.7vw;
        }
      }
    </style>
  </head>
  <body>
    <div class="root" style="width: 100vw">
      <!-- 视频播放容器 -->
      <div id="container">
        <!-- 底部监控设备信息 -->
        <div id="bottomInfo">
          <img
            id="monitorStatusIconOnline"
            class="monitor_status_icon"
            src="../shmStatic/icon/monitor_online_icon_20250514.png"
            alt=""
          />
          <img
            id="monitorStatusIconOutline"
            class="monitor_status_icon"
            src="../shmStatic/icon/monitor_outline_icon_20250516.png"
            alt=""
          />
          <div id="monitorDeviceName" class="monitor_device_name"></div>
        </div>
        <!-- 暂停播放遮罩、按钮 -->
        <div id="pauseMask">
          <img
            id="pauseIcon"
            class="pause_icon"
            src="../shmStatic/icon/pause_status_icon_20250514.png"
            alt=""
          />
        </div>
        <div id="outlineNotice">设备离线，无法下发点播指令</div>
      </div>
    </div>

    <script>
      // 播放容器
      var $container = document.getElementById("container");
      // 暂停遮罩
      var $pauseMask = document.getElementById("pauseMask");
      // 设备在线图标
      var $monitorStatusIconOnline = document.getElementById("monitorStatusIconOnline");
      // 设备离线图标
      var $monitorStatusIconOutline = document.getElementById("monitorStatusIconOutline");
      // 设备名称
      var $monitorDeviceName = document.getElementById("monitorDeviceName");
      // 设备离线提示语
      var $outlineNotice = document.getElementById("outlineNotice");
      // 暂停按钮
      var $pauseIcon = document.getElementById("pauseIcon");
      // 设备信息
      var $bottomInfo = document.getElementById("bottomInfo");

      $monitorStatusIconOnline.style.display = "none";
      $monitorStatusIconOutline.style.display = "none";

      // 播放器实例
      var jessibuca = null;
      // 监控视频流地址
      // let vedioUrl = "ws://192.168.2.53:8586/rtp/34020000001320000001_34020000001320000001.live.flv";
      let vedioUrl = ""
      let deviceName = ""
      let deviceStatus = ""

      // 监听来自 UniApp 的推消息
      window.addEventListener('message', function(event) {
        console.log('收到父端数据：', event.data, typeof event.data);
        
        vedioUrl = event.data?.streamContent?.ws_flv || '' // 视频流地址

        deviceName = event.data.name // 监控设备名称
        $monitorDeviceName.textContent = deviceName;

        deviceStatus = event.data.deviceStatus // 监控设备状态
        if(deviceStatus == '1'){
          $monitorStatusIconOnline.style.display = 'block'
          $monitorStatusIconOutline.style.display = 'none'
        }else{
          $monitorStatusIconOnline.style.display = 'none'
          $monitorStatusIconOutline.style.display = 'block'
        }

        if(vedioUrl){
          $outlineNotice.style.display = "none";
          jessibuca.play(vedioUrl);
          jessibuca.setScaleMode(0);
        }else{
          $pauseMask.style.display = "none";
          $outlineNotice.style.display = "block";
          jessibuca.pause().finally(()=>{
            jessibuca.close()
            jessibuca.clearView()
          })
        }
      });


      function create() {
        jessibuca = new Jessibuca({
          __version__: "1.0.0",
          // url: $playHref.value,
          container: $container,
          videoBuffer: 0.2, // 缓存时长
          isResize: true,
          text: "",
          loadingText: "加载中",
          debug: true,
          showBandwidth: false, // 是否显示网速
          operateBtns: {
            fullscreen: false, // 是否显示全屏按钮
            screenshot: false, // 是否显示截图按钮
            play: false, // 是否显示播放暂停按钮
            audio: false, // 是否显示声音按钮
          },
          forceNoOffscreen: true, // 是否不使用离屏模式（提升渲染能力）（废弃）
          isNotMute: false, // 是否开启声音，默认是关闭声音播放的
          // supportDblclickFullscreen: true, // 是否支持屏幕的双击事件，触发全屏，取消全屏事件
        });

        jessibuca.onLog = (msg) => console.error(msg);
        jessibuca.onRecord = (status) => console.log("onRecord", status);
        jessibuca.onPause = () => console.log("onPause");
        jessibuca.onPlay = () => console.log("onPlay");
        jessibuca.onFullscreen = (msg) => console.log("onFullscreen", msg);
        jessibuca.onMute = (msg) => console.log("onMute", msg);
        $pauseMask.style.display = "none";
      }

      // setTimeout(() => {
      //   jessibuca.play(vedioUrl);
      // }, 1000);
      create();
      // 监听 暂停 按钮点击
      let clickTimer = null;
      $container.addEventListener(
        "click",
        function (e) {
          if(vedioUrl){
            // 每次单击先清除上一个定时器
            clearTimeout(clickTimer);
            clickTimer = setTimeout(() => {
              console.log("点击容器111", e.detail);
              jessibuca.pause();
              $pauseMask.style.display = "flex";
            }, 300);
          }
        },
        false
      );
      // 继续播放
      $pauseIcon.addEventListener(
        "click",
        function (e) {
          e.stopPropagation(); // 阻止事件冒泡到 #container
          jessibuca.play(vedioUrl);
          $pauseMask.style.display = "none";
        },
        false
      );
      // 双击全屏/取消全屏
      let isFullScreen = false;
      $container.addEventListener(
        "dblclick",
        function (e) {
          if(vedioUrl){
            // 双击时清除单击定时器
            clearTimeout(clickTimer);
            isFullScreen = !isFullScreen;
            if (isFullScreen) {
              jessibuca.setScaleMode(1);
              $bottomInfo.style.display = "none";
            } else {
              jessibuca.setScaleMode(0);
              $bottomInfo.style.display = "flex";
            }
            jessibuca.setFullscreen(isFullScreen);
          }
        },
        false
      );
    </script>
  </body>
</html>
