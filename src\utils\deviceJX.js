// 检查app的定位权限   android
export async function checkAppLocationPermission(platform) {
  return new Promise((resolve, reject) => {
    if (platform === "android") {
      try {
        // 获取当前应用的MainActivity实例，作为Android API调用的上下文环境；
        const main = plus.android.runtimeMainActivity();
        // 导入Android权限管理类，用于访问权限常量
        const PackageManager = plus.android.importClass(
          "android.content.pm.PackageManager"
        );
        // 调用Android API检查是否拥有精确定位权限，返回授权状态码
        const checkResult = main.checkSelfPermission(
          "android.permission.ACCESS_FINE_LOCATION"
        );
        // 对比返回的状态码与“已授权”常量
        const isGranted = checkResult === PackageManager.PERMISSION_GRANTED;
        resolve(isGranted);
      } catch (error) {
        reject(error);
      }
    } else if (platform === "ios") {
      try {
        const CLLocationManager = plus.ios.importClass("CLLocationManager");
        const authStatus = CLLocationManager.authorizationStatus();
        // iOS权限状态对照表
        const statusMap = {
          0: "kCLAuthorizationStatusNotDetermined", // 未决定
          1: "kCLAuthorizationStatusRestricted", // 受限制
          2: "kCLAuthorizationStatusDenied", // 已拒绝
          3: "kCLAuthorizationStatusAuthorizedAlways", // 始终允许
          4: "kCLAuthorizationStatusAuthorizedWhenInUse", // 使用时允许
        };
        console.log("iOS定位权限状态:", statusMap[authStatus]);
        // 判断是否已授权（包含两种授权状态）
        resolve(authStatus === 3 || authStatus === 4);
      } catch (err) {
        reject(`iOS权限检查异常: ${err}`);
      }
    }
  });
}

// 检查系统定位权限 android
export async function checkSystemLocationService(platform) {
  if (platform === "android") {
    // 导入Android系统服务相关类
    const Context = plus.android.importClass("android.content.Context");
    const LocationManager = plus.android.importClass(
      "android.location.LocationManager"
    );
    // 获取当前Activity实例
    const main = plus.android.runtimeMainActivity();
    // 获取系统定位服务管理器
    const locationManager = main.getSystemService(Context.LOCATION_SERVICE);
    // 检查GPS或网络定位是否开启
    return (
      locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
      locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
    );
  } else if (platform === "ios") {
    return new Promise((resolve) => {
      const CLLocationManager = plus.ios.importClass("CLLocationManager");
      const manager = CLLocationManager.alloc().init();
      const isEnabled = manager.locationServicesEnabled(); // 关键API
      plus.ios.deleteObject(manager);
      resolve(isEnabled);
    });
  }
}
