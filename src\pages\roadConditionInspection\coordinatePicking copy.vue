<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-09-20 14:22:23
 * @Description: 
-->
<template>
  <view class="container">
    <uv-search
      v-if="!isDetail"
      shape="square"
      searchIcon="../../static/icon/search_icon.png"
      searchIconSize="18"
      placeholder="地点搜索"
      placeholderColor="#A09F9F;"
      bgColor="#fff"
      :clearabled="false"
      :showAction="false"
      :customStyle="searchCustomStyle"
      :boxStyle="searchBoxStyle"
      :inputStyle="{ fontSize: '28rpx' }"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <!-- 搜索地址列表 -->
    <view
      class="address_list"
      overflow-y="scroll"
      v-if="showAddressList && !isDetail"
    >
      <view v-for="(item, index) in addressList" :key="index">
        <view class="top_line"></view>
        <view class="address_item" @click="chooseAddress(item)">
          <view class="left">
            <view class="address_name">{{ item.name }}</view>
            <view class="address_detail"
              >{{ item.address }}</view
            >
          </view>
          <!-- <view class="right">0米</view> -->
        </view>
      </view>
    </view>
    <view v-show="!showAddressList">
      <!-- 地图组件 -->
      <view class="map_box">
        <!-- :style="{width: '100%', height: `calc(100vh - ${isDetail?'104px':'164px'})`}" -->
        <!-- :style="{width: '100%', height: `580px`}" -->
        <map
          id="map"
          ref="mapRef"
          :style="{width: '100%', height: `calc(100vh - 328rpx)`}"
          :enable-poi="true"
          :enable-rotate="false"
          :longitude="myPosition.longitude"
          :latitude="myPosition.latitude"
          :markers="covers"
          @regionchange="regionchange"
        >
        </map>
        <cover-image
          class="reset_icon"
          src="../../static/icon/current_position.png"
          @click="resetPosition"
        ></cover-image>
      </view>
    </view>
    <view class="bottom_btn" v-show="!showAddressList">
      <template v-if="!isDetail">
        <view class="cur_address_desc">已选地址：{{ myPosition.address }}</view>
        <view class="comfrim_btn" @click="confirmAddress">确 定</view>
      </template>
      <template v-else>
        <view class="detail_address">
          <view class="cur_address_desc mb0">{{ myPosition.address }}</view>
          <image class="img" @click="goMap" src="../../static/icon/to_navigation_icon.png" />
        </view>
      </template>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref,computed } from "vue";
import { DataAcquisitionService } from "@/service";
import { onLoad, onReady } from "@dcloudio/uni-app";
import { getCurLocation, reverseGeocode,reTransformLngLat } from "@/utils/location";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();

// 获取当前定位
let myPosition = reactive({
  latitude: "",
  longitude: "",
  address: "",
});
// 地图上的标记点
let covers = computed(() => {
  return [
    {
      latitude: myPosition.latitude,
      longitude: myPosition.longitude,
      iconPath: "../../static/icon/cur_position.png",
    },
  ];
});
let curFormItemKey = ref("");
let isDetail = ref(false);
let flagStr = ref(""); // 用于判断当前的地址是拖动地图得到的（高德地图返回的），还是搜索列表里选择的（天地图api返回的）
onLoad((options) => {
  console.log("查看地址options", options);
  curFormItemKey.value = options?.unionKey;
  isDetail.value = options?.isDetail || false;
  // 查看详情
  if (isDetail.value) {
    myPosition.longitude = options.curLongitude;
    myPosition.latitude = options.curLatitude;
    myPosition.address = options.curAddress;
  } else {
    getLocation();
  }
});

onReady(() => {
  if (isDetail.value) {
    uni.setNavigationBarTitle({
      title: "事件详情",
    });
  }
});
// 搜索
const searchBoxStyle = reactive({
  height: "80rpx",
  borderRadius: "8px",
  boxSizing: "border-box",
});
const searchCustomStyle = reactive({
  width: "100%",
  height: "80rpx",
});

const inpVal = ref("");
const inpChange = (val) => {
  if (!val) {
    addressList.value = [];
    showAddressList.value = false;
    return;
  }
  inpVal.value = val;
  addressSearch();
};
const inpSearch = (val) => {
  if (!val) {
    addressList.value = [];
    showAddressList.value = false;
    return;
  }
  inpVal.value = val;
  addressSearch();
};
// 搜索地址
let addressList = ref([]);
let showAddressList = ref(false);
const addressSearch = () => {
  let params = {
    // location: `${myPosition.longitude},${myPosition.latitude}`,
    // radius: 50000,
    inpVal: inpVal.value,
  };
  // DataAcquisitionService.addressSearch(params).then((res) => {
  //   if (res.status == 1 && res.pois.length) {
  //     addressList.value = res.pois;
  //     showAddressList.value = true;
  //   } else if (res.status == 0) {
  //     uni.showToast({
  //       icon: "none",
  //       title: "请求地址失败，请稍后重试~",
  //     });
  //   }
  //   console.log("查看地址结果", addressList.value);
  // });
  DataAcquisitionService.getAddreeeList(params).then((res) => {
    if (res.pois && res.pois.length > 0) {
      addressList.value = res.pois.reduce((acc, current) => {
        let nameArr = acc.map(accitem=>accitem.name);
        if (!nameArr.includes(current.name)) {
          let obj = {
            name: current.name,
            address: current.province + current.city + current.county === current.address?current.address+current.name:current.province + current.city + current.county + current.address,
            location: current.lonlat
          }
          acc.push(obj);
        }
        return acc;
      }, []);
      showAddressList.value = true;
    } else if (res.area && res.area.lonlat) {
      addressList.value[0] = {
        name: res.area.name,
        address: null,
        location: res.area.lonlat
      }
      showAddressList.value = true;
    } else {
      addressList.value = []
    }
    console.log("查看地址结果", addressList.value);
  });
};

const chooseAddress = (addressItem) => {
  let locationArr = addressItem.location.split(",");
  let trans = reTransformLngLat(
    locationArr[0],
    locationArr[1]
  );
  myPosition.longitude = String(trans[0]);
  myPosition.latitude = String(trans[1]);
  myPosition.address = addressItem.address;
  flagStr.value += "t";
  console.log('选择列表地址',addressItem,myPosition);
  showAddressList.value = false;
  addressList.value = [];
};

let isGetCenterLatLong = ref(0);
const getLocation = async () => {
  // 初次渲染地图、不需要调用getCenterLatLong方法，以免影响初始定位准确性；
  isGetCenterLatLong.value++;
  // 定位开启状态 true=开启，false=未开启
  let bool = false;
  // android平台
  if (uni.getSystemInfoSync().platform == "android") {
    var context = plus.android.importClass("android.content.Context");
    var locationManager = plus.android.importClass(
      "android.location.LocationManager"
    );
    var main = plus.android.runtimeMainActivity();
    var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
    bool = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER);

    uni.getLocation({
      type: "gcj02", //返回可以用于uni.openLocation的经纬度
      success: function (res) {
        myPosition.latitude = res.latitude;
        myPosition.longitude = res.longitude;
        console.log("获取定位成功", res);
        transformAddress();
      },
    });
  }

  // 未开启定位功能
  if (bool === false) {
    uni.showModal({
      title: "提示",
      content: "请打开定位服务",
      success: ({ confirm, cancel }) => {
        if (confirm) {
          // android平台
          if (uni.getSystemInfoSync().platform == "android") {
            var Intent = plus.android.importClass("android.content.Intent");
            var Settings = plus.android.importClass(
              "android.provider.Settings"
            );
            var intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            var main = plus.android.runtimeMainActivity();
            main.startActivity(intent); // 打开系统设置GPS服务页面
          }
          // ios平台
          if (uni.getSystemInfoSync().platform == "ios") {
            var UIApplication = plus.ios.import("UIApplication");
            var application2 = UIApplication.sharedApplication();
            var NSURL2 = plus.ios.import("NSURL");
            var setting2 = NSURL2.URLWithString(
              "App-Prefs:root=Privacy&path=LOCATION"
            );
            application2.openURL(setting2);
            plus.ios.deleteObject(setting2);
            plus.ios.deleteObject(NSURL2);
            plus.ios.deleteObject(application2);
          }
        }
        // 用户取消前往开启定位服务
        if (cancel) {
          console.log("用户取消前往开启定位服务");
          // do sth...
        }
      },
    });
  }
};

// 地址逆解析
const transformAddress = async () => {
  if(flagStr.value=='t'||flagStr.value.length>1&&flagStr.value[flagStr.value.length-2]=='t'){
    return;
  }
  let address = await reverseGeocode(myPosition.longitude, myPosition.latitude);
  console.log("地址逆解析", address);
  if (address) {
    myPosition.address = address;
  }
};

// 拖动地图视野发生变化，保持定位图标始终在地图中心位置
const regionchange = (e) => {
  console.log("拖动地图视野发生变化",e, isGetCenterLatLong.value);
  if (isDetail.value) {
    return;
  }
  if (
    ["onRegionchange", "regionchange", "end"].includes(e.type) &&
    isGetCenterLatLong.value > 1
  ) {
    //在安卓中是 end 事件
    getCenterLatLong(); // 地图移动时获取中心点的经纬度
  }
  isGetCenterLatLong.value++;
};
let mapRef = ref(null);
let mapContext = ref(null);
mapContext.value = uni.createMapContext("map");
const getCenterLatLong = () => {
  mapContext.value.getCenterLocation({
    success: (res) => {
      console.log("中心位置", res);
      myPosition.latitude = res.latitude;
      myPosition.longitude = res.longitude;
      flagStr.value += "g";
      transformAddress();
    },
    fail: (error) => {
      console.log("中心位置失败", error);
    },
  });
};

// 点击地图右下角回到当前位置
const resetPosition = () => {
  console.log("点击地图右下角回到当前位置");
  if(isDetail.value){
    mapContext.value.moveToLocation({
      longitude: myPosition.longitude,
      latitude: myPosition.latitude,
    })
  }else{
    getLocation();
  }
};

// 确定选择的地址
const confirmAddress = () => {
  if (!myPosition.address) {
    uni.showToast({
      icon: "none",
      title: "请选择地址~",
    });
    return;
  }
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: curFormItemKey.value,
      value: myPosition.address,
      label: `${myPosition.longitude},${myPosition.latitude}`,
    },
  });
  uni.navigateBack({
    delta: 1,
  });
};




// 唤起本机地图软件
let curPosition = reactive({
  longitude: "",
  latitude: ""
})
const goMap = async () => {
  if (!myPosition.latitude || !myPosition.longitude || !myPosition.address) {
    uni.showToast({ title: '定位信息不完整', icon: 'none' })
    return
  }

  // 获取用户当前所处位置
  let locationRes = await getCurLocation();
  if(locationRes.errMsg == 'getLocation:ok'){
    curPosition.longitude = locationRes.longitude;
    curPosition.latitude = locationRes.latitude;
  }

  // 定义地图应用的名称和对应的包名/URL Scheme
  const mapNames = [
    {
      title: '高德地图',
      name: 'amap',
      androidName: 'com.autonavi.minimap',
      iosName: 'iosamap://'
    },
    {
      title: '百度地图',
      name: 'baidumap',
      androidName: 'com.baidu.BaiduMap',
      iosName: 'baidumap://'
    },
    {
      title: '腾讯地图',
      name: 'qqmap',
      androidName: 'com.tencent.map',
      iosName: 'qqmap://'
    }
  ]

  // 获取系统信息
  const platform = uni.getSystemInfoSync().platform

  // 判断设备上已安装的地图应用
  const buttons = mapNames.filter(item => {
    return platform === 'android'
      ? plus.runtime.isApplicationExist({ pname: item.androidName })
      : platform === 'ios' && plus.runtime.isApplicationExist({ action: item.iosName })
  })

  if (buttons.length) {
    // 显示选择菜单
    uni.showActionSheet({
      itemList: buttons.map(item => item.title),
      success: async (res) => {
        const selectedMap = buttons[res.tapIndex]
        await openURL(selectedMap, platform)
      },
      fail: (error) => {
        if(error.errMsg.indexOf('cancel')){
          console.log('取消',error);
        }else{
          uni.showToast({ title: '操作失败~', icon: 'none' })
        }
      }
    })
  } else {
    uni.showToast({ title: '请安装地图软件', icon: 'none' })
  }
}
// 打开指定的地图应用
const openURL = async (map, platform) => {
  const urls = {
    android: {
      amap: `amapuri://route/plan/?sid=&did=&dlat=${myPosition.latitude}&dlon=${myPosition.longitude}&dname=${myPosition.address}&dev=0&t=0`,
      baidumap: `baidumap://map/direction?origin=${curPosition.latitude},${curPosition.longitude}&destination=name:${myPosition.address}|latlng:${myPosition.latitude},${myPosition.longitude}&coord_type=wgs84&mode=driving`,
      qqmap: `qqmap://map/routeplan?type=drive&from=我的位置&to=${myPosition.address}&at=${myPosition.latitude},${myPosition.longitude}`
    },
    ios: {
      amap: `iosamap://path?sourceApplication=myApp&dlat=${myPosition.latitude}&dlon=${myPosition.longitude}&dname=${myPosition.address}&dev=0&t=0`,
      baidumap: `baidumap://map/direction?origin=${curPosition.latitude},${curPosition.longitude}&destination=name:${myPosition.address}|latlng:${myPosition.latitude},${myPosition.longitude}&mode=driving`,
      qqmap: `qqmap://map/routeplan?type=drive&from=我的位置&to=${myPosition.address}&at=${myPosition.latitude},${myPosition.longitude}`
    }
  }

  const url = encodeURI(urls[platform][map.name])
  plus.runtime.openURL(url, (res) => {
    if (res.message) {
      uni.showModal({
        title: '错误',
        content: res.message
      })
    }
  }, map.androidName || '')
}






</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f4f8ff;
}
.map_box {
  position: relative;
  // margin-top: -40rpx;
  .reset_icon {
    width: 80rpx;
    height: 80rpx;
    position: absolute;
    right: 40rpx;
    bottom: 40rpx;
  }
}

.bottom_btn {
  width: 100%;
  position: fixed;
  bottom: 0rpx;
  box-sizing: border-box;
  padding: 32rpx 40rpx 20rpx 40rpx;
  background: #f4f8ff;
  .cur_address_desc {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .mb0 {
    margin-bottom: 0rpx;
  }
  .comfrim_btn {
    width: 670rpx;
    height: 84rpx;
    box-sizing: border-box;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
  .plain_btn {
    margin-bottom: 28rpx;
    border: 2rpx solid #4378ff;
    background: #f4f8ff;
    color: #4378ff;
  }
  .detail_address {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 60rpx;
    .cur_address_desc {
      width: 520rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #4378ff;
      line-height: 40rpx;
    }
    .img {
      display: block;
      width: 72rpx;
      height: 72rpx;
    }
  }
}
.address_list {
  position: absolute;
  top: 80rpx;
  left: 0;
  padding: 0 24rpx;
  width: 100%;
  height: calc(100vh - 80rpx);
  box-sizing: border-box;
  background: #fff;
  .top_line {
    height: 2rpx;
    background: #d8d6d6;
  }
  .address_item {
    padding: 24rpx 0rpx;
    display: flex;
    justify-content: space-between;
    align-content: center;
    .left {
      .address_name {
        color: #373737;
        font-size: 32rpx;
      }
      .address_detail {
        color: #a09f9f;
        font-size: 22rpx;
        margin-top: 8rpx;
      }
    }
    .right {
      color: #666;
      font-size: 14rpx;
    }
  }
}
</style>
