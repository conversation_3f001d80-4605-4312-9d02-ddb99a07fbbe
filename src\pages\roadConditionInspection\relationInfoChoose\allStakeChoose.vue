<template>
  <view class="container">
    <uv-search
      height="36"
      shape="square"
      searchIcon="../../../static/icon/search_icon.png"
      searchIconSize="16"
      placeholder="输入桩号名称搜索"
      placeholderColor="#C1C1C1;"
      bgColor="#fff"
      :showAction="false"
      :boxStyle="searchStyle"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <!-- 列表 -->
    <view class="list_box" v-if="dataList.length">
      <view
        :class="['card', curItem.id === item.id ? 'active_card' : '']"
        v-for="item in dataList"
        :key="item.id"
        @click="chooseItem(item)"
      >
        <view class="item_title">{{ item.stakeName }}</view>
        <view class="item_info">{{ item.updownMarkName }}</view>
      </view>
    </view>
    <no-data class="nodata" v-else></no-data>
    <view class="bottom_box">
      <view class="choosed_data_show" v-if="curItem.stakeName"
        >已选 {{ curItem.stakeName }}({{ curItem.updownMarkName }})</view
      >
      <view class="btn" @click="confirm">确 定</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { onLoad, onReachBottom } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { DataAcquisitionService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

let unionKey = ref("");
let sectionId = ref("");
onLoad(async (options) => {
  console.log("options", options);
  unionKey.value = options.unionKey;
  sectionId.value = options?.sectionId || "";
  // 获取桩号列表
  await getList();
  await handleCurChoosed(options.curItemId);
});

// 处理回显当前已经选择的隧道
const handleCurChoosed = (curItemId) => {
  dataList.value.forEach((item) => {
    if (item.id === curItemId) {
      curItem.value = item;
    }
  });
};

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  marginBottom: "28rpx",
});

let inpVal = ref("");
const inpChange = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  dataList.value = [];
  getList();
};
const inpSearch = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  dataList.value = [];
  getList();
};

// 获取隧道列表
let dataList = ref([]);
let pageInfo = reactive({
  page: 1,
  limit: 10,
});
let noData = ref(false);
let loading = ref(false);
const getList = async () => {
  if (!sectionId.value) return;
  console.log("搜索框的值", inpVal.value);

  let params = {
    searchName: encodeURIComponent(inpVal.value),
    sectionId: sectionId.value,
    page: pageInfo.page,
    limit: pageInfo.limit,
  };
  loading.value = true;
  noData.value = false;
  const { code, data, total } = await DataAcquisitionService.getAllStakeList(
    params
  );
  console.log("请求的params", params);
  loading.value = false;
  console.log("所有桩号结果", data);

  // 如果接口异常，page-1，不执行后续操作
  if (code != 200) {
    pageInfo.page--;
    return;
  }
  dataList.value = dataList.value.concat(data);
  // 小于10条，加载完成
  if (data.length < pageInfo.limit) {
    noData.value = true;
  } else if (data.length === pageInfo.limit && total === pageInfo.limit) {
    noData.value = true;
  } else {
    noData.value = false;
  }
  console.log("请求桩号", dataList.value);
};

// 选择项目
let curItem = ref({});
const chooseItem = (item) => {
  curItem.value = item;
};

// 提交
const confirm = () => {
  if (!curItem.value.id) {
    uni.showToast({
      title: "请选择桩号",
      duration: 2000,
      icon: "none",
    });
    return false;
  }
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: unionKey.value,
      value: curItem.value.id,
      label: curItem.value.stakeName,
    },
  });
  uni.navigateBack({
    data: 1,
  });
};

/** 页面上拉 **/
onReachBottom(() => {
  if (noData.value) {
    uni.showToast({
      icon: "none",
      title: "没有更多了~",
    });
    return;
  }
  pageInfo.page++;
  getList();
});
</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 20rpx 40rpx;
  background: #f4f8ff;
}
.list_box {
  margin: 12rpx 0 200rpx 0;
  .card {
    margin-bottom: 28rpx;
    padding: 22rpx 28rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 16rpx;
    font-family: PingFang SC, PingFang SC;
    .item_title {
      font-weight: 400;
      color: #373737;
      font-size: 32rpx;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .item_info {
      font-size: 28rpx;
      color: #a09f9f;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .active_card {
    background: #4378ff;
    .item_title {
      color: #ffffff;
    }
    .item_info {
      color: #ffffff;
    }
  }
}
.nodata {
  margin-top: 136rpx;
}
.bottom_box {
  width: calc(100% - 80rpx);
  position: fixed;
  bottom: 0rpx;
  padding: 20rpx 0;
  background: #f4f8ff;
  .choosed_data_show {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .btn {
    width: 670rpx;
    height: 84rpx;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
}
</style>
