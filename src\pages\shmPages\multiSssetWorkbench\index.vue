<template>
  <view class="container">
    <uv-navbar
      title="结构健康监测"
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;z-index:11"
      leftIconColor="#fff"
      :bgColor="bgColor"
      @leftClick="goBack"
    >
    </uv-navbar>
    <view :scroll-top="0" scroll-y="true" class="scroll-Y">
      <view class="abnormal_tatistics">
        <view class="data-tatistics">
          <view
            v-for="(item, index) in healthLevel"
            :key="index"
            class="data-item"
            @click.stop="handleChange(item.healthCode)"
          >
            <view class="num" :class="'num' + item.healthCode">{{
              item.num
            }}</view>
            <text
              class="name"
              :class="
                activeHealthCode === item.healthCode
                  ? 'active' + item.healthCode
                  : ''
              "
              >{{ item.name }}</text
            >
          </view>
        </view>
        <view class="asset-tatistics">
          <uv-search
            placeholder="搜索资产名称"
            shape="square"
            :showAction="false"
            v-model="searchvalue"
            @search="handleSearch"
            @clear="handleClear"
          ></uv-search>
          <template v-if="assetInfo.length > 0">
            <view class="asset-box">
              <uv-scroll-list :indicator="false">
                <view
                  class="asset-item"
                  v-for="(item, index) in assetInfo"
                  :key="index"
                  :class="
                    activeHealthCode === item.healthCode
                      ? 'health' + item.healthCode
                      : ''
                  "
                  @click.stop="handleAssetDetail(item)"
                >
                  <!-- :class="'health' + item.healthCode" -->
                  <view class="img_box">
                    <image
                      v-if="item.picture"
                      class="img"
                      :src="showImg(item.picture)"
                      mode="aspectFit"
                      radius="8"
                    >
                    </image>
                    <image
                      v-else
                      class="nodata_img"
                      :src="noDataImg"
                      mode="aspectFit"
                      radius="8"
                    >
                    </image>
                    <view class="asset-name ellipsis-one">{{
                      item.assetName
                    }}</view>
                  </view>
                </view>
              </uv-scroll-list>
            </view>
          </template>
          <view class="no-data" v-else>
            <image class="img" :src="noDataImg" mode="aspectFit" />
            <view class="text">暂无资产</view>
          </view>
        </view>
      </view>
      <view class="card-box mb28">
        <view class="same-add-title">报警次数月度统计</view>
        <NumberAlarms />
      </view>
      <view class="card-box">
        <view class="same-add-title">设备在线率月度统计</view>
        <EquipmentOnlineRate />
      </view>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onPageScroll } from "@dcloudio/uni-app";
import { ref } from "vue";
import { getSysteminfo, showImg } from "@/utils";
import { AssetService } from "@/service";
import { cloneDeep as _cloneDeep } from "lodash";
import { useUserStore } from "@/store/user";
const userStore = useUserStore();
import noDataImg from "@/static/shmStatic/image/asset-nodata-img.png";
import NumberAlarms from "./components/NumberAlarms.vue";
import EquipmentOnlineRate from "./components/EquipmentOnlineRate.vue";
// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;

// 左上角返回
const goBack = () => {
  console.log("结构健康监测返回", getCurrentPages());
  uni.navigateBack({
    delta: 1,
  });
};

console.log(systemBarHeight);
const healthLevel = ref([
  {
    healthCode: "0",
    name: "基本完好",
    num: "0", // 数量
  },
  {
    healthCode: "1",
    name: "轻度异常",
    num: "0", // 数量
  },
  {
    healthCode: "2",
    name: "中等异常",
    num: "0", // 数量
  },
  {
    healthCode: "3",
    name: "重度异常",
    num: "0", // 数量
  },
]);
const assetInfo = ref([]); //资产列表
const assetInfoCopy = ref([]); //资产列表，搜索的时候使用
const searchvalue = ref(""); //搜索字段
// 输入框搜索
const handleSearch = (value) => {
  console.log(value);
  if (value && value.length > 0) {
    // 选中了健康度需要判断健康度和搜索条件是否匹配
    if (activeHealthCode.value != null) {
      assetInfo.value = assetInfoCopy.value.filter(
        (item) =>
          item.assetName.includes(value) &&
          item.healthCode === activeHealthCode.value
      );
    } else {
      assetInfo.value = assetInfoCopy.value.filter((item) =>
        item.assetName.includes(value)
      );
    }
  } else {
    if (activeHealthCode.value != null) {
      handleChange(activeHealthCode.value);
    } else {
      assetInfo.value = _cloneDeep(assetInfoCopy.value);
    }
  }
};
// 清空
const handleClear = () => {
  if (activeHealthCode.value != null) {
    handleChange(activeHealthCode.value);
  } else {
    assetInfo.value = _cloneDeep(assetInfoCopy.value);
  }
};
const activeHealthCode = ref(null);
// 点击健康度
const handleChange = (healthCode) => {
  console.log(healthCode);
  // 再次点击当前健康度等级，则取消选中状态
  if (activeHealthCode.value === healthCode) {
    activeHealthCode.value = null;
    assetInfo.value = assetInfoCopy.value;
    return;
  }
  if (assetInfoCopy.value.length === 0) return false;
  activeHealthCode.value = healthCode;
  searchvalue.value = null;
  assetInfo.value = assetInfoCopy.value.filter(
    (item) => item.healthCode === healthCode
  );
};

// 获取健康度及资产统计数据
const getAssetLevel = () => {
  AssetService.healthList().then((res) => {
    console.log(res);
    let resHealthLevel = res.data.healthLevel ? res.data.healthLevel : [];
    let data = res.data.assetInfo ? res.data.assetInfo : [];
    assetInfo.value = data;
    assetInfoCopy.value = _cloneDeep(data);
    healthLevel.value.map((item) => {
      let filterItem = resHealthLevel.filter(
        (i) => i.healthCode === item.healthCode
      );
      if (filterItem.length > 0) {
        item.num = filterItem[0].num;
      }
    });
  });
};

// 跳转单资产
const handleAssetDetail = (item) => {
  let params = {
    objectName: item.assetName,
    objectId: item.assetId,
    type: null,
    typeName: null,
    sectionId: null,
    bridgeAlias: null,
    mainBridgeType: null,
    bridgeFrontPhoto: item.picture,
    maintainUnit: null,
  };
  userStore.updateAssetInfo(params);
  uni.navigateTo({
    url: `/pages/shmPages/singleAssetWorkbench/index`,
  });
};
// 自定义导航栏背景色
// 监听页面滚动，修改导航栏背景色
let bgColor = ref("transparent");
onPageScroll((e) => {
  if (e.scrollTop > 35) {
    bgColor.value = "#4383F8";
  } else {
    bgColor.value = "transparent";
  }
});

onLoad(async () => {
  console.log("homei请求数据");
  await getAssetLevel();
});
</script>

<style lang="scss" scoped>
:deep(.uv-search__content) {
  border-radius: 16rpx !important;
}
:deep(.uv-scroll-list) {
  padding-bottom: 0 !important;
}
// 超出省略
.ellipsis-one {
  display: -webkit-box;
  /**对象作为伸缩盒子模型展示**/
  -webkit-box-orient: vertical;
  /**设置或检索伸缩盒子对象的子元素的排列方式**/
  -webkit-line-clamp: 1;
  /**显示的行数**/
  overflow: hidden;
  /**隐藏超出的内容**/
  text-overflow: ellipsis;
  /*超出内容显示为省略号*/
  word-break: break-all;
}
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding: v-bind(systemBarHeight) 0 40rpx 0;
  background-image: url("/static/shmStatic/image/shm-home-bg.png");
  background-color: #e3edff;
  background-size: cover;
  background-repeat: no-repeat;
  .scroll-Y {
    margin-top: v-bind(systemBarHeight);
    height: calc(100% - v-bind(systemBarHeight));
    overflow: auto;
    .abnormal_tatistics {
      padding: 0 40rpx;
      .data-tatistics {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        padding: 32rpx 38rpx;
        margin-bottom: 28rpx;
        border-radius: 24rpx;
        box-shadow: 4rpx 4rpx 24rpx 0 rgba(88, 139, 255, 0.2),
          -4rpx -4rpx 24rpx 0 rgba(88, 139, 255, 0.2);
        .num {
          font-size: 48rpx;
          line-height: 56rpx;
          height: 56rpx;
          text-align: center;
          font-weight: 700;
          margin-bottom: 10rpx;
          &.num0 {
            color: #48c471;
          }
          &.num1 {
            color: #ffbf00;
          }
          &.num2 {
            color: #ff7d26;
          }
          &.num3 {
            color: #ff2d2d;
          }
        }
        .name {
          font-size: 28rpx;
          line-height: 40rpx;
          height: 40rpx;
          text-align: center;
          color: #373737;
          font-weight: 400;
          &.active0 {
            color: #48c471;
          }
          &.active1 {
            color: #ffbf00;
          }
          &.active2 {
            color: #ff7d26;
          }
          &.active3 {
            color: #ff2d2d;
          }
        }
      }
      .asset-tatistics {
        padding: 28rpx;
        margin-bottom: 28rpx;
        border-radius: 24rpx;
        background-color: #fff;
        box-shadow: 4rpx 4rpx 24rpx 0 rgba(88, 139, 255, 0.2),
          -4rpx -4rpx 24rpx 0 rgba(88, 139, 255, 0.2);
        .asset-box {
          margin-top: 24rpx;
          .asset-item {
            width: 250rpx;
            height: 216rpx;
            border-radius: 24rpx;
            background-color: rgba(242, 242, 242, 1);
            margin-right: 28rpx;
            position: relative;
            box-sizing: border-box;
            border: 1rpx solid #f2f2f2;
            &.health0 {
              border: 1rpx solid #48c471;
            }
            &.health1 {
              border: 1rpx solid #ffbf00;
            }
            &.health2 {
              border: 1rpx solid #ff7d26;
            }
            &.health3 {
              border: 1rpx solid #ff2d2d;
            }
            .img_box {
              width: 250rpx;
              height: 216rpx;
              .img {
                width: 250rpx;
                height: 216rpx;
                // width: 100%;
                // height: 100% !important;
                border-radius: 24rpx;
                :deep(div) {
                  background-size: cover !important;
                }
              }
              .nodata_img {
                width: 250rpx;
                height: 180rpx;
              }
              .asset-name {
                background: rgba(10, 10, 10, 0.6);
                height: 48rpx;
                line-height: 48rpx;
                padding: 0 10rpx;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 248rpx;
                box-sizing: border-box;
                border-radius: 0 0 24rpx 24rpx;
                text-align: center;
                color: #fff;
                font-size: 24rpx;
              }
            }
          }
        }
        .no-data {
          width: 100%;
          padding: 24rpx 0;
          .img {
            margin: 0 auto;
            display: block;
            width: 160rpx;
            height: 160rpx;
          }
          .text {
            text-align: center;
            font-size: 28rpx;
            color: #a09f9f;
          }
        }
      }
    }
    .card-box {
      margin: 0 40rpx 28rpx 40rpx;
      .same-add-title {
        font-size: 28rpx !important;
        font-weight: 500;
      }
    }
  }
}
</style>
