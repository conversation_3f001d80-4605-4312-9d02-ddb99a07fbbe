<template>
  <view>
    <uv-popup
      ref="popup"
      mode="bottom"
      round="8"
      @change="change"
      :customStyle="customStyle"
    >
      <view class="box">
        <view class="title">请添加备注</view>
        <uv-textarea
          disabledColor="#ffffff"
          height="170"
          placeholder="请输入备注（最多150字）"
          :textStyle="{fontSize: '28rpx'}"
          :count="true"
          :maxlength="150"
          v-model="inpVal"
          @blur="onBlur"
        >
        </uv-textarea>
        <view class="btn_box">
          <view class="confirm btn" @click="onConfirm">确认</view>
        </view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { reactive, ref, watch } from "vue";

const props = defineProps({
  curRemark:{
    type: String,
    default:''
  }
})



const inpVal = ref(props.curRemark);

// 监听 props 的变化，并更新 localValue
watch(() => props.curRemark, (newVal) => {
  inpVal.value = newVal;
});

const customStyle = reactive({
  // width: "608rpx",
  padding: "48rpx 44rpx",
  boxSizing: "border-box",
});




const emit = defineEmits(["onRemarkCallback"]);

const onBlur = (env) => {
  console.log('env',env);
  inpVal.value = env.detail.value;
};
// 取消
const onConfirm = () => {
  emit("onRemarkCallback",{remark: inpVal.value});
};
// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const change = (e) => {
  console.log("弹窗状态改变：", e);
};

defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.box {
  box-sizing: border-box;
  .title{
    text-align: center;
    border-bottom: 2rox solid #F0F0F0;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #373737;
    line-height: 44rpx;
  }
  :deep(.uv-textarea){
    margin-top: 40rpx;
  }
  .btn_box {
    margin-top: 48rpx;
    .btn {
      // width: 520rpx;
      width: 100%;
      height: 72rpx;
      border-radius: 8rpx;
      padding: 14rpx 0;
      font-weight: 600;
      font-size: 32rpx;
      line-height: 44rpx;
      text-align: center;
      margin-bottom: 28rpx;
      box-sizing: border-box;
    }
    .confirm {
      background: #4378ff;
      color: #ffffff;
    }
  }
}
</style>
