<template>
  <uv-popup
    ref="popupRef"
    mode="bottom"
    :closeOnClickOverlay="false"
    round="48rpx"
    custom-style="height: 60vh;"
  >
    <view class="content-box">
      <view class="title-box">
        <text class="title">选择资产</text>
        <image
          src="../../../../static/shmStatic/icon/close-icon.png"
          mode="scaleToFill"
          class="close-icon"
          @click="close"
        />
      </view>
      <view class="tab-box">
        <uv-tabs :list="list" @click="changeTab"></uv-tabs>
      </view>
      <template v-if="itemArr && itemArr.length > 0">
        <scroll-view
          ref="listView"
          class="list-cell-box"
          @scroll="scrollHandler"
          :scroll-y="true"
          :scroll-top="scrollTop"
          use-compositing
        >
          <view
            v-for="(item, index) in itemArr"
            :key="index"
            class="list-cell"
            :id="'section-' + item.letterType"
          >
            <view :class="['title', index == 0 ? 'first_title' : '']">{{
              item.letterType
            }}</view>
            <view
              v-for="(temp, m) in item.assetDataList"
              :key="m"
              class="list-cell-item"
              @click="changeItem(temp)"
              :class="{
                active: activeItem && activeItem.objectId === temp.objectId,
              }"
              >{{ temp.objectName }}</view
            >
          </view>
          <view class="index-box">
            <view
              v-for="(item, n) in indexList"
              :key="n"
              class="index"
              @click="scrollToSection(n)"
              :class="{ active: indexActive === '#section-' + item }"
              >{{ item }}</view
            >
          </view>
        </scroll-view>
      </template>
      <view>
        <ylg-nodata
          :imgStyle="{
            width: '420rpx',
          }"
          textSize="30rpx"
          textMarginT="0rpx"
        />
      </view>
    </view>
    <view class="bottom-box flex-center">
      <uv-button
        type="primary"
        text="确 定"
        :custom-style="customStyle"
        @click="submit"
      ></uv-button>
    </view>
  </uv-popup>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { AssetService } from "@/service";
import { useUserStore } from "@/store/user";
const userStore = useUserStore();
const popupRef = ref(null);
const customStyle = ref({
  width: "100%",
  height: "84rpx",
  background: "#4378FF",
  boxSizing: "border-box",
});

const list = ref([
  { name: "桥梁", value: "5" },
  { name: "隧道", value: "6" },
  { name: "涵洞", value: "7" },
]);

const indexList = ref([]);
const itemArr = ref([]);

const listView = ref(null);

const sectionHeights = ref([]);
const scrollTop = ref(0);
const calculateSectionHeights = () => {
  try {
    // const query = uni.createSelectorQuery();
    console.log("查看itemArr", itemArr.value);
    const query = uni.createSelectorQuery();
    query
      .selectAll(".list-cell")
      .boundingClientRect((rects) => {
        // rects是一个数组，对应页面上所有 .list-cell 节点
        // 它们的顺序与DOM顺序一致
        const temp = [];
        rects.forEach((rect, i) => {
          // rect.top: 该分组相对于scroll-view可视区域顶端的距离
          // rect.height: 分组本身的高度
          // 由于你在 template 中是 v-for="(item, index) in itemArr"
          // rects[i] 对应 itemArr[i] ，可以直接通过 i 获取 letterType
          const letter = itemArr.value[i].letterType;
          temp.push({
            letterType: `#section-${letter}`,
            top: rect.top - topHeight.value - 9, // 分组顶部
            bottom: rect.top + rect.height, // 分组底部
            height: rect.height,
          });
        });

        // 按照 top 从小到大排序（一般 rects 返回顺序就和DOM一致，不排也OK）
        temp.sort((a, b) => a.top - b.top);
        sectionHeights.value = temp;
      })
      .exec();
  } catch (error) {
    console.log("calculateSectionHeights error", error);
  }
};

let topHeight = ref(0);
const calculateFirstTitleTop = () => {
  uni
    .createSelectorQuery()
    .select(`.first_title`)
    .boundingClientRect((data) => {
      if (data) {
        console.log("查看第一个title的高度", data);
        topHeight.value = data.top;
      }
    })
    .exec();
};

// 滚动事件
const scrollHandler = (e) => {
  scrollTop.value = e.detail.scrollTop;
  console.log("滚动", scrollTop.value, sectionHeights.value);
  // 遍历顺序：从最后一个往前找，或从前往后都行
  // 只要逻辑保持一致即可
  let activeLetter = "";
  for (let i = sectionHeights.value.length - 1; i >= 0; i--) {
    // 如果当前 scrollTop >= 分组的top
    if (scrollTop.value >= sectionHeights.value[i].top) {
      if (!indexActive1.value) {
        activeLetter = sectionHeights.value[i].letterType;
      } else {
        activeLetter = indexActive1.value;
        indexActive1.value = "";
      }
      break;
    }
  }
  // 如果都没找到，可能在最顶部
  if (!activeLetter && sectionHeights.value.length) {
    activeLetter = sectionHeights.value[0].letterType;
  }

  indexActive.value = activeLetter;
};

let indexActive = ref("");
let indexActive1 = ref("");
// 索引点击事件
const scrollToSection = (index) => {
  let filterItem = sectionHeights.value.filter(
    (item) => item.letterType === `#section-${itemArr.value[index].letterType}`
  );
  if (filterItem.length > 0) {
    nextTick(() => {
      // scrollTop.value = filterItem[0].cumulativeHeight;
      scrollTop.value = filterItem[0].top;
      indexActive.value = `#section-${itemArr.value[index].letterType}`;
      indexActive1.value = `#section-${itemArr.value[index].letterType}`;
    });
  }
};

// tab切换
const changeTab = (e) => {
  console.log(e);
  type.value = e.value;
  getSelectList();
};
const type = ref("5");
const getSelectList = () => {
  scrollTop.value = 0;
  itemArr.value = [];
  indexList.value = [];
  sectionHeights.value = [];
  indexActive.value = null;
  AssetService.assetDataListByLetterType({ type: type.value })
    .then((res) => {
      if (res.data.length) {
        res.data.forEach((item) => {
          if (item.letterType === "#") {
            item.letterType = "0-9";
          }
        });
      }
      itemArr.value = res.data ? res.data : [];
      if (itemArr.value.length > 0) {
        indexList.value = itemArr.value.map((item) => item.letterType);
        indexActive.value = "#section-" + itemArr.value[0].letterType;
        setTimeout(() => {
          calculateFirstTitleTop();
          calculateSectionHeights();
        }, 800);
      }
    })
    .catch(() => {
      itemArr.value = [];
    });
};

// 打开
const open = () => {
  popupRef.value.open();
  activeItem.value = userStore.assetInfo;

  getSelectList();
};
// 关闭
const close = () => {
  popupRef.value.close();
  emits("closeAssetPicker");
};
const activeItem = ref(null);
// 选择资产
const changeItem = (cell) => {
  activeItem.value = cell;
};

const emits = defineEmits(["updateData", "closeAssetPicker"]);
const submit = () => {
  userStore.updateAssetInfo(activeItem.value);
  emits("updateData", true);
};

defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.title-box {
  padding: 28rpx;
  font-weight: bold;
  color: #373737;
  font-size: 32rpx;
  height: 44rpx;
  line-height: 44rpx;
  text-align: center;
  position: relative;
  .close-icon {
    position: absolute;
    top: 24rpx;
    right: 28rpx;
    width: 32rpx;
    height: 32rpx;
  }
}
.tab-box {
  padding: 0 4rpx;
  position: relative;
  z-index: 11;
  background: #fff;
  &::after {
    content: "";
    width: 100%;
    height: 2rpx;
    background-color: #f2f2f2;
    position: absolute;
    bottom: 3rpx;
    left: 0;
  }
}
.list-cell-box {
  padding: 0 70rpx 28rpx 32rpx;
  height: 41vh;
  position: relative;
  box-sizing: border-box;
  -webkit-transform: translate3d(0, 0, 0); /* 启用硬件加速 */
  transform: translate3d(0, 0, 0);
  .list-cell {
    width: 100%;
    .title {
      height: 64rpx;
      line-height: 64rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #a09f9f;
      border-bottom: 2rpx solid #f2f2f2;
      position: sticky;
      top: 0;
      background: #f2f2f2;
      padding-left: 20rpx;
    }
    .list-cell-item {
      height: 64rpx;
      line-height: 64rpx;
      color: #404040;
      font-size: 28rpx;
      padding-left: 20rpx;
      border-bottom: 2rpx solid #f2f2f2;
      &.active {
        background: #e0e9ff;
        color: #4378ff;
      }
    }
  }
  .index-box {
    position: fixed;
    right: 10rpx;
    top: 50%;
    transform: translateY(-50%);
    .index {
      width: 46rpx;
      height: 46rpx;
      text-align: center;
      line-height: 46rpx;
      border-radius: 50%;
      color: #a09f9f;
      font-size: 24rpx;
      &.active {
        background: #4378ff;
        color: #fff;
      }
    }
  }
}
.bottom-box {
  width: 100%;
  box-sizing: border-box;
  padding: 18rpx 40rpx;
  height: 120rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 0;
  left: 0;
}
</style>
