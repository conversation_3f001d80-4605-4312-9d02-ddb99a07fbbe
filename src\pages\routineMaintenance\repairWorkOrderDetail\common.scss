// 通用变量
$primary-color: #4378ff;
$text-primary: #373737;
$text-secondary: #404040;
$text-muted: #a09f9f;
$font-family:
  PingFang SC,
  PingFang SC;
$border-radius: 24rpx;
$box-shadow: 4rpx 4rpx 20rpx 0 rgba(0, 0, 0, 0.08);

.flex-row-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.blue-bar {
  margin-right: 8rpx;
  width: 6rpx;
  height: 28rpx;
  background: #4378ff;
  border-radius: 4rpx;
}
.bar-text {
  font-family: $font-family;
  font-weight: 500;
  font-size: 32rpx;
  color: $text-primary;
}
.status-label {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 44rpx;
  font-family: $font-family;
  font-weight: 400;
  font-size: 24rpx;
  border-radius: 0 16rpx 0 16rpx;
}
.status-label.pending {
  color: #5c9ef7;
  background: #e2eeff;
}
.status-label.to-do {
  color: #ff9e26;
  background: #fff3e4;
}
.status-label.in-progress {
  color: #5771f3;
  background: #eaf0ff;
}
.status-label.verify {
  color: #b97dee;
  background: #f8efff;
}
.status-label.completed {
  color: #63c960;
  background: #e8f9e7;
}
