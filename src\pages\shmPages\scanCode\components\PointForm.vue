<template>
  <scroll-view scroll-y="true" class="content">
    <ylg-auto-form
      ref="autoFormRef"
      :isAllDisabled="false"
      :isFromDetail="false"
      btnLoading=""
      :formConfig="formConfig"
      :formData="localFormData"
      :rules="rules"
      :labelStyle="labelStyle"
      :formItemStyle="formItemStyle"
      :placeholderStyle="placeholderStyle"
      @onChange="onFormInpChange"
      @onRightSlot="onRightSlot"
      @onDictChange="onFormDictChange"
      @handleTouchMove="handleTouchMove"
    >
      <template #bottomBtns>
        <view class="btns flex-between-center">
          <view class="btn" v-if="!isLast" @click="cancelAddPoint"
            >取消创建</view
          >
          <view class="btn disabled_btn" v-else>取消创建</view>
          <view class="btn primary_btn" @click="addPoint">保存并继续创建</view>
        </view>
      </template>
    </ylg-auto-form>
    <view class="black" style="height: 100rpx"></view>
    <uv-toast ref="toast"></uv-toast>
  </scroll-view>
</template>
<script setup>
import { ref, reactive, computed, watch } from "vue";
import { onLoad, onShow, onBackPress } from "@dcloudio/uni-app";
import { cloneDeep as _cloneDeep } from "lodash";
import { showImg, findItemByComponentTypeId } from "@/utils";
import { ShmService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();

const props = defineProps({
  stateData: {
    type: Object,
    required: true,
    default: () => {},
  },
  // 资产桥梁类型
  propertyBridgeType: {
    type: String,
    required: true,
    default: "",
  },
  // 资产别名
  propertyBridgeAlias: {
    type: String,
    required: true,
    default: "",
  },
  formData: {
    type: Object,
    required: true,
    default: () => {},
  },
  choosedMonitorItems: {
    type: Array,
    required: true,
    default: () => [],
  },
  isLast: {
    type: Boolean,
    required: true,
    default: false,
  },
  pointIndex: {
    type: Number,
    required: true,
    default: 0,
  },
});

let toast = ref(null);

const handleTouchMove = ({ e }) => {
  // 阻止事件冒泡，不让 swiper 处理
  e.stopPropagation();
};

onLoad(() => {
  // 1清空pinia中缓存的之前选择的数据项
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: "",
      value: "",
      label: "",
    },
  });
});

// 截面序号列表
let sectionNumberOptions = ref([]);
let localFormData = ref({ ..._cloneDeep(props.formData) });
watch(
  () => props.formData,
  (newval, oldval) => {
    if (newval) {
      localFormData.value = newval;
    }
  },
  { immediate: true, deep: true }
);
const rules = computed(() => {
  return {
    pointName: {
      type: "string",
      required: true,
      message: "请输入测点名称",
      trigger: ["blur"],
    },
    pointCode: {
      type: "string",
      required: true,
      message: "请输入测点编码",
      trigger: ["blur"],
    },
    monitorItemLabel: {
      type: "string",
      required: true,
      message: "请选择监测项",
      trigger: ["blur", "change"],
    },
    monitorInfo: {
      type: "array",
      required: props.propertyBridgeType ? true : false,
      message: "请选择监测类别和内容",
      trigger: ["blur", "change"],
    },
    locationIdLabel: {
      type: "string",
      required: true,
      message: "请选择监测位置",
      trigger: ["blur", "change"],
    },
  };
});
const formConfig = computed(() => {
  console.log("查看stateData", props.stateData);
  return [
    {
      items: [
        {
          type: "input",
          modelType: "model",
          label: "测点名称",
          placeholder: "请输入（最多10字）",
          maxlen: 10,
          unionKey: "pointName",
        },
        props.propertyBridgeType && {
          type: "treeSelect",
          label: "关联构件",
          placeholder: "请选择（非必填）",
          pickerTextW: "310rpx",
          map: { text: "name", value: "id" },
          hasOptions: props.stateData.componentOptions?.length,
          toastMsg:'当前资产暂无关联构件~',
          unionKey: "assetContentDTO.componentId",
          options: props.stateData.componentOptions,
        },
        props.propertyBridgeType && {
          type: "select",
          modelType: "model",
          label: "截面序号",
          placeholder: "请选择（非必填）",
          unionKey: "sectionNumberIdLabel",
          options: sectionNumberOptions.value,
          pathKey: "assetContentDTO.componentId",
          isNeedPathKeyValue: true,
          toastMsg: "请先选择关联构件~",
        },
        {
          type: "input",
          modelType: "model",
          label: "测点编码",
          placeholder: "请输入（最多20字）",
          maxlen: 20,
          slotText: props.propertyBridgeType ? "生成" : null,
          slotTextDis: isAutoGenerateDis.value,
          unionKey: "pointCode",
        },
        {
          type: "select",
          modelType: "model",
          label: "监测项",
          placeholder: "请选择",
          unionKey: "monitorItemLabel",
          options: props.stateData.monitorOptions || [],
        },
        props.propertyBridgeType && {
          type: "treeSelect",
          label: "监测类别和内容",
          placeholder: "请选择",
          pickerTextW: "310rpx",
          unionKey: "monitorInfo",
          hasOptions: props.stateData[props.propertyBridgeType]?.length,
          toastMsg:'当前资产暂无监测类别和内容~',
          map: { text: "label", value: "value" },
          options: props.stateData[props.propertyBridgeType] || [],
        },
        {
          type: "select",
          modelType: "model",
          label: "关联监测位置",
          placeholder: "请选择",
          borderBottom: false,
          unionKey: "locationIdLabel",
          options: props.stateData.locationImgOptions,
        },
        localFormData.value.locationIdLabel && {
          type: "additionalImg",
          label: "",
          urlPath: showImg(localFormData.value.locationImg),
          unionKey: "locationIdImg",
        },
      ],
    },
  ];
});

const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
  // console.log("setNestedValue", localFormData.value);
};
// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  // console.log("formItem数据修改回调", unionKey, val);
  setNestedValue(localFormData.value, unionKey.split("."), val);
};
// formItem dict 字典数据修改；级联数据修改
const onFormDictChange = ({ val, unionKey }) => {
  // console.log("选择字典数据", val, unionKey);
  // 选择关联构件
  if (unionKey === "assetContentDTO.componentId") {
    let componentTypeCodeId = val[val.length - 1];
    let curChooseComponent = findItemByComponentTypeId(
      props.stateData.componentOptions,
      componentTypeCodeId,
      "id"
    );
    // 设置构件类型编码
    setNestedValue(
      localFormData.value,
      "assetContentDTO.componentTypeCode".split("."),
      curChooseComponent.componentTypeCode
    );
    // 设置构件序号
    setNestedValue(
      localFormData.value,
      "assetContentDTO.componentNumber".split("."),
      curChooseComponent.componentNumber
    );
    // 清空截面序号
    setNestedValue(localFormData.value, "sectionNumberIdLabel".split("."), "");
    // 找出该构件的截面序号列表
    sectionNumberOptions.value = curChooseComponent.sectionalInfoList.map(
      (item) => {
        item.label = `${item.serialNumber}-${item.name}`;
        item.dictKey = item.id;
        item.dictValue = `${item.serialNumber}-${item.name}`;
        return item;
      }
    );
    console.log("查看截面序号", sectionNumberOptions.value);
  } else if (unionKey === "sectionNumberIdLabel") {
    // 选择截面序号
    let curChooseSectionNumber = findItemByComponentTypeId(
      sectionNumberOptions.value,
      val,
      "id"
    );
    setNestedValue(
      localFormData.value,
      "sectionNumber".split("."),
      curChooseSectionNumber.serialNumber
    );
    // console.log("选择的截面序号", curChooseSectionNumber.serialNumber);
  } else if (unionKey === "monitorItemLabel") {
    // 选择监测项
    console.log("选择监测项", val, unionKey);
    if (!props.choosedMonitorItems.includes(val)) {
      emits("onChooseMonitorItem", { curPointInd: props.pointIndex, val });
    }
  } else if (unionKey === "locationIdLabel") {
    localFormData.value.locationImg = props.stateData.locationImgOptions.find(
      (locationItem) => locationItem.id === val
    ).layoutPath;
  }
};
// 计算是否可以自动生成测点编码
// 设备所属资产具备简称，关联构件、截面序号、监测类别和内容均已填写，才可以自动生成
const isAutoGenerateDis = computed(() => {
  return !(
    props.propertyBridgeAlias &&
    localFormData.value.assetContentDTO.componentId.length > 1 &&
    localFormData.value.sectionNumber &&
    localFormData.value.monitorInfo.length == 3
  );
});
// formItem 从选择页面回调的 select 数据修改
let autoFormRef = ref(null);
const onFormSelChange = () => {
  console.log(
    "dataAcquisitionInfo.stateData.key",
    dataAcquisitionInfo.stateData.key
  );

  // 1如果选择的是 “所属路段”，则需要清空表单中选择的桩号信息
  // ...
  // 2处理select选择的label
  setNestedValue(
    localFormData.value,
    dataAcquisitionInfo.stateData.key.split("."),
    dataAcquisitionInfo.stateData.label
  );
  // 3处理select选择的value
  let str = dataAcquisitionInfo.stateData.key.replace(/Label$/, "");
  setNestedValue(
    localFormData.value,
    str.split("."),
    dataAcquisitionInfo.stateData.value
  );
  console.log("选择更改", localFormData.value);
  if (autoFormRef.value?.formRef) {
    autoFormRef.value.formRef.validateField(dataAcquisitionInfo.stateData.key);
  }
};

// 自动生成测点编码
let generateLoading = ref(false);
const onRightSlot = async () => {
  if (isAutoGenerateDis.value) return;
  console.log("点击生成按钮");
  try {
    let params = {
      bridgeAlia: props.propertyBridgeAlias,
      componentTypeCode: localFormData.value.assetContentDTO.componentTypeCode,
      componentNumber: localFormData.value.assetContentDTO.componentNumber,
      sectionNumber: localFormData.value.sectionNumber,
      monitorType: localFormData.value.monitorInfo[1],
    };
    generateLoading.value = true;
    let { code, data } = await ShmService.generateMonitoringPointCode(params);
    generateLoading.value = false;
    if (code === 200) {
      setNestedValue(localFormData.value, "pointCode".split("."), data);
    }
  } catch (error) {
    console.log(error);
    generateLoading.value = false;
  }
};

const emits = defineEmits(["onDelPoint", "onAddPoint", "onChooseMonitorItem"]);
const cancelAddPoint = () => {
  emits("onDelPoint");
};
const addPoint = () => {
  // console.log("查看填写的测点数据", localFormData.value);
  autoFormRef.value.formRef
    .validate()
    .then((res) => {
      localFormData.value.isFinished = true;
      emits("onAddPoint", { data: localFormData.value });
    })
    .catch((errors) => {
      console.log("校验失败", errors);
    });
};

onShow(() => {
  // 处理从别的页面中选择之后的数据显示，如所属路线选择
  onFormSelChange();
});

defineExpose({
  autoFormRef,
  onFormDictChange,
  localFormData: localFormData.value,
  setNestedValue,
});

// form表单样式配置
const labelStyle = reactive({
  width: "208rpx",
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  // lineHeight: "33rpx",
  lineHeight: "48rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 20rpx",
  borderBottom: "none",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  overflow: "hidden",
});
const placeholderStyle = ref("color: #C1C1C1");
</script>
<style lang="scss" scoped>
.content {
  // height: 1112rpx;
  height: calc(100% - 92rpx);
  margin-top: 0rpx;
  .btns {
    position: fixed;
    bottom: 40rpx;
    background: #fff;
    .btn {
      width: 252rpx;
      height: 72rpx;
      margin: 0 28rpx;
      border: 2rpx solid #4378ff;
      text-align: center;
      color: #4378ff;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      line-height: 72rpx;
      border-radius: 8rpx;
    }
    .primary_btn {
      background: rgba(67, 120, 255, 1);
      color: #fff;
    }
    .disabled_btn {
      color: #fff;
      border: 2rpx solid #e4e4e4;
      background: rgba(160, 159, 159, 0.8);
    }
  }
  :deep(.uv-overlay) {
    border-radius: 28rpx;
  }
  :deep(.uni-data-tree-cover) {
    border-radius: 28rpx;
  }
}
</style>
