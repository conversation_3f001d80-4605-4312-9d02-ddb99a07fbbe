{"name": "养道", "appid": "__UNI__8A196AA", "description": "androidYangdaoApp", "versionName": "5.7.0", "versionCode": 570, "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "compatible": {"ignoreVersion": true}, "modules": {"Geolocation": {}, "Maps": {}, "Camera": {}, "Push": {}, "Webview-x5": {}, "LivePusher": {}, "VideoPlayer": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SECURE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "minSdkVersion": 21, "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"idfa": false, "dSYMs": false, "infoPlist": {"NSLocationWhenInUseUsageDescription": "需要获取您的位置以提供相关服务", "NSLocationAlwaysAndWhenInUseUsageDescription": "需要持续获取您的位置以提供相关服务"}}, "sdkConfigs": {"geolocation": {"amap": {"name": "wx_小高", "__platform__": ["ios", "android"], "appkey_ios": "27b9bf2cce516b95dcfbaa1914ef09ad", "appkey_android": "27b9bf2cce516b95dcfbaa1914ef09ad"}, "system": {"__platform__": ["ios", "android"]}}, "maps": {"amap": {"name": "wx_小高", "appkey_ios": "27b9bf2cce516b95dcfbaa1914ef09ad", "appkey_android": "27b9bf2cce516b95dcfbaa1914ef09ad"}}, "ad": {}, "push": {"unipush": {"version": "2", "offline": false, "clickAction": {"type": "none"}, "appid": "Nzzxle2We3AB1NWswvs632", "appkey": "ArgoUR7Xo39vKyuIeugxG3", "appsecret": "B4TZPVcYFH9WyRNoAFl566", "icons": {"push": {"hdpi": "unpackage/res/icons/tubiao.png", "ldpi": "unpackage/res/icons/tubiao.png", "mdpi": "unpackage/res/icons/tubiao.png", "xhdpi": "unpackage/res/icons/tubiao.png", "xxhdpi": "unpackage/res/icons/tubiao.png"}, "small": {"hdpi": "unpackage/res/icons/tubiao.png", "ldpi": "unpackage/res/icons/tubiao.png", "mdpi": "unpackage/res/icons/tubiao.png", "xhdpi": "unpackage/res/icons/tubiao.png", "xxhdpi": "unpackage/res/icons/tubiao.png"}}}}}, "splashscreen": {"androidStyle": "default", "android": {"hdpi": "src/static/image/new_start480.png", "xhdpi": "src/static/image/new_start720.png", "xxhdpi": "src/static/image/new_start1080.png"}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"devServer": {"port": 3200}}}