<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-11-06 09:52:09
 * @Description: 
-->
<template>
  <view class="container">
    <view class="item_box">
      <view class="label">事件编码</view>
      <view class="content">{{ detailData.eventCode || "-" }}</view>
      <view class="right_btn" @click="copy(detailData.eventCode)">复制</view>
    </view>
    <view class="item_box">
      <view class="label">上报人</view>
      <view class="content">{{ detailData.submitUserName || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">上报时间</view>
      <view class="content">{{ detailData.submitTime || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">事件类型</view>
      <view class="content">{{ detailData.eventTypeName || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">所属路段</view>
      <view class="content">{{ detailData.sectionName || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">事件对象类型</view>
      <view class="content">{{ detailData.eventObjectTypeName || "-" }}</view>
    </view>
    <view class="item_box" v-if="detailData.eventType == 1">
      <view class="label">病害类型</view>
      <view class="content">{{ detailData.diseasesTypeName || "-" }}</view>
    </view>
    <view class="item_box" v-if="detailData.eventType == 1">
      <view class="label">病害计量</view>
      <view class="content">{{ detailData.diseasesCount || "-" }}{{ diseasesCountUnit || '' }}</view>
    </view>
    <view class="item_box">
      <view class="label">事件对象名称</view>
      <view class="content">{{ detailData.eventObjectName || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">位置方向</view>
      <view class="content">{{ detailData.updownMarkName || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">起点桩号</view>
      <view class="content">{{ detailData.stakeName || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">终点桩号</view>
      <view class="content">{{ detailData.endStakeName || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">地址信息</view>
      <view class="content">{{ detailData.address || "-" }}</view>
      <view class="right_btn" @click="viewLocation">查看</view>
    </view>
    <view class="item_box">
      <view class="label">事件描述</view>
      <view class="content">{{ detailData.remark || "-" }}</view>
    </view>
    <view class="item_box">
      <view class="label">照片上传</view>
      <view class="imgs">
        <view v-if="!detailData.fileAttributes?.length">-</view>
        <template v-else>
          <ylg-load-image
            class="img"
            v-for="(img, index) in detailData.fileAttributes"
            :key="index"
            :scroll-top="0"
            loading-mode="spin-circle"
            :image-src="showImg(img.path)"
            @click="previewImg(index)"
          ></ylg-load-image>
        </template>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, reactive } from "vue";
import { DataAcquisitionService, RoadInspectionService } from "@/service";
import { onLoad } from "@dcloudio/uni-app";
import { reTransformLngLat } from "@/utils/location";
import { showImg } from "@/utils";

onLoad(async (options) => {
  if (options?.id) {
    await getDicts();
    getDetail(options.id);
  }
});

let dicts = ref({});
const getDicts = async () => {
  let { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes:
      "embankmentDiseasesType,pavementDiseasesType,bridgeDiseasesType,tunnelDiseasesType,culvertDiseasesType,rfDiseasesType",
  });
  if (code == 200) {
    dicts.value = data;
  }
};

let detailData = ref({});
let imgPreviewUrls = reactive([]);
const getDetail = async (id) => {
  try {
    let { code, data } = await RoadInspectionService.getEventDetail(id);
    console.log("事件详情", code, data);

    if (code == 200) {
      detailData.value = data;
      if (data.fileAttributes?.length) {
        data.fileAttributes.forEach((item) => {
          imgPreviewUrls.push(showImg(item.path));
        });
      }
      handleDiseasesCountUnit();
    }
  } catch (error) {
    console.log("获取详情失败", error);
  }
};

let diseasesCountUnit = ref('');
const handleDiseasesCountUnit = () =>{
  let menu = {
    3: "embankmentDiseasesType",
    4: "pavementDiseasesType",
    5: "bridgeDiseasesType",
    6: "tunnelDiseasesType",
    7: "culvertDiseasesType",
    8: "rfDiseasesType",
  };
  let type = menu[detailData.value.eventObjectType];
  let diseasesTypeDicts = dicts.value[type];
  let curChoose = diseasesTypeDicts.find(item=>item.dictKey === detailData.value.diseasesType);
  diseasesCountUnit.value = curChoose.remark || '';
}

const copy = (e) => {
  uni.setClipboardData({
    data: e, // e是你要保存的内容
    success: function () {
      uni.showToast({
        title: "复制成功！",
        icon: "none",
      });
    },
  });
};
// 查看定位
const viewLocation = () => {
  // uni.navigateTo({
  //   url: `/pages/roadConditionInspection/coordinatePicking?isDetail=${true}&curLongitude=${detailData.value.longitude}&curLatitude=${detailData.value.latitude}&curAddress=${detailData.value.address}`,
  // });
  // 将后端返回的标准坐标转换为高德使用的gcj02坐标
  let reTrans = reTransformLngLat(
    detailData.value.longitude,
    detailData.value.latitude
  );
  uni.navigateTo({
    url: `/pages/roadConditionInspection/coordinatePicking?isDetail=${true}&curLongitude=${String(
      detailData.value.longitude
    )}&curLatitude=${String(detailData.value.latitude)}&curAddress=${
      detailData.value.address
    }`,
  });
};

// 预览图片
const previewImg = (cur) => {
  uni.previewImage({
    urls: imgPreviewUrls, // 需要预览的图片HTTP链接列表
    current: cur, // 当前显示图片的链接索引
  });
};
</script>
<style lang="scss" scoped>
// .container {
// }
.item_box {
  display: flex;
  align-items: center;
  padding: 24rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #404040;
  line-height: 40rpx;
  .label {
    width: 168rpx;
  }
  .content {
    margin-left: 66rpx;
    flex: 1;
    // width: 300rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .right_btn {
    width: 96rpx;
    text-align: right;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 24rpx;
    color: #4378ff;
    line-height: 34rpx;
  }
  .imgs {
    display: flex;
    align-items: center;
    margin: 0 0 0 66rpx;
    .img {
      display: block;
      width: 176rpx;
      height: 176rpx;
      border-radius: 8rpx;
      margin-right: 34rpx;
    }
  }
}
</style>
