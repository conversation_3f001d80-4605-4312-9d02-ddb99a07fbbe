import { ref } from "vue";

export default (handleMessage) => {
  let heartTime = null; // 心跳定时器实例
  let socketHeart = 0; // 心跳次数
  let HeartTimeOut = 30000; // 心跳超时时间
  let socketError = 0; // 错误次数
  let websock = ref(null); // websocket实例
  let isWebsocketReady = ref(false); // 是否已创建 WebSocket连接
  let typeMsg = ref(null); // 接收到的消息类型
  // 初始化socket
  const initWebSocket = (isPlatformIot = false, deviceId = "") => {
    let wsuri = null;
    if (isPlatformIot) {
      // 提测打包时，用本地&测试环境域名，发版时要改回正式的域名
      if (import.meta.env.MODE === "production") {
        wsuri = `wss://platform.ylgsz.com/api/websocket/webs/iot:device:${deviceId}`;
      } else if (import.meta.env.MODE === "development") {
        wsuri = `ws://192.168.2.110:8080/websocket/webs/iot:device:${deviceId}`;
      } else {
        wsuri = `ws://192.168.2.145:8080/websocket/webs/iot:device:${deviceId}`;
      }
    } else {
      // 提测打包时，用本地&测试环境域名，发版时要改回正式的域名
      if (import.meta.env.MODE === "production") {
        wsuri = `wss://platform.ylgsz.com/api/websocket/new/webs/shm`;
      } else if (import.meta.env.MODE === "development") {
        wsuri = `ws://192.168.2.110:8080/websocket/new/webs/shm`;
      } else {
        wsuri = `ws://192.168.2.134:8080/websocket/new/webs/shm`; // 森珂
      }
    }

    // console.log('socket链接地址', wsuri);
    // websock.value = new WebSocket(wsuri)
    // 使用 uni.connectSocket 替代 new WebSocket
    websock.value = uni.connectSocket({
      url: wsuri,
      success: () => {
        console.log("WebSocket 连接请求发送成功");
      },
      fail: (err) => {
        console.error("WebSocket 连接请求失败:", err);
      },
    });

    onWebSocketOpen();
    onWebSocketMessage();
    onWebSocketError();
    onWebSocketClose();
    sendSocketHeart();
  };

  // socket 连接成功
  const onWebSocketOpen = () => {
    websock.value.onOpen(() => {
      console.log("WebSocket 连接成功");
      isWebsocketReady.value = true;
      resetHeart();
    });
  };

  // socket 连接失败
  const onWebSocketError = () => {
    websock.value.onError((e) => {
      console.error("WebSocket 连接失败:", e);
      reconnect();
    });
  };

  // socket 断开链接
  const onWebSocketClose = () => {
    websock.value.onClose(() => {
      console.log("WebSocket 断开连接");
      isWebsocketReady.value = false;
      clearInterval(heartTime);
      handleMessage({ data: "断开连接 onClose" });
    });
  };

  // socket 接收数据
  const onWebSocketMessage = () => {
    websock.value.onMessage((res) => {
      console.log("收到 WebSocket 消息:", res);
      handleMessage(res);
    });
  };

  // socket 重置心跳
  const resetHeart = () => {
    socketHeart = 0;
    socketError = 0;
    clearInterval(heartTime);
    sendSocketHeart();
  };

  // socket心跳发送
  const sendSocketHeart = () => {
    heartTime = setInterval(() => {
      // 如果连接正常则发送心跳
      if (isWebsocketReady.value) {
        websock.value.send(
          JSON.stringify({
            type: "online",
          })
        );
        socketHeart = socketHeart + 1;
      } else {
        // 重连
        reconnect();
      }
    }, HeartTimeOut);
  };

  // 发送 WebSocket 消息
  const sendMsg = (msg) => {
    if (websock.value) {
      websock.value.send({
        data: msg,
        success: () => {
          console.log("消息发送成功:", msg);
        },
        fail: (err) => {
          console.error("消息发送失败:", err);
        },
      });
    } else {
      console.error("WebSocket 未连接，消息发送失败");
    }
  };

  // socket重连
  const reconnect = () => {
    if (socketError <= 2) {
      clearInterval(heartTime);
      initWebSocket();
      socketError = socketError + 1;
      console.log("socket重连", socketError);
    } else {
      console.log("重试次数已用完的逻辑", socketError);
      clearInterval(heartTime);
    }
  };
  // 其他函数...

  return {
    websock,
    initWebSocket,
    typeMsg,
    // 将其他函数也暴露出来，如sendMsg, reconnect等
    resetHeart,
    sendMsg,
    isWebsocketReady,
    reconnect,
  };
};
