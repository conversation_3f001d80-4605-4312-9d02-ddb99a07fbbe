<template>
  <view class="alarm-device-status">
    <view class="title">今日报警设备状态</view>
    <view v-if="executeDetailVOList && executeDetailVOList.length > 0">
      <view
        class="card-box"
        v-for="(item, index) in executeDetailVOList"
        :key="index"
      >
        <view
          class="status"
          :class="'status' + (item.deviceStatus === '离线' ? 2 : 1)"
          >{{ item.deviceStatus }}</view
        >
        <view class="device-top">
          <view class="name">{{ item.deviceName || "-" }}</view>
          <view class="flex-start-center device-item">
            <view class="label">设备ID：</view>
            <view class="content">{{ item.deviceId || "-" }}</view>
          </view>
          <view class="flex-start-center device-item">
            <view class="label">触发时间：</view>
            <view class="content">{{ item.alarmTime || "-" }}</view>
          </view>
          <view class="flex-start-top device-item event">
            <view class="label">触发事件：</view>
            <view class="content flex-between-center">
              <text class="uuid">{{
                item.alarmEventUuid ? item.alarmEventUuid : "-"
              }}</text>
              <view
                class="check"
                v-if="item.alarmEventId"
                @click="goDeatils(item.alarmEventId)"
                >查看 ></view
              >
            </view>
          </view>
        </view>
        <view class="flex-between-center device-btn">
          <view class="label">开关状态</view>
          <view class="content flex-end-center">
            <!-- 开关状态(0关，1开，2开操作，3关操作) -->
            <uv-switch
              v-if="['0', '1'].includes(item.onOffStatus)"
              v-model="item.onOffStatus"
              activeValue="1"
              asyncChange
              inactiveValue="0"
              size="28"
              inactive-color="#ddd"
              active-color="#3770FF"
              :disabled="!hasAuth('alarmDeviceStatusEdit')"
              @change="(val) => setExecuteOnOff(val, index)"
              ></uv-switch>
            <uv-button
              type="primary"
              v-if="item.onOffStatus === '2' || item.onOffStatus === '3'"
              :plain="true"
              loading
              loadingText="加载中"
            ></uv-button>
            <uv-tags
              v-if="
                item.onOffStatus === null && item.deviceOnOffStatus === null
              "
              text="开关无效"
              bgColor="#E5E5E5"
              borderColor="#E5E5E5"
              size="medium"
              color="#9B9B9B"
              type="warning"
              disabled
            ></uv-tags>
          </view>
        </view>
      </view>
      <view class="black" style="height: 100rpx"></view>
    </view>
    <ylgNodata
      v-else
      :imgStyle="{
        width: '244rpx',
      }"
      textSize="32rpx"
      textColor="#fff"
      imgUrl="/static/shmStatic/image/asset-nodata-img.png"
    />
  </view>
</template>

<script setup>
import { onUnmounted, ref } from "vue";
import { AssetService } from "@/service";
import { useUserStore } from "@/store/user";
import { hasAuth } from "@/config/permissions.js";
import ylgNodata from "@/components/ylg-nodata.vue";
const userStore = useUserStore();
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});
const executeDetailVOList = ref([]);
// 单个报警设备执行（开关操作）
const setExecuteOnOff = (val, idx) => {
  executeDetailVOList.value[idx].onOffStatus = "2";
  AssetService.executeOnOff({
    deviceId: executeDetailVOList.value[idx].deviceId,
    onOff: val,
  });
  setTimeout(() => {
    getAppDeviceAlarmEvent();
  }, 2000);
  // watchDeviceAlarmEvent()
};
const timer = ref(null);
const watchDeviceAlarmEvent = () => {
  timer.value = setInterval(() => {
    getAppDeviceAlarmEvent();
  }, 5000);
};

// 跳转事件详情
const goDeatils = (id) => {
  uni.navigateTo({
    url: `/pages/shmPages/alarmEvents/detail?id=${id}&type=1`,
  });
};

// 报警统计
const getAppDeviceAlarmEvent = () => {
  AssetService.appDeviceAlarmEvent({
    assetId: userStore?.assetInfo?.objectId,
  })
    .then((res) => {
      executeDetailVOList.value = res.data
        ? res.data.map((item) => {
            item.onOffStatus =
              item.onOffStatus === null
                ? item.deviceOnOffStatus
                : item.onOffStatus;
            return item;
          })
        : [];
    })
    .catch((error) => {
      console.log("报警设备 error", error);
      executeDetailVOList.value = [];
    });
};

const clearTimer = () => {
  clearInterval(timer.value);
  timer.value = null;
};

onUnmounted(() => {
  console.log("组件onUnmounted1111111");
  clearTimer();
  console.log("组件onUnmounted222222", timer.value);
});

defineExpose({
  getAppDeviceAlarmEvent,
  watchDeviceAlarmEvent,
  clearTimer,
});

const value = ref(false);
</script>

<style lang="scss" scoped>
.title {
  color: #373737;
  font-size: 32rpx;
  font-weight: 500;
  margin: 48rpx 0 28rpx 0;
}
.card-box {
  padding: 32rpx 0 0 0;
  position: relative;
  margin-bottom: 28rpx;
  .status {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 26rpx;
    width: 96rpx;
    height: 52rpx;
    line-height: 52rpx;
    text-align: center;
    color: #fff;
    &.status1 {
      background: url("../../../../static/shmStatic/icon/online-icon.png")
        center center no-repeat;
      background-size: 100%;
    }
    &.status2 {
      background: url("../../../../static/shmStatic/icon/offline-icon.png")
        center center no-repeat;
      background-size: 100%;
    }
  }
  .label {
    font-size: 28rpx;
    color: #b0b0b0;
    width: 140rpx;
  }
  .content {
    flex: 1;
    font-size: 28rpx;
    color: #404040;
    word-break: break-all;
    .check {
      font-size: 28rpx;
      color: #3770ff;
    }
  }
  .uuid {
    width: 370rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .device-top {
    padding: 0 28rpx 28rpx 28rpx;
    .device-item {
      margin-top: 20rpx;
      height: 40rpx;
      line-height: 40rpx;
      .content {
        flex: 1;
      }
    }
  }
  .device-btn {
    padding: 14rpx 28rpx;
    border-top: 1px solid #f2f2f2;
  }
}
</style>
