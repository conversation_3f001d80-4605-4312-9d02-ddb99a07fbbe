<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-14 14:13:34
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 录入设备id、扫码 -->
    <view class="id_enter_card" id="idEnterCard">
      <view class="enter_box">
        <view class="scan_box">
          <image
            class="scan_icon"
            src="/static/shmStatic/icon/scan_icon_20250310.png"
            @click="startScan"
          ></image>
        </view>
        <view class="enter_input flex-start-center">
          <view class="label">设备ID号：</view>
          <uv-input
            placeholder="请输入id"
            border="none"
            :maxlength="19"
            v-model="idVal"
            fontSize="16px"
            color="#373737"
          ></uv-input>
        </view>
      </view>
      <view class="btns">
        <uv-button
          type="primary"
          class="clear_btn"
          :custom-style="clearBtnStyle"
          :customTextStyle="btnTextStyle"
          :plain="true"
          text="清 空"
          @click="clearId"
        ></uv-button>
        <uv-button
          :loading="comfirmBtnLoading"
          class="confirm_btn"
          :custom-style="primaryBtnStyle"
          :customTextStyle="btnTextStyle"
          type="primary"
          text="确 定"
          @click="confirmId"
        ></uv-button>
      </view>
    </view>
    <!-- 设备id列表 -->
    <view class="id_list_card">
      <view class="title same-add-title">设备ID列表</view>
      <scroll-view
        class="list_box"
        scroll-y="true"
        v-if="historyDeviceIdList.length"
      >
        <view
          class="id_item flex-between-center"
          v-for="deviceIdItem in historyDeviceIdList"
          :key="deviceIdItem.deviceId"
          @click="onClickDeviceId(deviceIdItem)"
        >
          <view :class="[deviceIdItem.permission ? 'id' : 'no_right_id']">{{
            deviceIdItem.deviceId
          }}</view>
          <image
            v-show="deviceIdItem.permission && !deviceIdItem.idItemLoading"
            class="right_arrow_icon"
            src="/static/shmStatic/icon/right_arrow_20250310.png"
          ></image>
          <uv-loading-icon
            v-show="deviceIdItem.permission && deviceIdItem.idItemLoading"
            class="right_arrow_icon"
          ></uv-loading-icon>
        </view>
      </scroll-view>
      <ylg-nodata
        v-else
        imgUrl="/static/shmStatic/image/list_nodata_20250324.png"
        noticeTip="暂无数据~"
        :imgStyle="{ width: '380rpx' }"
        textSize="32rpx"
      ></ylg-nodata>
    </view>
    <!-- 提示弹窗 -->
    <uv-modal
      ref="noRightModalRef"
      title="无法查看"
      @confirm="onNoRightConfirm"
    >
      <view class="slot-content">
        <view class="dialog_text"
          >当前登录账号，无此设备所属租户的查阅权限！</view
        >
        <view class="dialog_text">设备ID：{{ idVal }}</view>
      </view>
      <template v-slot:confirmButton
        ><view class="comfirm_btn" @click="onNoRightConfirm"
          >我知道了</view
        ></template
      >
    </uv-modal>
    <uv-modal
      ref="noDeviceModalRef"
      title="无法查看"
      @confirm="onNoDeviceConfirm"
    >
      <view class="slot-content">
        <view class="dialog_text"
          >未查询到对应设备ID，请检查设备ID是否正确识别？</view
        >
      </view>
      <template v-slot:confirmButton
        ><view class="comfirm_btn" @click="onNoDeviceConfirm"
          >我知道了</view
        ></template
      >
    </uv-modal>
    <uv-toast ref="toast" position="top"></uv-toast>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ShmService } from "@/service";
import { onLoad } from "@dcloudio/uni-app";

const idVal = ref("");
// const idVal = ref("111111");
// const idVal = ref("1902602683147689988");
// const idVal = ref("1902602683147689999");
// const idVal = ref("1902602683147682586");
// const idVal = ref("1902602683147689989");
// const idVal = ref("1892839722736746702");

onLoad(() => {
  getDeviceList();
});

// 历史设备id列表
const getDeviceList = async () => {
  try {
    let { code, data } = await ShmService.getDeviceHistoryList();
    if (code == 200) {
      historyDeviceIdList.value = data || [];
    } else {
      historyDeviceIdList.value = [];
    }
  } catch (error) {
    historyDeviceIdList.value = [];
  }
};

// 唤起扫码
const startScan = () => {
  uni.scanCode({
    success: (res) => {
      console.log("扫码结果:", res);
      uni.showToast({
        title: `扫描成功~`,
        icon: "none",
      });
      idVal.value = res?.result.split("=")[1] || "";
      if (idVal.value) getDeviceInfo();
    },
    fail: (err) => {
      console.error("扫码失败:", err);
    },
  });
};
// 根据id查询设备信息
let historyDeviceIdList = ref([]);
let monitorItemList = ref([]);
const getDeviceInfo = async () => {
  try {
    let { code, data } = await ShmService.getDeviceScanInfo({
      deviceId: idVal.value,
    });
    comfirmBtnLoading.value = false;
    historyDeviceIdList.value.forEach((item) => {
      item.idItemLoading = false;
    });
    if (code == 200) {
      // 历史设备id列表
      historyDeviceIdList.value = data.idList || [];
      // 监测项
      monitorItemList.value = data.deviceInfo?.metadataList?.map((item) => {
        item.dictValue = item.name;
        item.dictKey = item.name;
        return item;
      });
      console.log("查看监测项", monitorItemList.value);

      // 二维码携带id正确，再判断是否具备当前扫码设备查看权限
      if (data.bool === 1) {
        // 1)无权限查看
        if (!data.devicePermission) {
          noRightModalRef.value.open();
        } else {
          // 2)有权限查看
          if (!data.devicePresent && !data.pointPresent) {
            // 2.1）不存在设备 & 不存在测点，跳转到“创建设备和测点页”（默认创建设备步骤；
            uni.navigateTo({
              url: `/pages/shmPages/scanCode/createDevice?deviceId=${
                idVal.value
              }&deviceStatus=${
                data.deviceStatus
              }&step=0&deviceInfo=${JSON.stringify(
                data.deviceInfo
              )}&monitorItemList=${JSON.stringify(monitorItemList.value)}`,
            });
          } else if (data.devicePresent && !data.pointPresent) {
            // 2.2）存在设备 & 不存在测点，跳转到“创建设备和测点页”（默认创建测点步骤，设备表单回显设备相关信息，并禁用部分字段不可修改
            uni.navigateTo({
              url: `/pages/shmPages/scanCode/createDevice?deviceId=${
                idVal.value
              }&deviceStatus=${
                data.deviceStatus
              }&step=1&deviceInfo=${JSON.stringify(
                data.deviceInfo
              )}&monitorItemList=${JSON.stringify(
                monitorItemList.value
              )}&isDeviceEdit=${1}`,
            });
          } else if (data.devicePresent && data.pointPresent) {
            // 2.3）存在设备 & 存在测点，跳转到“详情页”
            toDetail(idVal.value, data.deviceInfo.assetInfo[1]);
          }
        }
      } else {
        // 二维码携带id错误
        noDeviceModalRef.value.open();
      }
    }
  } catch (error) {
    historyDeviceIdList.value.forEach((item) => {
      item.idItemLoading = false;
    });
    comfirmBtnLoading.value = false;
  }
};

// 确认设备id
let toast = ref(null);
let comfirmBtnLoading = ref(false);
const confirmId = () => {
  if (!idVal.value) {
    toast.value.show({
      type: "warning",
      message: "请输入设备ID或扫描设备二维码",
    });
    return;
  }
  comfirmBtnLoading.value = true;
  getDeviceInfo();
};
// 清空设备id
const clearId = () => {
  // uni.navigateTo({
  //   url: `/pages/shmPages/realTimeData/index`,
  // });
  idVal.value = "";
};
// 跳转至详情页
const toDetail = (deviceId, assetId) => {
  uni.navigateTo({
    url: `/pages/shmPages/scanCode/detail?deviceId=${deviceId}&assetId=${assetId}&pageFrom=scan`,
  });
};

// 点击某个设备id
const onClickDeviceId = (idItem) => {
  if (!idItem.permission || idItem.idItemLoading) return;
  historyDeviceIdList.value.forEach((item) => {
    if (item.deviceId === idItem.deviceId) {
      item.idItemLoading = true;
    } else {
      item.idItemLoading = false;
    }
  });
  idVal.value = idItem.deviceId;
  getDeviceInfo();
};

const noRightModalRef = ref(null);
const noDeviceModalRef = ref(null);
const onNoRightConfirm = () => {
  idVal.value = "";
  noRightModalRef.value.close();
};
const onNoDeviceConfirm = () => {
  noDeviceModalRef.value.close();
};

let otherHeight = ref(0);
onMounted(() => {
  // 创建节点查询实例
  const query = uni.createSelectorQuery();
  query
    .select("#idEnterCard") // 选择对应id的元素
    .boundingClientRect((rect) => {
      // 获取尺寸信息
      if (rect) {
        console.log("元素高度:", rect.height);
        // rect.height id输入卡片高度
        // 56 容器的上下padding（28rpx）
        // 40 预留一点空间
        otherHeight.value = rect.height * 2 + 56 + 40 + "rpx";
        // 这里可以处理获取到的高度数据
      }
    })
    .exec(); // 执行查询
});

const clearBtnStyle = reactive({
  width: "158rpx",
  height: "72rpx",
  lineHeight: "62rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  border: "2rpx solid #4378ff",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 500,
  color: "#4378ff",
  background: "#fff",
});
const primaryBtnStyle = reactive({
  width: "158rpx",
  height: "72rpx",
  lineHeight: "62rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 500,
  color: "#fff",
  background: "#4378ff",
});
const btnTextStyle = reactive({
  fontSize: "28rpx",
});
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding: 28rpx 40rpx;
  background: #f4f8ff;
}
.id_enter_card {
  width: 100%;
  border-radius: 24rpx;
  background: #fff;
  box-sizing: border-box;
  .enter_box {
    box-sizing: border-box;
    width: 100%;
    padding: 30rpx 28rpx 28rpx 28rpx;
    border-bottom: 2rpx solid #f0f0f0;
    .scan_box {
      text-align: right;
      .scan_icon {
        width: 48rpx;
        height: 48rpx;
      }
    }
    .enter_input {
      .label {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #373737;
        line-height: 40rpx;
      }
    }
  }
  .btns {
    width: 100%;
    height: 108rpx;
    box-sizing: border-box;
    position: relative;
    display: flex;
    align-items: center;
    // justify-content: end;
    padding: 28rpx 28rpx 26rpx 28rpx;
    .confirm_btn {
      margin-left: 48rpx;
      position: absolute;
      right: 28rpx;
    }
    .clear_btn {
      margin-left: 48rpx;
      position: absolute;
      right: 222rpx;
    }
  }
}
.id_list_card {
  margin-top: 28rpx;
  width: 100%;
  height: calc(100vh - v-bind(otherHeight));
  border-radius: 24rpx;
  background: #fff;
  box-sizing: border-box;
  padding: 28rpx;
  .title {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #404040;
  }
  .list_box {
    // margin-top: 32rpx;
    height: calc(100% - 76rpx);
    .id_item {
      padding: 32rpx 0 16rpx 0;
      border-bottom: 2rpx solid #f0f0f0;
      .id {
        font-size: 28rpx;
        color: #373737;
        line-height: 40rpx;
      }
      .no_right_id {
        font-size: 28rpx;
        color: #bababa;
        line-height: 40rpx;
      }
      .right_arrow_icon {
        display: block;
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
  :deep(.content) {
    margin-top: 120rpx;
  }
}
.dialog_text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #404040;
  line-height: 40rpx;
}
.dialog_text:last-child {
  margin-top: 20rpx;
}
.comfirm_btn {
  background: rgba(67, 120, 255, 1);
  border-radius: 8rpx;
  width: calc(100% - 100rpx);
  height: 72rpx;
  margin: 0 auto;
  margin-bottom: 44rpx;
  box-sizing: border-box;
  font-family: PingFang SC, PingFang SC;
  text-align: center;
  font-weight: 600;
  font-size: 32rpx;
  color: #ffffff;
  line-height: 72rpx;
}
:deep(.uv-modal__title) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 36rpx;
  color: #373737;
  line-height: 50rpx;
}
</style>
