<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-10-08 14:45:22
 * @Description: 
-->
<template>
  <view class="container">
    <web-view
      v-show="!showAddressList"
      class="webview_box"
      :src="webviewMapUrl"
      @onPostMessage="handlePostMessage"
      @message="handlePostMessage"
      ref="webview"
      :webview-styles="webviewStyles"
    > 
    </web-view>
    <uv-search
      shape="square"
      searchIcon="../../../static/icon/search_icon.png"
      searchIconSize="18"
      placeholder="地点搜索"
      placeholderColor="#A09F9F;"
      bgColor="#fff"
      :clearabled="false"
      :showAction="false"
      :customStyle="searchCustomStyle"
      :boxStyle="searchBoxStyle"
      :inputStyle="{ fontSize: '28rpx' }"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <!-- 搜索地址列表 -->
    <view class="address_list" overflow-y="scroll" v-if="showAddressList">
      <view v-for="(item, index) in addressList" :key="index">
        <view class="top_line"></view>
        <view class="address_item" @click="chooseAddress(item)">
          <view class="left">
            <view class="address_name">{{ item.name }}</view>
            <view class="address_detail"
              >{{ item.address }}</view
            >
          </view>
          <!-- <view class="right">0米</view> -->
        </view>
      </view>
    </view>
    <view v-show="!showAddressList">
      <!-- 地图组件 -->
      <!-- <view class="map_box">
        <map
          id="map"
          ref="mapRef"
          style="width: 100%; height: 672rpx"
          :enable-poi="true"
          :enable-rotate="false"
          :longitude="myPosition.longitude"
          :latitude="myPosition.latitude"
          :markers="covers"
          @regionchange="regionchange"
        >
        </map>
        <cover-image
          class="reset_icon"
          src="../../../static/icon/current_position.png"
          @click="resetPosition"
        ></cover-image>
      </view> -->
      <!-- 列表 -->
      <view class="list_content">
        <uv-loading-icon class="loading_icon" :show="pageLoading" mode="semicircle" size="36" :vertical="true" text="努力加载中..."></uv-loading-icon>
        <view v-if="!pageLoading">
          <scroll-view :scroll-y="true" class="list_box" :style="{height: noNew?'580rpx':'500rpx'}" v-if="dataList.length">
            <view class="notie"
              >当前定位周围共有推荐桩号
              <text class="num">{{ dataList.length }}</text></view
            >
            <view
              :class="['card', curItem.id === item.id ? 'active_card' : '']"
              v-for="item in dataList"
              :key="item.id"
              @click="chooseItem(item)"
            >
              <view class="card_conent">
                <view class="item_title_box">
                  <view class="item_title">{{ item.stakeName }}</view>
                  <view class="item_title">{{ item.tipText }}</view>
                </view>
                <view class="item_info">{{
                  item.updownMarkName || item.updownMarkLabel
                }}</view>
              </view>
              <image
                v-if="item.tipText"
                class="del_icon"
                src="/static/icon/del_icon.png"
                mode="widthFix"
                @click.stop="delPileNumber(item.id)"
              />
            </view>
            <view class="black" style="height: 120rpx;"></view>
          </scroll-view>
          <view v-else class="nodata">
            <image class="img" src="/static/image/nodata_pile_number.png" />
            <view class="noice"
              >{{noNew?'当前路段暂无桩号信息':'当前路段暂无桩号信息，如需新增桩号请点击下方的“新增桩号信息”'}}</view
            >
          </view>
        </view>
        <view class="bottom_box">
          <view class="btn plain_btn" @click="addPileNumber" v-if="!noNew">新增桩号信息</view>
          <view class="btn" @click="confirm">确 定</view>
        </view>
      </view>
    </view>
    <uv-toast ref="toast"></uv-toast>
  </view>
</template>
<script setup>
import { reactive, ref, computed } from "vue";
import { onLoad, onReady, onShow } from "@dcloudio/uni-app";
import { DataAcquisitionService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { usePileNumberStore } from "@/store/pileNumber";
const pileNumberInfo = usePileNumberStore();
// import { transformLngLat, reTransformLngLat } from '@/utils/location';
import { debounce } from '@/utils';
// webview实例
const pages = getCurrentPages();
let wv = ref(null);

const handlePostMessage = (data) => {
  console.log("webview天地图发来的数据",data.detail.data[0]);
	switch (data.detail.data[0].action) {
    // 地图被拖动，中心点发生变化
		case 'moveMap':
      console.log('data.detail.data[0].action',data.detail.data[0].center);
      myPosition.latitude = data.detail.data[0].center.lat;
      myPosition.longitude = data.detail.data[0].center.lng;
      // 根据选择的定位请求附近的五个桩号
      pageLoading.value = true;
      dataList.value = [];
      debouncedGetList();
			break;
		default:
			break;
	}
};
let webviewMapUrl = ref(`./static/tianditu/map.html?isDetail=${false}&ControlTop=280px`);

let unionKey = ref("");
let curUsingIds = ref([]); // 当前正在采集的数据，已经选择的桩号
let curItemId = ref(""); // 当前所选桩号id
let sectionId = ref("");
let noNew = ref(false);
onLoad((options) => {
  noNew.value = options.noNew||false;
  console.log("optionsssss", options);
  // curUsingIds.value = options?.curItemId || "";
  curUsingIds.value = pileNumberInfo.curChooedMap.map(item=>Object.values(item)[0]);
  sectionId.value = options?.sectionId || ""; // 1818894340832989186
  curItemId.value = options?.curItemId || "";
  unionKey.value = options.unionKey;
  console.log('查看本地保存的新增桩号',pileNumberInfo.pileNumbers);
});

const getWebView = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const webView = pages[pages.length - 1].$getAppWebview().children()[0];
      console.log('webView',webView);
      resolve(webView);
    }, 1000);
  });
}
// onShow(async () => {
//   wv.value = await getWebView();
//   handleDataList();
// });

onReady(async() => {
  wv.value = await getWebView();
  uni.setNavigationBarTitle({
    title: "桩号选择",
  });
  await getLocation();
})

let inpVal = ref("");
const inpChange = (val) => {
  if (!val) {
    addressList.value = [];
    showAddressList.value = false;
    return;
  }
  inpVal.value = val;
  addressSearch();
};
const inpSearch = (val) => {
  if (!val) {
    addressList.value = [];
    showAddressList.value = false;
    return;
  }
  inpVal.value = val;
  addressSearch();
};

// 搜索地址
let addressList = ref([]);
let showAddressList = ref(false);
const addressSearch = () => {
  let params = {
    // location: `${myPosition.longitude},${myPosition.latitude}`,
    // radius: 10000,
    inpVal: inpVal.value,
  };
  // DataAcquisitionService.addressSearch(params).then((res) => {
  //   if (res.status == 1 && res.pois.length) {
  //     addressList.value = res.pois;
  //     showAddressList.value = true;
  //   } else if (res.status == 0) {
  //     uni.showToast({
  //       icon: "none",
  //       title: "请求地址失败，请稍后重试~",
  //     });
  //   }
  //   console.log("查看地址结果", addressList.value);
  // });
  DataAcquisitionService.getAddreeeList(params).then((res) => {
    if (res.pois && res.pois.length > 0) {
      addressList.value = res.pois.reduce((acc, current) => {
        let nameArr = acc.map(accitem=>accitem.name);
        if (!nameArr.includes(current.name)) {
          let obj = {
            name: current.name,
            address: current.province + current.city + current.county === current.address?current.address:current.province + current.city + current.county + current.address,
            location: current.lonlat
          }
          acc.push(obj);
        }
        return acc;
      }, []);
      showAddressList.value = true;
    } else if (res.area && res.area.lonlat) {
      addressList.value[0] = {
        name: res.area.name,
        address: null,
        location: res.area.lonlat
      }
      showAddressList.value = true;
    } else {
      addressList.value = []
    }
    console.log("查看地址结果", addressList.value);
  });
};

const chooseAddress = (addressItem) => {
  let locationArr = addressItem.location.split(",");
  // let reTrans = reTransformLngLat(
  //   locationArr[0],
  //   locationArr[1]
  // );
  myPosition.longitude = String(locationArr[0]);
  myPosition.latitude = String(locationArr[1]);

  console.log('选择列表地址',addressItem,myPosition);
  showAddressList.value = false;
  addressList.value = [];
  inpVal.value = "";

  // 通知webview更新定位
  wv.value.evalJS(`receiveData({type:'changeLocation',data:{lng:${myPosition.longitude},lat:${myPosition.latitude}}})`);
  // 根据选择的定位请求附近的五个桩号
  pageLoading.value = true;
  dataList.value = [];
  debouncedGetList();
};

let covers = computed(() => {
  let myPosiArr = [
    {
      id: `${myPosition.latitude}${myPosition.longitude}`,
      latitude: myPosition.latitude,
      longitude: myPosition.longitude,
      iconPath: "../../../static/icon/cur_position.png",
    },
  ];
  return [...myPosiArr, ...mapMarkArr.value];
});
// 获取当前定位
let myPosition = reactive({
  latitude: "",
  longitude: "",
});
// 
let isGetCenterLatLong = ref(0);
const getLocation = async () => {
  // 初次渲染地图、不需要调用getCenterLatLong方法，以免影响初始定位准确性；
  isGetCenterLatLong.value ++;
  // 定位开启状态 true=开启，false=未开启
  let bool = false;
  // android平台
  if (uni.getSystemInfoSync().platform == "android") {
    var context = plus.android.importClass("android.content.Context");
    var locationManager = plus.android.importClass(
      "android.location.LocationManager"
    );
    var main = plus.android.runtimeMainActivity();
    var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
    bool = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER);

    uni.getLocation({
      // type: "gcj02", //返回可以用于uni.openLocation的经纬度
      type: "wgs84",
      success: function (res) {
        myPosition.latitude = res.latitude;
        myPosition.longitude = res.longitude;
        console.log("获取定位成功", res);
        // 通知webview更新定位
        wv.value = pages[pages.length - 1].$getAppWebview().children()[0];
		    wv.value.evalJS(`receiveData({type:'initLocation',data:{lng:${res.longitude},lat:${res.latitude}}})`);
        
        // 根据定位请求附近的五个桩号
        setTimeout(() => {
          getList();
        }, 100);
      },
    });
  }

  // 未开启定位功能
  if (bool === false) {
    uni.showModal({
      title: "提示",
      content: "请打开定位服务",
      success: ({ confirm, cancel }) => {
        if (confirm) {
          // android平台
          if (uni.getSystemInfoSync().platform == "android") {
            var Intent = plus.android.importClass("android.content.Intent");
            var Settings = plus.android.importClass(
              "android.provider.Settings"
            );
            var intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            var main = plus.android.runtimeMainActivity();
            main.startActivity(intent); // 打开系统设置GPS服务页面
          }
          // ios平台
          if (uni.getSystemInfoSync().platform == "ios") {
            var UIApplication = plus.ios.import("UIApplication");
            var application2 = UIApplication.sharedApplication();
            var NSURL2 = plus.ios.import("NSURL");
            var setting2 = NSURL2.URLWithString(
              "App-Prefs:root=Privacy&path=LOCATION"
            );
            application2.openURL(setting2);
            plus.ios.deleteObject(setting2);
            plus.ios.deleteObject(NSURL2);
            plus.ios.deleteObject(application2);
          }
        }
        // 用户取消前往开启定位服务
        if (cancel) {
          console.log("用户取消前往开启定位服务");
          // do sth...
        }
      },
    });
  }
};

// 拖动地图视野发生变化，保持定位图标始终在地图中心位置
const regionchange = (e) => {
  console.log("拖动地图视野发生变化", isGetCenterLatLong.value);
  if (["onRegionchange", "regionchange", "end"].includes(e.type) && isGetCenterLatLong.value >1 ) {
    //在安卓中是 end 事件
    getCenterLatLong(); // 地图移动时获取中心点的经纬度
  }
  isGetCenterLatLong.value++;
};
let mapRef = ref(null);
let mapContext = ref(null);
const getCenterLatLong = () => {
  mapContext.value = uni.createMapContext("map");
  console.log("mapContext", mapContext.value);
  mapContext.value.getCenterLocation({
    success: (res) => {
      console.log("中心位置", res);
      myPosition.latitude = res.latitude;
      myPosition.longitude = res.longitude;
      // 根据选择的定位请求附近的五个桩号
      pageLoading.value = true;
      dataList.value = [];
      debouncedGetList();
      
    },
    fail: (error) => {
      console.log("中心位置失败", error);
    },
  });
};

// 点击地图右下角回到当前位置
const resetPosition = () => {
  console.log("点击地图右下角回到当前位置");
  getLocation();
};

// 获取桩号列表
let dataList = ref([]); // 最终整合好的
let resList = ref([]); // 接口返回的
let mapMarkArr = ref([]); // 要在map地图上标记的桩号
let pageLoading = ref(false);
const getList = async () => {
  if (!sectionId.value) return;
  try {
    pageLoading.value = true;
    let params = {
      longitude: myPosition.longitude,
      latitude: myPosition.latitude,
      // distance: 5000, // km  产品要求查1km范围内，现在方便测试，所以取5000km
      distance: 1, // km  产品要求查1km范围内，现在方便测试，所以取5000km
      projectId: projectInfo.projectId,
      page: 1,
      limit: 5,
      sectionId: sectionId.value,
    };
    const { code, data } = await DataAcquisitionService.getStakeList(params);
    pageLoading.value = false;
    // console.log("获取桩号列表成功", data);
    resList.value = data;
    // resList.value = [];
    console.log("re转换后端返回列表经纬度", resList.value);
    handleDataList();
  } catch (error) {
    pageLoading.value = false;
    console.log("请求桩号失败", error);
  }
};

const debouncedGetList = debounce(getList, 1000);

// 处理列表数据：
const handleDataList = () => {
  // 1.整合本地pinia中新增保存的桩号、接口返回的5条最近范围的桩号
  dataList.value = [...pileNumberInfo.pileNumbers, ...resList.value];
  console.log('整合列表数据11111111',dataList.value);
  if(!dataList.value.length) return;
  // 2.将所有桩号都在地图组件上标记出来
  wv.value.evalJS(`receiveData({type:'upnewPilelist',data:{list:${JSON.stringify(dataList.value)}}})`);
  // 3.处理已选桩号回显
  handleCurChoosed(curItemId.value);
};

// 处理回显当前已经选择的桩号，并将其置顶
const handleCurChoosed = (curId) => {
  let curItemIndex = ref(-1);
  dataList.value.forEach((item, index) => {
    if (item.id === curId) {
      curItem.value = item;
      curItemIndex.value = index;
    }
  });
  if (curItemIndex.value !== -1) {
    let [it] = dataList.value.splice(curItemIndex.value, 1);
    dataList.value.unshift(it);
  }
  wv.value.evalJS(`receiveData({type:'choosePile',data:{choosedId:${curId},dataList:${JSON.stringify(dataList.value)}}})`);
};



// 选择桩号
let curItem = ref({});
const chooseItem = (item) => {
  curItem.value = item;
  wv.value.evalJS(`receiveData({type:'choosePile',data:{choosedId:${item.id},dataList:${JSON.stringify(dataList.value)}}})`);
};

// 确定选择某个桩号
const confirm = () => {
  if(!curItem.value.id) return;
  console.log("确定选择某项", curItem.value);
  pileNumberInfo.updateCurChoosedMap({[unionKey.value]: curItem.value.id});
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: unionKey.value,
      value: curItem.value.id,
      label: curItem.value.stakeName,
    },
  });
  uni.navigateBack({
    data: 1,
  });
};

// 删除本地新增的桩号
let toast = ref(null);
const delPileNumber = (id) => {
  if (curUsingIds.value.includes(id)) {
    uni.showToast({
      icon: "none",
      title: "该桩号已被占用，无法删除~",
    });
    return;
  }
  pileNumberInfo.delPileNumber(id);
  handleDataList();
  uni.showToast({
    icon: "none",
    title: "删除成功~",
  });
};

// 新增桩号
let curRoadPartId = ref("111"); // 当前路段id
const addPileNumber = () => {
  uni.navigateTo({
    url: `/pages/dataAcquisition/pileNumberCollection?scene=2&curRoadPartId=${curRoadPartId.value}&curFormItemKey=${unionKey.value}`,
  });
};

const webviewStyles = computed(()=>{
  return {
    position: "fixed",
    width: "100%",
    // height: "calc(100vh - 164px)",
    height: "340px",
    top: '40px',
    left: "0",
  }
});
// 搜索
const searchBoxStyle = reactive({
  height: "80rpx",
  borderRadius: "8px",
});
const searchCustomStyle = reactive({
  width: "100%",
  height: "80rpx",
});

</script>
<style lang="scss" scoped>
.container {
  width: 100vw;
  min-height: 100vh;
  background: #f4f8ff;
}
.map_box {
  position: relative;
  // margin-top: -40rpx;
  .reset_icon {
    width: 80rpx;
    height: 80rpx;
    position: absolute;
    right: 40rpx;
    bottom: 40rpx;
  }
}
.list_content {
  width: 100%;
  position: fixed;
  // top: 740rpx;
  top: 380px;
  // margin-top: 700rpx;
  padding: 20rpx 0 20rpx 40rpx;
  box-sizing: border-box;
}
.list_box {
  height: 500rpx;
  margin: 12rpx 0 260rpx 0;
  .notie {
    margin-bottom: 24rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #a09f9f;
    line-height: 40rpx;
    .num {
      margin-left: 8rpx;
      color: #4378ff;
    }
  }
  .card {
    width: 670rpx;
    position: relative;
    margin-bottom: 28rpx;
    padding: 22rpx 28rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 16rpx;
    font-family: PingFang SC, PingFang SC;
    .card_conent {
      display: flex;
      justify-content: space-between;
      .item_title_box{ 
        flex: 1;
        display: flex;
        justify-content: space-between;
      }
      .item_title,
      .item_info {
        font-weight: 400;
        color: #373737;
        font-size: 32rpx;
        line-height: 44rpx;
      }
      .item_info{
        width: 68rpx;
      }
    }
    .del_icon {
      display: block;
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      top: -8rpx;
      right: -8rpx;
    }
  }
  .active_card {
    background: #4378ff;
    .card_conent {
      .item_title,
      .item_info {
        color: #ffffff;
      }
    }
  }
}
.nodata {
  width: 670rpx;
  margin-bottom: 280rpx;
  .img {
    display: block;
    width: 670rpx;
    height: 294rpx;
    margin-bottom: 28rpx;
  }
  .noice {
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #a09f9f;
    line-height: 44rpx;
  }
}
.bottom_box {
  width: calc(100% - 80rpx);
  position: fixed;
  bottom: 0rpx;
  padding: 20rpx 0;
  background: #f4f8ff;
  .choosed_data_show {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .btn {
    width: 670rpx;
    height: 84rpx;
    box-sizing: border-box;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
  .plain_btn {
    margin-bottom: 28rpx;
    border: 2rpx solid #4378ff;
    background: #f4f8ff;
    color: #4378ff;
  }
}
.address_list {
  position: absolute;
  top: 80rpx;
  left: 0;
  padding: 0 24rpx;
  width: 100%;
  height: calc(100vh - 80rpx);
  box-sizing: border-box;
  background: #fff;
  .top_line {
    height: 2rpx;
    background: #d8d6d6;
  }
  .address_item {
    padding: 24rpx 0rpx;
    display: flex;
    justify-content: space-between;
    align-content: center;
    .left {
      .address_name {
        color: #373737;
        font-size: 32rpx;
      }
      .address_detail {
        color: #A09F9F;
        font-size: 22rpx;
        margin-top: 8rpx;
      }
    }
    .right {
      color: #666;
      font-size: 14rpx;
    }
  }
}
</style>
