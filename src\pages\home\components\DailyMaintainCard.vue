<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-16 11:17:18
 * @Description: 
-->
<template>
  <view class="list_box" v-if="dataList?.length">
    <view
      class="card"
      v-for="item in dataList"
      :key="item.id"
      @click="toDetail(item)"
    >
      <view :class="['card_top_status', 'card_top_status' + item.taskStatus]">
        {{ item.taskStatusName }}
      </view>
      <view class="card_top">
        <image
          class="card_top_icon"
          :src="cardConfig.home_daily_maintain_card"
        />
        <view class="card_top_title">
          {{ item.planName }}
        </view>
      </view>
      <view class="card_content">
        <view class="content_item">
          <view class="content_name">保养路段：</view>
          <view class="content_val">{{ item.sectionName || "--" }}</view>
        </view>
        <view class="content_item">
          <view class="content_name">桩号信息：</view>
          <view class="content_val">
            {{
              (item.startStakeName || "--") + "/" + (item.endStakeName || "--")
            }}
          </view>
        </view>
      </view>
      <view class="card_bottom">
        <view class="log_date">
          <image class="date_icon" :src="getIcon(item.taskStatus)" />
          <view class="date" :class="['date' + item.taskStatus]">
            {{ item.remark }}
          </view>
        </view>
        <image
          class="detail_icon"
          :src="basicConfig.eventHandling_detail_icon"
        />
      </view>
    </view>
  </view>
</template>
<script setup>
import { cardConfig, clockConfig, basicConfig } from "./staticConfig";

const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
});
const getIcon = (taskStatus) => {
  if (!taskStatus) return "";
  const iconMap = {
    2: clockConfig.home_clock_light_blue,
    3: clockConfig.home_clock_blue,
    6: clockConfig.home_clock_orange,
  };
  return iconMap[taskStatus];
};
const toDetail = (record) => {
  uni.navigateTo({
    url: `/pages/routinePreserve/preserve?pageFrom=home&taskStatus=${record.taskStatus}&taskId=${record.id}`,
  });
};
</script>
<style lang="scss" scoped>
.list_box {
  .card {
    width: 100%;
    height: 310rpx;
    position: relative;
    text-align: center;
    box-sizing: border-box;
    margin-bottom: 40rpx;
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0 0 8rpx 0 rgba($color: #000000, $alpha: 0.1);
    padding: 26rpx 0;
    .card_top_status {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      height: 44rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      border-radius: 0 16rpx 0 16rpx;
      padding: 0 16rpx;
      box-sizing: border-box;
      &.card_top_status2 {
        color: #6ba7f8;
        background: #e2eeff;
      }
      &.card_top_status3 {
        color: #526cf3;
        background: #eaf0ff;
      }
      &.card_top_status6 {
        color: #ff9a1e;
        background: #feecd6;
      }
    }
    .card_top {
      display: flex;
      position: relative;
      padding-right: 100rpx;
      .card_top_icon {
        display: block;
        width: 132rpx;
        height: 54rpx;
        margin: 0 20rpx 0 -16rpx;
      }
      .card_top_title {
        flex: 1;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        text-align: left;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 32rpx;
        color: #373737;
      }

      .data_title_box {
        display: flex;
        align-items: center;
        .data_logo {
          display: inline-block;
          margin-right: 20rpx;
          width: 108rpx;
          height: 48rpx;
        }
        .data_title {
          flex: 1;
          text-align: left;
        }
      }
    }
    .card_content {
      margin-top: 28rpx;
      padding: 0rpx 28rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      border-bottom: 2rpx solid #f0f0f0;
      .content_item {
        margin-bottom: 20rpx;
        display: flex;
        align-items: baseline;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 40rpx;
        .content_name {
          width: 140rpx;
          color: #b0b0b0;
          text-align: right;
        }
        .content_val {
          flex: 1;
          text-align: left;
          color: #404040;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
    .card_bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 28rpx;
      .log_date {
        display: flex;
        align-items: center;
        .date_icon {
          display: inline-block;
          margin-right: 8rpx;
          width: 28rpx;
          height: 28rpx;
        }
        .date {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          &.date2 {
            color: #6ba7f8;
          }
          &.date3 {
            color: #526cf3;
          }
          &.date6 {
            color: #ff9a1e;
          }
        }
      }
      .detail_icon {
        display: block;
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
</style>
