import { useUserStore } from "@/store/user";
import { useSystemStore } from "@/store/system";
import { hideLoading, showLoading, showToast } from "@/utils/index";
const errorFlag = "error_flag_yh_app"; // 接口请求异常错误标识
const loadingStack = new Map();
/**
 * 新增加载框堆栈
 * @param option 接口请求options
 * @param task RequestTask
 */
const addLoadinStack = (option, task) => {
  showLoading();
  loadingStack.set(option, task);
};
/**
 * 移除加载框堆栈
 * @param option 接口请求options
 */
const removeLoadinStack = (option) => {
  loadingStack.delete(option);
  if (![...loadingStack.keys()].length) {
    hideLoading();
  }
};
// 请求拦截器
const requestInterceptor = (request) => {
  // debugger;
  const customHeader = {};
  const userInfo = useUserStore();
  // console.log("查看请求request", request);
  // 4 rm
  // 5 shm
  // 6 养护工程
  // 7 道路资产
  if (request.url.includes("/project-resource")) {
    customHeader["SystemCode"] = "7";
  } else if (request.url.includes("/shm/")) {
    customHeader["SystemCode"] = "5";
  } else if (request.url.includes("/yh/")) {
    customHeader["SystemCode"] = "4";
  } else {
    console.log("没有设置 systemCode header");
  }

  userInfo.token && (customHeader["Authorization"] = userInfo.token);
  // customHeader["Authorization"] = 'eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxODE3NzY4Nzg5Mzg4OTIyODgyLCJ1c2VyX2tleSI6IjE4MTc3Njg3ODkzODg5MjI4ODI6OWRiNTA5MjctZGQyMC00ZmYyLWJmMzgtZGJhMzI5ZjVmMjk0IiwidXNlcm5hbWUiOiLmnY7oib4ifQ.NszuxYAFIwIUfk5iSLUq1VbHAVeWnT5ZW1L9hEmRY3k0f_4igmJkuLXNuHO-pQFtqBasF3eoSSpMskc5GH1u6g';
  request.header = Object.assign(request.header || {}, customHeader);
};
// 返回拦截器
const responseInterceptor = (response, request) => {
  const userInfo = useUserStore();
  const systemInfo = useSystemStore();
  // 请求日志
  const { data, statusCode } = response;
  //   console.log("response", response);
  if (statusCode !== 200 && statusCode !== 201) {
    // 请求异常
    // showToast("服务异常，请刷新后重试！");
    if (statusCode === 401 || statusCode === 403) {
      showToast("登录过期，请重新登录");
      userInfo.clearToken();
      systemInfo.updateSystemCode([]);
      loadingStack.forEach((task) => {
        task.abort();
      });
      setTimeout(() => {
        uni.redirectTo({
          url: `/pages/login/index`,
        });
      }, 0);
    } else {
      console.error("服务异常，请刷新后重试！");
      showToast("服务异常，请刷新后重试！");
    }
    return errorFlag;
  }
  // 天地图返回不一样，需要特殊处理
  if (
    response.data &&
    response.data.status &&
    response.data.status.cndesc &&
    response.data.status.cndesc === "服务正常" &&
    response.data.status.infocode === 1000
  ) {
    return response.data;
  }
  if (Number(data.code) === 401) {
    showToast("登录过期，请重新登录");
    userInfo.clearToken();
    systemInfo.updateSystemCode([]);
    loadingStack.forEach((task) => {
      task.abort();
    });
    setTimeout(() => {
      console.error("跳转到登录页");
      uni.redirectTo({
        url: `/pages/login/index`,
      });
    }, 0);
    return errorFlag;
  }
  // noProxy表示返回值不做处理
  if (request.header?.noProxy) {
    console.log("地址地址", response);
    return response.data;
  }
  if (Number(data.code) !== 0 && Number(data.code) !== 200) {
    showToast(data.message || data.msg || "服务异常");
    return errorFlag;
  }
  // return data.data || data.retdata;
  return data;
};
class BaseService {
  httpOptions;
  baseUrl = import.meta.env.VITE_BASE_URL;
  constructor() {
    this.httpOptions = {
      url: "",
      header: {
        "Content-Type": "application/json",
        // SystemCode: 4,
      },
      timeout: 30000,
    };
  }
  /**
   * 网络请求Post
   * @param {*} url 请求地址
   * @param {*} params 请求参数
   * @param  {any} header 剩余参数
   * @param  {boolean} hideFailToast 失败弹窗
   * @returns APIPromise
   */
  post(url, params, header = {}, hideFailToast = false) {
    return this.request("POST", url, params, header, hideFailToast);
  }
  /**
   * 网络请求Put
   * @param {*} url 请求地址
   * @param {*} params 请求参数
   * @param  {any} header 剩余参数
   * @param  {boolean} hideFailToast 失败弹窗
   * @returns APIPromise
   */
  put(url, params, header = {}, hideFailToast = false) {
    return this.request("PUT", url, params, header, hideFailToast);
  }
  /**
   * 网络请求Get
   * @param {*} url 请求地址
   * @param {*} params 请求参数
   * @param  {any} header 剩余参数
   * @param  {boolean} hideFailToast 失败弹窗
   * @returns APIPromise
   */
  get(url, params, header = {}, hideFailToast = false) {
    return this.request("GET", url, params, header, hideFailToast);
  }
  /**
   * 网络请求DELETE
   * @param {*} url 请求地址
   * @param {*} params 请求参数
   * @param  {any} header 剩余参数
   * @param  {boolean} hideFailToast 失败弹窗
   * @returns APIPromise
   */
  delete(url, header = {}, hideFailToast = false) {
    return this.request("DELETE", url, {}, header, hideFailToast);
  }
  /**
   * 网络请求
   * @param {*} method 请求方法
   * @param {*} path 请求地址
   * @param {*} params 请求参数
   * @param {*} header axios额外参数
   * @param {*} hideFailToast 失败弹窗
   * @returns
   */
  async request(method, path, params, header = {}, hideFailToast = false) {
    let url = "";
    // 请求天地图服务
    if (path.includes("/v2/search") || path.includes("/transit")) {
      if (import.meta.env.MODE === "production") {
        url = `https://platform.ylgsz.com/tianditu${path}`;
      } else if (import.meta.env.MODE === "development") {
        url = `http://*************/tianditu${path}`;
      } else {
        url = `/tiandituApi${path}`;
      }
    } else {
      // 其他服务
      url = path.startsWith("http")
        ? path
        : `${this.baseUrl}${path.startsWith("/") ? path : "/" + path}`;
    }

    const opt = {
      ...JSON.parse(JSON.stringify(this.httpOptions)),
      url,
      method,
      data: params,
    };
    opt.header = Object.assign(opt.header, header || {});
    requestInterceptor(opt);
    return new Promise((resolve, reject) => {
      const requestTask = uni.request({
        ...opt,
        success: (response) => {
          //   console.log("请求地址", opt);
          const result = responseInterceptor(response, opt);
          result === errorFlag ? reject(result) : resolve(result);
        },
        complete: () => {
          removeLoadinStack(opt);
        },
        fail: (err) => {
          console.log("errerr", err);
          if (!hideFailToast) {
            showToast("服务器错误，请稍后再试!!!");
          }
          reject(err);
        },
      });
      // header中没有noLoading,默认加载全局loading框
      !opt.header.noLoading && addLoadinStack(opt, requestTask);
    });
  }
}
export default BaseService;
