<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-10-12 09:30:34
 * @Description: 
-->
<template>
  <view class="card">
    <view class="draft_mark">日常保养</view>
    <view class="card_top">
      <view class="data_title_box">
        <image
          class="data_logo"
          :style="{
            width:
              cardItem.taskStatus == 5 || cardItem.taskStatus == 6
                ? '136rpx'
                : '88rpx',
          }"
          :src="preserveStatusMenu[cardItem.taskStatus]"
        />
        <view class="data_title">{{ cardItem.planName }}</view>
      </view>
    </view>
    <view class="card_content">
      <view class="content_item">
        <view class="content_name">保养路段：</view>
        <view class="content_val">{{ cardItem.sectionName }}</view>
      </view>
      <view class="flex">
        <view class="content_item">
          <view class="content_name w86">起点：</view>
          <view class="content_val w180">{{ cardItem.startStakeName }}</view>
        </view>
        <view class="content_item ml80">
          <view class="content_name w86">终点：</view>
          <view class="content_val w180">{{ cardItem.endStakeName }}</view>
        </view>
      </view>
    </view>
    <view class="card_bottom">
      <view class="log_date">
        <image class="date_icon" :src="clockUrls[cardItem.taskStatus]" />
        <view class="date" :style="{ color: colorMenu[cardItem.taskStatus] }">
          {{ cardItem.remark }}
        </view>
      </view>
      <view class="btn" v-if="cardItem.taskStatus!='1'" @click="toFinish(cardItem)">{{
        btnTexts[cardItem.taskStatus]
      }}</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { preserveStatusMenu } from "@/config/menu.js";

const props = defineProps({
  cardItem: {
    type: Object,
    required: true,
    default: () => {},
  },
});

// 状态图映射
// 1：待开始
// 2：待保养
// 3：进行中
// 4：已完成
// 5：超时已完成
// 6：超时未完成
// 7：已取消
const clockUrls = {
  2: "/static/icon/waiting_clock_icon20241008.png",
  3: "/static/icon/ongoing_clock_icon20241008.png",
  4: "/static/icon/finished_clock_icon20241008.png",
  5: "/static/icon/timeout_finished_clock_icon20241008.png",
  6: "/static/icon/timeout_clock_icon20241008.png",
};
const colorMenu = {
  2: "#6BA7F8",
  3: "#5282F3",
  4: "#75DC7F",
  5: "#5FD7A9",
  6: "#F19A65",
};
const btnTexts = {
  2: "开始保养",
  3: "去完成",
  4: "查看",
  5: "查看",
  6: "去完成",
};

const emit = defineEmits(["onCardCallBack"]);
const toFinish = (curItem) => {
  emit("onCardCallBack", { ...curItem });
};
</script>

<style lang="scss" scoped>
.card {
  position: relative;
  text-align: center;
  box-sizing: border-box;
  margin-top: 28rpx;
  border-radius: 28rpx;
  background: #fff;
  box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
  .draft_mark {
    position: absolute;
    right: 0;
    top: 0;
    background-color: rgba(239, 229, 255, 1);
    width: 136rpx;
    height: 52rpx;
    border-radius: 0 24rpx 0 24rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #AC79F6;
    line-height: 52rpx;
  }
  .card_top {
    padding: 32rpx 28rpx 0rpx 28rpx;
    .data_title_box {
      display: flex;
      align-items: center;
      .data_logo {
        display: inline-block;
        margin-right: 20rpx;
        width: 88rpx;
        height: 42rpx;
      }
      .data_title {
        // flex: 1;
        text-align: left;
        font-weight: 600;
        font-size: 32rpx;
        color: #373737;
        width: 336rpx;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
  .card_content {
    margin-top: 28rpx;
    padding: 0rpx 28rpx;
    font-family: PingFang SC, PingFang SC;
    border-bottom: 2rpx solid #f0f0f0;
    .content_item {
      margin-bottom: 24rpx;
      display: flex;
      align-items: baseline;
      font-weight: 400;
      font-size: 24rpx;
      line-height: 34rpx;
      .content_name {
        // width: 128rpx;
        text-align: right;
        color: #B0B0B0;
        font-size: 28rpx;
      }
      .content_val {
        flex: 1;
        font-size: 28rpx;
        text-align: left;
        color: #404040;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
    .content_item:last-child{
      margin-bottom: 24rpx;
    }
  }
  .flex_content {
    display: flex;
    flex-wrap: wrap;
    .content_item:nth-child(2n) {
      width: 40%;
    }
    .content_item:nth-child(2n + 1) {
      // margin-right: 112rpx;
      width: 60%;
    }
  }
  .card_bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 28rpx;
    .log_date {
      display: flex;
      align-items: center;
      .date_icon {
        display: inline-block;
        margin-right: 8rpx;
        width: 28rpx;
        height: 28rpx;
      }
      .date {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #a09f9f;
        // line-height: 34rpx;
      }
    }
    .btn {
      padding: 6rpx 12rpx;
      box-sizing: border-box;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 28rpx;
      color: #4378ff;
      line-height: 40rpx;
      border: 2rpx solid #4378ff;
      border-radius: 8rpx;
    }
  }
}
.flex{
  display: flex;
  align-items: center;
}
.ml80{
  margin-left: 80rpx;
}
.w86{
  width: 86rpx;
}
.w180{
  width: 180rpx;
}
</style>
