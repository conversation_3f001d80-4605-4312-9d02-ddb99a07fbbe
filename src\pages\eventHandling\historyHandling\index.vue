<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-14 15:05:37
 * @Description: 
-->
<template>
  <view class="container">
    <view class="top_sticky">
      <uv-search
        height="40"
        shape="square"
        searchIcon="../../../static/icon/search_icon.png"
        searchIconSize="18"
        placeholder="事件编码搜索"
        placeholderColor="#A09F9F"
        bgColor="#fff"
        :showAction="false"
        :boxStyle="searchStyle"
        v-model="inpVal"
        @change="inpChange"
        @search="inpSearch"
      ></uv-search>
    </view>
    <view class="data-list-container">
      <view class="tabs-box1">
        <view
          :class="['tab', activeTab === item.value ? 'tab-active' : '']"
          v-for="(item, index) in eventTypes"
          :key="index"
          @click="changeTab(item.value)"
        >
          {{ item.label }}
        </view>
      </view>
      <view class="tabs-box2">
        <uv-tabs
          :list="tabTypes"
          :current="tabType"
          @change="clickTab"
          lineColor="transparent"
          :activeStyle="{
            marginLeft: tabType === '' ? '2px' : '0px',
            height: '24px',
            lineHeight: '24px',
            padding: '4px 12px',
            borderRadius: '16px',
            backgroundColor: '#EDF2FF',
            color: '#4378FF',
            transform: 'scale(1.05)',
          }"
          :inactiveStyle="{
            padding: '4px 12px',
            borderRadius: '16px',
            backgroundColor: '#F2F2F2',
            color: '#83858B',
            transform: 'scale(1)',
          }"
          :itemStyle="searchTabsItemStyle"
        >
        </uv-tabs>
      </view>
      <view v-if="dataList.length">
        <view
          class="list_box"
          v-for="(monthGroup, index) in dataList"
          :key="index"
        >
          <view ref="stickyRef" class="month_sticky_bar">
            <view class="month">
              <text>{{ monthGroup.month }}</text>
              <text class="red_text">（{{ monthGroup.total }}）</text>
            </view>
            <image
              class="date_select_icon"
              src="../../../static/icon/dropdown_icon.png"
              @click="showTime"
            />
          </view>
          <view class="month_group_list">
            <view
              class="card"
              v-for="item in monthGroup.data"
              :key="item.id"
              @click="toDetail(item)"
            >
              <view
                :class="[
                  'card_top_status',
                  'card_top_status' + item.eventStatus,
                ]"
              >
                {{ item.eventStatusName }}
              </view>
              <view class="card_top">
                <image
                  class="card_top_icon"
                  :src="geteventSourceIcon(item.eventSource)"
                />
                <view v-if="item.eventType === '1'" class="card_top_title">
                  {{ item.diseasesTypeName }}（{{
                    (item.diseasesCount || "-") + (item.diseasesUnit || "-")
                  }}）
                </view>
                <view v-if="item.eventType === '2'" class="card_top_title">
                  {{ item.remark || "-" }}
                </view>
              </view>
              <view class="card_content">
                <view class="content_item">
                  <view class="content_name">所属路段：</view>
                  <view class="content_val">{{ item.sectionName || "-" }}</view>
                </view>
                <view class="content_item">
                  <view class="content_name">事件位置：</view>
                  <view class="content_val">
                    {{
                      (item.stakeName || "--") +
                      "/" +
                      (item.endStakeName || "--") +
                      "（" +
                      item.updownMarkName +
                      "） "
                    }}
                  </view>
                </view>
                <view class="content_item">
                  <view class="content_name">处置方式：</view>
                  <view class="content_val">{{
                    item.handleTypeName || "-"
                  }}</view>
                </view>
              </view>
              <view class="card_bottom">
                <view v-if="item.eventStatus === '2'" class="log_date">
                  <image class="date_icon" :src="eventHandling_date_icon" />
                  <view class="date">{{ item.handleConsumeTime }}</view>
                </view>
                <view v-if="item.eventStatus === '3'" class="log_date">
                  <image class="date_icon" :src="eventHandling_date_icon2" />
                  <view class="date" style="color: #61dc53">
                    {{ item.handleConsumeTime || "-" }}
                  </view>
                </view>
                <image class="detail_icon" :src="eventHandling_detail_icon" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <template v-else>
        <view v-if="startD" ref="stickyRef" class="month_sticky_bar">
          <view class="month">
            <text>{{ dayjs(startD).format("YYYY年M月") }}</text>
            <text class="red_text">（0）</text>
          </view>
          <image
            class="date_select_icon"
            src="../../../static/icon/dropdown_icon.png"
            @click="showTime"
          />
        </view>
        <ylg-nodata class="nodata"></ylg-nodata>
      </template>
    </view>
    <ylg-date-range-picker
      ref="datePicker"
      @confirmDate="confirmDate"
      @cancelDate="cancelDate"
    ></ylg-date-range-picker>
  </view>
</template>
<script setup>
import { reactive, ref, computed, watch } from "vue";
import { onShow, onReachBottom } from "@dcloudio/uni-app";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { getDaysInMonth } from "@/utils";
import { eventHandlingApi } from "@/service";

import eventHandling_report_person from "@/static/icon/eventHandling_report_person.png";
import eventHandling_report_car from "@/static/icon/eventHandling_report_car.png";
import eventHandling_report_machie from "@/static/icon/eventHandling_report_machie.png";
import eventHandling_report_active from "@/static/icon/eventHandling_report_active.png";
import eventHandling_date_icon from "@/static/icon/eventHandling_date_icon.png";
import eventHandling_date_icon2 from "@/static/icon/eventHandling_date_icon2.png";
import eventHandling_detail_icon from "@/static/icon/eventHandling_detail_icon.png";

const geteventSourceIcon = (eventSource) => {
  if (!eventSource) return "";
  const iconMap = {
    1: eventHandling_report_person,
    2: eventHandling_report_active,
    3: eventHandling_report_car,
    4: eventHandling_report_machie,
  };
  return iconMap[eventSource];
};
const toDetail = (record) => {
  const { id, eventType } = record;
  switch (eventType) {
    case "1":
      uni.navigateTo({
        url: `/pages/eventHandling/historyHandling/components/disease/HisDetail?id=${id}`,
      });
      break;
    case "2":
      uni.navigateTo({
        url: `/pages/eventHandling/historyHandling/components/other/HisDetail?id=${id}`,
      });
      break;
    default:
      break;
  }
};
const activeTab = ref("1");
const eventTypes = computed(() => {
  return [
    {
      value: "1",
      label: "病害事件",
    },
    {
      value: "2",
      label: "其他事件",
    },
  ];
});
const changeTab = (value) => {
  if (value === activeTab.value) return;
  activeTab.value = value;
  pageInfo.page = 1;
  getList();
};

// 列表相关数据
let dataList = ref([]);
// 分页查询参数
let pageInfo = reactive({
  page: 1,
  limit: 10,
});
let noData = ref(false);
let loading = ref(false);
let inpVal = ref("");
onShow(() => {
  pageInfo.page = 1;
  inpVal.value = "";
  getList();
});

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  marginBottom: "48rpx",
});
const tabType = ref(0);
const tabTypes = reactive([
  {
    name: "全部",
    type: 0, // 2,3
  },
  {
    name: "处理中",
    type: 1, // 2
  },
  {
    name: "已完成",
    type: 2, // 3
  },
]);
const searchTabsItemStyle = reactive({
  height: "26px",
  boxSizing: "border-box",
  padding: "0px 0px",
  backgroundColor: "#fff",
  borderRadius: "8px",
  marginRight: "12px",
  fontSize: "14px",
});

const inpChange = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  tabType.value = 0;
  startD.value = "";
  endD.value = "";
  getList();
};
const inpSearch = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  tabType.value = 0;
  startD.value = "";
  endD.value = "";
  getList();
};

const clickTab = (env) => {
  tabType.value = env.type;
  pageInfo.page = 1;
  getList();
};
const stickyZIndex = ref(999);
// 时间选择组件
const datePicker = ref(null);
const showTime = () => {
  stickyZIndex.value = 99;
  datePicker.value.open();
};
const startD = ref("");
const endD = ref("");
const confirmDate = (envRes) => {
  let { dateType, year, month, startDate, endDate } = envRes;
  if (dateType === "date") {
    let days = getDaysInMonth(year, Number(month));
    startD.value = `${year}-${month < 10 ? "0" + month : month}-01 00:00:00`;
    endD.value = `${year}-${month < 10 ? "0" + month : month}-${days} 23:59:59`;
  } else if (dateType === "daterange") {
    startD.value = startDate;
    endD.value = endDate;
  }
  stickyZIndex.value = 999;
  datePicker.value.close();
  pageInfo.page = 1;
  getList();
};
const cancelDate = () => {
  console.log("取消选择时间回调");
  stickyZIndex.value = 999;
};
import dayjs from "dayjs";
// 查询列表
const getList = async () => {
  const tabTypeMap = {
    0: "2,3",
    1: "2",
    2: "3",
  };
  let params = {
    projectId: projectInfo.projectId,
    eventCode: inpVal.value,
    eventType: activeTab.value,
    eventDFStatus: tabTypeMap[tabType.value],
    handleStartTime: startD.value,
    handleEndTime: endD.value,
    page: pageInfo.page,
    limit: pageInfo.limit,
  };
  loading.value = true;
  let {
    code,
    data: resData,
    total,
  } = await eventHandlingApi.inspectEventHisAppPage(params);
  // 如果接口异常，page-1，不执行后续操作
  if (code != 200) {
    pageInfo.page--;
    return;
  }
  if (pageInfo.page === 1) {
    dataList.value = [];
    noData.value = false;
  }
  // 处理数据，将月份相同的数据拼接到一个分组里
  console.log("resData", resData);
  let monthArr = dataList.value.map((grounp) => grounp?.month || "");
  console.log("monthArr", monthArr);
  resData?.forEach((resGrounp) => {
    let sameMonthIndex = monthArr.findIndex((item) => item === resGrounp.month);
    console.log("sameMonthIndex", sameMonthIndex);
    if (sameMonthIndex !== -1) {
      dataList.value[sameMonthIndex].data = dataList.value[
        sameMonthIndex
      ].data.concat(resGrounp.data);
    } else {
      dataList.value.push(resGrounp);
    }
  });
  loading.value = false;
  if (!resData?.length) {
    noData.value = true;
  }
  console.log("数据采集列表", dataList.value);
};

/** 页面上拉 **/
onReachBottom(() => {
  if (noData.value) {
    uni.showToast({
      icon: "none",
      title: "没有更多了~",
    });
    return;
  }
  pageInfo.page++;
  getList();
});
</script>
<style lang="scss" scoped>
.container {
  background: #f2f2f2f2;
  min-height: 100vh;
  padding: 48rpx 40rpx;
}
.data-list-container {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 28rpx 0;
  .tabs-box1 {
    display: flex;
    align-items: center;
    gap: 0 44rpx;
    height: 44rpx;
    padding: 0 28rpx;
    .tab {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: #404040;
    }
    .tab-active {
      font-weight: bold;
      background: linear-gradient(
          to right,
          rgba(51, 109, 255, 1),
          rgba(124, 161, 255, 0.8),
          rgba(67, 120, 255, 0.1),
          rgba(67, 120, 255, 0.05)
        )
        no-repeat bottom / 100% 8rpx;
    }
  }
  .tabs-box2 {
    margin-top: 32rpx;
    padding: 0 28rpx;
    .tabbar_more_icon {
      display: inline-block;
      width: 120rpx;
      height: 48rpx;
      vertical-align: middle;
    }
  }
  .list_box {
    .month_group_list {
      // margin-bottom: 34rpx;
      padding: 0 28rpx;
      .card {
        width: 100%;
        height: 360rpx;
        position: relative;
        text-align: center;
        box-sizing: border-box;
        // margin-top: 28rpx;
        margin-bottom: 28rpx;
        border-radius: 16rpx;
        background: #fff;
        box-shadow: 0 0 8rpx 0 rgba($color: #000000, $alpha: 0.1);
        padding: 26rpx 0;
        .card_top_status {
          position: absolute;
          top: 0;
          right: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100rpx;
          height: 44rpx;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          border-radius: 0 16rpx 0 16rpx;
          &.card_top_status1 {
            color: #5c9ef7;
            background: #e2eeff;
          }
          &.card_top_status2 {
            color: #4a7ef6;
            background: #dfe8ff;
          }
          &.card_top_status3 {
            color: #61dc53;
            background: #ebfff2;
          }
        }
        .card_top {
          display: flex;
          position: relative;
          padding-right: 100rpx;
          .card_top_icon {
            display: block;
            width: 132rpx;
            height: 54rpx;
            margin: 0 20rpx 0 -16rpx;
          }
          .card_top_title {
            flex: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            text-align: left;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            font-size: 32rpx;
            color: #373737;
          }

          .data_title_box {
            display: flex;
            align-items: center;
            .data_logo {
              display: inline-block;
              margin-right: 20rpx;
              width: 108rpx;
              height: 48rpx;
            }
            .data_title {
              flex: 1;
              text-align: left;
            }
          }
        }
        .card_content {
          margin-top: 28rpx;
          padding: 0rpx 28rpx;
          font-family:
            PingFang SC,
            PingFang SC;
          border-bottom: 2rpx solid #f0f0f0;
          .content_item {
            margin-bottom: 20rpx;
            display: flex;
            align-items: baseline;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 40rpx;
            .content_name {
              width: 140rpx;
              color: #b0b0b0;
              text-align: right;
            }
            .content_val {
              flex: 1;
              text-align: left;
              color: #404040;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }
        .card_bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20rpx 28rpx;
          .log_date {
            display: flex;
            align-items: center;
            .date_icon {
              display: inline-block;
              margin-right: 8rpx;
              width: 28rpx;
              height: 28rpx;
            }
            .date {
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 400;
              font-size: 24rpx;
              color: #6ba7f8;
            }
          }
          .detail_icon {
            display: block;
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
      .card:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.month_sticky_bar {
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  // z-index: v-bind(stickyZIndex);
  z-index: 99;
  height: 90rpx;
  background: #fff;
  padding: 0 28rpx;
  margin: 10rpx 0;
  .month {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: bold;
    font-size: 32rpx;
    color: #373737;
    line-height: 44rpx;
    .red_text {
      color: #ff3132;
    }
  }
  .date_select_icon {
    display: block;
    margin-left: 16rpx;
    width: 28rpx;
    height: 28rpx;
  }
}
.nodata {
  margin-top: 80rpx;
}
.black {
  height: 200rpx;
}
</style>
