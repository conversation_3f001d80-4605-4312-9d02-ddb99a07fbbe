<template>
  <view>
    <!-- 动态设置页面样式 -->
    <page-meta :page-style="pageStyle"></page-meta>
    <view class="container">
      <uv-navbar
        title="结构健康监测"
        height="84rpx"
        titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
        leftIconColor="#fff"
        :bgColor="bgColor"
        @leftClick="goBack"
      >
      </uv-navbar>
      <view class="asset-info" @click="changeAsset">
        {{ userStore?.assetInfo?.objectName }}
        <image
          style="width: 20rpx; height: 20rpx; margin-left: 8rpx"
          src="../../../static/shmStatic/icon/right-icon-white.png"
          mode="scaleToFill"
        />
      </view>
      <scroll-view :scroll-top="0" :scroll-y="isPageScroll" class="scroll-Y">
        <view style="padding: 0 40rpx">
          <view class="alarm-tatistics card-box flex-between-center">
            <view class="item-tatistics" @click="onTatisticClick(0)">
              <view class="num">{{ appAlarmStatistics.todayAlarmEvent }}</view>
              <text class="name">今日报警</text>
            </view>
            <view class="item-tatistics" @click="onTatisticClick(1)">
              <view class="num">
                {{ appAlarmStatistics.todayUnconfirmedAlarmEvent }}</view
              >
              <text class="name">今日未确认报警</text>
            </view>
            <view class="item-tatistics">
              <view class="num">
                <text class="online">{{
                  appAlarmStatistics.onLineDevice
                }}</text>
                <text class="all-device"
                  >/{{ appAlarmStatistics.deviceTotal }}</text
                >
              </view>
              <text class="name">在线设备</text>
            </view>
          </view>
          <view class="menu-list flex-between-center">
            <view
              class="menu-item"
              v-for="(item, index) of menuList"
              :key="index"
              @click="toPage(item.path)"
            >
              <image :src="item.icon" mode="scaleToFill" class="menu-icon" />
              <view class="icon-name">{{ item.name }}</view>
            </view>
          </view>
          <view class="todo-box">
            <view class="flex-between-center todo-title">
              <view class="title">今日待办事项</view>
              <view class="more flex-center" @click="toMoreTodo">
                <text class="text">更多待办</text>
                <image
                  src="../../../static/shmStatic/icon/right-icon.png"
                  mode="scaleToFill"
                  class="right-icon"
                />
              </view>
            </view>
            <view class="todo-list">
              <template v-if="alarmRulesList.length > 0">
                <AlarmRulesItem
                  v-for="(item, index) of alarmRulesList"
                  :key="index"
                  :data="item"
                  class="todo-item"
                  @click="toDetail(item.id)"
                />
              </template>
              <ylgNodata
                v-else
                :imgStyle="{
                  width: '244rpx',
                }"
                textSize="32rpx"
                textColor="#fff"
                imgUrl="../../../static/shmStatic/image/asset-nodata-img.png"
              />
            </view>
            <!-- 今日报警设备状态 -->
            <AlarmDeviceStatus ref="alarmDeviceStatusRef" />
          </view>
        </view>
      </scroll-view>

      <AssetSelect
        ref="assetSelectRef"
        @updateData="updateData"
        @closeAssetPicker="closeAssetPicker"
      />
    </view>
  </view>
</template>

<script setup>
import {
  onLoad,
  onShow,
  onPageScroll,
  onBackPress,
  onPullDownRefresh,
  onHide,
} from "@dcloudio/uni-app";
import { computed, nextTick, ref } from "vue";
import { getSysteminfo, showImg } from "@/utils";
import { AssetService } from "@/service";
import { cloneDeep as _cloneDeep } from "lodash";
import AssetSelect from "./components/AssetSelect.vue";
import AlarmRulesItem from "./components/AlarmRulesItem.vue";
import AlarmDeviceStatus from "./components/AlarmDeviceStatus.vue";
import realTimeData from "../../../static/shmStatic/icon/real-time-data.png";
import realTimeMonitoring from "../../../static/shmStatic/icon/real-time-monitoring.png";
import alarmEvents from "../../../static/shmStatic/icon/alarm-events.png";
import { useUserStore } from "@/store/user";
import ylgNodata from "@/components/ylg-nodata.vue";
const userStore = useUserStore();
import dayjs from "dayjs";
// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;

// 左上角返回
const goBack = () => {
  uni.navigateBack({
    delta: 1,
  });
};

const isPageScroll = ref(true);
const assetSelectRef = ref(null);
const pageStyle = ref(null);
// 资产选择
const changeAsset = () => {
  assetSelectRef.value.open();
  isPageScroll.value = false;
  // pageStyle.value = 'overflow: hidden;'; // 禁止页面滚动
};

// 点击顶部统计数据：跳转至对应页面
const onTatisticClick = (num) => {
  if (num === 0) {
    uni.navigateTo({
      url: "/pages/shmPages/alarmEvents/index?today=1",
    });
  } else if (num === 1) {
    uni.navigateTo({
      url: `/pages/shmPages/todoList/index?today=1`,
    });
  }
};

// 跳转列表
const toPage = (url) => {
  console.log(url);
  uni.navigateTo({ url });
};
// 跳转事件列表
const toMoreTodo = () => {
  uni.navigateTo({
    url: "/pages/shmPages/todoList/index",
  });
};
// 跳转事件详情
const toDetail = (id) => {
  uni.navigateTo({
    url: `/pages/shmPages/alarmEvents/detail?id=${id}`,
  });
};

const menuList = computed(() => {
  return [
    {
      name: "实时数据",
      icon: realTimeData,
      path: `/pages/shmPages/realTimeData/index?assetId=${
        userStore?.assetInfo?.objectId || ""
      }`,
    },
    {
      name: "实时监控",
      icon: realTimeMonitoring,
      path: `/pages/shmPages/singleAssetWorkbench/realTimeMonitor?assetId=${
        userStore?.assetInfo?.objectId || ""
      }`,
    },
    {
      name: "报警事件",
      icon: alarmEvents,
      path: "/pages/shmPages/alarmEvents/index",
    },
  ];
});

// 将 pointVOList 转换为二维数组，每个子数组最多两个元素
const chunkArray = (arr, size) => {
  const result = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};

// 统计对象
const appAlarmStatistics = ref({
  todayAlarmEvent: 0, // 今日报警事件
  todayUnconfirmedAlarmEvent: 0, // 今日未确认报警事件
  onLineDevice: 0, // 在线设备
  deviceTotal: 0, // 设备总数
});
// 报警统计
const getAppAlarmEventStatistics = () => {
  AssetService.appAlarmEventStatistics({
    assetId: userStore?.assetInfo?.objectId,
  }).then((res) => {
    console.log(res);
    for (let i in res.data) {
      appAlarmStatistics.value[i] = res.data[i];
    }
    console.log(appAlarmStatistics.value);
  });
};
const alarmRulesList = ref([]);
// 今日待办事项
const getAlarmEventPage = () => {
  const currentDate = dayjs().format("YYYY-MM-DD");
  const params = {
    assetId: userStore?.assetInfo?.objectId,
    multipleEventStatus: "1,6",
    page: 1,
    limit: 2,
    alarmTimeStart: currentDate + " 00:00:00",
    alarmTimeEnd: currentDate + " 23:59:59",
  };
  console.log(params);
  AssetService.alarmEventPage(params)
    .then((res) => {
      if (res.data && res.data.length > 0) {
        // 遍历最外层数组，对每个对象的 pointVOList 进行分块处理
        alarmRulesList.value = res.data.map((item) => {
          return {
            ...item, // 保留其他属性
            pointVOList: chunkArray(item.pointVOList, 2), // 分块处理 pointVOList
          };
        });
      } else {
        alarmRulesList.value = [];
      }
    })
    .catch(() => {
      alarmRulesList.value = [];
    });
};

// 自定义导航栏背景色
// 监听页面滚动，修改导航栏背景色
let bgColor = ref("transparent");
onPageScroll((e) => {
  if (e.scrollTop > 35) {
    bgColor.value = "#4383F8";
  } else {
    bgColor.value = "transparent";
  }
});

// 首页禁止左滑返回上一页
onBackPress((e) => {
  console.log(e);
});

const alarmDeviceStatusRef = ref(null);
// 选择资产
const updateData = (data) => {
  console.log("切换资产", data);
  if (data) {
    assetSelectRef.value.close();
    isPageScroll.value = true;
  }
  getAppAlarmEventStatistics();
  getAlarmEventPage();
  setTimeout(() => {
    alarmDeviceStatusRef.value.getAppDeviceAlarmEvent();
  }, 200);
};
// 关闭资产选择弹窗
const closeAssetPicker = () => {
  isPageScroll.value = true;
};

onLoad(async () => {
  await updateData();
});

onShow(() => {
  nextTick(()=>{
    alarmDeviceStatusRef.value.watchDeviceAlarmEvent()
  })
});

onHide(() => {
  console.log("页面隐藏");
  alarmDeviceStatusRef.value.clearTimer();
});
</script>

<style lang="scss" scoped>
// 超出省略
.ellipsis-one {
  display: -webkit-box;
  /**对象作为伸缩盒子模型展示**/
  -webkit-box-orient: vertical;
  /**设置或检索伸缩盒子对象的子元素的排列方式**/
  -webkit-line-clamp: 1;
  /**显示的行数**/
  overflow: hidden;
  /**隐藏超出的内容**/
  text-overflow: ellipsis;
  /*超出内容显示为省略号*/
  word-break: break-all;
}
.todo-item {
  padding: 0 !important;
}
.scroll-Y {
  height: calc(100vh - 84rpx - 90rpx);
}
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 0 20rpx 0;
  padding-top: v-bind(systemBarHeight);
  background-image: url("/static/shmStatic/image/shm-home-bg.png");
  background-color: #e3edff;
  background-size: cover;
  background-repeat: no-repeat;
  .asset-info {
    margin: 84rpx 40rpx 32rpx 40rpx;
    color: #ffffff;
    font-size: 28rpx;
    height: 40rpx;
    line-height: 40rpx;
  }
  .alarm-tatistics {
    padding: 32rpx 40rpx;
    .item-tatistics {
      .num {
        font-size: 48rpx;
        color: #373737;
        line-height: 56rpx;
        height: 56rpx;
        text-align: center;
        font-weight: bold;
        margin-bottom: 10rpx;
      }
      .name {
        font-size: 28rpx;
        color: #373737;
        height: 40rpx;
        line-height: 40rpx;
        text-align: center;
      }
      &:nth-child(2) .num {
        color: #ff2d2d;
      }
      &:nth-child(3) .all-device {
        font-size: 28rpx;
        font-weight: 400;
      }
    }
  }
  .menu-list {
    padding: 40rpx 0 56rpx 0;
    .menu-item {
      width: 27%;
      background-color: #ffffff;
      border-radius: 24rpx;
      box-shadow: -4rpx -4rpx 24rpx 0 rgba(142, 180, 255, 1);
      padding: 22rpx 0 20rpx 0;
      .menu-icon {
        width: 64rpx;
        height: 64rpx;
        display: block;
        margin: 0 auto 10rpx auto;
      }
      .icon-name {
        font-size: 24rpx;
        color: #8e8e8e;
        text-align: center;
      }
    }
  }
  .todo-title {
    padding-bottom: 28rpx;
    .title {
      color: #373737;
      font-size: 32rpx;
      font-weight: 500;
    }
    .more {
      .text {
        color: #3770ff;
        font-size: 28rpx;
        font-weight: bold;
        margin-right: 10rpx;
      }
      .right-icon {
        width: 20rpx;
        height: 20rpx;
      }
    }
  }
}
</style>
