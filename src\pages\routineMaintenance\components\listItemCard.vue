<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-10-12 09:30:16
 * @Description: 
-->
<template>
  <view class="card">
    <view class="draft_mark">{{ cardItem.typeName || '日常维修' }}</view>
    <image
      v-if="['0', '2'].includes(cardItem.workResult)"
      class="check_mark"
      :src="checkUrls[cardItem.workResult]"
    />

    <view class="card_top">
      <l-circle size="62" :max="cardItem.childWorkNum" :percent="cardItem.finishNum" :strokeColor="colorMenu[cardItem.workStatus]">
        <text class="circle_process_text" :style="{ color: colorMenu[cardItem.workStatus] }">{{cardItem.percentage}}<text class="character">%</text></text>
      </l-circle>
      <view class="right_text">
        <view class="data_title_box">
          <image
            class="data_logo"
            :src="maintenanceStatusMenu[cardItem.workStatus]"
          />
          <view class="data_title">{{ cardItem.workCode }}</view>
        </view>
        <view class="data_info">
          <view class="content_item">
            <view class="content_name">施工进度：</view>
            <view class="content_val"
              >{{ cardItem.finishNum }}/{{ cardItem.childWorkNum }}</view
            >
          </view>
        </view>
      </view>
    </view>
    <view class="card_bottom">
      <view class="log_date">
        <image class="date_icon" :src="clockUrls[cardItem.workStatus]" />
        <view class="date" :style="{ color: colorMenu[cardItem.workStatus] }">
          {{ cardItem.remark }}
        </view>
      </view>
      <view class="btn" @click="toFinish(cardItem)">{{
        btnTexts[cardItem.workStatus]
      }}</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { maintenanceStatusMenu } from "@/config/menu.js"

const props = defineProps({
  cardItem: {
    type: Object,
    required: true,
    default: () => {},
  },
});

const checkUrls = {
  0: "/static/icon/reject_mark_20240826.png",
  2: "/static/icon/pass_through_mark_20240826.png",
};

const clockUrls = {
  2: "/static/icon/waiting_clock_icon20241008.png", // 待施工
  3: "/static/icon/ongoing_clock_icon20241008.png", // 施工中
  4: "/static/icon/waiting_check_bell_20241008.png", // 待核验
  5: "/static/icon/finished_clock_icon20241008.png", // 已完成
};
const colorMenu = {
  2: "#6BA7F8",
  3: "#5282F3",
  4: "#C08DED",
  5: "#75DC7F",
};
const btnTexts = {
  2: "开始施工",
  3: "去完成",
  4: "查看",
  5: "查看",
};

const emit = defineEmits(["onCardCallBack"]);
const toFinish = (curItem) => {
  emit("onCardCallBack", { ...curItem });
};
</script>

<style lang="scss" scoped>
.card {
  position: relative;
  text-align: center;
  box-sizing: border-box;
  margin-top: 28rpx;
  border-radius: 28rpx;
  background: #fff;
  box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
  .draft_mark {
    z-index: 9;
    position: absolute;
    right: 0;
    top: 0;
    background-color: rgba(255, 232, 235, 1);
    width: 136rpx;
    height: 52rpx;
    border-radius: 0 24rpx 0 24rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 26rpx;
    color: #EF6A6B;
    line-height: 52rpx;
  }
  .check_mark {
    display: block;
    width: 136rpx;
    height: 136rpx;
    position: absolute;
    right: 0;
    top: 32rpx;
  }
  .card_top {
    padding: 28rpx 28rpx 0rpx 28rpx;
    border-bottom: 2rpx solid #f0f0f0;
    display: flex;
    // .left_process {
    //   width: 124rpx;
    //   height: 124rpx;
    // }
    .circle_process_text{
      .character{
        margin-left: 2rpx;
        font-size: 24rpx;
      }
    }
    .right_text {
      margin-left: 28rpx;
    }
    .data_title_box {
      display: flex;
      align-items: center;
      .data_logo {
        display: inline-block;
        margin-right: 20rpx;
        width: 88rpx;
        height: 42rpx;
      }
      .data_title {
        font-weight: 600;
        font-size: 32rpx;
        color: #373737;
        width: 236rpx;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        text-align: left;
      }
    }
  }
  .data_info {
    margin-top: 46rpx;
    font-family: PingFang SC, PingFang SC;
    .content_item {
      margin-bottom: 20rpx;
      display: flex;
      align-items: baseline;
      font-weight: 400;
      font-size: 28rpx;
      line-height: 34rpx;
      .content_name {
        width: 140rpx;
        color: #b0b0b0;
        text-align: right;
      }
      .content_val {
        flex: 1;
        font-weight: 600;
        text-align: left;
        color: #404040;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
  .flex_content {
    display: flex;
    flex-wrap: wrap;
    .content_item:nth-child(2n) {
      width: 40%;
    }
    .content_item:nth-child(2n + 1) {
      // margin-right: 112rpx;
      width: 60%;
    }
  }
  .card_bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 28rpx;
    .log_date {
      display: flex;
      align-items: center;
      .date_icon {
        display: inline-block;
        margin-right: 8rpx;
        width: 28rpx;
        height: 28rpx;
      }
      .date {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #a09f9f;
        // line-height: 34rpx;
      }
    }
    .btn {
      padding: 6rpx 12rpx;
      box-sizing: border-box;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 28rpx;
      color: #4378ff;
      line-height: 40rpx;
      border: 2rpx solid #4378ff;
      border-radius: 8rpx;
    }
  }
}
</style>
