<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-24 11:54:22
 * @Description: 
-->
<template>
  <view class="list_box" v-if="dataList?.length">
    <view
      class="card"
      v-for="item in dataList"
      :key="item.id"
      @click="toDetail(item)"
    >
      <view :class="['card_top_status', 'card_top_status' + item.workStatus]">
        {{ item.workStatusName }}
      </view>
      <view
        v-if="item.workResult == '0' || item.workResult == '5'"
        class="result_icon"
      >
        <image
          v-if="item.workResult == '0'"
          style="width: 104rpx; height: 92rpx"
          :src="basicConfig.reject_mark_20240826"
          mode="widthFix"
        />
        <image
          v-if="item.workResult == '5'"
          style="width: 104rpx; height: 92rpx"
          :src="basicConfig.pass_through_mark_20240826"
          mode="widthFix"
        />
      </view>
      <view class="card_top">
        <image class="card_top_icon" :src="cardConfig.home_daily_repair_card" />
        <view class="card_top_title">
          {{ item.workCode }}
        </view>
      </view>
      <view class="card_content">
        <view class="content_item">
          <view class="content_name">施工进度：</view>
          <view class="content_val">
            {{ item.finishNum }}/{{ item.childWorkNum }}
          </view>
        </view>

        <view class="content_item">
          <uv-line-progress
            :percentage="
              (Number(item.finishNum) / Number(item.childWorkNum)) * 100
            "
            height="16rpx"
            activeColor="#4378FF"
            :showText="false"
          ></uv-line-progress>
          <view style="margin-left: 20rpx; font-size: 24rpx; color: #606060">
            {{
              Math.floor(
                (Number(item.finishNum) / Number(item.childWorkNum)) * 100
              ) + "%"
            }}
          </view>
        </view>
      </view>
      <view class="card_bottom">
        <view class="log_date">
          <image class="date_icon" :src="getIcon(item.workStatus)" />
          <view :class="['date', 'date' + item.workStatus]">
            {{ item.remark }}
          </view>
        </view>
        <image
          class="detail_icon"
          :src="basicConfig.eventHandling_detail_icon"
        />
      </view>
    </view>
  </view>
</template>
<script setup>
import { cardConfig, clockConfig, basicConfig } from "./staticConfig";
const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
});
const getIcon = (workStatus) => {
  const iconMap = {
    1: clockConfig.home_clock_grey,
    2: clockConfig.home_clock_light_blue,
    3: clockConfig.home_clock_blue,
    4: clockConfig.home_clock_purple,
    5: clockConfig.home_clock_green,
  };
  return iconMap[workStatus];
};
// 跳转日常维修详情
const toDetail = (record) => {
  uni.navigateTo({
    url: `/pages/routineMaintenance/repairWorkOrderDetail/index?id=${record.id}`,
  });
};
</script>
<style lang="scss" scoped>
.list_box {
  .card {
    width: 100%;
    height: 310rpx;
    position: relative;
    text-align: center;
    box-sizing: border-box;
    margin-bottom: 40rpx;
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0 0 8rpx 0 rgba($color: #000000, $alpha: 0.1);
    padding: 26rpx 0;
    .card_top_status {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      height: 44rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      border-radius: 0 16rpx 0 16rpx;
      padding: 0 16rpx;
      box-sizing: border-box;
      &.card_top_status1 {
        color: #adadad;
        background: #ececec;
      }
      &.card_top_status2 {
        color: #5c9ef7;
        background: #e2eeff;
      }
      &.card_top_status3 {
        color: #526cf3;
        background: #eaf0ff;
      }
      &.card_top_status4 {
        color: #b473ed;
        background: #f2e3ff;
      }
      &.card_top_status5 {
        color: #6bc974;
        background: #e8f9e7;
      }
    }
    .result_icon {
      position: absolute;
      top: 54rpx;
      right: 0;
      width: 100rpx;
      height: 88rpx;
    }
    .card_top {
      display: flex;
      position: relative;
      padding-right: 100rpx;
      .card_top_icon {
        display: block;
        width: 132rpx;
        height: 54rpx;
        margin: 0 20rpx 0 -16rpx;
      }
      .card_top_title {
        flex: 1;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        text-align: left;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 32rpx;
        color: #373737;
      }

      .data_title_box {
        display: flex;
        align-items: center;
        .data_logo {
          display: inline-block;
          margin-right: 20rpx;
          width: 108rpx;
          height: 48rpx;
        }
        .data_title {
          flex: 1;
          text-align: left;
        }
      }
    }
    .card_content {
      margin-top: 28rpx;
      padding: 0rpx 28rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      border-bottom: 2rpx solid #f0f0f0;
      .content_item {
        margin-bottom: 20rpx;
        display: flex;
        align-items: baseline;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 40rpx;
        .content_name {
          width: 140rpx;
          color: #b0b0b0;
          text-align: right;
        }
        .content_val {
          flex: 1;
          text-align: left;
          color: #404040;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
    .card_bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 28rpx;
      .log_date {
        display: flex;
        align-items: center;
        .date_icon {
          display: inline-block;
          margin-right: 8rpx;
          width: 28rpx;
          height: 28rpx;
        }
        .date {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          &.date1 {
            color: #adadad;
          }
          &.date2 {
            color: #6ba7f8;
          }
          &.date3 {
            color: #526cf3;
          }
          &.date4 {
            color: #b473ed;
          }
          &.date5 {
            color: #6bc974;
          }
        }
      }
      .detail_icon {
        display: block;
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
</style>
