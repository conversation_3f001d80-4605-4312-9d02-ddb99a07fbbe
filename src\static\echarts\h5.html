<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>ECharts 3D</title>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts-gl@2.0.8/dist/echarts-gl.min.js"></script>
</head>
<body>
  <div id="chart" style="width: 100%; height: 200px;"></div>
  <script>
    const chart = echarts.init(document.getElementById('chart'));
    const option = {
      grid3D: {},
      xAxis3D: {},
      yAxis3D: {},
      zAxis3D: {},
      series: [{
        type: 'bar3D',
        data: [
          [0, 0, 0],
          [1, 1, 1],
          [2, 2, 2]
        ]
      }]
    };
    chart.setOption(option);
  </script>
</body>
</html>