<!--
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-03-27 16:59:36
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="沿线设施采集"
      leftIconColor="#fff"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      :autoBack="false"
      @leftClick="onNavBack"
      bgColor="#4d8bff"
    >
      <template v-if="isViewDetail" v-slot:right>
        <view class="uv-nav-slot" @click="onDel">删除</view>
      </template>
    </uv-navbar>
    <scroll-view scroll-y="true" class="content">
      <ylg-auto-form
        ref="autoFormRef"
        :isAllDisabled="isViewDetail"
        :isFromDetail="isFromDetail"
        :formConfig="formConfig"
        :formData="formData"
        :rules="rules"
        :btnLoading="btnLoading"
        :labelStyle="labelStyle"
        :formItemStyle="formItemStyle"
        :placeholderStyle="placeholderStyle"
        @onEdit="onFormEdit"
        @onChange="onFormInpChange"
        @onSubmit="onFormSubmit"
      ></ylg-auto-form>
      <uv-toast ref="toast"></uv-toast>
      <del-data-modal
        ref="delModal"
        :delMessage="delMessage"
        :delBtnLoading="delBtnLoading"
        :delMode="delMode"
        @onDelCallback="onDelModal"
      ></del-data-modal>
    </scroll-view>
    <back-page-modal ref="backModal" :btnLoading="btnLoading" @onModalCallback="onBackModal"></back-page-modal>
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import { computed, reactive, ref } from "vue";
import ylgAutoForm from "@/components/ylg-auto-form.vue";
import delDataModal from "./components/delDataModal.vue";
import backPageModal from "./components/backPageModal.vue";
import { onLoad, onShow, onBackPress } from "@dcloudio/uni-app";
import {
  reverseGeocode,
  reTransformLngLat,
  transformLngLat,
} from "@/utils/location";
import { DataAcquisitionService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { usePileNumberStore } from "@/store/pileNumber";
const pileNumberInfo = usePileNumberStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;

// form表单样式配置
const labelStyle = reactive({
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  lineHeight: "33rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
});
const placeholderStyle = ref("color: #C1C1C1");

let isViewDetail = ref(false); // 是否查询详情状态，控制form表单禁用
let isFromDetail = ref(false); // 是否从详情进入、再编辑，控制unionkey配置
onLoad((options) => {
  // 1清空pinia中缓存的之前选择的数据项：如所属路线、所属路段等；
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: "",
      value: "",
      label: "",
    },
  });
  if (options.id) {
    // 2.查询详情数据
    getDetail(options.id);
    isViewDetail.value = !!Number(options.status); // 0-草稿；1-正式
    isFromDetail.value = true;
  } else {
    isFromDetail.value = false;
  }
  // 3查询字典枚举库
  getDicts();
});
onShow(() => {
  // 处理从别的页面中选择之后的数据显示，如所属路线选择
  onFormSelChange();
});

let backModal = ref(null);
onBackPress((backOptions) => {
  if(backOptions.from === 'backbutton'){
    if (isViewDetail.value) {
      return false;
    } else {
      backModal.value.open();
      return true;
    }
  }else if(backOptions.from === 'navigateBack'){
		return false;
  }
})

const onNavBack = () => {
  if(isViewDetail.value){
    uni.navigateBack({
      delta: 1
    })
  }else{
    backModal.value.open();
  }
}
// 返回上一页modal组件回调事件
const onBackModal = (envRes) =>{
  switch (envRes.type) {
    case 'onCacel':
      backModal.value.close();
      break;
      case 'onNotSave':
      backModal.value.close();
      uni.navigateBack({
        delta: 1
      })
      break;
    case 'onTemporaryDraft':
      onFormSubmit({data:formData.value, status:0});
      break;
    default:
      break;
  }
}

// 查看详情时，点击删除
let delModal = ref(null);
let delMessage = ref('');
let delBtnLoading = ref(false);
let delMode = ref('nomal');
const onDel = async () => {
  delMessage.value = formData.value.facilityName;
  delModal.value.open();
};
const onDelModal = (envRes) => {
  switch (envRes.type) {
    case "onCacel":
      delMode.value = "nomal";
      delModal.value.close();
      break;
    case "onComfirm":
      delCurData();
      break;
    default:
      break;
  }
};
const delCurData = async () => {
  try {
    delBtnLoading.value = true;
    const {code,data,msg} = await DataAcquisitionService.roadRoadsideFacilityDel(formData.value.id);
    console.log('删除',code,data);
    delBtnLoading.value = false;
    if(code == 200){
      toast.value.show({
        type: "success",
        message: `删除成功`,
        complete() {
          uni.navigateBack({
            delta: 1,
          });
        },
      });
    }else if(code == 500){
      delMessage.value = msg;
      delMode.value = 'error';
    }else{
      toast.value.show({
        type: "error",
        message: `删除失败，请稍后重试~`
      });
    }
  } catch (error) {
    console.log('删除失败',error);
    delBtnLoading.value = false;
  }

};


// 查询字典枚举
let dicts = ref({});
const getDicts = async () => {
  let { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes: "updownMark,facilityType,signCategory,signBaseConstructionType,signPillarConstructionType,signSupportMethod,signMaterialAndLighting,markingLineType,markingLineColor,markingLineForm,markingLineMaterial,guardrailType,visualInductionFacilityType,isolationFenceType,preventCaptureType,antiGlareFacilityType,heightLimitFrameType,monitoringDeviceType,lightingFacilityType,ventilationFacilityType,firefightingFacilityType,pipelineFacilityType,meteorologicalFacilityType,powerSupplyMode,dataTransmissionMode,tollStationTypeCode,tollStationDirectionCode,tollNatureCode,yes_no,communicationFacilityType,communicationMode,serviceAreaFacilityType,serviceAreaLevel,maintenanceFacilityType,detectionStationType,detectionMethod,mobileAxleWeighingFacilityTypeCode,observationStationType,detectionSurveyEquipmentTypeCode,parkingAreaType,greenSoundBarrierType,greeningFacilityType,plantTypeCode,sewageTreatmentFacilityType,soilAndWaterConservationFacilityType,",
  });
  if (code == 200) {
    dicts.value = data;
  }
};

// 表单数据配置
const formData = ref({
  // 项目id
  projectId: projectInfo.projectId,
  // 设施名称
  facilityName: "",
  // 设施编码
  facilityCode: "",

  // 基础信息
  // 所属路段
  sectionId: "",
  sectionName: "",
  sectionIdLabel: "",
  // 设施桩号
  facilityStakeId: "",
  facilityStakeIdLabel: "",
  // 上下行标记
  updownMark: "",
  updownMarkLabel: "",
  updownMarkName: "",
  // 所在位置描述
  locationDescribe: "",
  
  // 设施特征
  // 设施类型
  facilityType: "",
  facilityTypeLabel: "",
  facilityTypeName: "",

  // 设施特征
  facilityFeature:{
    facilityType: "",
    // maintenanceFacilityManagementUnit: ""
    // ...
  },

  // 其他
  // 备注
  remark: "",
});
const rules = computed(() => {
  return {
    facilityName: {
      type: "string",
      required: true,
      message: "请填写设施名称",
      trigger: ["blur"],
    },
    culvertName: {
      type: "string",
      required: true,
      message: "请填写涵洞名称",
      trigger: ["blur", "change"],
    },
    sectionIdLabel: {
      type: "string",
      required: isFromDetail.value ? false : true,
      message: "请选择所属路段",
      trigger: ["blur", "change"],
    },
    updownMarkLabel: {
      type: "string",
      required: isFromDetail.value && formData.value.updownMark ? false : true,
      message: "请选择上下行标记",
      trigger: ["blur", "change"],
    },
    facilityTypeLabel: {
      type: "string",
      required: isFromDetail.value && formData.value.facilityType ? false : true,
      message: "请选择设施类型",
      trigger: ["blur", "change"],
    },
    'facilityFeature.tunnelIdLabel': {
      type: "string",
      required: isFromDetail.value && formData.value.facilityFeature.tunnelId ? false : true,
      message: "请选择所在隧道",
      trigger: ["blur", "change"],
    },
    'facilityFeature.pipelineFacilityTypeLabel': {
      type: "string",
      required: isFromDetail.value && formData.value.facilityFeature.pipelineFacilityType ? false : true,
      message: "请选择管线设施分类",
      trigger: ["blur", "change"],
    },
  };
});
const formConfig = computed(() => {
  let arr = [
    {
      items: [
        {
          type: "mainInput",
          maxlen: 50,
          placeholder: "请输入设施名称",
          unionKey: "facilityName",
        },
        {
          type: "input",
          maxlen: 20,
          label: "设施编码",
          placeholder: "请输入（非必填）",
          unionKey: "facilityCode",
        },
      ],
    },
    {
      title: "基础信息",
      items: [
        {
          type: "select",
          label: "所属路段",
          placeholder: "请选择",
          unionKey: "sectionIdLabel",
          unionKeyDetail: formData.value.sectionIdLabel
            ? "sectionIdLabel"
            : "sectionName",
          optionsPath:
            "/pages/dataAcquisition/relationInfoChoose/roadPartChoose",
        },
        {
          type: "select",
          label: "设施桩号",
          placeholder: "请选择（非必填）",
          unionKey: "facilityStakeIdLabel",
          unionKeyDetail: formData.value.facilityStakeIdLabel
            ? "facilityStakeIdLabel"
            : "facilityStake",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose`,
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: '请先选择所属路段~'
        },
        {
          type: "select",
          label: "上下行标记",
          placeholder: "请选择",
          unionKey: "updownMarkLabel",
          unionKeyDetail: formData.value.updownMarkLabel
            ? "updownMarkLabel"
            : "updownMarkName",
          options: dicts.value.updownMark||[],
        },
        {
          type: "input",
          maxlen: 20,
          label: "所在位置描述",
          placeholder: "请输入（非必填）",
          unionKey: "locationDescribe",
        },
      ],
    },
    {
      title: "设施特征",
      items: [
        {
          type: "select",
          label: "设施类型",
          placeholder: "请选择",
          unionKey: "facilityTypeLabel",
          unionKeyDetail: formData.value.facilityTypeLabel
            ? "facilityTypeLabel"
            : "facilityTypeName",
          options: dicts.value.facilityType||[],
        },
      ],
    },
  ]
  let facilityFeatures = [
    // 标志
    {
      name: 'sign',
      items: [
        {
          type: "select",
          label: "标志分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.signCategoryLabel",
          unionKeyDetail: formData.value.facilityFeature.signCategoryLabel?"facilityFeature.signCategoryLabel":"facilityFeature.signCategoryName",
          options: dicts.value.signCategory||[]
        },
        {
          type: "select",
          label: "标志底板构造形式",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.signBaseConstructionTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.signBaseConstructionTypeLabel?"facilityFeature.signBaseConstructionTypeLabel":"facilityFeature.signBaseConstructionTypeName",
          options: dicts.value.signBaseConstructionType||[]
        },
        {
          type: "select",
          label: "标志立柱构造形式",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.signPillarConstructionTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.signPillarConstructionTypeLabel?"facilityFeature.signPillarConstructionTypeLabel":"facilityFeature.signPillarConstructionTypeName",
          options: dicts.value.signPillarConstructionType||[]
        },
        {
          type: "select",
          label: "标志支撑方式",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.signSupportMethodLabel",
          unionKeyDetail: formData.value.facilityFeature.signSupportMethodLabel?"facilityFeature.signSupportMethodLabel":"facilityFeature.signSupportMethodName",
          options: dicts.value.signSupportMethod||[]
        },
        {
          type: "select",
          label: "标志逆反射材料与照明形式",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.signMaterialAndLightingLabel",
          unionKeyDetail: formData.value.facilityFeature.signMaterialAndLightingLabel?"facilityFeature.signMaterialAndLightingLabel":"facilityFeature.signMaterialAndLightingName",
          options: dicts.value.signMaterialAndLighting||[]
        },
      ]
    },
    // 标线
    {
      name: 'markingLine',
      items: [
        {
          type: "select",
          label: "标线分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.markingLineTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.markingLineTypeLabel?"facilityFeature.markingLineTypeLabel":"facilityFeature.markingLineTypeName",
          options: dicts.value.markingLineType||[]
        },
        {
          type: "select",
          label: "标线颜色",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.markingLineColorLabel",
          unionKeyDetail: formData.value.facilityFeature.markingLineColorLabel?"facilityFeature.markingLineColorLabel":"facilityFeature.markingLineColorName",
          options: dicts.value.markingLineColor||[]
        },
        {
          type: "select",
          label: "标线线划形式",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.markingLineFormLabel",
          unionKeyDetail: formData.value.facilityFeature.markingLineFormLabel?"facilityFeature.markingLineFormLabel":"facilityFeature.markingLineFormName",
          options: dicts.value.markingLineForm||[]
        },
        {
          type: "select",
          label: "标线材料",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.markingLineMaterialLabel",
          unionKeyDetail: formData.value.facilityFeature.markingLineMaterialLabel?"facilityFeature.markingLineMaterialLabel":"facilityFeature.markingLineMaterialName",
          options: dicts.value.markingLineMaterial||[]
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "标线宽度",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.markingLineWidth",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen:5,
          label: "标线数量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.markingLineCount",
        },
      ]
    },
    // 护栏和栏杆
    {
      name: 'guardrail',
      items: [
        {
          type: "select",
          label: "护栏分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.guardrailTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.guardrailTypeLabel?"facilityFeature.guardrailTypeLabel":"facilityFeature.guardrailTypeName",
          options: dicts.value.guardrailType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "护栏材料",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.guardrailMaterial",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "护栏长度",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.guardrailLength",
        },
      ]
    },
    // 视线诱导设施
    {
      name: 'visualInductionFacility',
      items: [
        {
          type: "select",
          label: "视线诱导设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.visualInductionFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.visualInductionFacilityTypeLabel?"facilityFeature.visualInductionFacilityTypeLabel":"facilityFeature.visualInductionFacilityTypeName",
          options: dicts.value.visualInductionFacilityType||[]
        },
      ]
    },
    // 隔离栅
    {
      name: 'isolationFence',
      items: [
        {
          type: "select",
          label: "隔离栅分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.isolationFenceTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.isolationFenceTypeLabel?"facilityFeature.isolationFenceTypeLabel":"facilityFeature.isolationFenceTypeName",
          options: dicts.value.isolationFenceType||[]
        },
      ]
    },
    // 防落网
    {
      name: 'preventCapture',
      items: [
        {
          type: "select",
          label: "防落网",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.preventCaptureTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.preventCaptureTypeLabel?"facilityFeature.preventCaptureTypeLabel":"facilityFeature.preventCaptureTypeName",
          options: dicts.value.preventCaptureType||[]
        },
      ]
    },
    // 防眩设施
    {
      name: 'antiGlareFacility',
      items: [
        {
          type: "select",
          label: "防眩设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.antiGlareFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.antiGlareFacilityTypeLabel?"facilityFeature.antiGlareFacilityTypeLabel":"facilityFeature.antiGlareFacilityTypeName",
          options: dicts.value.antiGlareFacilityType||[]
        },
      ]
    },
    // 限高架
    {
      name: 'heightLimitFrame',
      items: [
        {
          type: "select",
          label: "限高架分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.heightLimitFrameTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.heightLimitFrameTypeLabel?"facilityFeature.heightLimitFrameTypeLabel":"facilityFeature.heightLimitFrameTypeName",
          options: dicts.value.heightLimitFrameType||[]
        },
      ]
    },
    // 监控设施
    {
      name: 'monitoringFacility',
      items: [
        {
          type: "select",
          label: "设备类型代码",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.equipmentTypeCodeLabel",
          unionKeyDetail: formData.value.facilityFeature.equipmentTypeCodeLabel?"facilityFeature.equipmentTypeCodeLabel":"facilityFeature.equipmentTypeCodeName",
          options: dicts.value.equipmentTypeCode||[]
        },
        {
          type: "select",
          label: "监控设备分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.monitoringDeviceTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.monitoringDeviceTypeLabel?"facilityFeature.monitoringDeviceTypeLabel":"facilityFeature.monitoringDeviceTypeName",
          options: dicts.value.monitoringDeviceType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "监控设备管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.monitoringDeviceManagementUnit",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "生产厂家名称",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.manufacturerName",
        },
      ]
    },
    // 照明设施
    {
      name: 'lightingFacility',
      items: [
        {
          type: "select",
          label: "照明设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.lightingFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.lightingFacilityTypeLabel?"facilityFeature.lightingFacilityTypeLabel":"facilityFeature.lightingFacilityTypeName",
          options: dicts.value.lightingFacilityType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "照明设施管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.lightingFacilityManagementUnit",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "生产厂家名称",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.manufacturerName",
        },
      ]
    },
    // 隧道通风设施
    {
      name: 'tunnelVentilationFacility',
      items: [
        {
          type: "select",
          label: "所在隧道",
          placeholder: "请选择",
          unionKey: "facilityFeature.tunnelIdLabel",
          unionKeyDetail: formData.value.facilityFeature.tunnelIdLabel?"facilityFeature.tunnelIdLabel":"facilityFeature.tunnelName",
          optionsPath:
            "/pages/dataAcquisition/relationInfoChoose/tunnelChoose",
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: '请先选择所属路段~'
        },
        {
          type: "select",
          label: "通风设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.ventilationFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.ventilationFacilityTypeLabel?"facilityFeature.ventilationFacilityTypeLabel":"facilityFeature.ventilationFacilityTypeName",
          options: dicts.value.ventilationFacilityType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "设备管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.equipmentManagementUnit",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "生产厂家名称",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.manufacturerName",
        },
      ]
    },
    // 消防设施
    {
      name: 'firefightingFacility',
      items: [
        {
          type: "select",
          label: "消防设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.firefightingFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.firefightingFacilityTypeLabel?"facilityFeature.firefightingFacilityTypeLabel":"facilityFeature.firefightingFacilityTypeName",
          options: dicts.value.firefightingFacilityType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "设备管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.equipmentManagementUnit",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "生产厂家名称",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.manufacturerName",
        },
      ]
    },
    // 管线
    {
      name: 'pipeline',
      items: [
        {
          type: "select",
          label: "管线设施分类",
          placeholder: "请选择",
          unionKey: "facilityFeature.pipelineFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.pipelineFacilityTypeLabel?"facilityFeature.pipelineFacilityTypeLabel":"facilityFeature.pipelineFacilityTypeName",
          options: dicts.value.pipelineFacilityType||[]
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "距路中距离",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.distanceFromRoadCenter",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "下埋深度",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.burialDepth",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.managementUnit",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "埋置长度",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.burialLength",
        },
        {
          type: "datePicker",
          label: "设置日期",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.installationDate",
        },
      ]
    },
    // 气象设施
    {
      name: 'meteorologicalFacility',
      items: [
        {
          type: "select",
          label: "气象设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.meteorologicalFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.meteorologicalFacilityTypeLabel?"facilityFeature.meteorologicalFacilityTypeLabel":"facilityFeature.meteorologicalFacilityTypeName",
          options: dicts.value.meteorologicalFacilityType||[]
        },
        {
          type: "select",
          label: "供电方式",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.powerSupplyModeLabel",
          unionKeyDetail: formData.value.facilityFeature.powerSupplyModeLabel?"facilityFeature.powerSupplyModeLabel":"facilityFeature.powerSupplyModeName",
          options: dicts.value.powerSupplyMode||[]
        },
        {
          type: "select",
          label: "数据传输方式",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.dataTransmissionModeLabel",
          unionKeyDetail: formData.value.facilityFeature.dataTransmissionModeLabel?"facilityFeature.dataTransmissionModeLabel":"facilityFeature.dataTransmissionModeName",
          options: dicts.value.dataTransmissionMode||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "数据采集间隔",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.dataCollectionInterval",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "气象设施所属管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.managementUnit",
        },
      ]
    },
    // 收费设施
    {
      name: 'tollFacility',
      items: [
        {
          type: "select",
          label: "收费站分类代码",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.tollStationTypeCodeLabel",
          unionKeyDetail: formData.value.facilityFeature.tollStationTypeCodeLabel?"facilityFeature.tollStationTypeCodeLabel":"facilityFeature.tollStationTypeCodeName",
          options: dicts.value.tollStationTypeCode||[]
        },
        {
          type: "select",
          label: "收费站方向代码",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.tollStationDirectionCodeLabel",
          unionKeyDetail: formData.value.facilityFeature.tollStationDirectionCodeLabel?"facilityFeature.tollStationDirectionCodeLabel":"facilityFeature.tollStationDirectionCodeName",
          options: dicts.value.tollStationDirectionCode||[]
        },
        {
          type: "select",
          label: "收费性质代码",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.tollNatureCodeLabel",
          unionKeyDetail: formData.value.facilityFeature.tollNatureCodeLabel?"facilityFeature.tollNatureCodeLabel":"facilityFeature.tollNatureCodeName",
          options: dicts.value.tollNatureCode||[]
        },
        {
          type: "select",
          label: "是否有ETC车道",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.hasETCLaneLabel",
          unionKeyDetail: formData.value.facilityFeature.hasETCLaneLabel?"facilityFeature.hasETCLaneLabel":"facilityFeature.hasETCLaneName",
          options: dicts.value.yes_no||[]
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "ETC进出车道数量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.etcLaneCount",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "计重车道数",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.weightChargeLaneCount",
        },
      ]
    },
    // 通信设施
    {
      name: 'communicationFacility',
      items: [
        {
          type: "select",
          label: "通信设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.communicationFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.communicationFacilityTypeLabel?"facilityFeature.communicationFacilityTypeLabel":"facilityFeature.communicationFacilityTypeName",
          options: dicts.value.communicationFacilityType||[]
        },
        {
          type: "select",
          label: "通信方式",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.communicationModeLabel",
          unionKeyDetail: formData.value.facilityFeature.communicationModeLabel?"facilityFeature.communicationModeLabel":"facilityFeature.communicationModeName",
          options: dicts.value.communicationMode||[]
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "通信频率",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.communicationFrequency",
        },
      ]
    },
    // 服务区
    {
      name: 'serviceArea',
      items: [
        {
          type: "select",
          label: "服务区设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.serviceAreaFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.serviceAreaFacilityTypeLabel?"facilityFeature.serviceAreaFacilityTypeLabel":"facilityFeature.serviceAreaFacilityTypeName",
          options: dicts.value.serviceAreaFacilityType||[]
        },
        {
          type: "select",
          label: "服务区等级",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.serviceAreaLevelLabel",
          unionKeyDetail: formData.value.facilityFeature.serviceAreaLevelLabel?"facilityFeature.serviceAreaLevelLabel":"facilityFeature.serviceAreaLevelName",
          options: dicts.value.serviceAreaLevel||[]
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "服务区停车位数量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.parkingSpaceCount",
        },
      ]
    },
    // 养护设施
    {
      name: 'maintenanceFacility',
      items: [
        {
          type: "select",
          label: "养护设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.maintenanceFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.maintenanceFacilityTypeLabel?"facilityFeature.maintenanceFacilityTypeLabel":"facilityFeature.maintenanceFacilityTypeName",
          options: dicts.value.maintenanceFacilityType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "养护设施管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.maintenanceFacilityManagementUnit",
        },
      ]
    },
    // 治超检测站
    {
      name: 'overloadDetectionStation',
      items: [
        {
          type: "select",
          label: "检测站分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.detectionStationTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.detectionStationTypeLabel?"facilityFeature.detectionStationTypeLabel":"facilityFeature.detectionStationTypeName",
          options: dicts.value.detectionStationType||[]
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "检测站占地面积",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.landArea",
        },
        {
          type: "select",
          label: "检测站检测方法",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.detectionMethodLabel",
          unionKeyDetail: formData.value.facilityFeature.detectionMethodLabel?"facilityFeature.detectionMethodLabel":"facilityFeature.detectionMethodName",
          options: dicts.value.detectionMethod||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "检测车道号",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.detectionLaneNumber",
        }, 
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "日均检测货车数量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.averageDailyTruckDetectionCount",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "卸载区占地面积",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.unloadingArea",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "超限检测设备",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.overloadDetectionEquipment",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "超限检测设备名称",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.equipmentName",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "设备日均检测货车数量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.averageDailyTruckDetectionEquipmentCount",
        },
        {
          type: "select",
          label: "移动式轴载秤重设施类型编码",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.mobileAxleWeighingFacilityTypeCodeLabel",
          unionKeyDetail: formData.value.facilityFeature.mobileAxleWeighingFacilityTypeCodeLabel?"facilityFeature.mobileAxleWeighingFacilityTypeCodeLabel":"facilityFeature.mobileAxleWeighingFacilityTypeCodeName",
          options: dicts.value.mobileAxleWeighingFacilityTypeCode||[]
        },
      ]
    },
    // 观测站
    {
      name: 'observationStation',
      items: [
        {
          type: "select",
          label: "观测站分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.observationStationTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.observationStationTypeLabel?"facilityFeature.observationStationTypeLabel":"facilityFeature.observationStationTypeName",
          options: dicts.value.observationStationType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "检测调查设施名称",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.detectionSurveyFacilityName",
        }, 
        {
          type: "select",
          label: "检测调查设备类型代码",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.detectionSurveyEquipmentTypeCodeLabel",
          unionKeyDetail: formData.value.facilityFeature.detectionSurveyEquipmentTypeCodeLabel?"facilityFeature.detectionSurveyEquipmentTypeCodeLabel":"facilityFeature.detectionSurveyEquipmentTypeCodeName",
          options: dicts.value.detectionSurveyEquipmentTypeCode||[]
        },
      ]
    },
    // 停车区
    {
      name: 'parkingArea',
      items: [
        {
          type: "select",
          label: "停车区分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.parkingAreaTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.parkingAreaTypeLabel?"facilityFeature.parkingAreaTypeLabel":"facilityFeature.parkingAreaTypeName",
          options: dicts.value.parkingAreaType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "停车区管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.parkingAreaManagementUnit",
        },
      ]
    },
    // 绿化设施
    {
      name: 'greeningFacility',
      items: [
        {
          type: "select",
          label: "绿化设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.greeningFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.greeningFacilityTypeLabel?"facilityFeature.greeningFacilityTypeLabel":"facilityFeature.greeningFacilityTypeName",
          options: dicts.value.greeningFacilityType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "绿化设施管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.greeningFacilityManagementUnit",
        },
        {
          type: "select",
          label: "可绿化路段起点桩号",
          placeholder: "请选择（非必填）",
          unionKey: "startStakeLabel",
          unionKeyDetail: formData.value.startStakeLabel
            ? "startStakeLabel"
            : "startStake",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose`,
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: '请先选择所属路段~'
        },
        {
          type: "select",
          label: "可绿化路段止点桩号",
          placeholder: "请选择（非必填）",
          unionKey: "endStakeLabel",
          unionKeyDetail: formData.value.endStakeLabel
            ? "endStakeLabel"
            : "endStake",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose`,
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: '请先选择所属路段~'
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "已绿化信息",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.greeningInfo",
        },
        {
          type: "datePicker",
          label: "种植日期",
          placeholder: "请选择（非必填）",
          unionKey: "plantingDate",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "当年栽种换算数量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.annualPlantingConversionQuantity",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "当年新增绿地面积",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.annualNewGreenArea",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "当年绿化资金投入",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.annualGreeningInvestment",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "绿化植物名称",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.plantName",
        },
        {
          type: "select",
          label: "绿化植物种类代码",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.plantTypeCodeLabel",
          unionKeyDetail: formData.value.facilityFeature.plantTypeCodeLabel?"facilityFeature.plantTypeCodeLabel":"facilityFeature.plantTypeCodeName",
          options: dicts.value.plantTypeCode||[]
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "绿化植物高度",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.plantHeight",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "绿化植物胸径",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.plantDiameter",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "已绿化里程",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.greeningMileage",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 4,
          label: "绿化率(上限100)",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.greeningRate",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "已有绿化面积",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.existingGreenArea",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "苗圃名称",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.nurseryName",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "苗圃总面积",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.nurseryTotalArea",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "统计期内育苗数量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.annualSeedlingQuantity",
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "苗圃单位负责人",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.nurseryUnitManager",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "存圃量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.nurseryStockQuantity",
        },
      ]
    },
    // 声屏障
    {
      name: 'greenSoundBarrier',
      items: [
        {
          type: "select",
          label: "声屏障分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.greenSoundBarrierTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.greenSoundBarrierTypeLabel?"facilityFeature.greenSoundBarrierTypeLabel":"facilityFeature.greenSoundBarrierTypeName",
          options: dicts.value.greenSoundBarrierType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "声屏障管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.soundBarrierManagementUnit",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "服务区停车位数量",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.parkingSpaceCount",
        },
      ]
    },
    // 污水处理设施
    {
      name: 'sewageTreatmentFacility',
      items: [
        {
          type: "select",
          label: "污水处理设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.sewageTreatmentFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.sewageTreatmentFacilityTypeLabel?"facilityFeature.sewageTreatmentFacilityTypeLabel":"facilityFeature.sewageTreatmentFacilityTypeName",
          options: dicts.value.sewageTreatmentFacilityType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "污水处理设施管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.sewageTreatmentFacilityManagementUnit",
        },
      ]
    },
    // 水土保护设施
    {
      name: 'soilAndWaterConservationFacility',
      items: [
        {
          type: "select",
          label: "水土保护设施分类",
          placeholder: "请选择（非必填）",
          unionKey: "facilityFeature.soilAndWaterConservationFacilityTypeLabel",
          unionKeyDetail: formData.value.facilityFeature.soilAndWaterConservationFacilityTypeLabel?"facilityFeature.soilAndWaterConservationFacilityTypeLabel":"facilityFeature.soilAndWaterConservationFacilityTypeName",
          options: dicts.value.soilAndWaterConservationFacilityType||[]
        },
        {
          type: "input",
          maxlen: 20,
          inputType: 'text',
          label: "水土保护设施设施管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "facilityFeature.soilAndWaterConservationFacilityManagementUnit",
        },
      ]
    }
  ]
  let other = [
    {
      title: "其他",
      items: [
        {
          type: "textarea",
          label: "备注",
          maxlen: 150,
          placeholder: "如有备注请输入（非必填）",
          unionKey: "remark",
        },
      ],
    },
  ]
  if ((formData.value.facilityType !== null && formData.value.facilityType !== undefined)||facilityTypeValue.value) {
    let filterItem = facilityFeatures.find((item) => item.name === formData.value.facilityType) || facilityFeatures.find((item) => item.name === facilityTypeValue.value);
    console.log('切换设施类型1356',facilityTypeValue.value,formData.value.facilityType);
    formData.value.facilityFeature = {};
    if(filterItem){
      arr[2].items.push(...filterItem?.items);
      console.log('filterItem变化',formData.value.facilityType,filterItem?.items);
      return [...arr, ...other]
    }
  }
  return [...arr, ...other]
});

// 查看表单详情
let facilityTypeValue = ref('');
const getDetail = async (id) => {
  const res = await DataAcquisitionService.roadRoadsideFacilityDetail(id);
  facilityTypeValue.value = res.data.facilityType;
  setTimeout(() => {
    formData.value = res.data;
    console.log("沿线设施详情", facilityTypeValue.value,formData.value);
  }, 300);
};

// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  console.log("formItem数据修改回调", unionKey, val);
  setNestedValue(formData.value, unionKey.split("."), val);
  console.log("onFormInpChange", formData.value);
};
// formItem 从选择页面回调的 select 数据修改
let autoFormRef = ref(null);
const onFormSelChange = () => {
  // 1如果选择的是 “所属路段”，则需要清空表单中选择的桩号信息
  if (
    dataAcquisitionInfo.stateData.key === "sectionIdLabel" &&
    formData.value.sectionId !== dataAcquisitionInfo.stateData.value
  ) {
    formData.value.facilityStakeId = "";
    formData.value.facilityStakeIdLabel = "";
    formData.value.startStake = "";
    formData.value.startStakeLabel = "";
    formData.value.endStake = "";
    formData.value.endStakeLabel = "";
  }
  // 2处理select选择的label
  setNestedValue(
    formData.value,
    dataAcquisitionInfo.stateData.key.split("."),
    dataAcquisitionInfo.stateData.label
  );
  // 3处理select选择的value
  let str = dataAcquisitionInfo.stateData.key.replace(/Label$/, "");
  setNestedValue(
    formData.value,
    str.split("."),
    dataAcquisitionInfo.stateData.value
  );
  console.log("选择更改", formData.value);
  if(autoFormRef.value?.formRef){
    autoFormRef.value.formRef.validateField(dataAcquisitionInfo.stateData.key);
  }
};
const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
};

// 暂存时校验部分表单字段
const validateFormFields = async () => {  
  autoFormRef.value.formRef.clearValidate();  
  let mainUnionKey = 'facilityName';  
  let roadUnionKey = 'sectionIdLabel';  
  try {  
    // 使用 async/await 等待 validateField 方法的 Promise 解析  
    let err = await new Promise((resolve) => {  
      autoFormRef.value.formRef.validateField(mainUnionKey, (validateErr) => {  
        resolve(validateErr);  
      });  
    });  
    let err2 = await new Promise((resolve) => {  
      autoFormRef.value.formRef.validateField(roadUnionKey, (validateErr) => {  
        resolve(validateErr);  
      });  
    });  
    console.log('暂存校验结果1', err, err2);  
    if (!err.length && !err2.length) {  
      console.log('暂存校验结果2', flag.value); // 注意：这里的 flag 需要在外部定义并在需要时传递  
      flag.value = true; // 如果 flag 是组件内部状态，考虑使用 Vue 的响应式系统（如 ref 或 reactive）  
    }  
  } catch (error) {  
    console.error('验证过程中发生错误:', error);  
  }  
}

// form表单提交回调
let flag = ref(false);
let toast = ref(null);
let res = ref({});
let btnLoading = ref(""); // '0'-暂存按钮；'1'-提交按钮
const onFormSubmit = async ({ data, status }) => {
  console.log("提交沿线设施采集", data, status);
  if(status == 0){
    await validateFormFields();
  }else{
    flag.value = true;
  }
  if(!flag.value){
    console.log('校验不通过，return',flag.value);
    backModal.value.close();
    return;
  }
  // 正式-1；草稿-0
  data.status = status;
  data.facilityCode = data.facilityCode || null;
  // 沿线设施，关联字段匹配需要，特殊处理facilityType字段
  data.facilityFeature.facilityType = data.facilityType;

  btnLoading.value = String(status);

  // 将临时新增到本地保存的桩号，一同传递给后端， 
  if(pileNumberInfo.pileNumbers?.length){
    data.newStakes = pileNumberInfo.pileNumbers;
    data.newStakes.forEach(item=>{
      item.status = status;
      // // 经纬度转换
      // let trans = transformLngLat(item.longitude,item.latitude);
      // item.longitude = String(trans[0]);
      // item.latitude = String(trans[1]);
    });
  }

  try {
    if (data.id) {
      // 编辑
      res.value = await DataAcquisitionService.roadRoadsideFacilityEdit(data);
    } else {
      // 新增
      res.value = await DataAcquisitionService.roadRoadsideFacilityAdd(data);
    }
    btnLoading.value = "";
    console.log("res！！！！", res.value.data);
    if (res.value.code == 200) {
      // 清空本地保存的新增桩号
      pileNumberInfo.clearPileNumber();
      toast.value.show({
        type: "success",
        message: `数据${status?'提交':'暂存'}成功`,
        complete() {
          uni.navigateBack({
            delta: 1,
          });
        },
      });
    }
  } catch (error) {
    btnLoading.value = "";
  }
};

// 切换表单状态回调：详情-》编辑
const onFormEdit = () => {
  isViewDetail.value = false;
  isFromDetail.value = true;
};

</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding-top: v-bind(systemBarHeight);
  background: #f4f8ff;
}
.uv-nav-slot {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}
.content {
  height: calc(100% - 208rpx);
  margin-top: 0rpx;
  .info_card {
    background: #fff;
    margin-bottom: 48rpx;
    .info_title {
      height: 88rpx;
      padding: 22rpx 40rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #8e8e8e;
      line-height: 44rpx;
      box-sizing: border-box;
      border-bottom: 2rpx solid #f0f0f0;
    }
  }
}
.bottom_btns {
  position: fixed;
  bottom: 0rpx;
  width: 100vw;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  background: #f4f8ff;
  .btn {
    width: 256rpx;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    box-sizing: border-box;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .save_btn {
    color: #4378ff;
    background: #fff;
    border: 2rpx solid #4378ff;
  }
  .sub_btn {
    color: #fff;
    background: #4378ff;
  }
}
</style>
