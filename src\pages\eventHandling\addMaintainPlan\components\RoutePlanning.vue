<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-22 11:34:42
 * @Description: 
-->
<template>
  <view class="map-container" id="mapContainer">
    <view>
      <web-view
        class="webview_box"
        :src="webviewMapUrl"
        @onPostMessage="handlePostMessage"
        @message="handlePostMessage"
        ref="webview"
        :webview-styles="webviewStyles"
      >
      </web-view>
    </view>
    <view v-if="!pageLoading" class="bottom-box">
      <view class="search-box">
        <view class="search-item">
          <image
            class="search-icon"
            :src="eventHandling_route_plan_start"
            mode="scaleToFill"
          />
          <view class="search-text">
            {{ state.stakeAddress }}
          </view>
        </view>
        <view class="search-item">
          <image
            class="search-icon"
            :src="eventHandling_route_plan_end"
            mode="scaleToFill"
          />
          <view class="search-text">
            {{ state.endStakeAddress }}
          </view>
        </view>
      </view>
      <view class="tabs-box">
        <uv-tabs
          :list="tabs"
          :current="activeTab"
          lineWidth="64"
          lineColor="#4378FF"
          :activeStyle="{
            color: '#4378FF',
            fontWeight: 'bold',
            fontSize: '14px',
            paddingBottom: '10px',
          }"
          :inactiveStyle="{
            color: '#606060',
            fontSize: '14px',
            paddingBottom: '10px',
          }"
          itemStyle="padding:0 15px  ; height: 34px;"
          :customStyle="{
            borderBottom: '1px solid #F2F2F2',
            marginBottom: '24px',
          }"
          @click="tabClick"
        >
        </uv-tabs>
      </view>
      <view class="res-list">
        <view
          class="res-list-item"
          :class="[activePlan === item.type ? 'item-active' : '']"
          v-for="(item, index) in state.routeList"
          :key="index"
          @click="planClick(item)"
        >
          <view class="res-list-item-time">{{ formatSeconds(item.time) }}</view>
          <view class="res-list-item-text">
            {{ item.distance + "公里·" + item.tip }}
          </view>
        </view>
      </view>
      <view class="action-btn">
        <view class="cancel" @click="handleCancel">取 消</view>
        <view v-if="confirmLoading" class="confirm-disabled">
          <uv-loading-icon
            class="rotate-icon"
            size="24"
            color="#62697b"
            mode="circle"
          ></uv-loading-icon>
          确 定
        </view>
        <view v-else class="confirm" @click="handleConfirm"> 确 定 </view>
      </view>
    </view>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      bgColor="rgba(255,255,255,0.5)"
      font-size="24rpx"
    ></uv-loading-page>
  </view>
</template>
<script setup>
import { reactive, ref, computed } from "vue";
import { onLoad, onReady } from "@dcloudio/uni-app";
import eventHandling_route_plan_start from "@/static/icon/eventHandling_route_plan_start.png";
import eventHandling_route_plan_end from "@/static/icon/eventHandling_route_plan_end.png";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
dayjs.extend(duration);

const formatSeconds = (seconds) => {
  const duration = dayjs.duration(seconds, "seconds");

  const hours = duration.hours();
  const minutes = duration.minutes();
  const secs = duration.seconds();

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟`;
  } else {
    return `${secs}秒`;
  }
};
const handleCancel = () => {
  uni.navigateBack({
    delta: 1,
  });
};
const confirmLoading = ref(false);
const handleConfirm = () => {
  confirmLoading.value = true;
  wv.value.evalJS(`receiveData({type:'getPng',data:{}})`);
};

const pageLoading = ref(true);
const pages = getCurrentPages();
let wv = ref(null);
const state = reactive({
  screenshot: "",
  routeList: [],
  baseCopyData: [],
  stakeAddress: "",
  endStakeAddress: "",
});
const handlePostMessage = (data) => {
  uni.setNavigationBarTitle({
    title: "路线规划",
  });
  const { screenshot, routeList } = data.detail.data[0];
  if (screenshot) {
    state.screenshot = screenshot;
    dealFileAndUpload(screenshot, "路线规划");
  }

  if (routeList) {
    state.baseCopyData = routeList;
    pageLoading.value = false;
    switch (activeTab.value) {
      case 0:
        state.routeList = state.baseCopyData.filter((item) => item.type !== 3);
        break;
      case 1:
        state.routeList = state.baseCopyData.filter((item) => item.type === 3);
        break;
      default:
        break;
    }
  }
};
import { uploadFilePromise } from "@/utils";

// 处理文件
const dealFileAndUpload = async (dataUrl) => {
  const tempFilePath = await base64ToPath(dataUrl);
  const { code, data } = await uploadFilePromise(tempFilePath);
  console.log("上传文件", code, data);
  if (code === 200) {
    uni.$emit("updateRouteInfo", {
      url: data.url,
      tab: activeTab.value,
      type: activePlan.value,
    });
    uni.navigateBack({
      data: 1,
    });
  } else {
    uni.showToast({
      icon: "none",
      title: "保存图片失败！",
    });
  }
  confirmLoading.value = false;
};

const base64ToPath = (base64) => {
  return new Promise((resolve, reject) => {
    const [, format, bodyData] =
      /data:image\/(\w+);base64,(.*)/.exec(base64) || [];
    const bitmap = new plus.nativeObj.Bitmap("bitmap" + Date.now());
    bitmap.loadBase64Data(
      base64,
      () => {
        if (!format) {
          reject(new Error("ERROR_BASE64SRC_PARSE"));
        }
        const time = new Date().getTime();
        const filePath = `_doc/uniapp_temp/${time}.${format}`;

        bitmap.save(
          filePath,
          {},
          () => {
            bitmap.clear();
            resolve(filePath);
          },
          (error) => {
            bitmap.clear();
            console.error(`${JSON.stringify(error)}`);
            reject(error);
          }
        );
      },
      (error) => {
        bitmap.clear();
        console.error(`${JSON.stringify(error)}`);
        reject(error);
      }
    );
  });
};

// tabs
const activeTab = ref(0);
const tabs = reactive([
  {
    type: 0,
    name: "驾车",
  },
  {
    type: 1,
    name: "步行",
  },
]);
const tabClick = (record) => {
  activeTab.value = record.type;
  switch (record.type) {
    case 0:
      planClick({ type: 0 });
      state.routeList = state.baseCopyData.filter((item) => item.type !== 3);
      break;
    case 1:
      planClick({ type: 3 });
      state.routeList = state.baseCopyData.filter((item) => item.type === 3);
      console.log(state, "tabClick-state");
      break;
    default:
      break;
  }
};
// planList
const activePlan = ref(0);
const planClick = (record) => {
  activePlan.value = record.type;
  wv.value.evalJS(
    `receiveData({type:'changeRoute',data:{routeType:${record.type}}})`
  );
};

const webviewMapUrl = ref("");
onLoad((options) => {
  const {
    stakeLongitude,
    stakeLatitude,
    stakeAddress,
    endStakeLongitude,
    endStakeLatitude,
    endStakeAddress,
  } = options || {};
  state.stakeAddress = stakeAddress;
  state.endStakeAddress = endStakeAddress;
  const startParams = `stakeLongitude=${String(stakeLongitude)}&stakeLatitude=${String(stakeLatitude)}&stakeAddress=${String(stakeAddress)}`;
  const endParams = `endStakeLongitude=${String(endStakeLongitude)}&endStakeLatitude=${String(endStakeLatitude)}&endStakeAddress=${String(endStakeAddress)}`;
  activeTab.value = +options?.activeTab || 0;
  activePlan.value = +options?.activePlan || 0;
  webviewMapUrl.value = `./static/tianditu/routePlaningMap.html?&${startParams}&${endParams}&activeTab=${activeTab.value}&activePlan=${activePlan.value}`;
});

const getWebView = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const webView = pages[pages.length - 1].$getAppWebview().children()[0];
      console.log("webView", webView);
      resolve(webView);
    }, 1000);
  });
};

onReady(async () => {
  wv.value = await getWebView();
  uni.setNavigationBarTitle({
    title: "路线规划",
  });
});

const webviewStyles = computed(() => {
  return {
    position: "fixed",
    width: "100%",
    height: "40%",
    top: "0px",
    left: "0",
  };
});
</script>

<style lang="scss" scoped>
.map-container {
  min-height: 100vh;
  background: #ffffff;
}

.bottom-box {
  width: 100%;
  height: 56%;
  background: #ffffff;
  border-radius: 48rpx 48rpx 0rpx 0rpx;
  position: fixed;
  bottom: 0rpx;
  box-sizing: border-box;
  padding: 40rpx;
  .search-box {
    background: #f4f6ff;
    border-radius: 16rpx;
    box-sizing: border-box;
    .search-item {
      display: flex;
      align-items: center;
      gap: 0 20rpx;
      padding: 24rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      box-sizing: border-box;
      &:nth-child(1) {
        position: relative;
        &::after {
          content: "";
          width: 560rpx;
          height: 2rpx;
          background: #e1e4f2;
          position: absolute;
          // right: 0;
          left: 80rpx;
          bottom: -2rpx;
        }
        &::before {
          content: "";
          width: 2rpx;
          height: 28rpx;
          background: #e1e4f2;
          border-radius: 4rpx;
          position: absolute;
          left: 40rpx;
          bottom: -16rpx;
        }
      }
      .search-icon {
        display: block;
        width: 36rpx;
        height: 36rpx;
      }
      .search-text {
        flex: 1;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
  .tabs-box {
    margin: 40rpx 0;
  }
  .res-list {
    height: 340rpx;
    .res-list-item {
      display: flex;
      justify-content: space-between;
      height: 84rpx;
      border-radius: 8rpx;
      background: #ffffff;
      border: 2rpx solid #d9d9d9;
      margin-bottom: 28rpx;
      padding: 20rpx 16rpx 20rpx 28rpx;
      box-sizing: border-box;

      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      &.item-active {
        background: #4378ff;
        border: 2rpx solid #4378ff;
        .res-list-item-time {
          color: #ffffff;
        }
        .res-list-item-text {
          color: #ffffff;
        }
      }
      .res-list-item-time {
        font-size: 32rpx;
        color: #373737;
      }
      .res-list-item-text {
        font-size: 28rpx;
        color: #a09f9f;
      }
    }
  }
  .action-btn {
    display: flex;
    justify-content: space-between;
    gap: 0 40rpx;
    height: 96rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    margin-top: 20rpx;
    .cancel {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 200rpx;
      height: 100%;
      color: #62697b;
      border-radius: 8rpx;
      background: #dddddd;
    }
    .confirm {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      height: 100%;
      color: #ffffff;
      border-radius: 8rpx;
      background: #4378ff;
    }
    .confirm-disabled {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      height: 100%;
      color: #62697b;
      border-radius: 8rpx;
      background: #dddddd;
      .rotate-icon {
        margin-right: 8px;
      }
    }
  }
}
</style>
