// 按钮样式
button[type="default"] {
  color: #4378ff;
  background-color: #fff;
  font-weight: 800;
  font-size: 68rpx;
}
// 字体颜色
.cfff {
  color: #fff;
}
.c000 {
  color: #000;
}
.c666 {
  color: #666;
}

// 字体大小
.ft20 {
  font-size: 20rpx;
  line-height: 28rpx;
}
.ft24 {
  font-size: 24rpx;
  line-height: 34rpx;
}
.ft28 {
  font-size: 28rpx;
  line-height: 40rpx;
}
.ft32 {
  font-size: 32rpx;
  line-height: 44rpx;
}
.ft34 {
  font-size: 34rpx;
  line-height: 48rpx;
}

// 字体加粗
.ft-bold {
  font-weight: 800;
}

// 字体类型
.mainFont {
  font-family: PingFang SC, PingFang SC;
}

// 外边距设置
.mt8 {
  margin-top: 8rpx;
}
.mt10 {
  margin-top: 10rpx;
}
.mt12 {
  margin-top: 12rpx;
}
.mt16 {
  margin-top: 16rpx;
}
.ml8 {
  margin-left: 8rpx;
}
.mb28 {
  margin-bottom: 28rpx;
}

// flex布局
.flex-between {
  display: flex;
  justify-content: space-between;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
}
.flex-ac {
  display: flex;
  align-items: center;
}
.flex-center {
  display: flex;
  justify-content: center;
}
.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-between-start {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.flex-start-center {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.flex-baseline-center {
  display: flex;
  justify-content: flex-start;
  align-items: baseline;
}

.flex-start-top {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.flex-start-bottom {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-end-top {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

.flex-end-center {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.flex-between-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.same-add-title {
  font-size: 32rpx;
  color: #404040;
  padding-left: 20rpx;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    top: 54%;
    left: 0;
    transform: translate(0, -50%);
    width: 8rpx;
    height: 32rpx;
    background: #1890ff;
    border-radius: 4rpx;
  }

  &.required {
    span {
      position: relative;
      display: inline-block;
      height: 40rpx;
      line-height: 40rpx;

      &::after {
        content: "*";
        position: absolute;
        top: -8rpx;
        right: -20rpx;
        width: 4rpx;
        height: 4rpx;
        color: #ff3838;
      }
    }
  }
}
.card-box {
    padding: 28rpx;
    border-radius: 24rpx;
    background-color: #fff;
}
.shm-same-add-title {
    font-size: 28rpx;
    color: #404040;
    padding-left: 20rpx;
    position: relative;
  
    &::after {
      content: "";
      position: absolute;
      top: 54%;
      left: 0;
      transform: translate(0, -50%);
      width: 6rpx;
      height: 24rpx;
      background:#4378FF;
      border-radius: 4rpx;
    }
}
// 超出省略
.ellipsis-one {
    display: -webkit-box;
    /**对象作为伸缩盒子模型展示**/
    -webkit-box-orient: vertical;
    /**设置或检索伸缩盒子对象的子元素的排列方式**/
    -webkit-line-clamp: 1;
    /**显示的行数**/
    overflow: hidden;
    /**隐藏超出的内容**/
    text-overflow: ellipsis;
    /*超出内容显示为省略号*/
    word-break: break-all;
  }