<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-11 14:31:17
 * @Description: 
-->
<template>
  <view>
    <uv-popup ref="popup" mode="bottom" round="24" @change="change" @maskClick="maskClick">
      <view class="content">
        <view class="title">{{ title || "字典选择" }}</view>
        <scroll-view :scroll-y="true" class="items_box">
          <!-- <view class="xxx">{{curChoosedKey}}</view> -->
          <view
            :class="[
              'item',
              item.dictValue + item.dictKey == curChoosedKey ? 'active' : '',
              item.disabled ? 'dis_item' : '',
            ]"
            v-for="item in options"
            :key="item.dictKey"
            @click="select(item)"
            >{{ item.dictValue }}</view
          >
        </scroll-view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { reactive, ref, computed } from "vue";
const props = defineProps({
  curChoosedKey: {
    type: String,
    required: true,
    default: "",
  },
  unionKey: {
    type: String,
    required: false,
    default: "",
  },
  title: {
    type: String,
    required: true,
    default: "",
  },
  options: {
    type: Array,
    required: true,
    default: () => [],
  },
});

const emit = defineEmits(["onSelect", "onMaskClick"]);
const select = (item) => {
  if (item.disabled && item.dictValue + item.dictKey != props.curChoosedKey){
    return;
  }
  emit("onSelect", item);
};

// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const change = (e) => {
  // console.log("弹窗状态改变：", e);
};
const maskClick = () => {
  emit("onMaskClick");
};

defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.content {
  padding: 40rpx;
  box-sizing: border-box;
  margin-bottom: 100rpx;
  .title {
    text-align: center;
    border-bottom: 2rox solid #f0f0f0;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #373737;
    line-height: 44rpx;
  }
  .items_box {
    max-height: 600rpx;
    margin-top: 40rpx;
    text-align: center;
    .item {
      // margin-bottom: 16rpx;
      padding: 16rpx 0 10rpx 0;
      // border-top: 2rpx solid #F0F0F0;
      // border-bottom: 2rpx solid #F0F0F0;
      font-family: PingFang SC, PingFang SC;
      font-size: 32rpx;
      color: #404040;
      line-height: 44rpx;
      margin-bottom: 6rpx;
    }
    .dis_item {
      font-size: 32rpx;
      font-weight: 500;
      color: #a09f9f;
      border-radius: 16rpx;
      background: rgba($color: #d9d9d9, $alpha: 1);
    }
    .active {
      font-size: 38rpx;
      font-weight: 500;
      color: #065bff;
      background: none;
    }
  }
}
</style>
