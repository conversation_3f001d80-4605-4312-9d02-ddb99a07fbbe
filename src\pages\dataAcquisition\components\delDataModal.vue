<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-08-07 16:47:08
 * @Description: 
-->
<template>
  <view>
    <uv-popup
      ref="popup"
      mode="center"
      round="8"
      @change="change"
      :customStyle="customStyle"
    >
      <view class="box">
        <template v-if="delMode==='nomal'">
          <view class="title">确定要删除</view>
          <view class="content">{{ delMessage }}？</view>
          <view class="btn_box">
            <view class="cancel btn" @click="onCacel">取消</view>
            <uv-button
              :loading="delBtnLoading"
              :custom-style="btnStyle"
              :customTextStyle="btnTextStyle"
              text="确定删除"
              @click="onComfirm"
            ></uv-button>
          </view>
        </template>
        <template v-else>
          <view class="content">{{delMessage}}</view>
          <view class="notice">如仍需删除请前往网页端操作</view>
          <view class="btn_box">
            <view class="comfirm btn" @click="onCacel">我知道了</view>
          </view>
        </template>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
const props = defineProps({
  delMessage:{
    type: String,
    required: true,
    default:''
  },
  delBtnLoading:{
    type: Boolean,
    required: true,
    default:false
  },
  // nomal-常规；error-暂不支持删除
  delMode:{
    type: String,
    required: true,
    default: 'nomal'
  }
})


const customStyle = reactive({
  width: "608rpx",
  padding: "48rpx 44rpx",
  boxSizing: "border-box",
});

// 按钮样式
const btnStyle = reactive({
  width: "520rpx",
  height: "72rpx",
  lineHeight: "44rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 500,
  background: '#4378FF',
  color:' #FFFFFF',
  border: "2rpx solid #4378ff",
  marginBottom: '0rpx'
});
const btnTextStyle = reactive({
  fontSize: "32rpx",
});


const emit = defineEmits(["onDelCallback"]);
// 取消
const onCacel = () => {
  emit("onDelCallback", { type: "onCacel" });
};
// 确定删除
const onComfirm = () => {
  emit("onDelCallback", { type: "onComfirm" });
};
// 打开弹窗
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const change = (e) => {
  console.log("弹窗状态改变：", e);
};

defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
.box {
  text-align: center;
  box-sizing: border-box;
  .title,
  .content {
    font-weight: 500;
    font-size: 36rpx;
    color: #373737;
    line-height: 50rpx;
  }
  .content {
    margin-top: 20rpx;
  }
  .notice{
    margin-top: 20rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #A09F9F;
    line-height: 40rpx;
  }
  .btn_box {
    margin-top: 92rpx;
    .btn {
      width: 520rpx;
      height: 72rpx;
      border-radius: 8rpx;
      padding: 14rpx 0;
      font-weight: 500;
      font-size: 32rpx;
      line-height: 44rpx;
      text-align: center;
      margin-bottom: 28rpx;
      box-sizing: border-box;
    }
    // .btn:last-child {
    //   margin-bottom: 0 !important;
    // }
    .cancel {
      background: #e4e4e4;
      color: #373737;
    }
    .comfirm {
      background: #4378ff;
      color: #ffffff;
    }
  }
}
</style>
