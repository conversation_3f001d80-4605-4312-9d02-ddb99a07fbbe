/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-05-19 14:43:25
 * @Description:
 */
import BaseService from "../../request";

class ShmService extends BaseService {
  constructor() {
    super();
  }
  // 设备扫码
  getDeviceScanInfo(params) {
    return this.get(`/shm/app/device/scan`, params);
  }
  // 获取设备列表
  getDeviceHistoryList(params) {
    return this.get(`/shm/app/idList`, params);
  }
  // 根据key查询资产数据
  getPropertyByKey(params) {
    return this.get(`/project-resource/assetData/list`, params);
  }
  // 根据桥梁id查询构件信息
  selectComponentDataList(params) {
    return this.get(
      `project-resource/bridgeComponentData/selectComponentDataList/${params.bridgeId}`
    );
  }
  // 监测位置表不分页
  getMonitoringLocation(params) {
    return this.get(`/shm/monitoringLocation/list`, params);
  }
  // 监测设备详情
  // getDeviceInfoDetail(deviceId) {
  //   return this.get(`/shm/deviceInfo/detail/${deviceId}`);
  // }
  // 监测设备详情
  getDeviceInfoDetail(deviceId) {
    return this.get(`/shm/app/deviceInfo/${deviceId}`);
  }
  // 自动生成测点编码
  generateMonitoringPointCode(params) {
    return this.get(`/shm/monitoringPoint/bridge/code`, params);
  }
  // 添加设备和测点接口
  addDevicePoint(params) {
    return this.post(`/shm/app/deviceOrPoint/add`, params);
  }

  // 监测类别维度，查询监测类别下拉列表
  monitorTypeList(params) {
    return this.get(`/shm/monitoringPoint/monitorType/list`, params);
  }
  // 监测位置列表
  monitoringLocationPage(params) {
    return this.get(`/shm/monitoringLocation/page`, params);
  }
  // 监测测点表不分页-设备下
  getPointListByDeviceId(deviceId) {
    return this.get(`/shm/app/pointCode/${deviceId}`);
  }
  // 监测测点表不分页-资产下
  getMonitoringPointList(params) {
    return this.get(`/shm/monitoringPoint/list`, params);
  }
  // 根据监测位置返回测点列表组
  getPointListByLocationIds(params) {
    return this.get(`/shm/monitoringPoint/location/list`, params);
  }
  // 获取测点基础信息
  getMonitoringPointDetail(id) { 
    return this.get(`/shm/monitoringPoint/${id}`);
  }
  // 测点昨日数据概览
  getPoiontYesterdayData(params) {
    return this.get(`/shm/dataView/yesterday/data`, params);
  }
  // 设置消息推送
  setMessagePush(params) {
    return this.post(`/shm/alarmEvent/app/messagePush?cids=${params.cid}`, params);
  }
  // 获取监控视频设备列表
  getMonitorDevicePageApi(params) {
    console.log("params", params)
    return this.get(`/shm/monitorAndPlayback/monitorDevice/page`, params);
  }
  // 监控视频设备 云台控制
  sendMonitorPtzApi(deviceId, params) {
    return this.get(`/iot/media/ptz/${deviceId}`, params)
  }
}
export default new ShmService();
