<template>
  <view class="container">
    <uv-search
      height="36"
      shape="square"
      searchIcon="../../../../static/icon/search_icon.png"
      searchIconSize="16"
      placeholder="搜索姓名/部门"
      placeholderColor="#C1C1C1;"
      bgColor="#fff"
      :showAction="false"
      :boxStyle="searchStyle"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <template v-if="!pageLoading">
      <view v-if="filterList.length">
        <view class="select_box">
          <view class="count"
            >已添加
            <text style="color: #4378ff"> {{ selectedArr.length }}</text>
            人</view
          >
          <view v-if="selectedArr.length > 4" class="select">
            <view
              class="select_item"
              v-for="(item, index) in selectedArr.slice(0, 4)"
              :key="index"
              >{{ item.userName.slice(-2) }}
            </view>
            <view class="select_item select_more">
              +{{ selectedArr.length - 4 }}
            </view>
          </view>
          <view v-else class="select">
            <view
              class="select_item"
              v-for="item in selectedArr"
              :key="index"
              >{{ item.userName.slice(-2) }}</view
            >
          </view>
        </view>
        <view class="list_box">
          <view
            class="card"
            v-for="item in filterList"
            :key="item.userId"
            @click="chooseItem(item)"
          >
            <view class="item_left">
              <image
                v-if="isExistInArray(item.userId)"
                class="radio"
                :src="eventHandling_radio_active"
                mode="scaleToFill"
              />
              <image
                v-else
                class="radio"
                :src="eventHandling_radio_default"
                mode="scaleToFill"
              />
            </view>
            <view class="item_right">
              <view v-if="item.userFace" class="icon">
                <image
                  style="width: 76rpx; height: 76rpx"
                  :src="showImg(item.userFace)"
                  mode="aspectFill"
                />
              </view>
              <view v-else class="icon">
                {{ item.userName.slice(-2) }}
              </view>
              <view class="msg">
                <view class="name">
                  {{ item.userName }}
                </view>
                <view class="dpt">
                  {{ item.deptName }}
                </view>
              </view>
            </view>
          </view>
        </view>
        <view style="height: 200rpx"></view>
      </view>
      <no-data class="nodata" v-else></no-data>
    </template>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      font-size="24rpx"
    ></uv-loading-page>
    <view class="bottom_box">
      <view class="place_box"></view>
      <view class="btn_box">
        <view v-if="selectedArr.length" class="btn can-submit" @click="confirm">
          保 存
        </view>
        <view v-else class="btn">保 存</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { eventHandlingApi } from "@/service";
import eventHandling_radio_active from "@/static/icon/eventHandling_radio_active.png";
import eventHandling_radio_default from "@/static/icon/eventHandling_radio_default.png";
import { showImg } from "@/utils";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

onLoad(async (options) => {
  await getList();
  handelShowCurItem(options?.userList);
});

// 查询项目列表
let dataList = ref([]);
let filterList = ref([]);
let pageLoading = ref(false);
const getList = async () => {
  pageLoading.value = true;
  const res = await eventHandlingApi.maintainUserList({
    projectId: projectInfo.projectId,
  });
  console.log("请求项目", res);
  dataList.value = res.data;
  filterList.value = res.data;
  pageLoading.value = false;
};

// 回显当前已选择的项目，并将其置顶
const handelShowCurItem = (arr) => {
  selectedArr.value = JSON.parse(arr);
};

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  margin: "48rpx 40rpx",
});

let inpVal = ref("");
const inpChange = (val) => {
  inpVal.value = val;
  handleFilterProject();
};
const inpSearch = (val) => {
  inpVal.value = val;
  handleFilterProject();
};

const handleFilterProject = () => {
  filterList.value = dataList.value.filter(
    (item) =>
      item.userName.indexOf(inpVal.value) > -1 ||
      item.deptName.indexOf(inpVal.value) > -1
  );
};

// 选择项目
const selectedArr = ref([]);
const chooseItem = (record) => {
  if (isExistInArray(record.userId)) {
    selectedArr.value = selectedArr.value.filter(
      (item) => item.userId !== record.userId
    );
  } else {
    selectedArr.value.push(record);
  }
};
const isExistInArray = (userId) => {
  const ids = selectedArr.value.map((item) => item.userId);
  const blo = ids.includes(userId);
  return blo;
};
// 提交
const confirm = () => {
  uni.$emit("updateUserData", selectedArr.value); // 发送事件
  uni.navigateBack({
    data: 1,
  });
};
</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f2f2f2f2;
  box-sizing: border-box;
}
.select_box {
  height: 196rpx;
  background: #ffffff;
  margin-bottom: 32rpx;
  padding: 28rpx 40rpx 36rpx;
  box-sizing: border-box;
  .count {
    margin-bottom: 24rpx;
  }
  .select {
    display: flex;
    gap: 0 24rpx;
    .select_item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 76rpx;
      height: 76rpx;
      background: #4378ff;
      border-radius: 8rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #ffffff;
    }
    .select_more {
      justify-content: center;
      width: 84rpx;
    }
  }
}

.list_box {
  .card {
    display: flex;
    min-height: 140rpx;
    padding-top: 24rpx;
    box-sizing: border-box;
    background: #ffffff;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;

    .item_left {
      width: 126rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .radio {
        display: block;
        width: 56rpx;
        height: 56rpx;
      }
    }
    .item_right {
      display: flex;
      align-items: center;
      gap: 0 38rpx;
      width: calc(100% - 126rpx);
      border-bottom: 2px solid #f2f2f2;
      padding-right: 40rpx;
      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 76rpx;
        height: 76rpx;
        background: #4378ff;
        border-radius: 8rpx;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #ffffff;
      }
      .msg {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        .name {
          font-size: 32rpx;
          color: #373737;
          line-height: 52rpx;
        }
        .dpt {
          font-size: 28rpx;
          color: #a09f9f;
        }
      }
    }
  }
  .active_card {
    background: #4378ff;
    .item_title {
      color: #ffffff;
    }
  }
}
.nodata {
  margin-top: 136rpx;
}
.bottom_box {
  width: 100%;
  position: fixed;
  bottom: 0rpx;
  .place_box {
    width: 100%;
    height: 26rpx;
    background: #f2f2f2;
  }
  .btn_box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 160rpx;
    background: #ffffff;
    box-shadow: 0rpx -6rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
    .btn {
      width: 670rpx;
      height: 84rpx;
      background: #b0b0b0;
      border-radius: 8rpx;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 40rpx;
      color: #ffffff;
      line-height: 84rpx;
      text-align: center;
      &.can-submit {
        background: #4378ff;
      }
    }
  }
}
</style>
