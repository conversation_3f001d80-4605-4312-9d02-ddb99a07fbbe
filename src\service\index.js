/*
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-21 12:41:44
 * @Description: 模块导出
 */
import UserService from "./modules/user";
import ProjectService from "./modules/project";
import DataAcquisitionService from "./modules/dataAcquisition";
import RoadInspectionService from "./modules/roadInspection";
import RoutineMaintenanceService from "./modules/routineMaintenance";
import RoutinePreserveService from "./modules/routinePreserve";
import VersionService from "./modules/version";
import ShmService from "./modules/shm/shm";
import AssetService from "./modules/shm/asset";
import PlatformService from "./modules/platform";
import { eventHandlingApi } from "./modules/eventHandlingApi";
import { homepageApi } from "./modules/homepage";
export {
  UserService,
  ProjectService,
  DataAcquisitionService,
  RoadInspectionService,
  RoutineMaintenanceService,
  RoutinePreserveService,
  VersionService,
  ShmService,
  AssetService,
  PlatformService,
  eventHandlingApi,
  homepageApi,
};
