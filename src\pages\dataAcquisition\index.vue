<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-23 14:01:51
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="数据采集"
      leftIconColor="#fff"
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      :autoBack="true"
      :bgColor="bgColor"
    >
      <template v-slot:right>
        <view class="uv-nav-slot" @click="toHistoryPage">历史采集</view>
      </template>
    </uv-navbar>
    <view class="content">
      <!-- 统计数据 -->
      <view class="data_statistics">
        <view
          class="data_item"
          v-for="(item, index) in dataStatistics"
          :key="index"
        >
          <view :class="['data_val', item.redColor ? 'red_val' : '']">{{
            item.val
          }}</view>
          <view class="data_title">{{ item.title }}</view>
        </view>
      </view>
      <!-- 菜单列表 -->
      <view class="menu_list">
        <view class="menu_item" @click="toDataCollection(item.path)" v-for="(item, index) in menuList" :key="index">
          <image class="menu_icon" :src="item.icon" mode="widthFix" />
          <view class="menu_title">{{ item.title }}</view>
        </view>
      </view>
      <!-- 近期采集 -->
      <view class="recently_list">
        <view class="title_box">
          <view class="title">
            <text>近期采集</text>
            <text class="num" v-if="recentlyList.length">（{{draftNum}}/{{ recentlyList.length }}）</text>
          </view>
          <view class="more" @click="toHistoryPage">
            <text>更多</text>
            <image
              class="more_icon"
              src="../../static/icon/more_primary_icon.png"
              mode="widthFix"
            />
          </view>
        </view>
        <view class="list_box" v-if="recentlyList?.length">
          <view class="card" v-for="item in recentlyList" :key="item.id">
            <view class="draft_mark" v-if="item.status == 0">草稿</view>
            <view class="card_top">
              <view class="data_title_box">
                <image
                  class="data_logo"
                  :style="{ width: item.type == 8 ? '156rpx' : '108rpx' }"
                  :src="logoUrls[item.type]"
                />
                <view class="data_title">{{item.name}}</view>
              </view>
            </view>
            <!-- 桩号 -->
            <view class="card_content" v-if="item.type == 2">
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{item.sectionName}}</view>
              </view>
              <view class="content_item">
                <view class="content_name">上下行：</view>
                <view class="content_val">{{item.updownMarkName}}</view>
              </view>
            </view>
            <!-- 路段 -->
            <view class="card_content flex_content" v-if="item.type == 1">
              <view class="content_item">
                <view class="content_name">所属路线：</view>
                <view class="content_val">{{ item.routeName }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">起点：</view>
                <view class="content_val">{{item.startStakeName}}</view>
              </view>
              <view class="content_item">
                <view class="content_name">上下行：</view>
                <view class="content_val">{{item.updownMarkName}}</view>
              </view>
              <view class="content_item">
                <view class="content_name">终点：</view>
                <view class="content_val">{{item.endStakeName}}</view>
              </view>
            </view>
            <!-- 路基、路面、桥梁、隧道 -->
            <view
              class="card_content flex_content"
              v-if="['3', '4', '5', '6'].includes(item.type)"
            >
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{item.sectionName}}</view>
              </view>
              <view class="content_item">
                <view class="content_name">起点：</view>
                <view class="content_val">{{item.startStakeName}}</view>
              </view>
              <view class="content_item">
                <view class="content_name">上下行：</view>
                <view class="content_val">{{item.updownMarkName}}</view>
              </view>
              <view class="content_item">
                <view class="content_name">终点：</view>
                <view class="content_val">{{item.endStakeName}}</view>
              </view>
            </view>
            <!-- 涵洞 -->
            <view class="card_content flex_content" v-if="item.type == 7">
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{item.sectionName}}</view>
              </view>
              <view class="content_item">
                <view class="content_name">位置：</view>
                <view class="content_val">{{item.locationStakeName}}</view>
              </view>
              <view class="content_item">
                <view class="content_name">上下行：</view>
                <view class="content_val">{{item.updownMarkName}}</view>
              </view>
            </view>
            <!-- 沿线设施 -->
            <view class="card_content" v-if="item.type == 8">
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{ item.sectionName ||'-' }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">类型：</view>
                <view class="content_val">{{ item.facilityTypeName ||'-' }}</view>
              </view>
              <view class="flex_content">
                <view class="content_item">
                  <view class="content_name">位置：</view>
                  <view class="content_val">{{ item.locationStakeName ||'-' }}</view>
                </view>
                <view class="content_item">
                  <view class="content_name">上下行：</view>
                  <view class="content_val">{{ item.updownMarkName ||'-' }}</view>
                </view>
              </view>
            </view>
            <view class="card_bottom">
              <view class="log_date">
                <image
                  class="date_icon"
                  src="../../static/icon/date_icon.png"
                />
                <view class="date">录入时间：{{item.createTime}}</view>
              </view>
              <view class="btn" @click="toDetail(item.type, item.status, item.id)">{{ item.status == 1 ? "查看" : "继续录入" }}</view>
            </view>
          </view>
        </view>
        <!-- <view class="card" v-else>暂无待办任务</view> -->
        <no-data class="nodata" v-else></no-data>
        <view class="black"></view>
        <!-- 底部切换按钮 -->
        <ylg-my-btns :switchActive="switchActive" @myBtnCallback="myBtnCallback"></ylg-my-btns>
      </view>
    </view>
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import { computed, reactive, ref } from "vue";
import { onLoad, onPageScroll, onShow } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { DataAcquisitionService } from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { getCurLocation } from "@/utils/location";

// 自定义导航栏背景色
// 监听页面滚动，修改导航栏背景色
let bgColor = ref('transparent');
onPageScroll((e)=>{
  if(e.scrollTop>35){
    bgColor.value = '#4383F8';
  }else{
    bgColor.value = 'transparent';
  }
})

onLoad(()=>{
  // 定位热启动
  getCurLocation();
})

onShow(() => {
  switchActive.value = false;
  getTopData();
  getList();
});

// 获取顶部的统计数据
let statisticData = reactive({});
const getTopData = async () =>{
  try {
    let { code, data } = await DataAcquisitionService.getStatisticData({projectId: projectInfo.projectId});
    if(code == 200){
      statisticData.today = data.today;
      statisticData.history = data.history;
      statisticData.draft = data.draft;
    }
  } catch (error) {
    console.log(error);
  }

}


let recentlyList = ref([]);
let draftNum = ref(null);
const getList = async () => {
  let params = {
    projectId: projectInfo.projectId,
    searchName: '',
    type: '',
    status: '',
    createStartTime: '',
    createEndTime: '',
    page: 1,
    limit: 10,
  };
  let { code, data } = await DataAcquisitionService.getHistoryData(params);
  recentlyList.value = data.reduce((acc, cur) => {
    return [...acc,...cur.data];
  }, []);
  draftNum.value = recentlyList.value.filter(item=>item.status == 0).length;
  console.log("数据采集列表", recentlyList.value);
};

let detailPageUrls = {
  '1': '/pages/dataAcquisition/roadPartCollection',  // 路段
  '2': '/pages/dataAcquisition/pileNumberCollection', // 桩号
  '3': '/pages/dataAcquisition/roadbedCollection',  // 路基
  '4': '/pages/dataAcquisition/roadFaceCollection', // 路面
  '5': '/pages/dataAcquisition/bridgeCollection',  // 桥梁
  '6': '/pages/dataAcquisition/tunnelCollection',  // 隧道
  '7': '/pages/dataAcquisition/culvertCollection',  // 涵洞
  '8': '/pages/dataAcquisition/roadsideFacilities', // 沿线设施
}

// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;
console.log("系统栏高度", systemBarHeight);

// 顶部数据统计  
let dataStatistics = computed(()=>{
  return [
    {
      val: statisticData.today,
      title: "今日采集",
    },
    {
      val: statisticData.history,
      title: "历史采集",
      redColor: true,
    },
    {
      val: statisticData.draft,
      title: "草稿数据",
    },
  ]
});
// 菜单按钮
let menuList = reactive([
  {
    title: "桩号采集",
    icon: "../../static/icon/pile_number_icon.png",
    path: "/pages/dataAcquisition/pileNumberCollection?scene=1",
  },
  {
    title: "路段采集",
    icon: "../../static/icon/road_part_icon.png",
    path: "/pages/dataAcquisition/roadPartCollection",
  },
  {
    title: "路面采集",
    icon: "../../static/icon/road_face_icon.png",
    path: "/pages/dataAcquisition/roadFaceCollection",
  },
  {
    title: "路基采集",
    icon: "../../static/icon/roadbed_icon.png",
    path: "/pages/dataAcquisition/roadbedCollection",
  },
  {
    title: "桥梁采集",
    icon: "../../static/icon/bridge_icon.png",
    path: "/pages/dataAcquisition/bridgeCollection",
  },
  {
    title: "隧道采集",
    icon: "../../static/icon/tunnel_icon.png",
    path: "/pages/dataAcquisition/tunnelCollection",
  },
  {
    title: "涵洞采集",
    icon: "../../static/icon/culvert_icon.png",
    path: "/pages/dataAcquisition/culvertCollection",
  },
  {
    title: "沿线设施",
    icon: "../../static/icon/roadside_facilities_icon.png",
    path: "/pages/dataAcquisition/roadsideFacilities",
  },
]);

// logo图映射
const logoUrls = reactive({
  1: `../../static/icon/road_part_logo20241010.png`,
  2: `../../static/icon/pile_number_logo20241010.png`,
  3: `../../static/icon/roadbed_logo20241010.png`,
  4: `../../static/icon/road_face_logo20241010.png`,
  5: `../../static/icon/bridge_logo20241010.png`,
  6: `../../static/icon/tunnel_logo20241010.png`,
  7: `../../static/icon/culvert_logo20241010.png`,
  8: `../../static/icon/roadside_facilities_logo20241010.png`,
});

const toDataCollection = (path) => {
  console.log('path',path);
  uni.navigateTo({
    url: path,
  });
}

const toHistoryPage = () => {
  uni.navigateTo({
    url: "/pages/dataAcquisition/historicalCollection",
  });
};

const toDetail = (type,status,id) => {
  uni.navigateTo({
    url: `${detailPageUrls[type]}?status=${status}&id=${id}`
  })
}

let switchActive = ref(false);
const myBtnCallback = (envRes) => {
  switch (envRes.type) {
    case "toggoleSwitch":
      switchActive.value = !switchActive.value;
      break;
    case "toReportEvent":
      uni.navigateTo({
        url: `/pages/roadConditionInspection/eventReporting?pageFrom=index&inspectObject=1`
      })
      break;
    default:
      break;
  }
}
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: v-bind(systemBarHeight);
  background-image: url("../../static/image/home_bg.png");
  background-size: contain;
  background-color: #F0F5FF;
  background-repeat: no-repeat;
}
.uv-nav-slot {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}
.content {
  padding: 18rpx 40rpx 0 40rpx;
  margin-top: 88rpx;
}
.data_statistics {
  // margin-top: 32rpx;
  padding: 32rpx 38rpx;
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-radius: 24rpx;
  .data_item {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #373737;
    line-height: 40rpx;
    text-align: center;
    .data_val {
      font-family: Bakbak One, Bakbak One;
      font-weight: 400;
      font-size: 48rpx;
      color: #373737;
      line-height: 68rpx;
    }
    .red_val {
      color: #ff2d2d;
    }
    .data_title {
      margin-top: 8rpx;
    }
  }
}
.menu_list {
  margin: 40rpx 0 64rpx 0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .menu_item {
    margin-bottom: 28rpx;
    width: 140rpx;
    height: 140rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 16rpx 22rpx 18rpx 22rpx;
    box-sizing: border-box;
    text-align: center;
    .menu_icon {
      width: 64rpx;
      height: 64rpx;
    }
    .menu_title {
      font-family: PingFang SC-Regular;
      font-size: 24rpx;
      color: #8e8e8e;
      line-height: 28rpx;
    }
  }
  .menu_item:nth-last-child(-n + 4) {
    margin-bottom: 0rpx;
  }
}
.recently_list {
  .title_box {
    display: flex;
    justify-content: space-between;
    .title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #373737;
      line-height: 44rpx;
      .num {
        color: #ff3132;
      }
    }
    .more {
      text {
        font-family: PingFang SC-Medium;
        font-size: 28rpx;
        color: #4378ff;
        line-height: 40rpx;
      }
      .more_icon {
        margin-left: 8rpx;
        width: 20rpx;
        height: 20rpx;
      }
    }
  }
  .card {
    position: relative;
    text-align: center;
    box-sizing: border-box;
    margin-top: 28rpx;
    border-radius: 28rpx;
    background: #fff;
    box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
    .draft_mark {
      position: absolute;
      right: 0;
      top: 0;
      background-color: #EAEAEA;
      width: 92rpx;
      height: 44rpx;
      border-radius: 0 24rpx 0 24rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #404040;
      line-height: 44rpx;
    }
    .card_top {
      padding: 28rpx 28rpx 0rpx 28rpx;
      .data_title_box {
        display: flex;
        align-items: center;
        .data_logo {
          display: inline-block;
          margin-right: 20rpx;
          width: 108rpx;
          height: 48rpx;
        }
        .data_title{
          flex: 1;
          text-align: left;
        }
      }
    }
    .card_content {
      margin-top: 28rpx;
      padding: 0rpx 28rpx;
      font-family: PingFang SC, PingFang SC;
      border-bottom: 2rpx solid #f0f0f0;
      .content_item {
        margin-bottom: 20rpx;
        display: flex;
        align-items: baseline;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 40rpx;
        .content_name {
          width: 140rpx;
          color: #b0b0b0;
          text-align: right;
        }
        .content_val {
          flex: 1;
          text-align: left;
          color: #404040;
        }
      }
    }
    .flex_content {
      display: flex;
      flex-wrap: wrap;
      .content_item:nth-child(2n) {
        width: 40%;
      }
      .content_item:nth-child(2n + 1) {
        // margin-right: 112rpx;
        width: 60%;
      }
    }
    .card_bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 28rpx;
      .log_date {
        display: flex;
        align-items: center;
        .date_icon {
          display: inline-block;
          margin-right: 8rpx;
          width: 28rpx;
          height: 28rpx;
        }
        .date {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #a09f9f;
          // line-height: 34rpx;
        }
      }
      .btn {
        padding: 6rpx 20rpx;
        box-sizing: border-box;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 28rpx;
        color: #4378ff;
        line-height: 40rpx;
        border: 2rpx solid #4378ff;
        border-radius: 8rpx;
      }
    }
  }
  .black {
    height: 200rpx;
  }
}
</style>
