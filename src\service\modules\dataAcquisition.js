/*
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-04-01 17:32:53
 * @Description:
 */
import BaseService from "../request";

class DataAcquisitionService extends BaseService {
  constructor() {
    super();
  }

  // 字典获取-系统
  getdict(params) {
    return this.get(`/system/dict/item/list/${params.dictCodes}`)
  }
  // 字典获取-养护
  getYhBaseDict(params) {
    return this.get(`/project-base/dict/item/list/${params.dictCodes}`);
  }
  
  // 首页统计的数据值
  getHomeStatisticData(params) {
    return this.get(`/yh/app/home/<USER>
  }

  // 数据采集统计的数据值
  getStatisticData(params) {
    return this.get(`/yh/historyData/statistic/data?projectId=${params.projectId}`);
  }

  // 历史采集
  getHistoryData(params) {
    return this.get(
      `/yh/historyData/page?projectId=${params.projectId}&searchName=${params.searchName}&type=${params.type}&status=${params.status}&createStartTime=${params.createStartTime}&createEndTime=${params.createEndTime}&page=${params.page}&limit=${params.limit}`
    );
  }

  // 路线列表不分页
  getRoadLineList(params) {
    return this.get(`/project-resource/routeData/list?searchName=${params.searchVal}`);
  }

  // 路段列表分页
  getRoadPartList(params) {
    return this.get(`/project-resource/sectionData/page?name=${params.searchVal}&projectId=${params.projectId}&page=${params.page}&limit=${params.limit}`);
  }  
  // 路段采集（正式保存/暂存）
  roadLineAdd(data) {
    return this.post(`/project-resource/sectionData/save`, data);
  }
  // 修改路段
  roadLineEdit(data) {
    return this.put(`/project-resource/sectionData/update`, data);
  }
  // 路段详情
  roadLineDetail(id) {
    return this.get(`/project-resource/sectionData/${id}`);
  }
  // 删除单个路段
  roadPartDel(id) {
    return this.delete(`/project-resource/sectionData/del/${id}`,{ noProxy: true });
  }

  // 路面采集（正式保存/暂存）
  roadFaceAdd(data) {
    return this.post(`/project-resource/pavementData/save`, data);
  }
  // 修改路面
  roadFaceEdit(data) {
    return this.put(`/project-resource/pavementData/update`, data);
  }
  // 路面详情
  roadFaceDetail(id) {
    return this.get(`/project-resource/pavementData/${id}`);
  }
  // 删除单个路面
  roadFaceDel(id) {
    return this.delete(`/project-resource/pavementData/del/${id}`, { noProxy: true });
  }
  // 地址搜索
  address(params) {
    // return this.get(`https://restapi.amap.com/v3/assistant/inputtips?key=342053d626eebc75f3190e7f3c5f6226&keywords=${params.inpval}`);
    return this.get(`https://restapi.amap.com/v5/place/text?key=342053d626eebc75f3190e7f3c5f6226&keywords=${params.inpval}`,params,{ noProxy: true });
  }
  // 高德地址搜索
  addressSearch(params){
    // return this.get(`/yh-base/place/text?key=342053d626eebc75f3190e7f3c5f6226&location=${params.location}&radius=${params.radius}&keywords=${params.inpVal}&page_size=20`,params,{ noProxy: true })
    return this.get(`/yh-base/place/text?key=342053d626eebc75f3190e7f3c5f6226&keywords=${params.inpVal}&page_size=10`,params,{ noProxy: true })
    // return this.get(`https://restapi.amap.com/v5/place/text?&key=342053d626eebc75f3190e7f3c5f6226&keywords=${params.inpVal}`,{ noProxy: true })
  }
  // https://restapi.amap.com/v5/place/text?&key=342053d626eebc75f3190e7f3c5f6226&keywords=${params.keywords}

  // 天地图地址搜索
  getAddreeeList(params) {
    return this.get(`/v2/search?postStr={"keyWord":"${params.inpVal}","level":10,"mapBound":"-180,-90,180,90","queryType":1,"start":0,"count":20,"show":2}&type=query&tk=307b00f916df23b531cf212d1e73216b`)
  }




  // 桩号采集（正式保存/暂存）
  stakeAdd(data) {
    return this.post(`/project-resource/stakeData/save`, data);
  }
  // 修改桩号
  stakeEdit(data) {
    return this.put(`/project-resource/stakeData/update`, data);
  }
  // 桩号详情
  stakeDetail(id) {
    return this.get(`/project-resource/stakeData/${id}`);
  }
  // 附近桩号列表
  getStakeList(params) {
    return this.get(`/project-resource/stakeData/recent/page?longitude=${params.longitude}&latitude=${params.latitude}&distance=${params.distance}&projectId=${params.projectId}&page=${params.page}&limit=${params.limit}&sectionId=${params.sectionId}`);
  } 
  // 所有桩号列表
  getAllStakeList(params) {
    return this.get(`/project-resource/stakeData/page?sectionId=${params.sectionId}&name=${params.searchName}&page=${params.page}&limit=${params.limit}`)
  }
  //自动新增桩号id
  snowflakeId() {
    return this.get(`/project-resource/stakeData/snowflake/id`)
  }
  // 删除单个桩号
  stakeDel(id) {
    return this.delete(`/project-resource/stakeData/del/${id}`, { noProxy: true });
  }
  

  // 路基采集（正式保存/暂存）
  roadBedAdd(data) {
    return this.post(`/project-resource/embankmentData/save`, data);
  }
  // 修改路基
  roadBedEdit(data) {
    return this.put(`/project-resource/embankmentData/update`, data);
  }
  // 路基详情
  roadBedDetail(id) {
    return this.get(`/project-resource/embankmentData/${id}`);
  }
  // 删除单个路基
  roadBedDel(id) {
    return this.delete(`/project-resource/embankmentData/del/${id}`,{ noProxy: true });
  }

  // 桥梁采集（正式保存/暂存）
  roadBridgeAdd(data) {
    return this.post(`/project-resource/bridgeData/save`, data);
  }
  // 修改桥梁
  roadBridgeEdit(data) {
    return this.put(`/project-resource/bridgeData/update`, data);
  }
  // 桥梁详情
  roadBridgeDetail(id) {
    return this.get(`/project-resource/bridgeData/${id}`);
  }
  // 删除单个桥梁
  roadBridgeDel(id) {
    return this.delete(`/project-resource/bridgeData/del/${id}`,{ noProxy: true });
  }


  // 隧道列表分页
  getTunnelList(params) {
    return this.get(`/project-resource/tunnelData/page?name=${params.searchVal}&projectId=${params.projectId}&sectionId=${params.sectionId}&page=${params.page}&limit=${params.limit}`);
  }  
  // 隧道采集（正式保存/暂存）
  roadTunnelAdd(data) {
    return this.post(`/project-resource/tunnelData/save`, data);
  }
  // 修改隧道
  roadTunnelEdit(data) {
    return this.put(`/project-resource/tunnelData/update`, data);
  }
  // 隧道详情
  roadTunnelDetail(id) {
    return this.get(`/project-resource/tunnelData/${id}`);
  }
  // 删除单个隧道
  roadTunnelDel(id) {
    return this.delete(`/project-resource/tunnelData/del/${id}`,{ noProxy: true });
  }


  // 涵洞采集（正式保存/暂存）
  roadCulvertAdd(data) {
    return this.post(`/project-resource/culvertData/save`, data);
  }
  // 修改涵洞
  roadCulvertEdit(data) {
    return this.put(`/project-resource/culvertData/update`, data);
  }
  // 涵洞详情
  roadCulvertDetail(id) {
    return this.get(`/project-resource/culvertData/${id}`);
  }
  // 删除单个涵洞
  roadCulvertDel(id) {
    return this.delete(`/project-resource/culvertData/del/${id}`,{ noProxy: true });
  }

  // 沿线设施采集（正式保存/暂存）
  roadRoadsideFacilityAdd(data) {
    return this.post(`/project-resource/roadsideFacilityData/save`, data);
  }
  // 修改沿线设施
  roadRoadsideFacilityEdit(data) {
    return this.put(`/project-resource/roadsideFacilityData/update`, data);
  }
  // 沿线设施详情
  roadRoadsideFacilityDetail(id) {
    return this.get(`/project-resource/roadsideFacilityData/${id}`);
  }
  // 删除单个沿线设施
  roadRoadsideFacilityDel(id) {
    return this.delete(`/project-resource/roadsideFacilityData/del/${id}`,{ noProxy: true });
  }


}
export default new DataAcquisitionService();
