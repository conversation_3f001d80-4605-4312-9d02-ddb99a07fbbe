import BaseService from "../request";

class RoutineMaintenanceService extends BaseService {
  constructor() {
    super();
  }

  // 日常维修，首页列表，今日（代办、驳回）
  getTodayList(projectId) {
    return this.get(`/yh/workOrder/mobile/today/todo?projectId=${projectId}`);
  }
  // 日常维修，首页列表，往日
  getLastList(params) {
    return this.get(
      `/yh/workOrder/mobile/oneDay?projectId=${params.projectId}&date=${params.date}`
    );
  }

  // 日常维修，历史列表
  getHistoryData(params) {
    return this.get(`/yh/workOrder/mobile/history/page`, params);
  }

  // 根据工单id查询人机料情况
  getCostInfo(parentId) {
    return this.get(`/yh/workOrder/rel/list/${parentId}`);
  }
  // 开始施工确认
  startConstructionConfirm(data) {
    return this.post(`/yh/workOrder/work/confirm`, data);
  }
  // 子工单列表
  getChildOrder(parentId) {
    return this.get(`/yh/workOrder/mobile/child?parentId=${parentId}`);
  }
  // 上报施工作业
  reportBuild(data) {
    return this.post(`/yh/workOrderActivity/add`, data);
  }
  // 工单基本信息
  workOrderBaseInfo(id) {
    return this.get(`/yh/workOrder/base/${id}`);
  }
  // 工单详情
  workOrderDetail(id) {
    return this.get(`/yh/workOrder/${id}`);
  }
  // 工单完成情况
  workOrderCompleteInfo(id) {
    return this.get(`/yh/workOrder/mobile/finish/${id}`);
  }
  // 子工单详情
  childOrderDetail(params) {
    return this.get(`/yh/workOrderActivity/detail`, params);
  }
  // 子工单详情(图片)
  getInspectEventDetail(eventId) {
    return this.get(`/yh/inspectEvent/detail/${eventId}`);
  }
  // 保养人员
  getMaintainUserList(params) {
    return this.get(`/system/projectMember/list/${params.projectId}`, params);
  }
  // 作业
  getMaintainWorkList(params) {
    return this.get(`/yh/maintainConfig/all`, params);
  }
  // 获取warehouseList
  getWarehouseList(params) {
    return this.get(`/yh/warehouse/list`, params);
  }
  // 材料
  getMaterialInformationList(params) {
    return this.get(`/yh/materialInformation/selectPage`, params);
  }
  // 机械设备
  getMachineInformationList(params) {
    return this.get(`/yh/machineInformation/page`, params);
  }
  // 确认完成
  addOrdinary(params) {
    return this.post(`/yh/workOrder/plan/add/ordinary`, params);
  }
  // 工单驳回
  workOrderReject(params) {
    return this.post(`/yh/workOrder/work/reject`, params);
  }
  // 工单验收
  workOrderVerification(params) {
    return this.post(`/yh/workOrder/plan/verification`, params);
  }
}
export default new RoutineMaintenanceService();
