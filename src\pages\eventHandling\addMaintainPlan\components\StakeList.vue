<template>
  <view class="container">
    <uv-search
      height="36"
      shape="square"
      searchIcon="../../../../static/icon/search_icon.png"
      searchIconSize="16"
      placeholder="输入桩号名称搜索"
      placeholderColor="#C1C1C1;"
      bgColor="#fff"
      :showAction="false"
      :boxStyle="searchStyle"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <template v-if="!pageLoading">
      <view class="list_box" v-if="filterList.length">
        <view
          :class="['card', curItem.id === item.id ? 'active_card' : '']"
          v-for="item in filterList"
          :key="item.id"
          @click="chooseItem(item)"
        >
          <view class="item_title">{{ item.stakeName }}</view>
          <view class="item_updowm">{{ item.updownMarkName }}</view>
        </view>
        <view style="height: 220rpx"></view>
      </view>
      <no-data class="nodata" v-else></no-data>
    </template>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      font-size="24rpx"
    ></uv-loading-page>
    <view class="bottom_box">
      <view class="choosed_data_show" v-if="curItem.stakeName"
        >已选 {{ curItem.stakeName }}</view
      >
      <view class="btn" @click="confirm">保 存</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { eventHandlingApi } from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

onLoad(async (options) => {
  console.log("options桩号选择", options);
  await getList(options.sectionId);
  handelShowCurItem(options?.stakeId);
  stakeType.value = options?.stakeType;
});

const stakeType = ref("");
const dataList = ref([]);
const filterList = ref([]);
const pageLoading = ref(false);
const getList = async (sectionId) => {
  pageLoading.value = true;
  const res = await eventHandlingApi.getAllStakeList({
    projectId: projectInfo.projectId,
    sectionId,
    page: 1,
    limit: 300,
  });
  console.log("请求项目", res);
  dataList.value = res.data;
  filterList.value = res.data;
  pageLoading.value = false;
};

// 回显当前已选择的项目，并将其置顶
const handelShowCurItem = (stakeId) => {
  let curItemIndex = ref(-1);
  filterList.value.forEach((item, index) => {
    if (item.id == stakeId) {
      curItem.value = item;
      curItemIndex.value = index;
    }
  });
  if (curItemIndex.value !== -1) {
    let [it] = filterList.value.splice(curItemIndex.value, 1);
    filterList.value.unshift(it);
  }
};

const searchStyle = reactive({
  borderRadius: "8px",
  margin: "48rpx 40rpx",
});

const inpVal = ref("");
const inpChange = (val) => {
  inpVal.value = val;
  handleFilterProject();
};
const inpSearch = (val) => {
  inpVal.value = val;
  handleFilterProject();
};

const handleFilterProject = () => {
  filterList.value = dataList.value.filter(
    (item) => item.stakeName.indexOf(inpVal.value) > -1
  );
};
const curItem = ref({});
const chooseItem = (item) => {
  curItem.value = item;
};
const confirm = () => {
  uni.$emit("updateStakeData", curItem.value, stakeType.value);
  uni.navigateBack({
    data: 1,
  });
};
</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f2f2f2f2;
  box-sizing: border-box;
}
.list_box {
  margin: 12rpx 0 200rpx 0;
  padding: 20rpx 40rpx;
  .card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 112rpx;
    margin-bottom: 28rpx;
    padding: 0 64rpx 0 28rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 16rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    .item_title {
      font-weight: 400;
      color: #373737;
      font-size: 32rpx;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .item_updowm {
    }
  }
  .active_card {
    background: #4378ff;
    .item_title,
    .item_updowm {
      color: #ffffff;
    }
  }
}
.nodata {
  margin-top: 136rpx;
}
.bottom_box {
  padding: 0 40rpx;
  width: 100%;
  position: fixed;
  bottom: 0;
  padding-bottom: 64rpx;
  background: #fff;
  .choosed_data_show {
    padding-top: 20rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .btn {
    width: 670rpx;
    height: 84rpx;
    background: #4378ff;
    border-radius: 8rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
}
</style>
