/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-09 10:18:25
 * @Description:
 */
import BaseService from "../../request";

class AssetService extends BaseService {
  constructor() {
    super();
  }
  // 根据key查询资产数据
  healthList(params) {
    return this.get(`/shm/health/app/list`, params);
  }
  // 报警次数月度统计
  monthStatistics(params) {
    return this.get(`/shm/alarmEvent/app/monthStatistics`);
  }
  // 设备在线率月度统计
  deviceOnlineStatistics(params) {
    return this.get(
      `/shm/deviceStatusRecord/app/deviceOnlineStatistics`,
      params
    );
  }
  // 桥隧涵列表（按首字母分类）
  assetDataListByLetterType(params) {
    return this.get(`/project-resource/assetData/listByLetterType`, params);
  }
  // app端报警统计
  appAlarmEventStatistics(params) {
    return this.get(`/shm/alarmEvent/appAlarmEventStatistics`, params);
  }
  // app端设备报警事件列表
  appDeviceAlarmEvent(params) {
    return this.get(`/shm/alarmEvent/appDeviceAlarmEvent`, params);
  }
  // 设备在线率月度统计
  alarmEventPage(params) {
    return this.get(`/shm/alarmEvent/page`, params);
  }
  // 报警详情
  alarmEventDetail(params) {
    return this.get(`/shm/alarmEvent/detail/${params.id}`, params);
  }
  // app端报警事件触发源数据详情
  appMonitorPointDataDetail(params) {
    return this.get(`/shm/alarmEvent/appMonitorPointDataDetail`, params);
  }
  // app端报警事件触发源数据详情
  appexecuteAndNotice(params) {
    return this.get(
      `/shm/alarmEvent/executeAndNotice/detail/${params.id}`,
      params
    );
  }
  // 报警详情
  alarmEventHandle(data) {
    return this.put(`/shm/alarmEvent/handle`, data);
  }
  // // 单个报警设备执行（执行或中断）
  alarmEventExecute(data) {
    return this.post(`/shm/alarmEvent/execute/${data.alarmEventExecuteId}?stop=${data.executeType}`, data);
  }
  // 根据部门id拥有权限的用户列表
  deptAuthListNew(params) {
    return this.get(`/system/orgStructure/auth/dept/userList`, params);
  }
  // 单个报警设备执行（开关操作）
  executeOnOff(data) {
    return this.post(`/shm/alarmEvent/executeOnOff?onOff=${data.onOff}&deviceId=${data.deviceId}`, data);
  }
}
export default new AssetService();
