import BaseService from "../request";

class HomepageApi extends BaseService {
  constructor() {
    super();
  }
  // 我的待办列表
  homeTodayTodoList(params) {
    return this.get("/yh/app/home/<USER>/todo/list", params);
  }
  // 我的待办列表统计
  homeTodayTodoCount(params) {
    return this.get("/yh/app/home/<USER>/todo/count", params);
  }

  // 日常维修列表
  yhWorkOrderMobileTodayTodoList(params) {
    return this.get("/yh/workOrder/mobile/today/todo/list", params);
  }
  // 日常维修统计
  yhWorkOrderMobileTodayTodoCount(params) {
    return this.get("/yh/workOrder/mobile/today/todo/count", params);
  }
  // 日常维修-查询工单历史数量
  yhWorkOrderMobileHistoryCount(params) {
    return this.get("/yh/workOrder/mobile/history/count", params);
  }
}
export const homepageApi = new HomepageApi();
