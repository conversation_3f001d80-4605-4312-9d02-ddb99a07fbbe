<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-03-14 11:28:16
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="桥梁采集"
      leftIconColor="#fff"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      :autoBack="false"
      @leftClick="onNavBack"
      bgColor="#4d8bff"
    >
      <template v-if="isViewDetail" v-slot:right>
        <view class="uv-nav-slot" @click="onDel">删除</view>
      </template>
    </uv-navbar>
    <scroll-view scroll-y="true" class="content">
      <ylg-auto-form
        ref="autoFormRef"
        :isAllDisabled="isViewDetail"
        :isFromDetail="isFromDetail"
        :formConfig="formConfig"
        :formData="formData"
        :rules="rules"
        :btnLoading="btnLoading"
        :labelStyle="labelStyle"
        :formItemStyle="formItemStyle"
        :placeholderStyle="placeholderStyle"
        @onEdit="onFormEdit"
        @onChange="onFormInpChange"
        @onSubmit="onFormSubmit"
      ></ylg-auto-form>
      <uv-toast ref="toast"></uv-toast>
      <del-data-modal
        ref="delModal"
        :delMessage="delMessage"
        :delBtnLoading="delBtnLoading"
        :delMode="delMode"
        @onDelCallback="onDelModal"
      ></del-data-modal>
    </scroll-view>
    <back-page-modal
      ref="backModal"
      :btnLoading="btnLoading"
      @onModalCallback="onBackModal"
    ></back-page-modal>
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import { computed, reactive, ref } from "vue";
import ylgAutoForm from "@/components/ylg-auto-form.vue";
import delDataModal from "./components/delDataModal.vue";
import backPageModal from "./components/backPageModal.vue";
import { onLoad, onShow, onBackPress } from "@dcloudio/uni-app";
import { DataAcquisitionService } from "@/service";
import {
  reverseGeocode,
  reTransformLngLat,
  transformLngLat,
} from "@/utils/location";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { usePileNumberStore } from "@/store/pileNumber";
const pileNumberInfo = usePileNumberStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;

let isViewDetail = ref(false); // 是否查询详情状态，控制form表单禁用
let isFromDetail = ref(false); // 是否从详情进入、再编辑，控制unionkey配置
onLoad((options) => {
  // 1清空pinia中缓存的之前选择的数据项：如所属路线、所属路段等；
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: "",
      value: "",
      label: "",
    },
  });
  if (options.id) {
    // 2.查询详情数据
    getDetail(options.id);
    isViewDetail.value = !!Number(options.status); // 0-草稿；1-正式
    isFromDetail.value = true;
  } else {
    isFromDetail.value = false;
  }
  // 3查询字典枚举库
  getDicts();
});
onShow(() => {
  // 处理从别的页面中选择之后的数据显示，如所属路线选择
  onFormSelChange();
});



let backModal = ref(null);
onBackPress((backOptions) => {
  if(backOptions.from === 'backbutton'){
    if (isViewDetail.value) {
      return false;
    } else {
      backModal.value.open();
      return true;
    }
  }else if(backOptions.from === 'navigateBack'){
		return false;
  }
})


// form表单样式配置
const labelStyle = reactive({
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  lineHeight: "33rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
});
const placeholderStyle = ref("color: #C1C1C1");

const onNavBack = () => {
  console.log("返回上一页", isViewDetail.value);
  if (isViewDetail.value) {
    uni.navigateBack({
      delta: 1,
    });
  } else {
    backModal.value.open();
  }
};
// 返回上一页modal组件回调事件
const onBackModal = (envRes) => {
  switch (envRes.type) {
    case "onCacel":
      backModal.value.close();
      break;
    case "onNotSave":
      backModal.value.close();
      uni.navigateBack({
        delta: 1,
      });
      break;
    case "onTemporaryDraft":
      onFormSubmit({ data: formData.value, status: 0 });
      break;
    default:
      break;
  }
};

// 查看详情时，点击删除
let delModal = ref(null);
let delMessage = ref("");
let delBtnLoading = ref(false);
let delMode = ref("nomal");
const onDel = async () => {
  delMessage.value = formData.value.bridgeName;
  delModal.value.open();
};
const onDelModal = (envRes) => {
  switch (envRes.type) {
    case "onCacel":
      delMode.value = "nomal";
      delModal.value.close();
      break;
    case "onComfirm":
      delCurData();
      break;
    default:
      break;
  }
};
const delCurData = async () => {
  try {
    delBtnLoading.value = true;
    const { code, data, msg } = await DataAcquisitionService.roadBridgeDel(
      formData.value.id
    );
    delBtnLoading.value = false;
    console.log("删除", code, data);
    if (code == 200) {
      toast.value.show({
        type: "success",
        message: `删除成功`,
        complete() {
          uni.navigateBack({
            delta: 1,
          });
        },
      });
    } else if (code == 500) {
      delMessage.value = msg;
      delMode.value = "error";
    } else {
      toast.value.show({
        type: "error",
        message: `删除失败，请稍后重试~`,
      });
    }
  } catch (error) {
    console.log("删除失败", error);
    delBtnLoading.value = false;
  }
};

// 查询字典枚举
let dicts = ref({});
const getDicts = async () => {
  let { code, data } = await DataAcquisitionService.getYhBaseDict({
    dictCodes:
      "crossingType,updownMark,interchangeType,mainStructureForm,bridgeProperty,spanClassification,pierType,abutmentType,deckPavementType,expansionJointType,bearingType,skewFeature,collisionProtectionType,navigationLevel,designLoadClass,designFloodFrequency,seismicDesignLevel",
  });
  if (code == 200) {
    dicts.value = data;
  }
};

// 表单数据配置
const formData = ref({
  // 项目id
  projectId: projectInfo.projectId,
  // 桥梁名称
  bridgeName: "",
  // 桥梁编码
  bridgeCode: "",

  // 基础信息
  // 所属路段
  sectionId: "",
  sectionName: "",
  sectionIdLabel: "",
  // 桥梁代码
  bridge: "",
  // 起点桩号
  startStake: "",
  startStakeId: "",
  startStakeIdLabel: "",
  // 终点桩号
  endStake: "",
  endStakeId: "",
  endStakeIdLabel: "",
  // 中心桩号
  centerStake: "",
  centerStakeId: "",
  centerStakeIdLabel: "",
  // 跨越地物类型
  crossingType: "",
  crossingTypeLabel: "",
  // 跨越地物名称
  crossingName: "",
  crossingNameLabel: "",
  // 上下行标记
  updownMark: "",
  updownMarkLabel: "",
  updownMarkName: "",

  // 尺寸信息
  // 桥梁全长（米）
  bridgeLength: "",
  // 桥梁全宽（米）
  bridgeWidth: "",
  // 桥梁净宽（米）
  bridgeClearWidth: "",
  // 桥梁跨数（米）
  bridgeSpans: "",
  // 跨径总长
  totalSpanLength: "",
  // 桥梁孔数
  bridgeOpenings: "",
  // 单孔最大跨径
  maxSpan: "",
  // 斜交角
  skewAngle: "",
  // 前引桥长
  frontApproachLength: "",
  // 后引桥长
  rearApproachLength: "",
  // 桥下净空
  clearanceUnderBridge: "",
  // 桥跨序号
  spanNumber: "",
  // 跨径
  spanLength: "",

  // 桥梁结构信息
  // 立交桥类别
  interchangeType: "",
  interchangeTypeLabel: "",
  // 主桥上部结构形式 ？
  mainStructureForm: "",
  mainStructureFormLabel: "",
  // 桥梁性质 ？
  bridgeProperty: "",
  bridgePropertyLabel: "",
  // 桥梁跨径分类 ？
  spanClassification: "",
  spanClassificationLabel: "",
  // 桥墩类型
  pierType: "",
  pierTypeLabel: "",
  // 桥台类型
  abutmentType: "",
  abutmentTypeLabel: "",
  // 桥面铺装类型
  deckPavementType: "",
  deckPavementTypeLabel: "",
  // 伸缩缝类型
  expansionJointType: "",
  expansionJointTypeLabel: "",
  // 支座类型
  bearingType: "",
  bearingTypeLabel: "",
  // 弯坡斜特征
  skewFeature: "",
  skewFeatureLabel: "",

  // 防撞设施
  // 墩台防撞设施类型 ？
  collisionProtectionType: "",
  collisionProtectionTypeLabel: "",
  // 桥梁防撞设施超高
  collisionProtectionHeight: "",
  // 桥梁防撞设施超宽
  collisionProtectionWidth: "",

  // 设计信息
  // 桥梁设计单位
  designUnit: "",
  // 桥梁交工日期
  handoverDate: "",
  // 桥梁竣工日期
  completionDate: "",
  // 总造价（万元）
  totalCost: "",
  // 设计基准期
  designLifespan: "",
  // 通航等级
  navigationLevel: "",
  navigationLevelLabel: "",
  // 设计载荷等级
  designLoadClass: "",
  designLoadClassLabel: "",
  // 设计洪水频率
  designFloodFrequency: "",
  designFloodFrequencyLabel: "",
  // 设计抗震等级
  seismicDesignLevel: "",
  seismicDesignLevelLabel: "",

  // 相关管理单位
  // 桥梁设计单位
  designUnit: "",
  // 桥梁施工单位
  constructionUnit: "",
  // 桥梁管理单位
  managementUnit: "",
  // 桥梁建设单位
  buildUnit: "",
  // 桥梁监管单位
  supervisionUnit: "",
  // 桥下管理单位
  underbridgeUnit: "",
  // 桥梁养护单位
  conserveUnit: "",

  // 其他
  // 桥梁附着管线情况
  attachedPipeline: "",
  // 桥下空间可用情况
  underBridgeSpace: "",
  // 备注
  remark: "",
});
const rules = computed(() => {
  return {
    bridgeName: {
      type: "string",
      required: true,
      message: "请填写桥梁名称",
      trigger: ["blur", "change"],
    },
    sectionIdLabel: {
      type: "string",
      required: isFromDetail.value ? false : true,
      // required: formData.value.sectionId ? false : true,
      message: "请选择所属路段",
      trigger: ["blur", "change"],
    },
    crossingTypeLabel: {
      type: "string",
      required: isFromDetail.value && formData.value.crossingType ? false : true,
      message: "请选择跨越地物类型",
      trigger: ["blur", "change"],
    },
    updownMarkLabel: {
      type: "string",
      required: isFromDetail.value && formData.value.updownMark ? false : true,
      message: "请选择上下行标记",
      trigger: ["blur", "change"],
    },
    bridgeLength: {
      type: "string",
      required: true,
      message: "请填写桥梁全长",
      trigger: ["blur"],
    },
    bridgeWidth: {
      type: "string",
      required: true,
      message: "桥梁全宽",
      trigger: ["blur"],
    },
    bridgeClearWidth: {
      type: "string",
      required: true,
      message: "桥梁净宽",
      trigger: ["blur"],
    },
    bridgeSpans: {
      type: "string",
      required: true,
      message: "桥梁跨数",
      trigger: ["blur"],
    },
  };
});
const formConfig = computed(() => {
  return [
    {
      items: [
        {
          type: "mainInput",
          maxlen: 50,
          placeholder: "请输入桥梁名称",
          unionKey: "bridgeName",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥梁编码",
          placeholder: "请输入（非必填）",
          unionKey: "bridgeCode",
        },
      ],
    },
    {
      title: "基础信息",
      items: [
        {
          type: "select",
          label: "所属路段",
          placeholder: "请选择",
          unionKey: "sectionIdLabel",
          unionKeyDetail: formData.value.sectionIdLabel
            ? "sectionIdLabel"
            : "sectionName",
          optionsPath:
            "/pages/dataAcquisition/relationInfoChoose/roadPartChoose",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥梁代码",
          placeholder: "请输入（非必填）",
          unionKey: "bridge",
        },   
        {
          type: "select",
          label: "起点桩号",
          placeholder: "请选择（非必填）",
          unionKey: "startStakeIdLabel",
          unionKeyDetail: formData.value.startStakeIdLabel
            ? "startStakeIdLabel"
            : "startStake",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose`,
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: '请先选择所属路段~'
        },
        {
          type: "select",
          label: "终点桩号",
          placeholder: "请选择（非必填）",
          unionKey: "endStakeIdLabel",
          unionKeyDetail: formData.value.endStakeIdLabel
            ? "endStakeIdLabel"
            : "endStake",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose`,
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: '请先选择所属路段~'
        },
        {
          type: "select",
          label: "中心桩号",
          placeholder: "请选择（非必填）",
          unionKey: "centerStakeIdLabel",
          unionKeyDetail: formData.value.centerStakeIdLabel
            ? "centerStakeIdLabel"
            : "centerStake",
          optionsPath: `/pages/dataAcquisition/relationInfoChoose/pileNumberChoose`,
          pathKey: "sectionId",
          isNeedPathKeyValue: true,
          toastMsg: '请先选择所属路段~'
        },
        {
          type: "select",
          label: "跨越地物类型",
          placeholder: "请选择",
          unionKey: "crossingTypeLabel",
          unionKeyDetail: formData.value.crossingTypeLabel
            ? "crossingTypeLabel"
            : "crossingTypeName",
          options: dicts.value.crossingType||[],
        },
        {
          type: "input",
          maxlen: 20,
          label: "跨越地物名称",
          placeholder: "请输入（非必填）",
          unionKey: "crossingName",
        },
        {
          type: "select",
          label: "上下行标记",
          placeholder: "请选择",
          unionKey: "updownMarkLabel",
          unionKeyDetail: formData.value.updownMarkLabel
            ? "updownMarkLabel"
            : "updownMarkName",
          options: dicts.value.updownMark||[]
        },
      ],
    },
    {
      title: "尺寸信息",
      items: [
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥梁全长（m）",
          placeholder: "请输入",
          unionKey: "bridgeLength",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥梁全宽（m）",
          placeholder: "请输入",
          unionKey: "bridgeWidth",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥梁净宽（m）",
          placeholder: "请输入",
          unionKey: "bridgeClearWidth",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥梁跨数（m）",
          placeholder: "请输入",
          unionKey: "bridgeSpans",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "跨径总长",
          placeholder: "请输入（非必填）",
          unionKey: "totalSpanLength",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥梁孔数",
          placeholder: "请输入（非必填）",
          unionKey: "bridgeOpenings",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "单孔最大跨径",
          placeholder: "请输入（非必填）",
          unionKey: "maxSpan",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "斜交角",
          placeholder: "请输入（非必填）",
          unionKey: "skewAngle",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "前引桥长",
          placeholder: "请输入（非必填）",
          unionKey: "frontApproachLength",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "后引桥长",
          placeholder: "请输入（非必填）",
          unionKey: "rearApproachLength",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥下净空",
          placeholder: "请输入（非必填）",
          unionKey: "clearanceUnderBridge",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥跨序号",
          placeholder: "请输入（非必填）",
          unionKey: "spanNumber",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "跨径",
          placeholder: "请输入（非必填）",
          unionKey: "spanLength",
        },
      ],
    },
    {
      title: "桥梁结构信息",
      items: [
        {
          type: "select",
          label: "立交桥类别",
          placeholder: "请选择（非必填）",
          unionKey: "interchangeTypeLabel",
          unionKeyDetail: formData.value.interchangeTypeLabel
            ? "interchangeTypeLabel"
            : "interchangeTypeName",
          options: dicts.value.interchangeType||[],
        },
        {
          type: "select",
          label: "主桥上部结构形式",
          placeholder: "请选择（非必填）",
          unionKey: "mainStructureFormLabel",
          unionKeyDetail: formData.value.mainStructureFormLabel
            ? "mainStructureFormLabel"
            : "mainStructureFormName",
          options: dicts.value.mainStructureForm||[],
        },
        {
          type: "select",
          label: "桥梁性质",
          placeholder: "请选择（非必填）",
          unionKey: "bridgePropertyLabel",
          unionKeyDetail: formData.value.bridgePropertyLabel
            ? "bridgePropertyLabel"
            : "bridgePropertyName",
          options: dicts.value.bridgeProperty||[],
        },
        {
          type: "select",
          label: "桥梁跨径分类",
          placeholder: "请选择（非必填）",
          unionKey: "spanClassificationLabel",
          unionKeyDetail: formData.value.spanClassificationLabel
            ? "spanClassificationLabel"
            : "spanClassificationName",
          options: dicts.value.spanClassification||[],
        },
        {
          type: "select",
          label: "桥墩类型",
          placeholder: "请选择（非必填）",
          unionKey: "pierTypeLabel",
          unionKeyDetail: formData.value.pierTypeLabel
            ? "pierTypeLabel"
            : "pierTypeName",
          options: dicts.value.pierType||[],
        },
        {
          type: "select",
          label: "桥台类型",
          placeholder: "请选择（非必填）",
          unionKey: "abutmentTypeLabel",
          unionKeyDetail: formData.value.abutmentTypeLabel
            ? "abutmentTypeLabel"
            : "abutmentTypeName",
          options: dicts.value.abutmentType||[],
        },
        {
          type: "select",
          label: "桥面铺装类型",
          placeholder: "请选择（非必填）",
          unionKey: "deckPavementTypeLabel",
          unionKeyDetail: formData.value.deckPavementTypeLabel
            ? "deckPavementTypeLabel"
            : "deckPavementTypeName",
          options: dicts.value.deckPavementType||[],
        },
        {
          type: "select",
          label: "伸缩缝类型",
          placeholder: "请选择（非必填）",
          unionKey: "expansionJointTypeLabel",
          unionKeyDetail: formData.value.expansionJointTypeLabel
            ? "expansionJointTypeLabel"
            : "expansionJointTypeName",
          options: dicts.value.expansionJointType||[],
        },
        {
          type: "select",
          label: "支座类型",
          placeholder: "请选择（非必填）",
          unionKey: "bearingTypeLabel",
          unionKeyDetail: formData.value.bearingTypeLabel
            ? "bearingTypeLabel"
            : "bearingTypeName",
          options: dicts.value.bearingType||[],
        },
        {
          type: "select",
          label: "弯坡斜特征",
          placeholder: "请选择（非必填）",
          unionKey: "skewFeatureLabel",
          unionKeyDetail: formData.value.skewFeatureLabel
            ? "skewFeatureLabel"
            : "skewFeatureName",
          options: dicts.value.skewFeature||[],
        },
      ],
    },
    {
      title: "防撞设施",
      items: [
        {
          type: "select",
          label: "墩台防撞设施类型",
          placeholder: "请选择（非必填）",
          unionKey: "collisionProtectionTypeLabel",
          unionKeyDetail: formData.value.collisionProtectionTypeLabel
            ? "collisionProtectionTypeLabel"
            : "collisionProtectionTypeName",
          options: dicts.value.collisionProtectionType||[],
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥梁防撞设施超高",
          placeholder: "请输入（非必填）",
          unionKey: "collisionProtectionHeight",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "桥梁防撞设施超宽",
          placeholder: "请输入（非必填）",
          unionKey: "collisionProtectionWidth",
        },
      ],
    },
    {
      title: "设计信息",
      items: [
        {
          type: "input",
          maxlen: 20,
          label: "桥梁设计单位",
          placeholder: "请输入（非必填）",
          unionKey: "designUnit",
        },
        {
          type: "datePicker",
          label: "桥梁交工日期",
          placeholder: "请选择（非必填）",
          unionKey: "handoverDate",
        },
        {
          type: "datePicker",
          label: "桥梁竣工日期",
          placeholder: "请选择（非必填）",
          unionKey: "completionDate",
        },
        {
          type: "input",
          inputType: 'number',
          maxlen: 8,
          label: "总造价（万元）",
          placeholder: "请输入（非必填）",
          unionKey: "totalCost",
        },
        {
          type: "datePicker",
          label: "设计基准期",
          placeholder: "请选择（非必填）",
          unionKey: "designLifespan",
        },
        {
          type: "select",
          label: "通航等级",
          placeholder: "请选择（非必填）",
          unionKey: "navigationLevelLabel",
          unionKeyDetail: formData.value.navigationLevelLabel
            ? "navigationLevelLabel"
            : "navigationLevelName",
          options: dicts.value.navigationLevel||[],
        },
        {
          type: "select",
          label: "设计载荷等级",
          placeholder: "请选择（非必填）",
          unionKey: "designLoadClassLabel",
          unionKeyDetail: formData.value.designLoadClassLabel
            ? "designLoadClassLabel"
            : "designLoadClassName",
          options: dicts.value.designLoadClass||[],
        },
        {
          type: "select",
          label: "设计洪水频率",
          placeholder: "请选择（非必填）",
          unionKey: "designFloodFrequencyLabel",
          unionKeyDetail: formData.value.designFloodFrequencyLabel
            ? "designFloodFrequencyLabel"
            : "designFloodFrequencyName",
          options: dicts.value.designFloodFrequency||[],
        },
        {
          type: "select",
          label: "设计抗震等级",
          placeholder: "请选择（非必填）",
          unionKey: "seismicDesignLevelLabel",
          unionKeyDetail: formData.value.seismicDesignLevelLabel
            ? "seismicDesignLevelLabel"
            : "seismicDesignLevelName",
          options: dicts.value.seismicDesignLevel||[],
        },
      ],
    },
    {
      title: "相关管理单位",
      items: [
        {
          type: "input",
          maxlen: 20,
          label: "桥梁设计单位",
          placeholder: "请输入（非必填）",
          unionKey: "designUnit",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥梁施工单位",
          placeholder: "请输入（非必填）",
          unionKey: "constructionUnit",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥梁管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "managementUnit",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥梁建设单位",
          placeholder: "请输入（非必填）",
          unionKey: "buildUnit",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥梁监管单位",
          placeholder: "请输入（非必填）",
          unionKey: "supervisionUnit",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥下管理单位",
          placeholder: "请输入（非必填）",
          unionKey: "underbridgeUnit",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥梁养护单位",
          placeholder: "请输入（非必填）",
          unionKey: "conserveUnit",
        },
      ],
    },
    {
      title: "其他",
      items: [
        {
          type: "input",
          maxlen: 20,
          label: "桥梁附着管线情况",
          placeholder: "请输入（非必填）",
          unionKey: "attachedPipeline",
        },
        {
          type: "input",
          maxlen: 20,
          label: "桥下空间可用情况",
          placeholder: "请输入（非必填）",
          unionKey: "underBridgeSpace",
        },
        {
          type: "textarea",
          label: "备注",
          maxlen: 150,
          placeholder: "请输入（非必填）",
          unionKey: "remark",
        },
      ],
    },
  ];
});

// 查看表单详情
const getDetail = async (id) => {
  const res = await DataAcquisitionService.roadBridgeDetail(id);
  formData.value = res.data;
  console.log("桥梁详情", formData.value);
};

// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  console.log("formItem数据修改回调", unionKey, val);
  setNestedValue(formData.value, unionKey.split("."), val);
  console.log("onFormInpChange", formData.value);
};
// formItem 从选择页面回调的 select 数据修改
let autoFormRef = ref(null);
const onFormSelChange = () => {
  // 1如果选择的是 “所属路段”，则需要清空表单中选择的桩号信息
  if (
    dataAcquisitionInfo.stateData.key === "sectionIdLabel" &&
    formData.value.sectionId !== dataAcquisitionInfo.stateData.value
  ) {
    formData.value.startStakeId = "";
    formData.value.startStakeIdLabel = "";
    formData.value.endStakeId = "";
    formData.value.endStakeIdLabel = "";
    formData.value.centerStakeId = "";
    formData.value.centerStakeIdLabel = "";
  }
  // 2处理select选择的label
  setNestedValue(
    formData.value,
    dataAcquisitionInfo.stateData.key.split("."),
    dataAcquisitionInfo.stateData.label
  );
  // 3处理select选择的value
  let str = dataAcquisitionInfo.stateData.key.replace(/Label$/, "");
  setNestedValue(
    formData.value,
    str.split("."),
    dataAcquisitionInfo.stateData.value
  );
  console.log("选择更改", formData.value);
  if(autoFormRef.value?.formRef){
    autoFormRef.value.formRef.validateField(dataAcquisitionInfo.stateData.key);
  }
};
const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
  console.log("setNestedValue", formData.value);
};


// 暂存时校验部分表单字段
const validateFormFields = async () => {  
  autoFormRef.value.formRef.clearValidate();  
  let mainUnionKey = 'bridgeName';  
  let roadUnionKey = 'sectionIdLabel';  
  try {  
    // 使用 async/await 等待 validateField 方法的 Promise 解析  
    let err = await new Promise((resolve) => {  
      autoFormRef.value.formRef.validateField(mainUnionKey, (validateErr) => {  
        resolve(validateErr);  
      });  
    });  
    let err2 = await new Promise((resolve) => {  
      autoFormRef.value.formRef.validateField(roadUnionKey, (validateErr) => {  
        resolve(validateErr);  
      });  
    });  
    console.log('暂存校验结果1', err, err2);  
    if (!err.length && !err2.length) {  
      console.log('暂存校验结果2', flag.value); // 注意：这里的 flag 需要在外部定义并在需要时传递  
      flag.value = true; // 如果 flag 是组件内部状态，考虑使用 Vue 的响应式系统（如 ref 或 reactive）  
    }  
  } catch (error) {  
    console.error('验证过程中发生错误:', error);  
  }  
}

// form表单提交回调
let flag = ref(false);
let toast = ref(null);
let res = ref({});
let btnLoading = ref(""); // '0'-暂存按钮；'1'-提交按钮
const onFormSubmit = async ({ data, status }) => {
  console.log("提交桥梁采集", data, status);
  if(status == 0){
    await validateFormFields();
  }else{
    flag.value = true;
  }
  if(!flag.value){
    console.log('校验不通过，return',flag.value);
    backModal.value.close();
    return;
  }


  // 正式-1；草稿-0
  data.status = status;
  data.bridgeCode = data.bridgeCode || null;
  btnLoading.value = String(status);

  // 将临时新增到本地保存的桩号，一同传递给后端
  if(pileNumberInfo.pileNumbers?.length){
    data.newStakes = pileNumberInfo.pileNumbers;
    data.newStakes.forEach(item=>{
      item.status = status;
      // // 经纬度转换
      // let trans = transformLngLat(item.longitude,item.latitude);
      // item.longitude = String(trans[0]);
      // item.latitude = String(trans[1]);
    });
  }

  try {
    if (data.id) {
      // 编辑
      res.value = await DataAcquisitionService.roadBridgeEdit(data);
    } else {
      // 新增
      res.value = await DataAcquisitionService.roadBridgeAdd(data);
    }
    btnLoading.value = "";
    console.log("res", res.value.data);
    if (res.value.code == 200) {
      // 清空本地保存的新增桩号
      pileNumberInfo.clearPileNumber();
      toast.value.show({
        type: "success",
        message: `数据${status ? "提交" : "暂存"}成功`,
        complete() {
          uni.navigateBack({
            delta: 1,
          });
        },
      });
    }
  } catch (error) {
    btnLoading.value = "";
  }
};

// 切换表单状态回调：详情-》编辑
const onFormEdit = () => {
  isViewDetail.value = false;
  isFromDetail.value = true;
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding-top: v-bind(systemBarHeight);
  background: #f4f8ff;
}
.uv-nav-slot {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}
.content {
  height: calc(100% - 208rpx);
  margin-top: 0rpx;
  .info_card {
    background: #fff;
    margin-bottom: 48rpx;
    .info_title {
      height: 88rpx;
      padding: 22rpx 40rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #8e8e8e;
      line-height: 44rpx;
      box-sizing: border-box;
      border-bottom: 2rpx solid #f0f0f0;
    }
  }
}
.bottom_btns {
  position: fixed;
  bottom: 0rpx;
  width: 100vw;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  background: #f4f8ff;
  .btn {
    width: 256rpx;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    box-sizing: border-box;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
  }
  .save_btn {
    color: #4378ff;
    background: #fff;
    border: 2rpx solid #4378ff;
  }
  .sub_btn {
    color: #fff;
    background: #4378ff;
  }
}
</style>
