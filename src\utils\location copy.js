import coordtransform from "coordtransform";
import {
  checkAppLocationPermission,
  checkSystemLocationService,
} from "./deviceJX";

let tk = "307b00f916df23b531cf212d1e73216b";

// 获取用户定位
export const getCurLocation = () => {
  return new Promise((resolve, reject) => {
    // 定位开启状态 true=开启，false=未开启
    let bool = false;
    // android平台
    let platform = uni.getSystemInfoSync().platform
    if (platform == "android") {
      var context = plus.android.importClass("android.content.Context");
      var locationManager = plus.android.importClass(
        "android.location.LocationManager"
      );
      var main = plus.android.runtimeMainActivity();
      var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
      bool = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER);
      uni.getLocation({
        type: "wgs84", //返回可以用于uni.openLocation的经纬度
        success: function (res) {
          console.log("获取定位成功11", res);
          resolve(res);
        },
        fail: function (err) {
          reject(err);
          console.log("获取定位失败11", err);
          showPermissionAlert(platform);
        },
      });
    }

    // 未开启定位功能
    if (bool === false) {
      console.log("获取定位失败22");
      // showPermissionAlert();
    }
  });
};
// 判断是系统定位没开启 还是应用定位没有开启
const showPermissionAlert = async (platform) => {
  let count = 0;
  // 1. 先检查App是否有定位权限
  const hasAppPermission = await checkAppLocationPermission(platform);
  // 2. 再检查系统定位服务是否开启
  const isSystemEnabled = await checkSystemLocationService(platform);
  console.log("定位弹窗授权判断", hasAppPermission, isSystemEnabled);
  if (!hasAppPermission && count === 0) {
    console.log("定位弹窗授权判断1 !hasAppPermission");
    count++;
    // 跳转APP权限设置页
    uni.showModal({
      title: "权限申请",
      content: "需要应用位置权限，请前往设置开启",
      confirmText: "去设置",
      success: (res) => {
        console.log("打开弹窗成功1", count);

        if (res.confirm) {
          count--;
          openAppPermissionSettings(platform);
        } else {
          count--;
          console.log("取消，用户点击了取消111", count);
        }
      },
    });
  } else if (!isSystemEnabled && count === 0) {
    count++;
    console.log("定位弹窗授权判断2 !isSystemEnabled", count);
    // 2.跳转系统定位设置
    uni.showModal({
      title: "权限申请",
      content: "需要系统位置权限，请前往设置开启",
      confirmText: "去设置",
      success: (res) => {
        console.log("打开弹窗成功2", count);

        if (res.confirm) {
          count--;
          openPhoneSystemSettings(platform);
        } else {
          count--;
          console.log("取消，用户点击了取消222", count);
        }
      },
    });
  } else {
    // 3. 其他错误（GPS信号弱等）
    console.error("定位失败原因:", err);
  }
};
const openAppPermissionSettings = (platform) => {
  if (platform === "android") {
    const main = plus.android.runtimeMainActivity();
    const Intent = plus.android.importClass("android.content.Intent");
    const Settings = plus.android.importClass("android.provider.Settings");
    const intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
    const uri = plus.android.invoke(
      "android.net.Uri",
      "fromParts",
      "package",
      main.getPackageName(),
      null
    );
    intent.setData(uri);
    main.startActivity(intent);
  } else if (platform === "ios") {
    const UIApplication = plus.ios.importClass("UIApplication");
    const application = UIApplication.sharedApplication();
    const NSURL = plus.ios.importClass("NSURL");
    const url = NSURL.URLWithString("app-settings:");
    application.openURL(url);
  }
};
const openPhoneSystemSettings = (platform) => {
  // android平台
  if (platform == "android") {
    var Intent = plus.android.importClass("android.content.Intent");
    var Settings = plus.android.importClass("android.provider.Settings");
    var intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
    var main = plus.android.runtimeMainActivity();
    main.startActivity(intent); // 打开系统设置GPS服务页面
  }else if (platform == "ios") {
    var UIApplication = plus.ios.import("UIApplication");
    var application2 = UIApplication.sharedApplication();
    var NSURL2 = plus.ios.import("NSURL");
    var setting2 = NSURL2.URLWithString("App-Prefs:root=Privacy&path=LOCATION");
    application2.openURL(setting2);
    plus.ios.deleteObject(setting2);
    plus.ios.deleteObject(NSURL2);
    plus.ios.deleteObject(application2);
  }
};
// 通过经纬度，逆解析得到中文地址
export const reverseGeocode = (longitude, latitude, noFormat = false) => {
  // const url = `https://restapi.amap.com/v3/geocode/regeo?key=342053d626eebc75f3190e7f3c5f6226&location=${longitude},${latitude}&output=json`;
  const url = `http://api.tianditu.gov.cn/geocoder?postStr={'lon':${longitude},'lat':${latitude},'ver':1}&type=geocode&tk=${tk}`;
  return new Promise((resolve, reject) => {
    uni.request({
      url: url,
      success: (res) => {
        console.log("逆解析地址", res);
        // return
        if (res.statusCode === 200) {
          const data = res.data;
          // if (
          //   data.status === "1" &&
          //   data.regeocode &&
          //   data.regeocode.formatted_address
          // ) {
          if (
            data.status === "0" &&
            data.result &&
            data.result.formatted_address
          ) {
            // 成功获取到地址信息
            if (noFormat) {
              resolve(data.result);
            } else {
              const address = data.result.formatted_address; // 格式化后的地址信息
              console.log("地址:", address);
              resolve(address);
            }
            // 在这里处理地址信息，如显示到页面上
          } else {
            console.error("地址解析失败:", data.msg);
            reject("地址解析失败");
          }
        } else {
          console.error("请求失败:", res.statusCode);
          reject("地址解析请求失败");
        }
      },
      fail: (err) => {
        console.error("请求失败:", err);
        reject("请求失败: " + err);
      },
    });
  });
};

// 坐标转换 gcj02 -》 标准wgs84
export const transformLngLat = (longitude, latitude) => {
  return coordtransform.gcj02towgs84(longitude, latitude);
};
// 坐标转换 标准wgs84 -》 gcj02
export const reTransformLngLat = (longitude, latitude) => {
  return coordtransform.wgs84togcj02(longitude, latitude);
};

export const map2Img = (location) => {
  return `https://restapi.amap.com/v3/staticmap?location=${location.longitude},${location.latitude}&zoom=12&markers=-1,https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png,0:${location.longitude},${location.latitude}&key=342053d626eebc75f3190e7f3c5f6226`;
};

export const getRegionCodeByLatLng = (longitude, latitude) => {
  return new Promise((resolve, reject) => {
    const url = `http://api.tianditu.gov.cn/geocoder?postStr={"lon":${longitude},"lat":${latitude}}&type=rs&tk=${tk}`;
    uni.request({
      url: url, // 发送请求到天地图的逆地理编码API
      method: "GET",
      success: (res) => {
        console.log("解析行政区域编码", res);

        if (res.statusCode === 200 && res.data) {
          const data = res.data;
          // 解析行政区域编码
          if (data && data.result && data.result.addressComponent) {
            const regionCode =
              data.result.addressComponent.town_code ||
              data.result.addressComponent.county_code ||
              data.result.addressComponent.city_code ||
              data.result.addressComponent.province_code; // 获取行政区域编码(镇、区、市、省)
            resolve(regionCode);
          } else {
            reject("无法获取行政区域编码");
          }
        } else {
          reject("请求失败");
        }
      },
      fail: (err) => {
        reject("请求出错: " + err);
      },
    });
  });
};
