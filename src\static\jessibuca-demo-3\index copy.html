<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <script src="./jessibuca.js"></script>
    <style>
      * {
        margin: 0;
      }
      html,
      body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
      }
      .root {
        margin-top: 3rem;
      }

      #container {
        background: rgba(13, 14, 27, 0.7);
        width: 100vw;
        height: 25vh;
      }

      .input {
        display: flex;
        margin-top: 10px;
        color: white;
        place-content: stretch;
      }

      .input input {
        flex: auto;
      }

      @media (max-width: 720px) {
        #container {
          width: 90vw;
          height: 52.7vw;
        }
      }
    </style>
  </head>
  <body class="page">
    <div class="root" style="width: 100vw; height: 100vh">
      <div id="container"></div>
      <div class="input">
        <div>输入URL：</div>
        <input
          autocomplete="on"
          id="playUrl"
          value="ws://************:8586/rtp/34020000001320000001_34020000001320000001.live.flv"
        />
        <!-- <div id="playUrl" style="width:100%;height:100%"></div> -->
        <button id="play">播放</button>
        <button id="pause" style="display: none">停止</button>
      </div>
      <div class="input" style="line-height: 30px">
        <button id="destroy">销毁</button>
      </div>
    </div>

    <script>
      var $player = document.getElementById("play");
      var $pause = document.getElementById("pause");
      var $playHref = document.getElementById("playUrl");
      var $container = document.getElementById("container");
      var $destroy = document.getElementById("destroy");

      var showOperateBtns = true; // 是否显示按钮
      var forceNoOffscreen = true; //
      var jessibuca = null;

      function create() {
        jessibuca = new Jessibuca({
          __version__: "1.0.0",
          // url: $playHref.value,
          container: $container,
          videoBuffer: 0.2, // 缓存时长
          isResize: false,
          text: "",
          loadingText: "加载中",
          debug: true,
          showBandwidth: showOperateBtns, // 显示网速
          operateBtns: {
            fullscreen: showOperateBtns,
            screenshot: showOperateBtns,
            play: showOperateBtns,
            audio: showOperateBtns,
          },
          forceNoOffscreen: forceNoOffscreen,
          isNotMute: false,
        });

        jessibuca.onLog = (msg) => console.error(msg);
        jessibuca.onRecord = (status) => console.log("onRecord", status);
        jessibuca.onPause = () => console.log("onPause");
        jessibuca.onPlay = () => console.log("onPlay");
        jessibuca.onFullscreen = (msg) => console.log("onFullscreen", msg);
        jessibuca.onMute = (msg) => console.log("onMute", msg);
        $player.style.display = "inline-block";
        $pause.style.display = "none";
        $destroy.style.display = "none";
      }

      setTimeout(() => {
        jessibuca.play(
          "ws://************:8586/rtp/34020000001320000001_34020000001320000001.live.flv"
        );
      }, 3000);
      create();
      $player.addEventListener(
        "click",
        function () {
          var href =
            "ws://************:8586/rtp/34020000001320000001_34020000001320000001.live.flv";
          if (href) {
            jessibuca.play(href);
            $player.style.display = "none";
            $pause.style.display = "inline-block";
            $destroy.style.display = "inline-block";
          }
        },
        false
      );

      $pause.addEventListener("click", function () {
        $player.style.display = "inline-block";
        $pause.style.display = "none";
        jessibuca.pause();
      });

      $destroy.addEventListener("click", function () {
        if (jessibuca) {
          jessibuca.destroy();
        }
        create();
      });
    </script>
  </body>
</html>
