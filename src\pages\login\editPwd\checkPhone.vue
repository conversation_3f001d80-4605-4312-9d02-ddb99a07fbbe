<template>
  <view class="container">
    <view class="guid_text">{{scene==='resetPwd'?'重置密码需验证当前账号绑定的手机号':'请先验证需要修改密码的手机号'}}</view>
    <!-- 登录信息表单 -->
    <uv-form :model="formInfo" :rules="rules" ref="formRef">
      <uv-form-item prop="phone">
        <uv-input
          fontSize="14px"
          color="8a8a8a"
          :customStyle="inputStyle"
          border="none"
          :maxlength="11"
          placeholder="请输入手机号"
          prefixIcon="../../static/icon/phone_num_icon.png"
          prefixIconStyle="font-size: 44rpx;color: #909399"
          v-model="formInfo.phone"
        ></uv-input>
      </uv-form-item>
      <uv-form-item prop="code">
        <uv-input
          border="none"
          fontSize="14px"
          color="8a8a8a"
          :maxlength="6"
          :customStyle="inputStyle"
          placeholder="请输入验证码"
          prefixIcon="../../static/icon/code_icon.png"
          prefixIconStyle="font-size: 44rpx;color: #909399"
          v-model="formInfo.code"
        >
          <!-- vue3模式下必须使用v-slot:suffix -->
          <template v-slot:suffix>
            <uv-code
              ref="uCode"
              @change="codeChange"
              seconds="60"
              changeText="X秒重新获取"
            ></uv-code>
            <text class="code_btn" @click="getCode">{{ tips }}</text>
          </template>
        </uv-input>
      </uv-form-item>
      <uv-form-item prop="newPwd">
        <uv-input
          border="none"
          fontSize="14px"
          color="8a8a8a"
          :customStyle="inputStyle"
          placeholder="请输入新密码（8-20位）"
          prefixIcon="../../static/icon/pwd_icon.png"
          prefixIconStyle="font-size: 44rpx;color: #909399"
          suffixIcon="../../static/icon/pwd_look_icon.png"
          suffixIconStyle="font-size: 36rpx;color: #909399"
          v-model="formInfo.newPwd"
        >
        </uv-input>
      </uv-form-item>
      <uv-button
        type="primary"
        text="提 交"
        :loading="btnLoading"
        :customStyle="submitBtnStyle"
        :customTextStyle="submitTextStyle"
        @click="submit"
      ></uv-button>
    </uv-form>
  </view>
</template>
<script setup>
import { getSysteminfo, getRsaCode } from "@/utils";
import { computed, reactive, ref } from "vue";
import { UserService } from "@/service";
import { onLoad } from "@dcloudio/uni-app";
import { useUserStore } from "@/store/user";
const userInfo = useUserStore();
// 获取手机系统栏高度
const systemBarHeight = `${Number(getSysteminfo().systemBarHeight) + 66}px`;
console.log("系统栏高度", systemBarHeight);

let scene = ref("");
onLoad((options)=>{
  if(options?.scene === 'resetPwd'){
    scene.value = options.scene;
    uni.setNavigationBarTitle({
      title: "重置密码",
    });
  }
})

// 表单
const inputStyle = reactive({
  width: "670rpx",
  height: "100rpx",
  background: "#F4F8FF",
  boxSizing: "border-box",
  padding: "28rpx 32rpx",
  borderRadius: "16rpx 16rpx 16rpx 16rpx",
});
const submitBtnStyle = reactive({
  marginTop: "220rpx",
  height: "100rpx",
});
const submitTextStyle = reactive({
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 500,
  fontSize: "40rpx",
  color: "#FFFFFF",
  lineHeight: "47rpx",
});

let formInfo = reactive({
  phone: "",
  code: "",
});
const rules = reactive({
  phone: [
    {
      type: "string",
      required: true,
      len: 11,
      message: "请填写11位手机号",
      trigger: ["blur", "change"],
    },
    {
      validator: (rule, value, callback) => {
        const regex = /^1(3|4|5|6|7|8|9)\d{9}$/;
        console.log("手机号校", regex.test(value));
        return regex.test(value);
      },
      message: "请输入正确的手机号",
      trigger: ["blur", "change"],
    },
  ],
  code: {
    type: "string",
    required: true,
    len: 6,
    message: "请填写6位验证码",
    trigger: ["blur", "change"],
  },
  newPwd: {
    type: "string",
    required: true,
    min: 8,
    max: 20,
    message: "请填写8-20位新密码",
    trigger: ["blur", "change"],
  },
});
let tips = ref("");
const codeChange = (text) => {
  tips.value = text;
};
const uCode = ref(null);
const getCode = async () => {
  if (!formInfo.phone) {
    uni.showToast({
      title: "请填写手机号~",
      duration: 2000,
      icon: "none",
    });
    return;
  }
  if (uCode.value.canGetCode) {
    // uni.showLoading({
    //   title: "正在获取验证码",
    // });
    const { code, data } = await UserService.resetPasswordCode(formInfo.phone);
    // uni.hideLoading();
    // 通知验证码组件内部开始倒计时
    uCode.value.start();
  }
};
let formRef = ref(null);
let btnLoading = ref(false);
const submit = async () => {
  formRef.value
    .validate()
    .then(async () => {
      btnLoading.value = true;

      // 1获取加密key
      const { code: publicKeyCode, data: publicKey } =
        await UserService.getAuthPublicKey();
      const rsaPwd = getRsaCode(formInfo.newPwd, publicKey);

      UserService.resetPassword({
        phoneNumber: formInfo.phone,
        code: formInfo.code,
        // password: formInfo.newPwd,
        password: rsaPwd,
      })
        .then((res) => {
          btnLoading.value = false;
          if (res.code == 200) {
            uni.showToast({
              title: "修改密码成功",
              duration: 2000,
              icon: "none",
            });
            if(scene.value === 'resetPwd'){
              // uni.reLaunch({
              //   url:'/pages/login/index'
              // })
              userInfo.logout();
            }else{
              uni.navigateBack({
                delta: 1,
              });
            }
          }
        })
        .catch(() => {
          btnLoading.value = false;
        });
    })
    .catch((errors) => {
      console.log("error validate!!!");
    });
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding: 40rpx;
}
.guid_text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: #404040;
  line-height: 38rpx;
}
:deep(.uv-form) {
  position: relative;
  margin-top: 32rpx;
  // padding: 0 40rpx;
  .uv-form-item {
    margin-top: 40rpx !important;
  }
  .uv-form-item__body {
    padding: 0 !important;
  }
  .code_btn {
    font-family: PingFang SC-Medium;
    font-size: 28rpx;
    color: #4378ff;
  }
  .forget_pwd {
    position: absolute;
    /* float: right; */
    right: 32rpx;
    /* text-align: right; */
    width: 120rpx;
    height: 34rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #4378ff;
    line-height: 28rpx;
  }
}
</style>
