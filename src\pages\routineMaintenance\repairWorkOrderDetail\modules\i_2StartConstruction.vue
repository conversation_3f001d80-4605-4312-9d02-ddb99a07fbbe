<template>
  <view class="construction-container">
    <view class="construction-content">
      <ConstructionCard
        :base-info="repairWorkOrderBaseInfo"
        :detail-info="repairWorkOrderDetail"
        :child-order-list="repairChildWorkOrderList"
        :config-detail="workOrderConfigDetail"
      />
    </view>

    <view class="action-footer">
      <uv-button
        v-if="hasPermission"
        :loading="isLoading"
        :custom-style="btnStyle"
        :custom-text-style="btnTextStyle"
        text="开始施工"
        @click="handleStart"
      />
    </view>

    <buildModal
      ref="buildModalRef"
      :cur-time="currentTime"
      :cur-remark="remark"
      :cur-address="position.address"
      :is-cancel-disabled="isCancelDisabled"
      :is-refresh-loading="isRefreshLoading"
      @on-build-callback="onBuildCallback"
    />

    <remarkModal
      ref="remarkModalRef"
      :cur-remark="remark"
      @on-remark-callback="onRemarkCallback"
    />
  </view>
</template>
<script setup>
import { ref, reactive, computed } from "vue";
import dayjs from "dayjs";
import buildModal from "../components/modal/BuildModal.vue";
import remarkModal from "@/components/ylg-remark-modal.vue";
import ConstructionCard from "../components/card/ConstructionCard.vue";
import { getCurLocation, reverseGeocode } from "@/utils/location";
import { RoutineMaintenanceService } from "@/service";
import { useUserStore } from "@/store/user";

const props = defineProps({
  // 工单基础信息
  repairWorkOrderBaseInfo: {
    type: Object,
    default: () => ({}),
  },
  // 工单详情信息
  repairWorkOrderDetail: {
    type: Object,
    default: () => ({}),
  },
  // 子工单列表
  repairChildWorkOrderList: {
    type: Object,
    default: () => [],
  },
  // 工单配置详情
  workOrderConfigDetail: {
    type: Object,
    default: () => ({}),
  },
});

// 用户store
const userInfo = useUserStore();
// 计算属性：权限检查
const hasPermission = computed(() => {
  // 检查资源权限
  const hasResourcePermission = props.repairWorkOrderBaseInfo?.resourceList?.includes("dailyRepairWork") ?? false;
  // 检查用户是否在工单配置中
  const currentUserId = userInfo.id;
  const isUserInWorkOrder = props.workOrderConfigDetail?.users?.some(
    (user) => user.relevancyId === currentUserId
  ) ?? false;

  return hasResourcePermission && isUserInWorkOrder;
});

// 响应式数据
const isLoading = ref(false); // 开工按钮加载状态
const buildModalRef = ref(null); // 开工弹窗引用
const remarkModalRef = ref(null); // 备注弹窗引用
const currentTime = ref(""); // 当前时间
const remark = ref(""); // 备注内容
const isCancelDisabled = ref(false); // 取消按钮禁用状态
const isRefreshLoading = ref(false); // 刷新加载状态

// 位置信息
const position = reactive({
  longitude: "", // 经度
  latitude: "", // 纬度
  address: "", // 地址
});

// 自定义按钮样式
const btnStyle = {
  height: "84rpx",
  borderRadius: "8rpx",
  fontWeight: 600,
  background: "#4378ff",
  color: "#fff",
};
const btnTextStyle = {
  fontSize: "40rpx",
};

/**
 * 处理开工按钮点击，获取当前时间和位置信息后打开弹窗
 */
const handleStart = async () => {
  currentTime.value = dayjs().format("HH:mm");
  await getAddress();
  buildModalRef.value.open();
};

/**
 * 获取当前位置信息
 */
const getAddress = async () => {
  try {
    isLoading.value = true;
    const locationRes = await getCurLocation();
    if (locationRes.errMsg === "getLocation:ok") {
      position.longitude = locationRes.longitude;
      position.latitude = locationRes.latitude;
      position.address = await reverseGeocode(
        locationRes.longitude,
        locationRes.latitude
      );
    }
  } catch (error) {
    uni.showToast({
      icon: "none",
      title: "获取定位失败" + error,
    });
  } finally {
    isLoading.value = false;
  }
};

const onBuildCallback = async (e) => {
  switch (e.type) {
    case "onStart":
      startConstruction();
      break;
    case "onRefreshLocation":
      isRefreshLoading.value = true;
      await getAddress();
      isRefreshLoading.value = false;
      break;
    case "onCancel":
      buildModalRef.value.close();
      remark.value = "";
      break;
    case "onRemark":
      remarkModalRef.value.open();
      break;
  }
};
const startConstruction = async () => {
  try {
    isCancelDisabled.value = true;
    if (!position.longitude || !position.latitude) {
      uni.showToast({
        icon: "none",
        title: "请先获取当前定位~",
      });
      return;
    }

    const params = {
      workId: props.repairWorkOrderBaseInfo?.id,
      remark: remark.value,
      address: position.address,
      longitude: String(position.longitude || ""),
      latitude: String(position.latitude || ""),
    };

    await RoutineMaintenanceService.startConstructionConfirm(params);
    uni.showToast({
      icon: "none",
      title: "施工作业开始",
    });
    buildModalRef.value.close();
    setTimeout(() => {
      uni.$emit("refreshWorkOrderBaseInfo");
    }, 2000);
  } catch (error) {
    console.error("开始施工请求失败:", error);
  } finally {
    isCancelDisabled.value = false;
  }
};

const onRemarkCallback = (envRes) => {
  if (!envRes.remark) {
    uni.showToast({
      icon: "none",
      title: "请填写备注~",
    });
    return;
  }
  remark.value = envRes.remark;
  remarkModalRef.value.close();
};
</script>
<style lang="scss" scoped>
@import "../common.scss";

.construction-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f4f8ff;

  .construction-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 40rpx;
  }

  .action-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f4f8ff;
    padding: 40rpx;
    flex-shrink: 0;
  }
}
</style>
