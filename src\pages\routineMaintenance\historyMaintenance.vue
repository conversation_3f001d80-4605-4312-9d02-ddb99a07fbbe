<template>
  <view class="container">
    <view class="top_sticky">
      <uv-search
        height="40"
        shape="square"
        searchIcon="../../static/icon/search_icon.png"
        searchIconSize="18"
        placeholder="搜索日常维修记录"
        placeholderColor="#A09F9F"
        bgColor="#fff"
        :showAction="false"
        :boxStyle="searchStyle"
        v-model="inpVal"
        @change="inpChange"
        @search="inpSearch"
      ></uv-search>
    </view>
    <view class="todo_container">
      <view class="tabs-container">
        <view class="tabs-box1">
          <view
            :class="['tab', activeTab === item.value ? 'tab-active' : '']"
            v-for="(item, index) in toDoTypes"
            :key="index"
            @click="changeTab(item.value)"
          >
            {{ item.label }}·{{ item.num }}
          </view>
        </view>
      </view>
    </view>
    <view v-if="dataList.length">
      <view
        class="mouth_list_box"
        v-for="(monthGroup, index) in dataList"
        :key="index"
      >
        <view ref="stickyRef" class="month_sticky_bar month_sticky_bar1">
          <view class="month">
            <text>{{ monthGroup.month }}</text>
            <text class="red_text">（{{ monthGroup.total }}）</text>
          </view>
          <image
            class="date_select_icon"
            src="../../static/icon/dropdown_icon.png"
            @click="showTime"
          />
        </view>
        <DailyRepairCard :dataList="monthGroup.data" />
      </view>
      <view style="height: 100rpx"></view>
    </view>
    <template v-else>
      <view
        v-if="startD"
        ref="stickyRef"
        class="month_sticky_bar month_sticky_bar2"
      >
        <view class="month">
          <text>{{ dayjs(startD).format("YYYY-M") }}</text>
          <text class="red_text">（0）</text>
        </view>
        <image
          class="date_select_icon"
          src="../../static/icon/dropdown_icon.png"
          @click="showTime"
        />
      </view>
      <ylg-nodata class="nodata"></ylg-nodata>
    </template>
    <ylg-date-range-picker
      ref="datePicker"
      @confirmDate="confirmDate"
      @cancelDate="cancelDate"
    ></ylg-date-range-picker>
  </view>
</template>
<script setup>
import { reactive, ref, computed } from "vue";
import { onShow, onReachBottom } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { RoutineMaintenanceService, homepageApi } from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { getDaysInMonth } from "@/utils";
import DailyRepairCard from "@/pages/home/<USER>/DailyRepairCard.vue";

const activeTab = ref("0");
const statisticData = reactive({
  workCount: 0,
  approvalCount: 0,
});
const toDoTypes = computed(() => {
  return [
    {
      value: "0",
      label: "作业单",
      num: statisticData.workCount,
    },
    {
      value: "1",
      label: "审批单",
      num: statisticData.approvalCount,
    },
  ];
});
const changeTab = (value) => {
  if (value === activeTab.value) return;
  activeTab.value = value;
  getList();
};

// 列表相关数据
const dataList = ref([]);
// 分页查询参数
const pageInfo = reactive({
  page: 1,
  limit: 10,
});
const noData = ref(false);
const loading = ref(false);
// 输入框搜索内容
const inpVal = ref("");
// 数据状态

onShow(() => {
  pageInfo.page = 1;
  inpVal.value = "";
  getList();
});

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  marginBottom: "28rpx",
});

const inpChange = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  startD.value = "";
  endD.value = "";
  getList();
};
const inpSearch = (val) => {
  inpVal.value = val;
  pageInfo.page = 1;
  startD.value = "";
  endD.value = "";
  getList();
};
const stickyZIndex = ref(999);
// 时间选择组件
const datePicker = ref(null);
const showTime = () => {
  stickyZIndex.value = 99;
  datePicker.value.open();
};
const startD = ref("");
const endD = ref("");
const confirmDate = (envRes) => {
  const { dateType, year, month, startDate, endDate } = envRes;
  if (dateType === "date") {
    const days = getDaysInMonth(year, Number(month));
    startD.value = `${year}-${month < 10 ? "0" + month : month}-01 00:00:00`;
    endD.value = `${year}-${month < 10 ? "0" + month : month}-${days} 23:59:59`;
  } else if (dateType === "daterange") {
    startD.value = startDate;
    endD.value = endDate;
  }
  stickyZIndex.value = 999;
  datePicker.value.close();
  pageInfo.page = 1;
  getList();
};
const cancelDate = () => {
  console.log("取消选择时间回调");
  stickyZIndex.value = 999;
};

// 查询列表
const getList = async () => {
  const params = {
    projectId: projectInfo.projectId,
    queryType: activeTab.value,
    workCode: inpVal.value,
    workStatus: "",
    startTime: startD.value,
    endTime: endD.value,
    page: pageInfo.page,
    limit: pageInfo.limit,
  };
  getCount(params);
  loading.value = true;
  const { code, data: resData } =
    await RoutineMaintenanceService.getHistoryData(params);
  // 如果接口异常，page-1，不执行后续操作
  if (code != 200) {
    pageInfo.page--;
    return;
  }
  if (pageInfo.page === 1) {
    dataList.value = [];
    noData.value = false;
  }

  // 处理数据，将月份相同的数据拼接到一个分组里
  console.log("resData", resData);
  let monthArr = dataList.value.map((grounp) => grounp?.month || "");
  console.log("monthArr", monthArr);
  resData.forEach((resGrounp) => {
    let sameMonthIndex = monthArr.findIndex((item) => item === resGrounp.month);
    // 处理进度条数据
    resGrounp.data?.forEach((item) => {
      item.childWorkNum = Number(item.childWorkNum);
      if (item.finishNum == 0) {
        item.percentage = 0;
      } else {
        item.percentage =
          (Number(item.finishNum) / Number(item.childWorkNum)).toFixed(2) * 100;
      }
    });
    console.log("sameMonthIndex", sameMonthIndex);
    // 处理月份分组
    if (sameMonthIndex !== -1) {
      dataList.value[sameMonthIndex].data = dataList.value[
        sameMonthIndex
      ].data.concat(resGrounp.data);
    } else {
      dataList.value.push(resGrounp);
    }
  });

  loading.value = false;
  if (!resData.length) {
    noData.value = true;
  }
  console.log("日常维修列表", dataList.value);
};
const getCount = async (params) => {
  const { projectId, startTime, endTime, workCode } = params;
  const { code, data: resData } =
    await homepageApi.yhWorkOrderMobileHistoryCount({
      projectId,
      startTime,
      endTime,
      workCode,
    });
  if (code == 200) {
    statisticData.workCount = resData.workCount;
    statisticData.approvalCount = resData.approvalCount;
  }
};
/** 页面上拉 **/
onReachBottom(() => {
  if (noData.value) {
    uni.showToast({
      icon: "none",
      title: "没有更多了~",
    });
    return;
  }
  pageInfo.page++;
  getList();
});

// const handleCardClick = (envRes) => {
//   console.log("卡片回调", envRes);
//   switch (envRes.workStatus) {
//     // "待核验" "已完成"
//     case "4":
//     case "5":
//       uni.navigateTo({
//         url: `/pages/routineMaintenance/repairWorkOrderDetail/index?workId=${envRes.id}`,
//       });
//       break;
//     default:
//       break;
//   }
// };
</script>
<style lang="scss" scoped>
.container {
  background: #f2f2f2;
  min-height: 100vh;
  padding-top: 28rpx;
  .top_sticky {
    padding: 0 28rpx;
  }
  .todo_container {
    .tabs-container {
      .tabs-box1 {
        display: flex;
        align-items: center;
        gap: 0 64rpx;
        padding: 28rpx;
        background: #fff;
        .tab {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 32rpx;
          color: #404040;
        }
        .tab-active {
          font-weight: bold;
          background: linear-gradient(
              to right,
              rgba(51, 109, 255, 1),
              rgba(124, 161, 255, 0.8),
              rgba(67, 120, 255, 0.1),
              rgba(67, 120, 255, 0.05)
            )
            no-repeat bottom / 100% 8rpx;
        }
      }
    }
  }
  .mouth_list_box {
    padding: 0 28rpx;
  }
  .month_sticky_bar {
    display: flex;
    align-items: center;
    position: sticky;
    top: 206rpx;
    top: 0rpx;
    z-index: v-bind(stickyZIndex);
    background: #f2f2f2;

    &.month_sticky_bar1 {
      padding: 40rpx 0;
    }
    &.month_sticky_bar2 {
      padding: 40rpx 28rpx;
    }
    .month {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #373737;
      line-height: 44rpx;
      .red_text {
        color: #ff3132;
      }
    }
    .date_select_icon {
      display: block;
      margin-left: 16rpx;
      width: 28rpx;
      height: 28rpx;
    }
  }
  .nodata {
    margin-top: 80rpx;
  }
}
</style>
