import BaseService from "../request";

class EventHandlingApi extends BaseService {
  constructor() {
    super();
  }
  // app查询处置事件统计
  appStatistic(params) {
    return this.get("/yh/inspectEvent/app/statistic", params, {
      "X-Client-Type": "APP",
    });
  }
  // 查询各类型事件处置总数
  eventNum(params) {
    return this.get("/yh/inspectEvent/project/event/num", params);
  }
  // app查询事件处置
  inspectEventPage(params) {
    return this.get("/yh/inspectEvent/page", params);
  }
  // 查询事件详情
  detailWithTask(id) {
    return this.get(`/yh/inspectEvent/detailWithTask/${id}`);
  }
  // 批量处置
  inspectEventBatchHandle(params) {
    return this.put(`/yh/inspectEvent/batch/handle`, params);
  }
  // 转为病害、其他事件
  inspectEventTransfer(params) {
    return this.put(`/yh/inspectEvent/transfer`, params);
  }
  // 事件处置记录分页
  inspectEventHisAppPage(params) {
    return this.get(`/yh/inspectEvent/app/page`, params, {
      "X-Client-Type": "APP",
    });
  }
  //#region 病害重新处置
  // 检查工单状态（重新处置按钮弹出触发）
  inspectEventCheckWorkOrder(id) {
    return this.get(`/yh/inspectEvent/check/workOrder/${id}`);
  }
  // 事件转类型（重新处置）
  inspectEventReHandleTransfer(params) {
    return this.put(`/yh/inspectEvent/reHandle/transfer`, params);
  }
  // 重新处置表单提交
  inspectEventReHandle(params) {
    return this.put(`/yh/inspectEvent/reHandle`, params);
  }
  //#endregion
  // 查询平铺保养作业配置
  allMaintainConfig(params) {
    return this.get(`/yh/maintainConfig/all`, params);
  }
  // 保养人员
  maintainUserList(params) {
    return this.get(`/system/projectMember/list/${params.projectId}`, params);
  }
  // 所有桩号列表
  getAllStakeList(params) {
    return this.get(`/project-resource/stakeData/page`, params);
  }
  // 桩号详情
  stakeDetail(id) {
    return this.get(`/project-resource/stakeData/${id}`);
  }
  // 生成计划并启用
  dailyMaintainPlanSave(params) {
    return this.post("/yh/dailyMaintainPlan/save", params, {
      "X-Client-Type": "APP",
    });
  }
}
export const eventHandlingApi = new EventHandlingApi();
