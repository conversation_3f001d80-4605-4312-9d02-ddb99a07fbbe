<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-08-14 20:20:01
 * @Description: 
-->
<template>
  <view class="container">
    <uv-search
      height="36"
      shape="square"
      searchIcon="../../../static/icon/search_icon.png"
      searchIconSize="16"
      placeholder="输入路线名称搜索"
      placeholderColor="#C1C1C1;"
      bgColor="#fff"
      :showAction="false"
      :boxStyle="searchStyle"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <!-- 列表 -->
    <template v-if="!pageLoading">
      <view class="list_box" v-if="dataList.length">
        <view
          :class="['card', curItem.id === item.id ? 'active_card' : '']"
          v-for="item in dataList"
          :key="item.id"
          @click="chooseItem(item)"
        >
          <view class="item_title">{{ item.routeName }}</view>
          <view class="item_info">{{ item.politicalName }}</view>
        </view>
      </view>
      <no-data class="nodata" v-else></no-data>
    </template>
    <uv-loading-page :loading="pageLoading" loading-text="加载中..." font-size="24rpx"></uv-loading-page>
    <view class="bottom_box">
      <view class="choosed_data_show" v-if="curItem.routeName"
        >已选 {{ curItem.routeName }}({{
          curItem.politicalName
        }})</view
      >
      <view class="btn" @click="confirm">确 定</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { DataAcquisitionService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();

let unionKey = ref("");
onLoad(async (option) => {
  unionKey.value = option.unionKey;
  // 获取路线列表
  await getList();
  await handleCurChoosed(option.curItemName);
});

// 处理回显当前已经选择的路线
const handleCurChoosed = (curItemName) => {
  dataList.value.forEach((item) => {
    if (item.routeName === curItemName) {
      curItem.value = item;
    }
  });
};

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  marginBottom: "28rpx",
});

let inpVal = ref("");
const inpChange = (val) => {
  console.log("搜索值", val);
  inpVal.value = val;
  getList();
};
const inpSearch = (val) => {
  console.log("搜索值", val);
  inpVal.value = val;
  getList();
};

// 获取路线列表
let dataList = ref([]);
let pageLoading = ref(false);
const getList = async () => {
  pageLoading.value = true;
  const res = await DataAcquisitionService.getRoadLineList({searchVal: inpVal.value});
  dataList.value = res.data;
  pageLoading.value = false;
};

// 选择项目
let curItem = ref({});
const chooseItem = (item) => {
  curItem.value = item;
};

// 提交
const confirm = () => {
  dataAcquisitionInfo.updateDataAcquisition({
    stateData:{
      key: unionKey.value,
      value: curItem.value.id,
      label: curItem.value.routeName,
    }
  })
  uni.navigateBack({
    data: 1,
  });
};
</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 20rpx 40rpx;
  background: #f4f8ff;
}
.list_box {
  margin: 12rpx 0 200rpx 0;
  .card {
    margin-bottom: 28rpx;
    padding: 22rpx 28rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 16rpx;
    font-family: PingFang SC, PingFang SC;
    .item_title {
      font-weight: 400;
      color: #373737;
      font-size: 32rpx;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .item_info {
      font-size: 28rpx;
      color: #a09f9f;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .active_card {
    background: #4378ff;
    .item_title {
      color: #ffffff;
    }
    .item_info {
      color: #ffffff;
    }
  }
}
.nodata {
  margin-top: 136rpx;
}
.bottom_box {
  width: calc(100% - 80rpx);
  position: fixed;
  bottom: 0rpx;
  padding: 20rpx 0;
  background: #f4f8ff;
  .choosed_data_show {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .btn {
    width: 670rpx;
    height: 84rpx;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
}
</style>
