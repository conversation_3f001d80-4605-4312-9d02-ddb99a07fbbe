<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-11-11 15:38:48
 * @Description: 
-->
<template>
  <view class="container">
    <uv-search
      shape="square"
      searchIcon="../../../static/icon/search_icon.png"
      searchIconSize="18"
      placeholder="地点搜索"
      placeholderColor="#A09F9F;"
      bgColor="#fff"
      :focus="true"
      :clearabled="false"
      :showAction="false"
      :customStyle="searchCustomStyle"
      :boxStyle="searchBoxStyle"
      :inputStyle="{ fontSize: '28rpx' }"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <!-- 搜索地址列表 -->
    <view class="address_list" overflow-y="scroll" v-if="addressList.length">
      <view v-for="(item, index) in addressList" :key="index">
        <view class="top_line"></view>
        <view class="address_item" @click="chooseAddress(item)">
          <view class="left">
            <view class="address_name">{{ item.name }}</view>
            <view class="address_detail">{{ item.address }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref, computed } from "vue";
import { onLoad, onReady, onShow } from "@dcloudio/uni-app";
import { DataAcquisitionService } from "@/service";

let pageFrom = ref('');
onLoad((options)=>{
  console.log('options!!!!!',options);
  
  pageFrom.value = options.pageFrom || ''
  console.log('options!!!!!',pageFrom.value);
})

let inpVal = ref("");
const inpChange = (val) => {
  if (!val) {
    addressList.value = [];
    return;
  }
  inpVal.value = val;
  addressSearch();
};
const inpSearch = (val) => {
  if (!val) {
    addressList.value = [];
    return;
  }
  inpVal.value = val;
  addressSearch();
};

// 搜索地址
let addressList = ref([]);
const addressSearch = () => {
  let params = {
    inpVal: inpVal.value,
  };
  DataAcquisitionService.getAddreeeList(params).then((res) => {
    if (res.pois && res.pois.length > 0) {
      addressList.value = res.pois.reduce((acc, current) => {
        let nameArr = acc.map((accitem) => accitem.name);
        if (!nameArr.includes(current.name)) {
          let obj = {
            name: current.name,
            address:
              current.province + current.city + current.county ===
              current.address
                ? current.address
                : current.province +
                  current.city +
                  current.county +
                  current.address,
            location: current.lonlat,
          };
          acc.push(obj);
        }
        return acc;
      }, []);
    } else if (res.area && res.area.lonlat) {
      addressList.value[0] = {
        name: res.area.name,
        address: null,
        location: res.area.lonlat,
      };
    } else {
      addressList.value = [];
    }
    console.log("查看地址结果", addressList.value);
  });
};
// 获取当前定位
let myPosition = ref({
  latitude: "",
  longitude: "",
  address: "",
  type: pageFrom.value
});
const chooseAddress = (addressItem) => {
  let locationArr = addressItem.location.split(",");
  myPosition.value.longitude = String(locationArr[0]);
  myPosition.value.latitude = String(locationArr[1]);
  myPosition.value.address = addressItem.address;
  myPosition.value.type = pageFrom.value;

  console.log("选择列表地址", addressItem, myPosition.value);
  addressList.value = [];
  inpVal.value = "";

  uni.setStorageSync('myPosition', JSON.stringify(myPosition.value))
  uni.navigateBack({
    delta:1
  })

  // 通知webview更新定位
  // wv.value.evalJS(
  //   `receiveData({type:'changeLocation',data:{lng:${myPosition.longitude},lat:${myPosition.latitude}}})`
  // );
  // // 根据选择的定位请求附近的五个桩号
  // pageLoading.value = true;
  // dataList.value = [];
  // debouncedGetList();
};

// 搜索
const searchBoxStyle = reactive({
  height: "80rpx",
  borderRadius: "8px",
});
const searchCustomStyle = reactive({
  width: "100%",
  height: "80rpx",
});
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  min-height: 100vh;
  background: #f4f8ff;
}
.address_list {
  position: absolute;
  top: 80rpx;
  left: 0;
  padding: 0 24rpx;
  width: 100%;
  height: calc(100vh - 80rpx);
  box-sizing: border-box;
  background: #fff;
  .top_line {
    height: 2rpx;
    background: #d8d6d6;
  }
  .address_item {
    padding: 24rpx 0rpx;
    display: flex;
    justify-content: space-between;
    align-content: center;
    .left {
      .address_name {
        color: #373737;
        font-size: 32rpx;
      }
      .address_detail {
        color: #a09f9f;
        font-size: 22rpx;
        margin-top: 8rpx;
      }
    }
    .right {
      color: #666;
      font-size: 14rpx;
    }
  }
}
</style>
