<!--
 * @Author: chenhl
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-02-26 10:32:33
 * @Description: 
-->
<template>
  <view class="container">
    <view class="top_bg"></view>
    <view class="content">
      <view
        :class="[
          'main_info_card',
          isRetract ? 'retractHieght' : '',
          isWait ? 'start_maintain' : '',
        ]"
      >
        <!-- <view class="main_info_card"> -->
        <view class="top_mark">日常保养</view>
        <view class="title">{{ taskDetail?.dailyMaintainPlan?.planName }}</view>
        <view class="detail_title flex">
          <view class="left_bar flex">
            <view class="bar"></view>
            <view class="text">任务详情</view>
          </view>
          <image
            class="right_img"
            :style="{
              width:
                taskDetail?.taskStatus == 5 || taskDetail?.taskStatus == 6
                  ? '136rpx'
                  : '88rpx',
            }"
            :src="preserveStatusMenu[taskDetail?.taskStatus]"
          ></image>
        </view>
        <view
          class="message_notice"
          v-if="taskDetail?.dailyMaintainPlan?.remark"
          >{{ taskDetail?.dailyMaintainPlan?.remark }}</view
        >
        <view class="info_box">
          <view class="left_title w200">保养路线规划：</view>
          <view class="right_content right_btn" @click="previewImg">{{
            taskDetail.trajectoryUrl ? "查看大图" : "-"
          }}</view>
        </view>
        <ylg-load-image
          class="map_img"
          v-if="taskDetail?.trajectoryUrl"
          :scroll-top="0"
          loading-mode="spin-circle"
          :image-src="showImg(taskDetail?.trajectoryUrl)"
        ></ylg-load-image>
        <view class="info_box">
          <view class="left_title">任务时效：</view>
          <view class="right_content">{{ taskDetail?.cycleTime }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">保养路段：</view>
          <view class="right_content">{{
            taskDetail?.dailyMaintainPlan?.sectionName || "-"
          }}</view>
        </view>

        <view class="info_box">
          <view class="left_title">路段划分：</view>
          <view class="right_content"
            >{{ taskDetail?.startStakeName }}—{{
              taskDetail?.endStakeName
            }}</view
          >
        </view>
        <!-- <view class="info_box">
          <view class="left_title">保养内容：</view>
          <view class="right_content">{{
            taskDetail?.preserveContent || "-"
          }}</view>
        </view> -->
        <view class="info_box">
          <view class="left_title">保养频率：</view>
          <view class="right_content">{{
            taskDetail?.dailyMaintainPlan?.maintainFrequencyName || "-"
          }}</view>
        </view>
        <view class="info_table">
          <view class="left_title">作业内容：</view>
          <view class="right_excel">
            <view class="table_header flex">
              <view class="table_item_text">作业内容</view>
              <view class="table_item_text">计划作业数量</view>
            </view>
            <!-- 防止为非数组 -->
            <view v-if="taskDetail?.dailyMaintainPlan?.maintainConfigs">
              <view
                :class="[
                  'table_body',
                  'flex',
                  settlementItem.changed === '1' ? 'notice_active' : '',
                ]"
                v-for="(settlementItem, settlementIndex) in taskDetail
                  ?.dailyMaintainPlan?.maintainConfigs"
                :key="settlementIndex"
              >
                <view class="table_item_text">
                  {{ settlementItem.content || "--" }}
                </view>
                <view class="table_item_text">
                  {{
                    formatPlanWorkNumber(
                      settlementItem.planNumber,
                      settlementItem.units
                    )
                  }}
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="touch_bar" v-if="!isWait" @click="toggleTopCard">
          <image
            class="down_img"
            src="../../static/icon/touch_down_icon.png"
          ></image>
        </view>
      </view>
      <view class="bottom_btns">
        <uv-button
          v-if="isWait"
          :loading="btnLoading"
          :custom-style="startBtnStyle"
          :customTextStyle="btnTextStyle"
          text="开始保养"
          @click="handleStart"
        ></uv-button>
      </view>
      <!-- 任务完成情况 -->
      <view v-if="!isWait" class="task_finish_card">
        <view class="detail_title flex">
          <view class="left_bar flex">
            <view class="bar"></view>
            <view class="text">任务完成情况</view>
          </view>
        </view>
        <!-- <template> -->
        <view class="info_box">
          <view class="left_title">开始时间：</view>
          <view class="right_content">{{
            taskCompleteInfo?.maintainStartTime || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">开始定位：</view>
          <view class="right_content">{{
            taskCompleteInfo?.maintainStartAddress || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">结束时间：</view>
          <view class="right_content">{{
            taskCompleteInfo?.maintainEndTime || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">结束定位：</view>
          <view class="right_content">{{
            taskCompleteInfo?.maintainEndAddress || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">任务用时：</view>
          <view class="right_content">{{
            taskCompleteInfo?.taskTakes || "-"
          }}</view>
        </view>
        <view class="info_box">
          <view class="left_title">作业上报：</view>
          <view class="right_content">{{
            taskCompleteInfo?.reportNum || "-"
          }}</view>
        </view>
        <view v-if="taskCompleteInfo?.reportList?.length">
          <!-- 上报列表 -->
          <view
            class="report_list_box"
            @click="toDiseaseDetail(eventIem)"
            v-for="(eventIem, index) in taskCompleteInfo?.reportList"
            :key="index"
          >
            <view class="draft_mark" v-if="eventIem.draft == '1'">草稿</view>
            <view class="main_info flex">
              <!-- 病害：病害类型 -->
              <view class="title">{{ eventIem?.maintainTypeName }}</view>
              <view class="time">{{ eventIem?.reportTime }}</view>
            </view>
            <!-- 病害：事件对象名称 -->
            <view class="desc">{{ eventIem?.reportAddress }}</view>
          </view>
        </view>
        <!-- </template> -->
        <view class="btns" v-if="taskStatus == 3 || taskStatus == 6">
          <view class="event_sub_btn" @click="toReport">保养作业上报</view>
          <uv-button
            :loading="btnLoading"
            :custom-style="endBtnStyle"
            :customTextStyle="btnTextStyle"
            text="结束保养"
            @click="handleEnd"
          ></uv-button>
        </view>
      </view>
    </view>
    <preserveModal
      ref="preserveModalRef"
      :modalType="preserveType"
      :curTime="curTime"
      :curRemark="remarkVal"
      :curAddress="myPosition.address"
      :isCancelDisabled="isCancelDisabled"
      :isRefreshLoading="isRefreshLoading"
      @onPreserveCallback="onPreserveCallback"
    ></preserveModal>
    <remarkModal
      ref="remarkModalRef"
      :curRemark="remarkVal"
      @onRemarkCallback="onRemarkCallback"
    ></remarkModal>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      bgColor="rgba(255,255,255,0.5)"
      font-size="24rpx"
    ></uv-loading-page>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import preserveModal from "./components/preserveModal.vue";
import remarkModal from "@/components/ylg-remark-modal.vue";
import { onHide, onLoad, onShow, onUnload } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { RoutinePreserveService } from "@/service";
import { preserveStatusMenu } from "@/config/menu.js";
import { showImg } from "@/utils";
import {
  getCurLocation,
  reverseGeocode,
  transformLngLat,
} from "@/utils/location";

// 状态图映射
// 1 待开始
// 2 待保养
// 3 进行中
// 4 已完成
// 5 超时已完成
// 6 超时未完成（需判断有无开始时间）

let taskStatus = ref("3");
let taskId = ref("");
let pageLoading = ref(false);
// 是否还没有开始
// 2-true；
// 3，4，5-false；
// 6，需要判断有无开始时间，无开始时间-true；有开始时间-false
let isWait = ref(true);
let isRetract = ref(true);
let topCardHeight = ref("244rpx");
let btnLoading = ref(false);
let pageFrom = ref("");
onLoad((options) => {
  taskStatus.value = options?.taskStatus;
  pageFrom.value = options?.pageFrom;
  if (taskStatus.value == "2") {
    isWait.value = true;
    isRetract.value = false;
  } else if (["3", "4", "5"].includes(taskStatus.value)) {
    isWait.value = false;
    isRetract.value = true;
  }
  taskId.value = options?.taskId;
});

onShow(async () => {
  // 获取任务详情
  await getDetail();
  if (!isWait.value) {
    await getCompleteInfo();
  }
  // if (taskStatus.value == "3" || taskStatus.value == "6") {
  //   watchLocation();
  // }
  if (taskStatus.value == "3" || (taskStatus.value == "6" && !isWait.value)) {
    watchLocation();
  }
});

let taskDetail = ref({});
const getDetail = async () => {
  try {
    pageLoading.value = true;
    let { code, data } = await RoutinePreserveService.getTaskDetail(
      taskId.value
    );
    console.log("任务详情", code, data);
    if (code == 200) {
      taskDetail.value = data;
      // taskDetail.value.preserveContent = data.dailyMaintainPlan?.configNodes
      //   ?.length
      //   ? data.dailyMaintainPlan.configNodes.map((item) => item.name).join("；")
      //   : "-";
      taskStatus.value = data.taskStatus;
      isWait.value = data.maintainStartTime ? false : true;
      isRetract.value = data.maintainStartTime ? true : false;
      // topCardHeight.value = data.maintainStartTime ? "244rpx" : "1000rpx";
    }
    pageLoading.value = false;
  } catch (error) {
    pageLoading.value = false;
  }
};
let taskCompleteInfo = ref({});
let draftNum = ref(0);
const getCompleteInfo = async () => {
  try {
    let { code, data } = await RoutinePreserveService.getTaskCompleteInfo(
      taskId.value
    );
    console.log("任务完成情况", code, data);
    taskCompleteInfo.value = data;
    // 计算草稿数量
    draftNum.value = data.reportList.filter((item) => item.draft == "1").length;
  } catch (error) {}
};

const previewImg = () => {
  uni.previewImage({
    urls: [showImg(taskDetail.value.trajectoryUrl)], // 需要预览的图片HTTP链接列表
    // urls: [
    //   "https://platform.ylgsz.com/minio/file/2024-09-14/路线规划_20240914161347A143.jpeg",
    // ], // 需要预览的图片HTTP链接列表
    current: 0, // 当前显示图片的链接索引
  });
};

// // 切换顶部详情卡片展示高度
const toggleTopCard = () => {
  isRetract.value = !isRetract.value;
};

// 切换顶部详情卡片展示高度
// const toggleTopCard = () => {
//   console.log("切换高度", topCardHeight.value);
//   topCardHeight.value =
//     topCardHeight.value === "1000rpx" ? "244rpx" : "1000rpx";
// };

let preserveModalRef = ref(null);
let preserveType = ref("start");
let curTime = ref("");
let myPosition = reactive({
  // longitude: "115.109334",
  // latitude: "39.633542",
  // address: "河北省保定市涞水县其中口乡108国道",
  longitude: "",
  latitude: "",
  address: "",
});
const handleStart = async () => {
  preserveType.value = "start";
  curTime.value = dayjs().format("HH:mm");
  btnLoading.value = true;
  await getAddress();
  preserveModalRef.value.open();
};
const getAddress = async () => {
  try {
    let locationRes = await getCurLocation();
    if (locationRes.errMsg == "getLocation:ok") {
      myPosition.longitude = locationRes.longitude;
      myPosition.latitude = locationRes.latitude;
      myPosition.address = await reverseGeocode(
        locationRes.longitude,
        locationRes.latitude
      );
    }
    btnLoading.value = false;
  } catch (error) {
    console.log("获取定位失败", error);
    btnLoading.value = false;
    uni.showToast({
      icon: "none",
      title: "获取当前定位失败" + error,
    });
  }
};

const handleEnd = async () => {
  debugger;
  if (draftNum.value > 0) {
    uni.showToast({
      icon: "none",
      title: "结束前请先完善草稿状态的作业上报数据",
      duration: 3000,
    });
    return;
  }
  preserveType.value = "end";
  curTime.value = dayjs().format("HH:mm");
  btnLoading.value = true;
  await getAddress();
  preserveModalRef.value.open();
};
// 开始保养/结束保养 弹窗回调事件
let remarkModalRef = ref(null);
let isRefreshLoading = ref(false);
const onPreserveCallback = async (e) => {
  switch (e.type) {
    case "onAction":
      // 开始保养
      if (isWait.value) {
        onStart();
      } else if (taskStatus.value == 3 || taskStatus.value == 6) {
        onEnd();
      }
      break;
    case "onRefreshLocation":
      isRefreshLoading.value = true;
      await getAddress();
      isRefreshLoading.value = false;
      break;
    case "onCacel":
      preserveModalRef.value.close();
      remarkVal.value = "";
      break;
    case "onRemark":
      console.log("添加备注");
      remarkModalRef.value.open();
      break;
    default:
      break;
  }
};

let remarkVal = ref("");
const onRemarkCallback = (envRes) => {
  console.log("remark", envRes.remark);
  if (!envRes.remark) {
    uni.showToast({
      icon: "none",
      title: "请填写备注~",
    });
    return;
  }
  remarkVal.value = envRes.remark;
  remarkModalRef.value.close();
};

let isCancelDisabled = ref(false);
const onStart = async () => {
  try {
    isCancelDisabled.value = true;
    if (!myPosition.longitude || !myPosition.latitude) {
      uni.showToast({
        icon: "none",
        title: "请先获取当前定位~",
      });
      return false;
    }
    pageLoading.value = true;
    let params = {
      maintainTaskId: taskId.value,
      maintainNode: "1",
      remark: remarkVal.value,
      ...myPosition,
    };
    let { code, data } = await RoutinePreserveService.startPreserveTask(params);
    console.log("开始保养结果", code, data);

    if (code == 200) {
      uni.showToast({
        icon: "none",
        title: "日常保养开始",
      });
      remarkVal.value = "";
      await getDetail();
      await getCompleteInfo();
      preserveModalRef.value.close();
      // 开始监听实时定位
      watchLocation();
    }
    pageLoading.value = false;
    isCancelDisabled.value = false;
  } catch (error) {
    pageLoading.value = false;
    isCancelDisabled.value = false;
    console.log("开始保养失败");
  }
};
const onEnd = async () => {
  try {
    isCancelDisabled.value = true;
    if (!myPosition.longitude || !myPosition.latitude) {
      uni.showToast({
        icon: "none",
        title: "请先获取当前定位~",
      });
      return false;
    }
    pageLoading.value = true;
    let params = {
      maintainTaskId: taskId.value,
      maintainNode: "2",
      remark: remarkVal.value,
      ...myPosition,
    };
    let { code, data } = await RoutinePreserveService.endPreserveTask(params);
    console.log("结束保养结果", code, data);

    if (code == 200) {
      uni.showToast({
        icon: "none",
        title: "日常保养结束",
      });
      remarkVal.value = "";
      // 停止监听实时定位
      // uni.stopLocationUpdate();
      stopLocationUpdate();
      // await getDetail();
      // await getCompleteInfo();
      preserveModalRef.value.close();
      if (pageFrom.value == "home") {
        uni.redirectTo({
          url: "/pages/routinePreserve/index",
        });
      } else {
        uni.navigateBack({
          delta: 1,
        });
      }
    }
    pageLoading.value = false;
    isCancelDisabled.value = false;
  } catch (error) {
    pageLoading.value = false;
    isCancelDisabled.value = false;
    console.log("结束保养失败");
  }
};

// 保养作业上报
const toReport = () => {
  uni.navigateTo({
    url: `/pages/routinePreserve/preserveReporting?&maintainTaskId=${taskDetail.value.id}&sectionId=${taskDetail.value.dailyMaintainPlan.sectionId}&maintainPlanId=${taskDetail.value.maintainPlanId}`,
  });
};

// 查看上报的作业详情/继续编辑草稿
const toDiseaseDetail = (item) => {
  if (item.draft == "1") {
    uni.navigateTo({
      url: `/pages/routinePreserve/preserveReporting?&maintainTaskId=${item.maintainTaskId}&id=${item.id}&sectionId=${taskDetail.value.dailyMaintainPlan.sectionId}`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/routinePreserve/preserveReportDetail?id=${item.id}`,
    });
  }
};

onHide(() => {
  console.log("hide页面");
  // uni.stopLocationUpdate();
  stopLocationUpdate();
});
onUnload(() => {
  console.log("onUnload页面");
  // uni.stopLocationUpdate();
  stopLocationUpdate();
});

const lastTimestamp = ref(0);
const watchLocation = () => {
  console.log(
    "taskDetail.value.dailyMaintainPlan.inspectTrajectory",
    taskDetail.value.dailyMaintainPlan.inspectTrajectory
  );

  if (taskDetail.value.dailyMaintainPlan.inspectTrajectory != 2) {
    return;
  }
  startLocationUpdate();
};
// 开始实时定位
const startLocationUpdate = () => {
  uni.startLocationUpdate({
    type: "wgs84",
    success: () => {
      console.log("开始实时定位更新");
      // 监听位置变化
      uni.onLocationChange(handleLocationChange);
    },
    fail: (err) => {
      console.error("启动定位更新失败", err);
    },
  });
};

const handleLocationChange = (res) => {
  const currentTimestamp = Date.now();
  if (currentTimestamp - lastTimestamp.value >= 5000) {
    lastTimestamp.value = currentTimestamp;
    console.log("监听位置改变5s打印一次", res);
  }

  if (res.accuracy && res.accuracy < 5) {
    getLocationAndReport(res);
  }
};

// 获取用户位置
const getLocationAndReport = (res) => {
  // 获取当前时间
  const currentTimestamp = Date.now();
  // 确保上次上报时间距离当前时间至少5秒
  if (currentTimestamp - lastTimestamp.value >= 5000) {
    lastTimestamp.value = currentTimestamp; // 更新上次上报的时间
    const { latitude, longitude } = res;
    uploadLocationToServer(latitude, longitude); // 上报位置到后端
  }
};

// 上传位置到服务器
let isUploading = ref(false);
const uploadLocationToServer = async (latitude, longitude) => {
  if (isUploading.value) return; // 如果上一次请求还没完成，直接返回
  isUploading.value = true;
  try {
    let params = {
      maintainTaskId: taskDetail.value.id,
      longitude: String(longitude),
      latitude: String(latitude),
    };
    let res = await RoutinePreserveService.updateLocationPreserve(params);
    console.log("实时上报位置", String(longitude), String(latitude));

    if (res.code != 200) {
      console.log("监听位置失败");
      uni.showToast({
        icon: "none",
        title: res.msg || "上报轨迹请求失败~",
      });
    }
  } catch (error) {
    console.error("位置上传失败:", error);
  } finally {
    isUploading.value = false; // 完成后重置标识位
  }
};

// 停止实时定位
const stopLocationUpdate = () => {
  uni.offLocationChange(handleLocationChange);
  uni.stopLocationUpdate({
    success: () => {
      console.log("停止实时定位更新");
    },
    fail: (err) => {
      console.error("停止定位更新失败", err);
    },
  });
};
//格式化计划作业数量
const formatPlanWorkNumber = (number, unit) => {
  if (number === null || number === undefined || number === "") {
    return "--";
  } else {
    return number.toString() + unit;
  }
};
const startBtnStyle = {
  height: "84rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378ff",
  color: " #ffffff",
};
const btnTextStyle = {
  fontSize: "40rpx",
};
const endBtnStyle = {
  marginTop: "28rpx",
  width: "616rpx",
  height: "84rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#FFFFFF",
  border: "2rpx solid #4378ff",
  color: " #4378FF",
};
</script>
<style lang="scss" scoped>
.container {
  background-color: #f4f8ff;
  min-height: 100vh;
  overflow: hidden;
  position: relative;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top_bg {
  position: fixed;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: #065bff;
}
.content {
  position: relative;
  top: 20rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
}
.main_info_card {
  position: relative;
  box-sizing: border-box;
  padding: 28rpx;
  width: 670rpx;
  overflow: hidden; // 查看、去完成
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
  .top_mark {
    position: absolute;
    right: 0;
    top: 0;
    background-color: rgba(239, 229, 255, 1);
    width: 136rpx;
    height: 52rpx;
    border-radius: 0 24rpx 0 24rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 26rpx;
    color: #ac79f6;
    line-height: 52rpx;
    text-align: center;
  }
  .title {
    font-weight: 600;
    font-size: 36rpx;
    color: #373737;
    line-height: 50rpx;
  }
  .detail_title {
    margin-top: 28rpx;
    .left_bar {
      .bar {
        margin-right: 8rpx;
        width: 6rpx;
        height: 24rpx;
        background: #4378ff;
        border-radius: 4rpx;
      }
      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 32rpx;
        color: #404040;
        line-height: 44rpx;
      }
    }
    .right_img {
      display: block;
      width: 88rpx;
      height: 42rpx;
    }
  }
  .message_notice {
    margin-top: 28rpx;
    padding: 10rpx 18rpx;
    background-color: rgba(254, 230, 228, 1);
    background: #fee6e4;
    border-radius: 8rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #ff3838;
    line-height: 40rpx;
  }
  .info_box {
    display: flex;
    align-items: baseline;
    // flex-wrap: wrap;
    margin-top: 24rpx;
    display: flex;
    .left_title {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #b0b0b0;
      line-height: 40rpx;
    }
    .w200 {
      width: 200rpx;
    }
    .right_content {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      line-height: 40rpx;
    }
    .right_btn {
      color: #4378ff;
    }
  }
  .info_table {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 24rpx;
    display: flex;
    .left_title {
      font-weight: 400;
      font-size: 28rpx;
      color: #b0b0b0;
      line-height: 40rpx;
    }
    .info_box_message_notice {
      margin-top: 8rpx;
      display: flex;
      align-items: center;
      width: 100%;
      background: rgba(249, 213, 217, 1);
      padding: 6rpx 18rpx;
      border-radius: 8rpx;
      image {
        width: 18px;
        height: 18px;
      }
      view {
        margin-left: 7px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #ff2e00;
        line-height: 40rpx;
      }
    }
    .right_content {
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      line-height: 40rpx;
    }
    .right_excel {
      margin-top: 20rpx;
      width: 100%;
      border-radius: 4rpx;
      border: 2rpx solid #f2f2f2;
      .table_header {
        height: 50%;
        line-height: 48rpx;
        background-color: rgba(242, 242, 242, 1);
        font-weight: 400;
        font-size: 28rpx;
        color: #636363;
      }
      .table_body {
        height: 50%;
        line-height: 48rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #404040;
        border-bottom: 2rpx solid #f2f2f2;
      }
      .table_body:last-child {
        border-bottom: none;
      }
      .notice_active {
        color: #ff2e00;
      }
      .table_item_text {
        width: 50%;
        text-align: center;
      }
    }
  }
  .map_img {
    margin-top: 28rpx;
    display: block;
    width: 614rpx;
    height: 300rpx;
  }
}
// 仅开始保养时调整高度
.start_maintain {
  max-height: calc(100vh - 184rpx);
  overflow-y: auto;
}
.bottom_btns {
  position: fixed;
  bottom: 0rpx;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  background: #f4f8ff;
  padding: 40rpx;
}
.retractHieght {
  height: 244rpx;
}
.touch_bar {
  .down_img {
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-40rpx);
    display: block;
    width: 80rpx;
    height: 8rpx;
  }
}
.bottom_btn {
  position: fixed;
  bottom: 40rpx;
  // left: 0;
  // right: 0;
  width: 670rpx;
  height: 84rpx;
  line-height: 84rpx;
  text-align: center;
  background: #4378ff;
  border-radius: 8rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 40rpx;
  color: #ffffff;
}

.task_finish_card {
  margin: 34rpx 0 80rpx 0;
  box-sizing: border-box;
  padding: 28rpx;
  width: 670rpx;
  background-color: #fff;
  border-radius: 24rpx;
  .detail_title {
    .left_bar {
      .bar {
        margin-right: 8rpx;
        width: 6rpx;
        height: 24rpx;
        background: #4378ff;
        border-radius: 4rpx;
      }
      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #404040;
        line-height: 40rpx;
      }
    }
    .right_img {
      display: block;
      width: 88rpx;
      height: 42rpx;
    }
  }
  .info_box {
    display: flex;
    align-items: baseline;
    // flex-wrap: wrap;
    margin-top: 24rpx;
    display: flex;
    .left_title {
      width: 140rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #b0b0b0;
      line-height: 40rpx;
    }
    .right_content {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: #404040;
      line-height: 40rpx;
    }
  }
  .report_list_box {
    position: relative;
    margin-top: 28rpx;
    padding: 12rpx 24rpx;
    box-sizing: border-box;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 2rpx solid #d9d9d9;
    .draft_mark {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 84rpx;
      height: 40rpx;
      background: rgba(234, 234, 234, 1);
      border-radius: 8rpx 0 0 0;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #404040;
      line-height: 40rpx;
      text-align: center;
    }
    .main_info {
      .title {
        flex: 1;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #404040;
        line-height: 40rpx;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      .time {
        width: 266rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #636363;
        line-height: 36rpx;
      }
    }
    .desc {
      margin-top: 8rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #a09f9f;
      line-height: 34rpx;
      width: 400rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .btns {
    padding: 20rpx 0;
    .event_sub_btn {
      box-sizing: border-box;
      width: 616rpx;
      height: 84rpx;
      line-height: 84rpx;
      text-align: center;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      background: #4378ff;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 40rpx;
      color: #ffffff;
    }
  }
}
</style>
