/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-24 16:15:22
 * @Description:
 */
import { defineStore } from "pinia";

export const useLocationStore = defineStore("location", {
  state: () => ({
    // 平台首页的天气定位
    initialLocation: {
      longitude: "",
      latitude: "",
    },
    initailAddress: {},
    initailWeather:{},
    initailWeatherAlarm:''
  }),
  actions: {
    // 更新当前定位
    updateInitialLocation(location) {
      this.initialLocation = location;
    },
    // 更新当前地区
    updateInitailAddress(address) {
      this.initailAddress = address;
    },
    // 更新当前天气
    updateInitailWeather(weather) {
      this.initailWeather = weather;
    },
    // 更新当前天气预警
    updateInitailWeatherAlarm(alarm) {
      this.initailWeatherAlarm = alarm;
    },
  },
});
