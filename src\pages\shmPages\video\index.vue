<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-05-14 11:32:01
 * @Description: 
-->
<template>
  <view class="video_container">
    <view class="device_box">
      <view class="device_item">
        <image
          class="device_item_img"
          src="../../../static/shmStatic/image/line_echart_point_nodata.png"
        />
        <view class="device_info_bar">
          <image
            class="monitor_status_icon"
            src="../../../shmStatic/icon/monitor_online_icon_20250514.png"
            alt=""
          />
          <div class="monitor_device_name">监控A</div>
        </view>
      </view>
    </view>
    <web-view
      ref="webviewRef"
      src="../../../static/jessibuca-demo-3/index.html"
    ></web-view>
  </view>
</template>
<script setup>
import { ref, computed } from "vue";
import { onLoad, onReady } from "@dcloudio/uni-app";
import { ShmService } from "@/service";

const getDetail = async (idData) => {
  try {
    const { data } = await ShmService.getMonitorDevicePageApi({
      assetId: "1867091080912158721",
      page: 1,
      limit: 10,
    });
    console.log("2");
    if (data?.length) {
      console.log("3");
      console.log(data);
    }
  } catch (error) {
    console.error("获取监控详情失败:", error);
    uni.showToast({
      title: "获取视频流失败",
      icon: "none",
    });
  }
};

const pages = getCurrentPages();
const getWebView = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const webView = pages[pages.length - 1].$getAppWebview().children()[0];
      console.log("webView", webView);
      resolve(webView);
    }, 1000);
  });
};
let wv = ref(null);
const webviewRef = ref(null);
onReady(async (option) => {
  // wv.value = await getWebView();
  console.log("wv", wv.value);
  getDetail();
});
</script>

<style scoped lang="scss">
.video_container {
  width: 100%;
  height: 100%;
}
.device_box {
  background: pink;
  display: flex;
  align-items: center;
  .device_item {
    position: relative;
    width: 432rpx;
    height: 244rpx;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.2);
    border-radius: 16rpx;
    border: 3rpx solid #4378ff;
    .device_item_img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
