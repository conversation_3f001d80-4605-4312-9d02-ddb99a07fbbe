<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-15 09:27:46
 * @Description: 
-->
<template>
  <view class="container">
    <view class="top_bg"></view>
    <view class="content">
      <view class="main_info_card" v-if="detailData && detailData.id">
        <view
          class="alarmLevelName"
          :class="'alarmLevel' + detailData.alarmLevel"
          >{{ detailData.alarmLevelName }}</view
        >
        <view class="alarm-id">{{ detailData.uuid }}</view>
        <view class="flex-between-center btn-box">
          <view class="alarm-label">{{ detailData.formatAlarmTime }}</view>
          <view
            @click="handleStatus"
            class="tag"
            :class="'tag' + detailData.eventStatus"
          >
            <text class="">{{ detailData.eventStatusName }}</text>
            <uv-icon
              class="status_select_icon"
              name="arrow-down"
              color="#fff"
              size="12"
            ></uv-icon>
          </view>
        </view>
        <view class="flex-start-top">
          <text class="alarm-type">{{ detailData.alarmSuggest }}</text>
        </view>
        <view class="alarm-rules-box" v-if="moreFlag === true">
          <view class="shm-same-add-title">报警规则</view>
          <view class="flex-start-top condition">
            <text class="alarm-label">触发条件</text>
            <text class="alarm-content">{{ detailData.triggerCondition }}</text>
          </view>
          <view class="flex-start-top">
            <text class="alarm-label">执行动作</text>
            <text class="alarm-content">{{ detailData.executeAction }}</text>
          </view>
        </view>
        <view class="flex-center">
          <view class="open-more" @click="moreFlag = !moreFlag"></view>
        </view>
      </view>
      <view class="card-box">
        <view class="tabs-box flex-center">
          <view
            class="tab-item"
            :class="{ active: tabIndex === 0 }"
            @click="tabChange(0)"
            >数据详情</view
          >
          <view
            class="tab-item"
            :class="{ active: tabIndex === 1 }"
            @click="tabChange(1)"
            >执行详情</view
          >
        </view>
        <data-detail ref="dataDetailRef" v-show="tabIndex === 0" />
        <implementation-details
          ref="implementationDetailsRef"
          v-show="tabIndex === 1"
          :eventItemId="eventItemId"
        />
      </view>
      <view class="bottom_btn">
        <uv-button
          :loading="btnLoading"
          :custom-style="startBtnStyle"
          :customTextStyle="btnTextStyle"
          :text="`报警处置（${detailData?.realEventStatusName || ''}）`"
          v-if="
            detailData.eventStatus === '1' || detailData.eventStatus === '2'
          "
          :disabled="
            !hasAuth('alarmEventsEdit') ||
            detailData.eventStatus === '1' ||
            detailData.eventStatus === '2' ||
            saveBtning
          "
        ></uv-button>
        <uv-button
          :loading="btnLoading"
          :custom-style="endBtnStyle"
          :customTextStyle="btnTextStyle"
          :text="`报警处置（${detailData?.realEventStatusName || ''}）`"
          v-else
          @click="toHandleAlarmEvent"
          :disabled="!hasAuth('alarmEventsEdit') || saveBtning"
        ></uv-button>
      </view>
    </view>
    <uv-picker
      ref="statusPickerRef"
      :closeOnClickOverlay="false"
      :columns="stateData.alarmEventStatus"
      keyName="dictValue"
      color="#404040"
      activeColor="#4378FF"
      @confirm="confirm"
    ></uv-picker>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      bgColor="rgba(255,255,255,0.5)"
      font-size="24rpx"
    ></uv-loading-page>
  </view>
</template>
<script setup>
import { computed, nextTick, onUnmounted, reactive, ref } from "vue";
import remarkModal from "@/components/ylg-remark-modal.vue";
import { onHide, onLoad, onShow, onUnload } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { AssetService } from "@/service";
import { hasAuth } from "@/config/permissions.js";
import DataDetail from "./components/DataDetail.vue";
import ImplementationDetails from "./components/ImplementationDetails.vue";
const pageLoading = ref(false);
const moreFlag = ref(false); //展示更多
const btnLoading = ref(false);
const detailData = ref({});
const stateData = reactive({
  alarmEventStatus: [
    [
      {
        dictKey: "2",
        dictValue: "无效报警",
      },
      {
        dictKey: "3",
        dictValue: "有效报警",
      },
    ],
  ], //事件状态
});
const tabIndex = ref(0); //tab切换
const statusPickerRef = ref(null);
const saveBtning = ref(false);
// 状态修改事件
const confirm = (val) => {
  if (saveBtning.value === true) return false;
  console.log();
  saveBtning.value = true;
  AssetService.alarmEventHandle({
    eventStatus: val.value[0].dictKey,
    id: detailData.value.id,
  })
    .then((res) => {
      getAlarmEventDetail(detailData.value.id);
    })
    .finally(() => {
      saveBtning.value = false;
    });
};

const handleStatus = () => {
  if (hasAuth("alarmEventsEdit") && detailData.value.eventStatus === "1") {
    statusPickerRef.value.open();
  }
};

const dataDetailRef = ref(null);
const implementationDetailsRef = ref(null);

// tab切换
const tabChange = (index) => {
  tabIndex.value = index;
  if (index === 1) {
    setTimeout(() => {
      implementationDetailsRef.value.getAppexecuteAndNotice(
        detailData.value.id
      );
    }, 100);
  } else {
    setTimeout(() => {
      if (!dataDetailRef.value) return;
      dataDetailRef.value.initData(detailData.value);
    }, 200);
  }
};

// 获取详情
const getAlarmEventDetail = (id, type) => {
	console.log('打印次数')
  pageLoading.value = true;
  AssetService.alarmEventDetail({ id })
    .then((res) => {
      console.log(res);
      detailData.value = res.data;

      // 备份真实状态值（底部按钮、报警处置表单页都需要用到）
      detailData.value.realEventStatus = detailData.value.eventStatus;
      detailData.value.realEventStatusName = detailData.value.eventStatusName;
      if (["5", "6", "7"].includes(res.data.eventStatus)) {
        detailData.value.eventStatus = "3";
        detailData.value.eventStatusName = "已确认有效";
      }
      if (type !== "1") {
        setTimeout(() => {
          if (!dataDetailRef.value) return;
          dataDetailRef.value.initData(res.data);
        }, 200);
      }
    })
    .finally(() => {
      pageLoading.value = false;
    });
};

// 报警事件处置
const toHandleAlarmEvent = () => {
  uni.navigateTo({
    url: `/pages/shmPages/alarmEvents/handleForm?detailData=${JSON.stringify(
      detailData.value
    )}`,
  });
};

let eventItemId = ref("");
let eventItemType = ref("");
onLoad((options) => {
// onLoad(({ id, type }) => {
	console.log('options')
	console.log(options)
  eventItemId.value = options.id;
  eventItemType.value = options.type
//   getAlarmEventDetail(id, type);
  if (options.type === "1") {
    tabIndex.value = 1;
    setTimeout(() => {
      implementationDetailsRef.value.getAppexecuteAndNotice(id);
    }, 100);
  }
});

onShow(async () => {
  getAlarmEventDetail(eventItemId.value, eventItemType.value);
});
const startBtnStyle = {
  width: "670rpx",
  height: "84rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#A09F9F",
  color: " #ffffff",
};
const btnTextStyle = {
  fontSize: "40rpx",
};
const endBtnStyle = {
  width: "670rpx",
  height: "84rpx",
  lineHeight: "56rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378FF",
  color: " #fff",
};
</script>
<style lang="scss" scoped>
.container {
  background-color: #f4f8ff;
  min-height: 100vh;
  overflow: hidden;
  position: relative;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top_bg {
  position: absolute;
  top: 0;
  width: 100vw;
  height: 100rpx;
  background-color: #065bff;
}
.content {
  position: relative;
  top: 20rpx;
  padding: 0 40rpx 180rpx 40rpx;
  box-sizing: border-box;
}
.condition {
  margin-bottom: 28rpx;
}
.main_info_card {
  position: relative;
  box-sizing: border-box;
  padding: 28rpx;
  width: 100%;
  height: v-bind(topCardHeight);
  transition: height 0.8s ease;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
  .alarmLevelName {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 26rpx;
    width: 136rpx;
    height: 52rpx;
    line-height: 52rpx;
    text-align: center;
    &.alarmLevel1 {
      color: #4688e5;
      background: url("../../../static/shmStatic/icon/alarmLevel1.png")
        center center no-repeat;
      background-size: 100%;
    }
    &.alarmLevel2 {
      color: #e3893b;
      background: url("../../../static/shmStatic/icon/alarmLevel2.png")
        center center no-repeat;
      background-size: 100%;
    }
    &.alarmLevel3 {
      color: #ed5354;
      background: url("../../../static/shmStatic/icon/alarmLevel3.png")
        center center no-repeat;
      background-size: 100%;
    }
  }
  .alarm-id {
    color: #373737;
    font-size: 36rpx;
    font-weight: 500;
    height: 80rpx;
    line-height: 40rpx;
    width: 80%;
    word-break: break-all;
  }
  .btn-box {
    padding: 28rpx 0;
    .tag {
      // background-color: #b0b0b0;
      border-radius: 24rpx;
      padding: 0 12rpx;
      height: 48rpx;
      line-height: 48rpx;
      background: #75dc7f;
      border-radius: 8rpx;
      font-size: 24rpx;
      color: #fff;
      .status_select_icon {
        display: inline-block;
      }
      &.tag1 {
        background: #fa6666;
      }
      &.tag2 {
        background: #b0b0b0;
      }
      &.tag3 {
        background: #75dc7f;
      }
    }
  }
  .alarm-type {
    background: #ffefed;
    border-radius: 8rpx;
    padding: 8rpx 28rpx;
    color: #ff3838;
    font-size: 28rpx;
    text-align: left;
    word-break: break-all;
    margin-bottom: 28rpx;
  }
  .open-more {
    width: 80rpx;
    height: 8rpx;
    background: #d9d9d9;
    border-radius: 8rpx;
  }
  .alarm-rules-box {
    padding: 18rpx 0 28rpx 0;
    transition: all 1.5s ease;
    .shm-same-add-title {
      margin-bottom: 28rpx;
    }
    .alarm-label {
      color: #404040;
      width: 140rpx;
      font-weight: 500;
      font-size: 24rpx;
      margin-bottom: 28rpx;
    }
    .alarm-content {
      flex: 1;
      color: #404040;
      font-size: 24rpx;
    }
  }
}
.card-box {
  margin-top: 28rpx;
  padding: 28rpx 0;
  box-shadow: 4rpx 4rpx 20rpx 0 rgba($color: #000000, $alpha: 0.08);
  .tabs-box {
    height: 64rpx;
    background: #f2f2f2;
    border-radius: 48rpx;
    margin: 0 28rpx 28rpx 28rpx;
    .tab-item {
      width: 50%;
      text-align: center;
      line-height: 64rpx;
      color: #9c9c9c;
      font-size: 28rpx;
      &.active {
        background: #4378ff;
        border-radius: 48rpx;
        color: #fff;
      }
    }
  }
}
.bottom_btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  padding: 0 40rpx;
  height: 124rpx;
  background: #f4f8ff;
  z-index: 11;
}
</style>
