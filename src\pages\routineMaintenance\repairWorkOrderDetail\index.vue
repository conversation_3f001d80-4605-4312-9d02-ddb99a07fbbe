<template>
  <view class="work-order-detail-container">
    <!-- 动态组件渲染，避免多个v-if判断 -->
    <component
      :is="currentStageComponent"
      v-bind="currentStageProps"
    />

    <!-- 加载骨架屏 -->
    <uv-skeleton
      v-if="isPageLoading"
      class="skeleton"
      rows="12"
      :loading="isPageLoading"
    />
  </view>
</template>

<script setup>
import { ref, watch, provide, onMounted, onUnmounted, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { RoutineMaintenanceService } from "@/service";
import { useRepairWorkOrderStore } from "@/store/repairWorkOrder";

import i_1Confirm from "./modules/i_1Confirm.vue";
import i_2StartConstruction from "./modules/i_2StartConstruction.vue";
import i_3UnderConstruction from "./modules/i_3UnderConstruction.vue";
import i_4Verification from "./modules/i_4Verification.vue";
import i_5Completed from "./modules/i_5Completed.vue";

// 工单状态映射配置
const WORK_STATUS_CONFIG = {
  1: { name: "待确认", class: "pending", component: i_1Confirm },
  2: { name: "待施工", class: "to-do", component: i_2StartConstruction },
  3: { name: "施工中", class: "in-progress", component: i_3UnderConstruction },
  4: { name: "待核验", class: "verify", component: i_4Verification },
  5: { name: "已完成", class: "completed", component: i_5Completed },
};

// Store实例
const workOrderStore = useRepairWorkOrderStore();

// 响应式数据
const workOrderId = ref(""); // 工单ID
const isPageLoading = ref(false); // 页面加载状态
const workOrderBaseInfo = ref({}); // 工单基础信息
const workOrderDetail = ref({}); // 工单详情信息
const childWorkOrderList = ref([]); // 子工单列表
const workOrderConfigDetail = ref({}); // 工单配置详情
const childWorkOrderCompleteInfo = ref({}); // 子工单完成信息

// 获取工单基础信息
const getWorkOrderBaseInfo = async () => {
  try {
    const { data } = await RoutineMaintenanceService.workOrderBaseInfo(workOrderId.value);
    workOrderStore.updateBaseInfo(data);
    workOrderBaseInfo.value = data;
  } catch (error) {
    console.error("获取工单基础信息失败:", error);
  }
};

// 获取工单详情
const getWorkOrderDetail = async (id) => {
  try {
    const { data } = await RoutineMaintenanceService.workOrderDetail(id);
    workOrderDetail.value = data;
  } catch (error) {
    console.error("获取工单详细信息失败:", error);
  }
};

// 获取子工单列表
const getChildOrderDetailList = async (id) => {
  try {
    const { data } = await RoutineMaintenanceService.getChildOrder(id);
    childWorkOrderList.value = data;
  } catch (error) {
    console.error("获取子工单列表失败:", error);
  }
};

// 获取工单维修配置
const getWorkOrderConfigDetail = async (id) => {
  try {
    const { data } = await RoutineMaintenanceService.getCostInfo(id);
    workOrderConfigDetail.value = data;
  } catch (error) {
    console.error("获取工单维修配置失败:", error);
  }
};

// 获取子工单任务完成情况
const getChildWorkOrderCompleteInfo = async (id) => {
  try {
    const { data } = await RoutineMaintenanceService.workOrderCompleteInfo(id);
    childWorkOrderCompleteInfo.value = data;
  } catch (error) {
    console.error("获取子工单任务完成情况失败:", error);
  }
};

// 状态数据配置
const STATUS_DATA_CONFIG = {
  1: ["childOrderDetailList"],
  2: ["childOrderDetailList", "workOrderConfigDetail", "workOrderDetail"],
  3: ["childOrderDetailList", "workOrderConfigDetail", "workOrderDetail", "childWorkOrderCompleteInfo"],
  4: ["childOrderDetailList", "workOrderConfigDetail", "workOrderDetail", "childWorkOrderCompleteInfo"],
  5: ["childOrderDetailList", "workOrderConfigDetail", "workOrderDetail", "childWorkOrderCompleteInfo"],
};

// 数据加载器映射
const DATA_LOADERS = {
  workOrderDetail: getWorkOrderDetail,
  workOrderConfigDetail: getWorkOrderConfigDetail,
  childOrderDetailList: getChildOrderDetailList,
  childWorkOrderCompleteInfo: getChildWorkOrderCompleteInfo,
};

/**
 * 当前工单状态对应的组件
 */
const currentStageComponent = computed(() => {
  const status = workOrderBaseInfo.value.workStatus;
  return WORK_STATUS_CONFIG[status]?.component || null;
});

// 当前阶段组件的props
const currentStageProps = computed(() => {
  const baseProps = {
    repairWorkOrderBaseInfo: workOrderBaseInfo.value,
    repairChildWorkOrderList: childWorkOrderList.value,
  };

  const status = Number(workOrderBaseInfo.value.workStatus);

  switch (status) {
    case 1:
      return baseProps;
    case 2:
      return {
        ...baseProps,
        repairWorkOrderDetail: workOrderDetail.value,
        workOrderConfigDetail: workOrderConfigDetail.value,
      };
    case 3:
      return {
        ...baseProps,
        repairWorkOrderDetail: workOrderDetail.value,
        workOrderConfigDetail: workOrderConfigDetail.value,
        childWorkOrderCompleteInfo: childWorkOrderCompleteInfo.value,
      };
    case 4:
    case 5:
      return {
        ...baseProps,
        repairWorkOrderDetail: workOrderDetail.value,
        workOrderConfigDetail: workOrderConfigDetail.value,
        childWorkOrderCompleteInfo: childWorkOrderCompleteInfo.value,
      };
    default:
      return baseProps;
  }
});

// 工单状态样式类
const getWorkStatusClass = (status) => WORK_STATUS_CONFIG[status]?.class;

// 数据加载逻辑
const loadDataByStatus = async (status, workOrderId) => {
  if (!status || !workOrderId) return;

  isPageLoading.value = true;
  try {
    const dataTypes = STATUS_DATA_CONFIG[status] || [];
    // 并行加载数据以提高性能
    const loadPromises = dataTypes.map(async (dataType) => {
      const loader = DATA_LOADERS[dataType];
      if (loader) {
        await loader(workOrderId);
      }
    });

    await Promise.all(loadPromises);
  } catch (error) {
    console.error("加载工单数据失败:", error);
  } finally {
    isPageLoading.value = false;
  }
};

// 监听器
watch(
  () => workOrderBaseInfo.value.workStatus,
  async (newStatus, oldStatus) => {
    const workOrderId = workOrderBaseInfo.value?.id;
    if (newStatus && workOrderId && newStatus !== oldStatus) {
      await loadDataByStatus(newStatus, workOrderId);
    }
  },
  { immediate: false }
);



// 依赖注入
provide("getWorkStatusClass", getWorkStatusClass);

// 组件挂载时添加事件监听
onMounted(() => {
  uni.$on("refreshWorkOrderBaseInfo", getWorkOrderBaseInfo);
  uni.$on("refreshChildOrderDetailList", () => {
    const workOrderId = workOrderBaseInfo.value?.id;
    if (workOrderId) {
      getChildOrderDetailList(workOrderId);
    }
  });
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off("refreshWorkOrderBaseInfo", getWorkOrderBaseInfo);
  uni.$off("refreshChildOrderDetailList");
});

onLoad((options) => {
  if (options?.id) {
    workOrderId.value = options.id;
    getWorkOrderBaseInfo();
  }
});
</script>
<style lang="scss" scoped>
.work-order-detail-container {
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
}
</style>
