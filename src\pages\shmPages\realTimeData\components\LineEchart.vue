<template>
  <view class="box">
    <view class="monitor_unit" v-show="hasData">{{
      curPointMonitorUnit
    }}</view>
    <l-echart v-show="hasData" ref="chartRef"></l-echart>
    <view v-show="!hasData" class="nodata">
      <image
        class="nodata_img"
        src="/static/shmStatic/image/line_echart_point_nodata.png"
      ></image>
      <view class="nodata_text">暂无数据~</view>
    </view>
  </view>
</template>
<script setup>
import { ref, onMounted, watch, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";
import useWebSocket from "@/hooks/useWebSocket";

const props = defineProps({
  assetId: {
    type: String,
    required: true,
    default: "",
  },
  pointId: {
    type: String,
    required: true,
    default: "",
  },
  curPointMonitorUnit: {
    type: String,
    required: true,
    default: "",
  },
});

// 实时数据图表
const chartRef = ref(null);
let chart1 = null;
let maxDataCount = 300;
let hasData = ref(false);

// 初始化 ECharts 图表
const initChart = async () => {
  // 获取 Canvas 的上下文
  if (!chartRef.value) return;
  chart1 = await chartRef.value.init(echarts);
  const option1 = {
    backgroundColor: "transparent",
    tooltip: {
      show: true,
      trigger: "axis",
      axisPointer: {
        type: "line",
      },
      backgroundColor: "rgba(11, 39, 87, 0.8)",
      borderColor: "rgba(14, 52, 141, 1)",
      textStyle: {
        fontSize: 12,
        color: "#A09F9F",
      },
    },
    legend: {
      left: "center",
      textStyle: {
        color: "#fff",
        fontSize: 12,
      },
      icon: "circle",
      itemWidth: 10,
      itemHeight: 10,
    },
    grid: {
      top: "4%",
      left: "0%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    graphic: {
      type: "text",
      left: "0px",
      top: "15%",
      style: {
        text: "单位:",
        textAlign: "left",
        fill: "#fff",
        fontSize: 14,
      },
    },
    xAxis: {
      // type: "category", // 使用时间轴
      type: "time", // 使用时间轴
      boundaryGap: false,
      axisLabel: {
        rotate: 45, // 标签旋转角度
        showMaxLabel: false,
        showMinLabel: false,
        color: "#86909C", // 设置字体颜色
      },
      // data: [
      //   "09:51:00",
      //   "09:51:56",
      //   "09:53:00",
      //   "09:53:35",
      //   "09:54:56",
      //   "09:56:00",
      //   "09:57:08",
      // ],
    },
    yAxis: [
      {
        type: "value",
        position: "left",
        alignTicks: true,
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: "#86909C",
          formatter: "{value} ",
        },
        splitLine: {
          lineStyle: {
            color: "rgba(229, 230, 235, 1)",
            type: "dashed",
          },
        },
      },
    ],
    dataZoom: [
      {
        type: "inside", // 鼠标滚轮缩放
        start: 0, // 默认数据初始选择范围为0%
        end: 100, // 默认数据结束选择范围为100%
      },
    ],
    series: {
      data: [150, 230, 224, 218, 135, 147, 260],
      type: "line",
    },
  };
  chart1.setOption(option1);
};

// 实时数据列表
let pointDataList = ref([]);

const updateChartData = () => {
  // 重新组织数据
  let myArr = [];
  if (pointDataList.value.length > maxDataCount) {
    pointDataList.value.splice(0, maxDataCount - 100);
  }
  myArr.push({ data: pointDataList.value });

  // 更新 ECharts 图表
  chart1.setOption({
    series: myArr,
    dataZoom: [
      {
        type: "inside", // 鼠标滚轮缩放
        startValue: new Date().getTime() - 1000 * 60 * 2, // 视图范围：过去5分钟
        endValue: new Date().getTime(), // 视图范围：当前时间
      },
    ],
  });
};

//socket 事件
const handleMessage = (e) => {
  // console.log('websocket接受信息', e)
  if (e.data.includes("客户端连接成功")) {
    sendWebsokectMsg();
    return;
  }

  if (JSON.parse(e.data).message == "更新参数成功") {
    console.log("更新参数成功啦", JSON.parse(e.data).message);
    return;
  }
  let msg = JSON.parse(e.data);
  console.log("收到websocket业务数据消息1", msg);
  if (msg.type !== "online" && msg.type === "realTimeStatistic") {
    let message = ref({});
    message.value = msg.message;
    // 添加数据到数组
    pointDataList.value.push([
      message.value.timestamp,
      message.value.value,
    ]);
    // 避免重复设置
    if (!hasData.value) {
      hasData.value = true;
      nextTick(() => {
        chart1.resize();
      });
    }

    updateChartData();
  }
  // 表示收到心跳
  if (msg.type === "online") {
    resetHeart();
  }
};
const sendWebsokectMsg = () => {
  let msg = JSON.stringify({
    type: "realTimeStatistic",
    message: [
      {
        key: "assetId",
        // value: props.assetId,
        value: "1866451235716112386",
      },
      {
        key: "pointId",
        value: [props.pointId],
        // value: ["1877554379332009985"],
      },
    ],
  });
  sendMsg(msg);
};
const {
  websock,
  initWebSocket,
  typeMsg,
  resetHeart,
  sendMsg,
  isWebsocketReady,
} = useWebSocket(handleMessage);

// 重置数据
const resetData = () => {
  chart1.setOption({
    series: {
      data: [],
    },
  });
  pointDataList.value = [];
};

watch(
  () => props.assetId,
  async (newval) => {
    if (newval) {
      if (chart1) resetData();
      if (isWebsocketReady.value) {
        sendWebsokectMsg();
      }
    }
  },
  { immediate: true, deep: false }
);
watch(
  () => props.pointId,
  async (newval) => {
    if (newval) {
      if (chart1) resetData();
      console.log("查看链接状态", isWebsocketReady.value);

      if (isWebsocketReady.value) {
        sendWebsokectMsg();
      }
    }
  },
  { immediate: true, deep: false }
);
watch(
  () => isWebsocketReady.value,
  (newval) => {
    if (newval === true) {
      sendWebsokectMsg();
    }
  }
);

onMounted(() => {
  initWebSocket();
  nextTick(() => {
    initChart();
  });
});
// 组件销毁时关闭 WebSocket 连接
onUnmounted(() => {
  if (websock.value) {
    websock.value.close();
  }
});
</script>

<style lang="scss" scoped>
.box {
  background: #fff;
  width: 100%;
  height: 300rpx;
  box-sizing: border-box;
  .monitor_unit {
    padding: 12rpx 0 0 60rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #86909c;
    line-height: 40rpx;
  }
  .nodata {
    margin-top: 32rpx;
    text-align: center;
    .nodata_img {
      width: 60%;
      height: 220rpx;
    }
    .nodata_text {
      margin-top: 12rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #a09f9f;
      line-height: 40rpx;
    }
  }
}
</style>
