<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-14 11:05:06
 * @Description: 
-->
<template>
  <scroll-view scroll-y="true" class="content">
    <ylg-auto-form
      ref="autoFormRef"
      :isAllDisabled="false"
      :isFromDetail="false"
      :formConfig="formConfig"
      :formData="formData"
      :rules="rules"
      btnLoading=""
      :labelStyle="labelStyle"
      :formItemStyle="formItemStyle"
      :placeholderStyle="placeholderStyle"
      @onChange="onFormInpChange"
    >
      <template #bottomBtns>
        <view class="btns flex-between-center">
          <view class="btn" @click="cancelCreate">取消</view>
          <uv-button
            :loading="btnLoading"
            :custom-style="startBtnStyle"
            :customTextStyle="btnTextStyle"
            text="保存"
            @click="onFormSubmit"
          ></uv-button>
        </view>
      </template>
    </ylg-auto-form>
    <!-- 取消 二次确认弹窗 -->
    <uv-modal
      ref="cancelModalRef"
      :title="cancelTitle"
      :showCancelButton="true"
      :content="cancelText"
      :textStyle="{ color: '#404040' }"
    >
      <template v-slot:confirmButton>
        <view class="cancel_btn no_cancel_btn" @click="cancelBack">取消</view>
        <view class="cancel_btn confirm_del_btn" @click="confirmBack"
          >确认</view
        >
      </template>
    </uv-modal>
    <uv-toast ref="toast"></uv-toast>
  </scroll-view>
</template>
<script setup>
import { computed, reactive, ref } from "vue";
import ylgAutoForm from "@/components/ylg-auto-form.vue";
import { onLoad, onShow, onBackPress } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { AssetService } from "@/service";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { usePileNumberStore } from "@/store/pileNumber";
const pileNumberInfo = usePileNumberStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();

let isEdit = ref(false);
onLoad(async (options) => {
  // 1清空pinia中缓存的之前选择的数据项：如所属路线、所属路段等；
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: "",
      value: "",
      label: "",
    },
  });

  // 2查询人员列表
  await getPersonList();
  // 处理详情数据
  await handleDetailData(options.detailData);
});

// 人员选择列表
let handleUserOptions = ref([]);
const getPersonList = async () => {
  try {
    let { code, data } = await AssetService.deptAuthListNew({
      deptId: "1873618650538229761",
    });
    console.log("查看人员列表", data);
    if (code == 200) {
      data = data.map((item) => {
        item.dictValue = item.userName;
        item.dictKey = item.id;
        return item;
      });
      handleUserOptions.value = data;
    }
  } catch (error) {
    console.log("人员列表 error", error);
    handleUserOptions.value = [];
  }
};

// 事件处理状态
let eventStatusOptions = ref([
  {
    dictKey: "6",
    dictValue: "处置中",
  },
  {
    dictKey: "7",
    dictValue: "处置完成",
  },
]);

// 取消弹窗
let cancelTitle = computed(() => (isEdit.value ? "取消编辑" : "取消新增"));
let cancelText = computed(() =>
  isEdit.value
    ? "当前编辑内容尚未保存，将回退至上一页面，请确认！"
    : "当前新增内容尚未保存，将回退至上一页面，请确认！"
);

onBackPress((backOptions) => {
  if (backOptions.from === "backbutton") {
    cancelModalRef.value.open();
    return true;
  } else if (backOptions.from === "navigateBack") {
    return false;
  }
});

// 表单数据配置
const formData = ref({
  id: "",
  // 处理人
  handleUserId: "",
  handleUserIdLabel: "",
  // 处理状态
  eventStatus: "",
  eventStatusLabel: "",
  // 处理时间
  handleTime: dayjs().format("YYYY-MM-DD"),
  // 处理措施
  handleMeasure: "",
});
const rules = computed(() => {
  return {
    handleUserIdLabel: {
      type: "string",
      required: true,
      message: "请选择处理人",
      trigger: ["blur", "change"],
    },
  };
});
const formConfig = computed(() => {
  return [
    {
      items: [
        {
          type: "select",
          modelType: "model",
          label: "处理人",
          placeholder: "请选择",
          unionKey: "handleUserIdLabel",
          options: handleUserOptions.value || [],
        },
        {
          type: "select",
          modelType: "model",
          label: "处理状态",
          placeholder: "请选择（非必填）",
          unionKey: "eventStatusLabel",
          options: eventStatusOptions.value || [],
        },
        {
          type: "datePicker",
          label: "处理时间",
          noMaxLimit: true,
          modelType: "model",
          placeholder: "请选择（非必填）",
          unionKey: "handleTime",
        },
        {
          type: "textarea",
          modelType: "model",
          label: "处理措施",
          maxlen: 500,
          placeholder: "请输入（非必填）",
          unionKey: "handleMeasure",
        },
      ],
    },
  ];
});

// 查看表单详情
const handleDetailData = async (data) => {
  try {
    let obj = JSON.parse(data);
    console.log("查看详情数据", obj);
    isEdit.value = obj.handleUserId ? true : false;
    formData.value = {
      id: obj.id,
      eventStatus:
        (obj.realEventStatus || obj.eventStatus) == "7"
          ? obj.realEventStatus || obj.eventStatus
          : "6",
      handleMeasure: obj.handleMeasure,
      handleTime: obj.handleTime
        ? dayjs(obj.handleTime).format("YYYY-MM-DD")
        : dayjs().format("YYYY-MM-DD"),
      handleUserId: obj.handleUserId,
    };
    // 处理人 显示
    handleUserOptions.value.some((person) => {
      if (person.id === formData.value.handleUserId) {
        formData.value.handleUserIdLabel = person.userName;
      }
    });
    // 处理状态 显示
    eventStatusOptions.value.some((statusItem) => {
      if (statusItem.dictKey === formData.value.eventStatus) {
        formData.value.eventStatusLabel = statusItem.dictValue;
      }
    });
    console.log("查看状态回显", formData.value);
  } catch (error) {
    console.log("查看回显 error", error);
  }
};

// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  setNestedValue(formData.value, unionKey.split("."), val);
};
const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
  console.log("setNestedValue", formData.value);
};

// 取消
let cancelModalRef = ref(null);
const cancelCreate = () => {
  cancelModalRef.value.open();
};
const cancelBack = () => {
  cancelModalRef.value.close();
};
const confirmBack = () => {
  cancelModalRef.value.close();
  uni.navigateBack({
    delta: 1,
  });
};

// form表单提交回调
let autoFormRef = ref(null);
let toast = ref(null);
let btnLoading = ref(false);
const onFormSubmit = async () => {
  autoFormRef.value.formRef
    .validate()
    .then(async (res) => {
      btnLoading.value = true;
      let params = {
        ...formData.value,
        handleTime: formData.value.handleTime + " 00:00:00",
      };
      console.log("请求后端接口", params);
      delete params.eventStatusLabel;
      delete params.handleUserIdLabel;
      let { code, data } = await AssetService.alarmEventHandle(params);
      if (code == 200) {
        toast.value.show({
          type: "success",
          message: "保存成功",
        });
        uni.navigateBack({
          delta: 1,
        });
      }
    })
    .catch((errors) => {
      console.log("校验失败", errors);
    });
};

// form表单样式配置
const labelStyle = reactive({
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  lineHeight: "33rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
});
const placeholderStyle = ref("color: #C1C1C1");

// 保存按钮样式
const startBtnStyle = {
  width: "282rpx",
  height: "72rpx",
  lineHeight: "72rpx",
  textAlign: "center",
  boxSizing: "border-box",
  borderRadius: "8rpx",
  fontFamily: "PingFang SC, PingFang SC",
  fontWeight: 600,
  background: "#4378ff",
  color: " #ffffff",
};
const btnTextStyle = {
  fontSize: "32rpx",
};
</script>
<style lang="scss" scoped>
.content {
  height: 80vh;
}
.btns {
  position: fixed;
  bottom: 40rpx;
  width: 100%;
  background: #fff;
  padding: 28rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  .btn {
    width: 282rpx;
    height: 72rpx;
    border: 2rpx solid #4378ff;
    text-align: center;
    color: #4378ff;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 32rpx;
    line-height: 72rpx;
    border-radius: 8rpx;
  }
  .primary_btn {
    background: rgba(67, 120, 255, 1);
    color: #fff;
  }
  .disabled_btn {
    color: #fff;
    border: 2rpx solid #e4e4e4;
    background: rgba(160, 159, 159, 0.8);
  }
}
.cancel_btn {
  margin: 0 auto;
  width: 520rpx;
  height: 72rpx;
  border-radius: 8rpx;
  text-align: center;
  box-sizing: border-box;
  line-height: 72rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #373737;
}
.no_cancel_btn {
  background: rgba(228, 228, 228, 1);
  color: #373737;
}
.confirm_del_btn {
  margin: 28rpx auto;
  background: rgba(67, 120, 255, 1);
  color: #ffffff;
}
:deep(.uv-modal__title) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 36rpx;
  color: #373737;
  line-height: 50rpx;
}
</style>
