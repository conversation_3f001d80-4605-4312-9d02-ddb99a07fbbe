<template>
  <scroll-view scroll-y="true" class="content">
    <ylg-auto-form
      ref="autoFormRef"
      :isAllDisabled="false"
      :isFromDetail="false"
      btnLoading=""
      :formConfig="formConfig"
      :formData="localFormData"
      :rules="rules"
      :labelStyle="labelStyle"
      :formItemStyle="formItemStyle"
      :placeholderStyle="placeholderStyle"
      @onChange="onFormInpChange"
      @onDictChange="onFormDictChange"
    >
      <template #bottomBtns>
        <view class="btns flex-between-center">
          <view class="btn" @click="cancelCreate">取消</view>
          <view class="btn primary_btn" @click="onFormSubmit">下一步</view>
        </view>
      </template>
    </ylg-auto-form>
    <view class="black" style="height: 100rpx"></view>
    <uv-toast ref="toast"></uv-toast>
  </scroll-view>
</template>
<script setup>
import { ref, reactive, computed, watch, nextTick } from "vue";
import { onLoad, onShow, onBackPress } from "@dcloudio/uni-app";
import { cloneDeep as _cloneDeep } from "lodash";
import { DataAcquisitionService, ShmService } from "@/service";
import { showImg } from "@/utils";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();

const props = defineProps({
  formData: {
    type: Object,
    required: true,
    default: () => {},
  },
  isDeviceEdit: {
    type: Boolean,
    required: true,
    default: false,
  },
});

let stateData = ref({
  shmAsset: [], // 资产
});
// 请求所属资产、设备类型
const getYhBaseDictData = () => {
  let str = `shmAsset,deviceType`;
  DataAcquisitionService.getYhBaseDict({
    dictCodes: str,
  }).then((res) => {
    try {
      console.log("请求字典", res);
      let data = res.data;
      for (var m in data) {
        stateData.value[m] = data[m];
        if (m === "shmAsset") {
          // 根据资产类型请求具体的资产数据列表
          data[m].forEach((item) => {
            getPropertyChildren(item.dictKey);
          });
        }
      }
    } catch (error) {
      console.log("请求字典错误", error);
    }
  });
};
// 根据资产类型请求具体的资产数据列表
const getPropertyChildren = (pkey) => {
  ShmService.getPropertyByKey({ type: pkey })
    .then((res) => {
      console.log("查询资产子级", res);
      if (res.code == 200 && res.data.length) {
        stateData.value.shmAsset.find((item) => {
          if (item.dictKey === pkey) {
            item.children = res.data.map((citem) => {
              citem.dictValue = citem.objectName;
              citem.dictKey = citem.objectId;
              return citem;
            });
            return;
          }
        });
      } else {
        stateData.value.shmAsset.find((item) => {
          if (item.dictKey === pkey) {
            item.children = [];
            return;
          }
        });
      }
    })
    .catch(() => {
      stateData.value.shmAsset.find((item) => {
        if (item.dictKey === pkey) {
          item.children = [];
          return;
        }
      });
    });
};

onLoad(() => {
  // 1清空pinia中缓存的之前选择的数据项
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: "",
      value: "",
      label: "",
    },
  });
  // 获取字典数据
  getYhBaseDictData();
});

// 表单数据配置
let localFormData = ref({ ..._cloneDeep(props.formData) });
console.log("设备数据1", localFormData.value);


const rules = computed(() => {
  return {
    deviceName: {
      type: "string",
      required: true,
      message: "请输入设备名称",
      trigger: ["blur"],
    },
    assetIdLabel: {
      type: "array",
      required: true,
      message: "请选择资产",
      trigger: ["blur", "change"],
    },
    deviceTypeLabel: {
      type: "string",
      required: true,
      message: "请选择设备类型",
      trigger: ["blur", "change"],
    },
    installLocation: {
      type: "string",
      required: true,
      message: "请输入安装位置",
      trigger: ["blur"],
    },
    installDate: {
      type: "string",
      required: true,
      message: "请选择安装日期",
      trigger: ["blur", "change"],
    },
  };
});
const formConfig = computed(() => {
  return [
    {
      items: [
        {
          type: "input",
          modelType: "model",
          label: "设备名称",
          placeholder: "请输入（最多30字）",
          maxlen: 30,
          unionKey: "deviceName",
          disabled: props.isDeviceEdit
        },
        {
          type: "input",
          modelType: "model",
          label: "设备编码",
          placeholder: "请输入（非必填，最多30字）",
          maxlen: 30,
          unionKey: "deviceCode",
          disabled: props.isDeviceEdit
        },
        {
          type: "treeSelect",
          label: "所属资产",
          placeholder: "请选择",
          map: { text: "dictValue", value: "dictKey" },
          unionKey: "assetIdLabel",
          options: stateData.value.shmAsset || [],
          hasOptions: stateData.value.shmAsset.some(item=>item.children?.length),
          toastMsg:'当前账号无资产~',
          disabled: props.isDeviceEdit
        },
        {
          type: "select",
          label: "设备类型",
          modelType: 'model',
          placeholder: "请选择",
          unionKey: "deviceTypeLabel",
          options: stateData.value.deviceType || [],
          disabled: props.isDeviceEdit
        },
        {
          type: "input",
          modelType: 'model',
          label: "安装位置",
          placeholder: "请输入（最多50字）",
          maxlen: 50,
          unionKey: "installLocation",
        },
        {
          type: "datePicker",
          modelType: "model",
          noMaxLimit: true,
          label: "安装日期",
          placeholder: "请选择",
          unionKey: "installDate",
        },
        {
          type: "upload",
          label: "设备照片",
          // fileList: ref([showImg(localFormData.value.picture)]),
          fileList: ref(localFormData.value.picture),
          maxCount: 1,
          unionKey: "picture",
        },
      ],
    },
  ];
});

const emits = defineEmits(["onChangeProperty", "toNext", "onCancel"]);

const setNestedValue = (obj, path, value) => {
  const lastKey = path.pop();
  const nestedObj = path.reduce((acc, key) => acc[key] || (acc[key] = {}), obj);
  nestedObj[lastKey] = value;
};
// formItem input 数据修改
const onFormInpChange = ({ val, unionKey }) => {
  // console.log("formItem数据修改回调", unionKey, val);
  setNestedValue(localFormData.value, unionKey.split("."), val);
};

// formItem dict 字典数据修改
const onFormDictChange = ({ val, unionKey }) => {
  // 选择资产
  if (unionKey === "assetIdLabel") {
    console.log("查看资产", val, stateData.value.shmAsset);
    if (val.length) {
      // 根据资产类型请求具体的资产数据列表
      // getPropertyChildren(val[0]);
      getYhBaseDictData()

      emits("onChangeProperty", val[1]);
    }
  }
};
// formItem 从选择页面回调的 select 数据修改
let autoFormRef = ref(null);
const onFormSelChange = () => {
  console.log(
    "dataAcquisitionInfo.stateData.key",
    dataAcquisitionInfo.stateData.key
  );

  // 1如果选择的是 “所属路线”，则需要清空表单中选择的桩号信息
  // ...

  // 2处理select选择的label
  setNestedValue(
    localFormData.value,
    dataAcquisitionInfo.stateData.key.split("."),
    dataAcquisitionInfo.stateData.label
  );
  // 3处理select选择的value
  let str = dataAcquisitionInfo.stateData.key.replace(/Label$/, "");
  setNestedValue(
    localFormData.value,
    str.split("."),
    dataAcquisitionInfo.stateData.value
  );
  // console.log("选择更改", localFormData.value);
  if (autoFormRef.value?.formRef) {
    autoFormRef.value.formRef.validateField(dataAcquisitionInfo.stateData.key);
  }
};

// 创建设备下一步
const onFormSubmit = ({ data, status: action }) => {
  console.log("下一步", data, action);
  autoFormRef.value.formRef
    .validate()
    .then((res) => {
      emits("toNext", { data: localFormData.value });
    })
    .catch((errors) => {
      console.log("校验失败", errors);
    });
};
const cancelCreate = () => {
  emits("onCancel");
};

onShow(() => {
  // 处理从别的页面中选择之后的数据显示，如所属路线选择
  onFormSelChange();
});

defineExpose({
  stateData,
  localFormData,
  autoFormRef
});

// form表单样式配置
const labelStyle = reactive({
  width: "208rpx",
  fontWeight: 400,
  fontSize: "28rpx",
  color: "#404040",
  // lineHeight: "33rpx",
  lineHeight: "48rpx",
});
const formItemStyle = reactive({
  height: "88rpx",
  background: "#FFF",
  boxSizing: "border-box",
  padding: "24rpx 40rpx",
  borderBottom: "none",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  overflow: "hidden",
});
const placeholderStyle = ref("color: #C1C1C1");
</script>
<style lang="scss" scoped>
.content {
  background: #fff;
  height: calc(100% - 208rpx);
  margin-top: 0rpx;
  .info_card {
    background: #fff;
    margin-bottom: 48rpx;
    .info_title {
      height: 88rpx;
      padding: 22rpx 40rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #8e8e8e;
      line-height: 44rpx;
      box-sizing: border-box;
      border-bottom: 2rpx solid #f0f0f0;
    }
  }
  .btns {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
    padding: 28rpx 0 40rpx 0;
    box-sizing: border-box;
    .btn {
      width: 282rpx;
      height: 72rpx;
      margin: 0 40rpx;
      border: 2rpx solid #4378ff;
      text-align: center;
      color: #4378ff;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      line-height: 72rpx;
      border-radius: 8rpx;
    }
    .primary_btn {
      background: rgba(67, 120, 255, 1);
      color: #fff;
    }
    .disabled_btn {
      color: #fff;
      border: 2rpx solid #e4e4e4;
      background: rgba(160, 159, 159, 0.8);
    }
  }
}
</style>
