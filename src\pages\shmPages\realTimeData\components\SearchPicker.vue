<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-04-11 14:24:08
 * @Description: 
-->
<template>
  <view>
    <uv-popup
      ref="popup"
      mode="bottom"
      round="24"
      @change="change"
      @maskClick="maskClick"
    >
      <view class="content">
        <!-- 顶部tab选项卡 -->
        <uv-tabs
          :current="curTab"
          :list="tabList"
          lineWidth="30"
          lineColor="#4378FF"
          :activeStyle="{
            color: '#303133',
            fontWeight: 'bold',
            transform: 'scale(1.05)',
            paddingBottom: '10px',
          }"
          :inactiveStyle="{
            color: '#606266',
            transform: 'scale(1)',
            paddingBottom: '10px',
          }"
          itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
          @change="onChangeTab"
        ></uv-tabs>
        <!-- 监测类别 -->
        <view class="monitor_type_box" v-show="curTab == 0">
          <template v-if="monitorTypeList.length">
            <view class="left_menu">
              <view
                :class="[
                  'left_menu_item',
                  curMonitorMenu.value === monitorItem.value
                    ? 'active_menu_item'
                    : '',
                ]"
                v-for="monitorItem in monitorTypeList"
                :key="monitorItem.value"
                @click="chooseMonitorTypeMenu(monitorItem)"
                >{{ monitorItem.label }}</view
              >
            </view>
            <view class="right_child">
              <view
                :class="[
                  'right_child_item',
                  curChooseTypeVal === childItem.value
                    ? 'active_right_child_item'
                    : '',
                ]"
                v-for="childItem in curMonitorMenu.children"
                :key="childItem.value"
                @click="
                  chooseMonitorType(curMonitorMenu.value, childItem.value)
                "
                >{{ childItem.label }}</view
              >
            </view>
          </template>
          <view class="nodata_box" v-else>
            <ylg-nodata
              imgUrl="/static/shmStatic/image/list_nodata_20250324.png"
              noticeTip="暂无数据~"
              textSize="32rpx"
            ></ylg-nodata>
          </view>
        </view>
        <!-- 监测位置 -->
        <view class="monitor_location_box" v-show="curTab == 1">
          <template v-if="monitorLocationList.length">
            <view class="switch_btn" @click="onSwitchView">
              <image
                class="switch_btn_icon"
                src="/static/shmStatic/icon/switch_icon_20250319.png"
              ></image>
              <text class="switch_btn_text">切换视图</text>
            </view>
            <view class="location_box">
              <view
                :class="[
                  view2Column ? 'location_item' : 'location_item_bigger',
                  curChooseLocationId === locationItem.id
                    ? 'active_location_item'
                    : '',
                ]"
                v-for="locationItem in monitorLocationList"
                :key="locationItem.id"
                @click="chooseLocation(locationItem.id)"
              >
                <image
                  :class="[
                    view2Column
                      ? 'location_item_img'
                      : 'location_item_bigger_img',
                  ]"
                  :src="showImg(locationItem.layoutPath)"
                ></image>
                <view class="location_item_name" ref="textWrapperRef">
                  <view
                    v-if="locationItem.locationNameLine1"
                    class="location_item_name_line1"
                    :style="{
                      lineHeight: !locationItem?.locationNameLine2
                        ? '80rpx'
                        : '',
                    }"
                    >{{ locationItem.locationNameLine1 }}
                  </view>
                  <view
                    v-if="locationItem?.locationNameLine2"
                    class="location_item_name_line2"
                    >{{ locationItem.locationNameLine2 }}
                  </view>
                </view>
              </view>
            </view>
          </template>
          <view class="nodata_box" v-else>
            <ylg-nodata
              imgUrl="/static/shmStatic/image/list_nodata_20250324.png"
              noticeTip="暂无数据~"
              textSize="32rpx"
            ></ylg-nodata>
          </view>
        </view>
      </view>
      <view class="btn_box">
        <view class="reset_btn btn" @click="reset">清空</view>
        <view
          class="comfirm_btn btn"
          v-if="curChooseTypeVal || curChooseLocationId"
          @click="comfirm"
          >确定</view
        >
        <view class="comfirm_btn_dis btn" v-else>确定</view>
      </view>
    </uv-popup>
  </view>
</template>
<script setup>
import { getCurrentInstance, onMounted, ref } from "vue";
import { cloneDeep as _cloneDeep } from "lodash";
import { ShmService } from "@/service";
import { showImg } from "@/utils";
import { onLoad } from "@dcloudio/uni-app";
const props = defineProps({
  assetId: {
    type: String,
    required: true,
    default: "",
  },
});

// tab标题列表
const tabList = ref([{ name: "监测类别" }, { name: "监测位置" }]);
let curTab = ref(0);
const onChangeTab = (e) => {
  console.log("切换tab", e);
  curTab.value = e.index;
};

// 监测类别相关
const monitorTypeList = ref([]);
let curMonitorMenu = ref({});
const getMonitorTypeList = async () => {
  try {
    let params = {
      // assetId: "1867091080912158721",
      assetId: props.assetId,
    };
    let { code, data } = await ShmService.monitorTypeList(params);
    if (code == 200 && data.length) {
      monitorTypeList.value = data;
      curMonitorMenu.value = data[0];
    } else {
      monitorTypeList.value = [];
    }
  } catch (error) {
    console.log("监测类别error", error);
  }
};
// 选择监测类别左侧菜单
const chooseMonitorTypeMenu = (item) => {
  curMonitorMenu.value = item;
};
// 选择监测类别右侧具体类别
let curChooseTypeVal = ref("");
let curChooseTypeValArr = ref([]);
const chooseMonitorType = (menuVal, typeVal) => {
  curChooseTypeVal.value = typeVal;
  curChooseTypeValArr.value = [];
  curChooseTypeValArr.value.push(menuVal);
  curChooseTypeValArr.value.push(typeVal);
  curChooseLocationId.value = ""; // 清空监测位置
};

// 监测位置相关
// 请求监测位置列表
let monitorLocationList = ref([]);
const getMonitorLocationList = async () => {
  try {
    let params = {
      // assetId: "1867091080912158721",
      assetId: props.assetId,
      page: -1,
      limit: -1,
      search: true,
    };
    let { code, data } = await ShmService.monitoringLocationPage(params);
    if (code == 200 && data.length) {
      // 处理监测位置名称断行
      for (let i = 0; i < data.length; i++) {
        // if (i == 0)
        //   data[i].locationName = data[i].locationName + "好的jsd嗯wd哈qw534rfd";
        setTimeout(() => {
          initSplitText(data[i]);
        }, 500);
      }
      monitorLocationList.value = data;
      view2Column.value = monitorLocationList.value.length > 2;
      console.log("查看监测位置", monitorLocationList.value);
    } else {
      monitorLocationList.value = [];
    }
  } catch (error) {
    console.log("监测位置 error", error);

    monitorLocationList.value = [];
  }
};
const textWrapperRef = ref(null);
// 处理监测位置名称换行
const initSplitText = async (item) => {
  // 1. 获取容器宽度
  const containerWidth = 143;
  // 2. 创建离屏Canvas测量文字宽度
  const fontSize = 13; // 第一行字体大小(px)
  const splitIndex = await findSplitIndex(
    containerWidth,
    fontSize,
    item.locationName
  );

  // 3. 分割文本
  // console.log("查看分割索引0", splitIndex, item.locationName);
  if (splitIndex === 0 || !view2Column.value) {
    item.locationNameLine1 = item.locationName;
    item.locationNameLine2 = "";
  } else {
    item.locationNameLine1 = item.locationName.slice(0, splitIndex);
    item.locationNameLine2 = item.locationName.slice(splitIndex);
  }
  item.line1Str =
    splitIndex === 0
      ? item.locationName
      : item.locationName.slice(0, splitIndex);
  item.line2Str = splitIndex === 0 ? "" : item.locationName.slice(splitIndex);
  console.log("查看分割索引011", item.line1Str);
  console.log("查看分割索引012", item.locationNameLine1);
  console.log("查看分割索引021", item.line2Str);
  console.log("查看分割索引022", item.locationNameLine2);
};
// **计算分割位置**
const findSplitIndex = async (containerWidthPx, fontSizePx, originalText) => {
  try {
    const ctx = uni.createCanvasContext("textMeasureCanvas");
    ctx.setFontSize(fontSizePx);
    let currentWidth = 0;
    let splitIndex = 0;

    for (let i = 0; i < originalText.length; i++) {
      const char = originalText[i];
      const metrics = ctx.measureText(char);
      currentWidth += metrics.width;
      if (currentWidth > containerWidthPx) {
        console.log("findSplitIndex >", currentWidth, char, metrics, i);
        splitIndex = i;
        break;
      }
    }
    return splitIndex;
  } catch (error) {
    console.log("findSplitIndex error", error);
  }
};
// 选择监测位置
let curChooseLocationId = ref("");
const chooseLocation = (curId) => {
  curChooseLocationId.value = curId;
  curChooseTypeVal.value = ""; // 清空监测类别
  curChooseTypeValArr.value = [];
};
// 切换视图
let view2Column = ref(monitorLocationList.value.length > 2);
// let view2Column = ref(true);
const onSwitchView = () => {
  view2Column.value = !view2Column.value;
  monitorLocationList.value.forEach((item) => {
    if (view2Column.value) {
      item.locationNameLine1 = item.line1Str;
      item.locationNameLine2 = item.line2Str;
    } else {
      item.locationNameLine1 = item.line1Str + (item.line2Str || "");
      item.locationNameLine2 = "";
    }
  });
};

const handleData = (data) => {
  let myData = [..._cloneDeep(data)];
  myData.forEach((item, index, arr) => {
    item.locationName = item.locationName + "是dferf哈哈哈";
    let lin1Str = item.locationName.slice(0, 13);
    let matches = lin1Str.match(/[a-zA-Z0-9]/g);
    if (matches && matches.length >= 12) {
      arr[index].locationNameLine1 = item.locationName.slice(0, 22);
      arr[index].locationNameLine2 = item.locationName.slice(22);
    } else if (matches && matches.length > 10) {
      // 如果监测位置名称的前13位字符中存在10个以上的数字或字母，则每行19个字符
      arr[index].locationNameLine1 = item.locationName.slice(0, 19);
      arr[index].locationNameLine2 = item.locationName.slice(19);
    } else if (matches && matches.length >= 5) {
      // 如果监测位置名称的前13位字符中存在6个以上的数字或字母，则每行16个字符
      arr[index].locationNameLine1 = item.locationName.slice(0, 13);
      arr[index].locationNameLine2 = item.locationName.slice(13);
    } else if (matches && matches.length < 3) {
      // 如果监测位置名称的前11位字符中存在3个以下的数字或字母，则每行11个字符
      arr[index].locationNameLine1 = item.locationName.slice(0, 11);
      arr[index].locationNameLine2 = item.locationName.slice(11);
    } else {
      arr[index].locationNameLine1 = item.locationName.slice(0, 12);
      arr[index].locationNameLine2 = item.locationName.slice(12);
    }
  });
  console.log("查看处理过的数据", myData);

  return myData;
};
const popup = ref(null);
const open = () => {
  console.log("触发子组件的open", popup);
  popup.value.open();
};
const close = () => {
  console.log("触发子组件的close", popup);
  popup.value.close();
};
const emit = defineEmits(["confirmTabs", "tabPickerCallback", "onMaskClick"]);
const change = (e) => {
  console.log("弹窗状态改变：", e);
  if (!e.show) {
    emit("tabPickerCallback", { type: "cancelTabs" });
  }
};
// 确认
const comfirm = () => {
  emit("tabPickerCallback", {
    type: "confirmTabs",
    data: curChooseLocationId.value || curChooseTypeValArr.value,
  });
};
// 清空
const reset = () => {
  if (!curChooseTypeVal.value && !curChooseLocationId.value) return;
  curChooseTypeVal.value = "";
  curChooseTypeValArr.value = [];
  curChooseLocationId.value = "";
  emit("tabPickerCallback", { type: "reset" });
};

const maskClick = () => {
  emit("onMaskClick");
};

onLoad(() => {
  getMonitorTypeList();
  getMonitorLocationList();
});

defineExpose({
  open,
  close,
  reset,
});
</script>
<style lang="scss" scoped>
.content {
  padding: 40rpx 0 10rpx 0;
  // margin-bottom: 100rpx;
}
.monitor_type_box {
  display: flex;
  align-items: flex-start;
  border-top: 2rpx solid #f2f2f2;
  // height: 948rpx;
  height: 928rpx;
  margin-bottom: 20rpx;
  overflow: auto;
  .left_menu {
    &_item {
      width: 174rpx;
      height: 92rpx;
      background: rgba(242, 242, 242, 1);
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #606060;
      line-height: 92rpx;
      text-align: center;
    }
    .active_menu_item {
      background: rgba(255, 255, 255, 1);
      color: #4378ff;
    }
  }
  .right_child {
    padding: 24rpx 0 0 40rpx;
    display: flex;
    flex-wrap: wrap;
    &_item {
      height: 56rpx;
      margin: 0 28rpx 28rpx 0;
      white-space: nowrap;
      padding: 8rpx 20rpx;
      box-sizing: border-box;
      background: rgba(236, 236, 236, 1);
      border-radius: 8rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #969696;
    }
    .active_right_child_item {
      background: rgba(67, 120, 255, 1);
      color: #ffffff;
    }
  }
  .nodata_box {
    margin: 60rpx auto;
  }
}
.monitor_location_box {
  padding: 20rpx 0 0 32rpx;
  border-top: 2rpx solid #f2f2f2;
  height: 948rpx;
  box-sizing: border-box;
  .switch_btn {
    width: 170rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12rpx 16rpx;
    box-sizing: border-box;
    border: 2rpx solid #4378ff;
    border-radius: 8rpx;
    &_icon {
      width: 28rpx;
      height: 24rpx;
    }
    &_text {
      color: #4378ff;
      line-height: 30rpx;
      font-size: 24rpx;
    }
  }
  .location_box {
    margin-top: 28rpx;
    display: flex;
    flex-wrap: wrap;
    height: 816rpx;
    overflow: auto;
    align-content: flex-start;
    .location_item {
      position: relative;
      width: 328rpx;
      height: 256rpx;
      margin: 0 30rpx 28rpx 0;
      border: 2rpx solid #d9d9d9;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      &_img {
        display: block;
        width: 328rpx;
        height: 256rpx;
        border-radius: 16rpx;
      }
      &_name {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 80rpx;
        background: rgba(64, 64, 64, 0.5);
        border-radius: 0 0 16rpx 16rpx;
        padding: 6rpx 20rpx;
        box-sizing: border-box;
        white-space: normal; /* 允许换行 */
        word-break: break-word; /* 自动换行 */
        &_line1 {
          font-size: 26rpx;
          color: #ffffff;
        }
        &_line2 {
          font-size: 22rpx;
          color: #ffffff;
          line-height: 32rpx;
          // color: red;
        }
      }
    }
    .location_item:nth-child(2n) {
      margin-right: 0;
    }
    .location_item_bigger {
      position: relative;
      width: 686rpx;
      height: 372rpx;
      margin: 0 0 28rpx 0;
      border: 2rpx solid #d9d9d9;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      &_img {
        display: block;
        width: 686rpx;
        height: 372rpx;
        border-radius: 16rpx;
      }
      &_name {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 60rpx;
        background: rgba(64, 64, 64, 0.5);
        border-radius: 0 0 16rpx 16rpx;
        padding: 12rpx 20rpx;
        box-sizing: border-box;
        &_line1 {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #ffffff;
          line-height: 36rpx;
        }
      }
    }
    .active_location_item {
      border: 2rpx solid #4378ff;
    }
  }
  .nodata_box {
    margin: 40rpx auto;
    margin-right: 32rpx;
  }
}
.btn_box {
  padding: 24rpx 40rpx 40rpx 40rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
  .btn {
    width: 208rpx;
    height: 72rpx;
    text-align: center;
    box-sizing: border-box;
    text-align: center;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    line-height: 72rpx;
    font-size: 32rpx;
  }
  .reset_btn {
    background: #ffffff;
    border: 2rpx solid #4378ff;
    color: #4378ff;
  }
  .comfirm_btn {
    width: 416rpx;
    background: #4378ff;
    color: #fff;
  }
  .comfirm_btn_dis {
    width: 416rpx;
    color: #fff;
    // border: 2rpx solid #e4e4e4;
    background: rgba(160, 159, 159, 0.8);
  }
}
</style>
