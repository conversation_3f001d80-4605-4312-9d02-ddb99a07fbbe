<template>
  <view class="alarmr-rules-item-wrap">
    <view class="alarmr-rules-item card-box">
      <view class="alarmLevelName" :class="'alarmLevel' + data.alarmLevel">{{
        data.alarmLevelName
      }}</view>
      <view class="item-title flex-start-center">
        <view class="tag" :class="'tag' + data.eventStatus">{{
          data.eventStatusName
        }}</view>
        <view class="time">{{ data.alarmTime }}</view>
      </view>
      <swiper
        class="swiper"
        :autoplay="true"
        :circular="true"
        :interval="5000"
        @change="swiperChange"
      >
        <swiper-item
          v-for="(temp, n) in data.pointVOList"
          :key="n"
          class="item-box flex-between-center"
          :class="swiperIndex === 1 ? 'activeLast' : ''"
        >
          <view v-for="(point, p) in temp" :key="p" class="point-item">
            <view class="point-code">{{ point.pointCode }}</view>
            <view class="point-txt">
              <text class="monitor-item">{{ point.monitorItem || '-' }}</text>
              <text
                class="monitor-value"
                :class="'monitor-value' + data.alarmLevel"
                >{{ point.overLimitValue || '-' }}</text
              >
              <text class="monitor-unit">{{ point.unit || '-' }}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
const swiperIndex = ref(0)
const swiperChange = (e) => {
  swiperIndex.value = e.detail.current
}
</script>

<style lang="scss" scoped>
.alarmr-rules-item-wrap {
  padding: 0 40rpx;
}
.alarmr-rules-item {
  box-shadow: 4rpx 4rpx 20rpx 0 rgba(0, 0, 0, 0.08);
  padding: 32rpx 0;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 28rpx;
  .alarmLevelName {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 26rpx;
    width: 136rpx;
    height: 52rpx;
    line-height: 52rpx;
    text-align: center;
    &.alarmLevel1 {
      color: #4688e5;
      background: url('../../../../static/shmStatic/icon/alarmLevel1.png')
        center center no-repeat;
      background-size: 100%;
    }
    &.alarmLevel2 {
      color: #e3893b;
      background: url('../../../../static/shmStatic/icon/alarmLevel2.png')
        center center no-repeat;
      background-size: 100%;
    }
    &.alarmLevel3 {
      color: #ed5354;
      background: url('../../../../static/shmStatic/icon/alarmLevel3.png')
        center center no-repeat;
      background-size: 100%;
    }
  }
  .item-title {
    margin-bottom: 40rpx;
    padding: 0 28rpx;
    .tag {
      padding: 0 14rpx;
      border-radius: 8rpx;
      height: 44rpx;
      line-height: 44rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      text-align: center;
      margin-right: 18rpx;
      &.tag1 {
        background-color: #fa6666;
      }
      &.tag2 {
        background-color: #b0b0b0;
      }
      &.tag3 {
        background-color: #75dc7f;
      }
      &.tag4 {
        background-color: #f19a65;
      }
      &.tag5 {
        background-color: #5fd7a9;
      }
      &.tag6 {
        background-color: #5282F3;
      }
      &.tag7 {
        background-color: #75DC7F;
      }
    }
    .time {
      width: 442rpx;
      height: 44rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #373737;
      line-height: 44rpx;
    }
  }
  .swiper {
    max-height: 150rpx !important;
    padding: 0 24rpx;
    .item-box {
      align-items: stretch; /* 关键，让子元素等高 */
      width: 100%;
      padding: 0 4rpx;
      box-sizing: border-box;
      overflow: inherit !important;
      &:nth-child(2) {
        right: -40rpx !important;
      }
      &.activeLast {
        &:nth-child(2) {
          right: 0rpx !important;
        }
      }
      .point-item {
        width: 48%;
        background: #ffffff;
        box-shadow: 0 0 10rpx 0rpx rgba(0, 0, 0, 0.1);
        border-radius: 8rpx;
        border: 2rpx solid rgba(0, 0, 0, 0.1);
        padding: 12rpx 8rpx;
        box-sizing: border-box;
        &:nth-child(2n-1) {
          margin-right: 28rpx;
        }
        .point-code {
          text-align: center;
          font-size: 24rpx;
          color: #404040;
          // height: 36rpx;
          line-height: 36rpx;
          margin-bottom: 6rpx;
        }
        .point-txt {
          text-align: center;
          .monitor-item {
            color: #a09f9f;
            font-size: 28rpx;
            margin-right: 10rpx;
          }
          .monitor-value {
            font-size: 36rpx;
            margin-right: 4rpx;
            &.monitor-value1 {
              color: #4688e5;
            }
            &.monitor-value2 {
              color: #e3893b;
            }
            &.monitor-value3 {
              color: #ed5354;
            }
          }
          .monitor-unit {
            font-size: 24rpx;
            color: #a09f9f;
          }
        }
      }
    }
  }
}
</style>
