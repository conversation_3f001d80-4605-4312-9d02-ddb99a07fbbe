<!--
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2025-01-16 10:05:37
 * @Description: 
-->
<template>
  <view class="container">
    <uv-search
      height="36"
      shape="square"
      searchIcon="../../../static/icon/search_icon.png"
      searchIconSize="16"
      placeholder="输入事件对象名称搜索"
      placeholderColor="#C1C1C1;"
      bgColor="#fff"
      :showAction="false"
      :boxStyle="searchStyle"
      v-model="inpVal"
      @change="inpChange"
      @search="inpSearch"
    ></uv-search>
    <!-- 列表 -->
    <template v-if="!pageLoading">
      <view class="list_box" v-if="dataList.length">
        <view
          :class="[
            'card',
            curItem.objectId === item.objectId ? 'active_card' : '',
          ]"
          v-for="item in dataList"
          :key="item.objectId"
          @click="chooseItem(item)"
        >
          <view class="item_title">{{ item.objectName }}</view>
          <!-- <view class="item_info">{{ item.startStakeName }}-{{ item.endStakeName }}</view> -->
        </view>
      </view>
      <no-data class="nodata" v-else></no-data>
    </template>
    <uv-loading-page
      :loading="pageLoading"
      loading-text="加载中..."
      font-size="24rpx"
    ></uv-loading-page>
    <view class="bottom_box">
      <view class="choosed_data_show" v-if="curItem.objectName"
        >已选 {{ curItem.objectName }}</view
      >
      <view class="btn" @click="confirm">确 定</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { onLoad, onReachBottom } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { useDataAcquisitionStore } from "@/store/dataAcquisition";
const dataAcquisitionInfo = useDataAcquisitionStore();
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { RoadInspectionService } from "@/service";

let unionKey = ref("");
// 关联的事件对象类型
let eventObjectType = ref("");
onLoad(async (options) => {
  console.log("options", options);
  eventObjectType.value = options?.eventObjectType;
  unionKey.value = options?.unionKey;
  // 获取事件对象名称列表
  await getList();
  await handleCurChoosed(options.curItemId);
});

// 处理回显当前已经选择的事件对象名称
const handleCurChoosed = (curItemId) => {
  dataList.value.forEach((item) => {
    // if (item.objectName === curItemName) {
    //   curItem.value = item;
    // }
    if (item.objectId === curItemId) {
      curItem.value = item;
    }
  });
};

// 搜索
const searchStyle = reactive({
  borderRadius: "8px",
  marginBottom: "28rpx",
});

let inpVal = ref("");
const inpChange = (val) => {
  inpVal.value = val;
  dataList.value = [];
  filterList();
};
const inpSearch = (val) => {
  inpVal.value = val;
  dataList.value = [];
  filterList();
};

const filterList = () => {
  dataList.value = allDataList.value.filter(
    (item) => item.objectName.indexOf(inpVal.value) > -1
  );
};

// 获取路段列表
let dataList = ref([]);
let allDataList = ref([]);
let pageInfo = reactive({
  // page: 1,
  // limit: 20,
  page: -1,
  limit: -1,
});
let pageLoading = ref(false);
const getList = async () => {
  pageLoading.value = true;
  let codeRes = ref("");
  let dataRes = ref([]);
  try {
    let inspectObject = uni.getStorageSync("inspectObject");
    let sectionId = uni.getStorageSync("sectionId");
    // inspectObject 事件对象类型 1：全路段；2：其他
    if (inspectObject && inspectObject == "1") {
      let params1 = {
        objectType: eventObjectType.value,
        sectionId,
        page: pageInfo.page,
        limit: pageInfo.limit,
      };
      let res = await RoadInspectionService.getEventObjName1(params1);
      codeRes.value = res.code;
      dataRes.value = res.data;
    } else if (inspectObject && inspectObject == "2") {
      let inspectPlanId = uni.getStorageSync("inspectPlanId");
      let params2 = {
        objectType: eventObjectType.value,
        inspectPlanId,
      };
      let res = await RoadInspectionService.getEventObjName2(params2);
      codeRes.value = res.code;
      dataRes.value = res.data;
    }
    pageLoading.value = false;
    if (codeRes.value == 200) {
      allDataList.value = dataRes.value;
      dataList.value = dataRes.value;
    } else {
      allDataList.value = [];
      dataList.value = [];
    }
  } catch (error) {
    console.log("请求失败，", error);
    pageLoading.value = false;
  }
};

// 选择项目
let curItem = ref({});
const chooseItem = (item) => {
  curItem.value = item;
};

// 提交
const confirm = () => {
  dataAcquisitionInfo.updateDataAcquisition({
    stateData: {
      key: unionKey.value,
      value: curItem.value.objectId,
      label: curItem.value.objectName,
    },
  });
  uni.navigateBack({
    data: 1,
  });
};
</script>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 20rpx 40rpx;
  background: #f4f8ff;
}
.list_box {
  margin: 12rpx 0 200rpx 0;
  .card {
    margin-bottom: 28rpx;
    padding: 22rpx 28rpx;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 16rpx;
    font-family: PingFang SC, PingFang SC;
    .item_title {
      font-weight: 400;
      color: #373737;
      font-size: 32rpx;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .item_info {
      font-size: 28rpx;
      color: #a09f9f;
      line-height: 44rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .active_card {
    background: #4378ff;
    .item_title {
      color: #ffffff;
    }
    .item_info {
      color: #ffffff;
    }
  }
}
.nodata {
  margin-top: 136rpx;
}
.bottom_box {
  width: calc(100% - 80rpx);
  position: fixed;
  bottom: 0rpx;
  padding: 20rpx 0;
  background: #f4f8ff;
  .choosed_data_show {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #4378ff;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .btn {
    width: 670rpx;
    height: 84rpx;
    background: #4378ff;
    border-radius: 8rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 84rpx;
    text-align: center;
  }
}
</style>
