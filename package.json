{"name": "uni-preset-vue", "version": "0.3.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3090820231124001", "@dcloudio/uni-app-plus": "3.0.0-3090820231124001", "@dcloudio/uni-components": "3.0.0-3090820231124001", "@dcloudio/uni-h5": "3.0.0-3090820231124001", "@dcloudio/uni-mp-alipay": "3.0.0-3090820231124001", "@dcloudio/uni-mp-baidu": "3.0.0-3090820231124001", "@dcloudio/uni-mp-jd": "3.0.0-3090820231124001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3090820231124001", "@dcloudio/uni-mp-lark": "3.0.0-3090820231124001", "@dcloudio/uni-mp-qq": "3.0.0-3090820231124001", "@dcloudio/uni-mp-toutiao": "3.0.0-3090820231124001", "@dcloudio/uni-mp-weixin": "3.0.0-3090820231124001", "@dcloudio/uni-mp-xhs": "3.0.0-3090820231124001", "@dcloudio/uni-quickapp-webview": "3.0.0-3090820231124001", "compressorjs": "^1.2.1", "coordtransform": "^2.1.2", "dayjs": "^1.11.12", "decimal.js": "^10.6.0", "dom-to-image": "^2.6.0", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "jsencrypt": "^3.3.2", "pinia": "2.0.14", "pinia-plugin-persist-uni": "^1.1.3", "uniapp-image-compress": "^1.0.5", "vue": "^3.3.0", "vue-demi": "^0.14.8", "vue-i18n": "^9.1.9"}, "devDependencies": {"@dcloudio/types": "3.4.3", "@dcloudio/uni-automator": "3.0.0-3090820231124001", "@dcloudio/uni-cli-shared": "3.0.0-3090820231124001", "@dcloudio/uni-stacktracey": "3.0.0-3090820231124001", "@dcloudio/vite-plugin-uni": "3.0.0-3090820231124001", "@vue/runtime-core": "3.3.9", "prettier": "^3.5.3", "vite": "4.0.3"}}