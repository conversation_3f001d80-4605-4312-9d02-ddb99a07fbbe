/*
 * @Author: chenhl
 * @LastEditors: chenhl
 * @LastEditTime: 2024-08-05 09:40:31
 * @Description: 
 */
import { defineStore } from 'pinia';

export const useProjectStore = defineStore({
  id: 'project',
  state: () => ({
    projectId: '',
    projectName: '',
    projectPermission: '',
  }),
  // 存储数据（同步|异步）
  actions: {
    updateProject(project) {
      Object.assign(this, { ...project });
    },
  },
  persist: {
    // 开启持久化
    enabled: true,
    H5Storage: window?.localStorage || ""
  },
});
