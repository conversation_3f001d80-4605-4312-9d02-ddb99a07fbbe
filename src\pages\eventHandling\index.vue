<!--
 * @Author: ---
 * @LastEditors: yjw <EMAIL>
 * @LastEditTime: 2025-07-14 15:04:01
 * @Description: 
-->
<template>
  <view class="container">
    <!-- 导航栏 -->
    <uv-navbar
      title="事件处置"
      leftIconColor="#fff"
      height="84rpx"
      titleStyle="font-weight: 500;font-size: 36rpx;color: #ffffff;"
      :autoBack="true"
      :bgColor="bgColor"
    >
      <template v-slot:right>
        <view class="uv-nav-slot" @click="toHistoryPage">事件处置记录</view>
      </template>
    </uv-navbar>
    <view class="top_bg"></view>
    <view class="content">
      <!-- 统计数据 -->
      <view class="data_statistics">
        <view
          class="data_item"
          v-for="(item, index) in dataStatistics"
          :key="index"
        >
          <view :class="['data_val', item.redColor ? 'red_val' : '']">{{
            item.val
          }}</view>
          <view class="data_title">{{ item.title }}</view>
        </view>
      </view>
      <!-- 待处置事件  -->
      <view class="recently_list">
        <view class="title_box">
          <view class="title"> 待处置事件 </view>
        </view>
        <SearchTabGroup ref="searchTabGroupRef" @queryList="queryList" />
        <view class="list_box" v-if="dataList?.length">
          <view
            class="card"
            v-for="item in dataList"
            :key="item.id"
            @click="toDetail(item)"
          >
            <view
              :class="['card_top_status', 'card_top_status' + item.eventStatus]"
            >
              {{ item.eventStatusName }}
            </view>
            <view class="card_top">
              <image
                class="card_top_icon"
                :src="geteventSourceIcon(item.eventSource)"
              />
              <view v-if="item.eventType === '1'" class="card_top_title">
                {{ item.diseasesTypeName }}（{{
                  (item.diseasesCount || "-") + (item.diseasesUnit || "-")
                }}）
              </view>
              <view v-if="item.eventType === '2'" class="card_top_title">
                {{ item.remark || "-" }}
              </view>
            </view>
            <view class="card_content">
              <view class="content_item">
                <view class="content_name">所属路段：</view>
                <view class="content_val">{{ item.sectionName || "-" }}</view>
              </view>
              <view class="content_item">
                <view class="content_name">事件位置：</view>
                <view class="content_val">
                  {{
                    (item.stakeName || "--") +
                    "/" +
                    (item.endStakeName || "--") +
                    "（" +
                    item.updownMarkName +
                    "） "
                  }}
                </view>
              </view>
            </view>
            <view class="card_bottom">
              <view class="log_date">
                <image class="date_icon" :src="eventHandling_date_icon" />
                <view class="date">{{ item.handleConsumeTime }}</view>
              </view>
              <image class="detail_icon" :src="eventHandling_detail_icon" />
            </view>
          </view>
        </view>
        <no-data class="nodata" v-else></no-data>
        <!-- 底部切换按钮 -->
        <ylg-my-btns
          :switchActive="switchActive"
          @myBtnCallback="myBtnCallback"
        ></ylg-my-btns>
      </view>
      <view class="black"></view>
    </view>
  </view>
</template>
<script setup>
import { getSysteminfo } from "@/utils";
import { computed, reactive, ref } from "vue";
import { onLoad, onPageScroll, onShow, onReachBottom } from "@dcloudio/uni-app";
import NoData from "@/components/ylg-nodata.vue";
import { eventHandlingApi } from "@/service";
import { useProjectStore } from "@/store/project";
const projectInfo = useProjectStore();
import { getCurLocation } from "@/utils/location";
import SearchTabGroup from "./components/SearchTabGroup.vue";
import eventHandling_report_person from "@/static/icon/eventHandling_report_person.png";
import eventHandling_report_car from "@/static/icon/eventHandling_report_car.png";
import eventHandling_report_machie from "@/static/icon/eventHandling_report_machie.png";
import eventHandling_report_active from "@/static/icon/eventHandling_report_active.png";
import eventHandling_date_icon from "@/static/icon/eventHandling_date_icon.png";
import eventHandling_detail_icon from "@/static/icon/eventHandling_detail_icon.png";

const globalBlue = ref("#4584FF");
// 自定义导航栏背景色
// 监听页面滚动，修改导航栏背景色
let bgColor = ref("transparent");
onPageScroll((e) => {
  if (e.scrollTop > 35) {
    bgColor.value = globalBlue.value;
  } else {
    bgColor.value = "transparent";
  }
});

onLoad(() => {
  // 定位热启动
  getCurLocation();
});
const searchTabGroupRef = ref();
onShow(() => {
  switchActive.value = false;
  getTopData();
  searchTabGroupRef.value.queryList();
});
const geteventSourceIcon = (eventSource) => {
  if (!eventSource) return "";
  const iconMap = {
    1: eventHandling_report_person,
    2: eventHandling_report_active,
    3: eventHandling_report_car,
    4: eventHandling_report_machie,
  };
  return iconMap[eventSource];
};
// 获取顶部的统计数据
const statisticData = reactive({});
const getTopData = async () => {
  try {
    let { code, data } = await eventHandlingApi.appStatistic({
      projectId: projectInfo.projectId,
    });
    if (code == 200) {
      statisticData.todayHandleCount = data.todayHandleCount;
      statisticData.historyHandleCount = data.historyHandleCount;
      statisticData.waitHandleCount = data.waitHandleCount;
    }
  } catch (error) {
    console.log(error);
  }
};
const noData = ref(false);
const pageInfo = reactive({
  page: 1,
  limit: 10,
});
const dataList = ref([]);
const queryParams = ref({
  eventType: "",
  eventSource: "",
  objectType: "",
  sectionId: "",
});
const getList = async () => {
  console.log("子组件查询参数", queryParams.value);
  console.log("eventType值", queryParams.value.eventType); // 直接访问
  const {
    eventType,
    eventSource = "",
    objectType = "",
    sectionId,
  } = queryParams.value;
  let params = {
    projectId: projectInfo.projectId,
    eventType, // 事件类型
    eventSource, // 事件来源
    eventStatus: "1", // 写死待处理
    sectionId, // 路段
    objectType, // 事件对象类型
    page: pageInfo.page,
    limit: pageInfo.limit,
  };
  console.log("params", params);
  const { code, data } = await eventHandlingApi.inspectEventPage(params);
  if (code !== 200) {
    pageInfo.page--;
    return;
  }
  if (pageInfo.page === 1) {
    dataList.value = data;
  } else {
    dataList.value = [...dataList.value, ...data];
  }
  noData.value = data.length < pageInfo.limit;
  console.log("当前数据总量", dataList.value.length);
};
/** 页面上拉 **/
onReachBottom(() => {
  if (noData.value) {
    uni.showToast({
      icon: "none",
      title: "没有更多了~",
    });
    return;
  }
  pageInfo.page++;
  getList();
});
const queryList = (params) => {
  pageInfo.page = 1;
  queryParams.value = { ...params };
  getList();
};
// 获取手机系统栏高度
const systemBarHeight = `${
  Number(getSysteminfo().systemBarHeight) * 2 + 18
}rpx`;
console.log("系统栏高度", systemBarHeight);

const dataStatistics = computed(() => {
  return [
    {
      val: statisticData.todayHandleCount,
      title: "今日处理",
    },
    {
      val: statisticData.historyHandleCount,
      title: "历史处理",
    },
    {
      val: statisticData.waitHandleCount,
      title: "待处理",
      redColor: true,
    },
  ];
});
const toHistoryPage = () => {
  uni.navigateTo({
    url: "/pages/eventHandling/historyHandling/index",
  });
};

const toDetail = (record) => {
  const { id, eventType } = record;
  switch (eventType) {
    case "1":
      uni.navigateTo({
        url: `/pages/eventHandling/diseaseHandling/index?id=${id}`,
      });
      break;
    case "2":
      uni.navigateTo({
        url: `/pages/eventHandling/otherHandling/index?id=${id}`,
      });
      break;
    default:
      break;
  }
};

let switchActive = ref(false);
const myBtnCallback = (envRes) => {
  switch (envRes.type) {
    case "toggoleSwitch":
      switchActive.value = !switchActive.value;
      break;
    case "toReportEvent":
      uni.navigateTo({
        url: `/pages/roadConditionInspection/eventReporting?pageFrom=index&inspectObject=1`,
      });
      break;
    default:
      break;
  }
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: v-bind(systemBarHeight);
  background: #f2f2f2;
}
.top_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 284rpx;
  background: v-bind(globalBlue);
}
.bottom_btn {
  z-index: 99 !important;
}
.uv-nav-slot {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}
.content {
  position: relative;
  padding: 18rpx 40rpx 0 40rpx;
  margin-top: 88rpx;
}
.data_statistics {
  // margin-top: 32rpx;
  padding: 32rpx 38rpx;
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  .data_item {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 600;
    font-size: 28rpx;
    color: #373737;
    line-height: 40rpx;
    text-align: center;
    .data_val {
      font-family:
        Bakbak One,
        Bakbak One;
      font-weight: bold;
      font-size: 48rpx;
      color: #373737;
      line-height: 68rpx;
    }
    .red_val {
      color: #ff2d2d;
    }
    .data_title {
      margin-top: 8rpx;
    }
  }
}
.menu_list {
  margin: 40rpx 0 64rpx 0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .menu_item {
    margin-bottom: 28rpx;
    width: 140rpx;
    height: 140rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 16rpx 22rpx 18rpx 22rpx;
    box-sizing: border-box;
    text-align: center;
    .menu_icon {
      width: 64rpx;
      height: 64rpx;
    }
    .menu_title {
      font-family: PingFang SC-Regular;
      font-size: 24rpx;
      color: #8e8e8e;
      line-height: 28rpx;
    }
  }
  .menu_item:nth-last-child(-n + 4) {
    margin-bottom: 0rpx;
  }
}
.recently_list {
  background: #fff;
  border-radius: 32rpx;
  padding-bottom: 32rpx;
  .title_box {
    display: flex;
    align-items: center;
    height: 100rpx;
    border-bottom: 2rpx solid #f0f0f0;
    padding: 0 28rpx;

    .title {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 36rpx;
      color: #373737;
      line-height: 52rpx;
      text-align: left;
    }
  }
  .list_box {
    padding: 0 28rpx;
    .card {
      width: 100%;
      height: 310rpx;
      position: relative;
      text-align: center;
      box-sizing: border-box;
      margin-top: 28rpx;
      border-radius: 16rpx;
      background: #fff;
      box-shadow: 0 0 8rpx 0 rgba($color: #000000, $alpha: 0.1);
      padding: 26rpx 0;
      .card_top_status {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        height: 44rpx;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        border-radius: 0 16rpx 0 16rpx;
        &.card_top_status1 {
          color: #5c9ef7;
          background: #e2eeff;
        }
        &.card_top_status2 {
          color: #4a7ef6;
          background: #dfe8ff;
        }
        &.card_top_status3 {
          color: #61dc53;
          background: #ebfff2;
        }
      }
      .card_top {
        display: flex;
        position: relative;
        padding-right: 100rpx;
        .card_top_icon {
          display: block;
          width: 132rpx;
          height: 54rpx;
          margin: 0 20rpx 0 -16rpx;
        }
        .card_top_title {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          text-align: left;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          font-size: 32rpx;
          color: #373737;
        }

        .data_title_box {
          display: flex;
          align-items: center;
          .data_logo {
            display: inline-block;
            margin-right: 20rpx;
            width: 108rpx;
            height: 48rpx;
          }
          .data_title {
            flex: 1;
            text-align: left;
          }
        }
      }
      .card_content {
        margin-top: 28rpx;
        padding: 0rpx 28rpx;
        font-family:
          PingFang SC,
          PingFang SC;
        border-bottom: 2rpx solid #f0f0f0;
        .content_item {
          margin-bottom: 20rpx;
          display: flex;
          align-items: baseline;
          font-weight: 400;
          font-size: 28rpx;
          line-height: 40rpx;
          .content_name {
            width: 140rpx;
            color: #b0b0b0;
            text-align: right;
          }
          .content_val {
            flex: 1;
            text-align: left;
            color: #404040;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .card_bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 28rpx;
        .log_date {
          display: flex;
          align-items: center;
          .date_icon {
            display: inline-block;
            margin-right: 8rpx;
            width: 28rpx;
            height: 28rpx;
          }
          .date {
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #6ba7f8;
          }
        }
        .detail_icon {
          display: block;
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
}

.black {
  height: 200rpx;
}
</style>
